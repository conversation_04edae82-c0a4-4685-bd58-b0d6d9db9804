common:
  metadata_db:
    host: $db_metadata_host
    port: $db_metadata_port
    user: root
    password: $db_metadata_password
    database: data_agent
  llm:
    api_key: $llm_api_key
    base_url: $llm_base_url
    model_name: $llm_model_name
    temperature: 0.7
  llm_embedding:
    api_key: $embedding_api_key
    model_name: hunyuan-embedding
    base_url: $embedding_base_url
    model_dims: $embedding_dims
observe:
  trace:
    otel_endpoint: $trace_otel_endpoint
    otel_token: $trace_otel_token
  metric:
    addr: $metric_prom_host
    port: $metric_prom_port
memory:
  es:
    host: $memory_es_host
    password: $memory_es_password
  vector_store:
    host: $memory_es_host
    password: $memory_es_password
    model_dims: $embedding_dims
automic:
  nl2sql:
    es:
      host: $knowledge_base_es_host
      password: $knowledge_es_password
    embedding:
      api_key: $embedding_api_key
      model_name: hunyuan-embedding
      base_url: $embedding_base_url
      model_dims: $embedding_dims
  aisearch:
    es:
      host: $knowledge_base_es_host
      index_name: $knowledge_base
      user: $knowledge_es_user
      password: $knowledge_es_password
    access_info:
      endpoint: $es_endpoint
      service: $es_service
      api_version: $es_api_version
      region: $es_region
      secret_id: $es_secret_id
      secret_key: $es_secret_key
    cos_info:
      secret_id: $cos_secret_id
      secret_key: $cos_secret_key
      region: $cos_region