ALTER TABLE knowledge_list
    ADD COLUMN knowledge_base_name VA<PERSON>HA<PERSON>(255) comment '知识库名称';

ALTER TABLE user_agent_versions
    ADD COLUMN app_id VARCHAR(20) comment '主用户 ID';

ALTER TABLE user_info
    ADD COLUMN app_id VARCHAR(20) comment '主用户 ID';

ALTER TABLE user_session
    ADD COLUMN app_id VARCHAR(20) comment '主用户 ID';

ALTER TABLE user_session DROP PRIMARY KEY;
UPDATE user_session SET app_id = '' WHERE app_id IS NULL;
ALTER TABLE user_session ADD PRIMARY KEY (app_id, sub_account_uin, session_id);

ALTER TABLE user_agent_versions DROP PRIMARY KEY;
UPDATE user_agent_versions SET app_id = '' WHERE app_id IS NULL;
ALTER TABLE user_agent_versions ADD PRIMARY KEY (app_id, sub_account_uin, agent_id);

CREATE TABLE if not exists  knowledge_base (
    id      VARCHAR(64) COMMENT '知识库 ID',
    name    VARCHAR(255) COMMENT '知识库 名称',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
    ) COMMENT='知识库表';
INSERT IGNORE INTO knowledge_base (id, name) VALUES ('default', 'default');