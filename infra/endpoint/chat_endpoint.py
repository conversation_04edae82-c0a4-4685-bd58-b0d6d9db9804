import asyncio
import json
import time
import traceback
import uuid

from common.database.database import global_metadata_db_pool
from common.logger.logger import logger, get_trace_logger
from common.metric import enpoints
from common.share.config import appConfig
from common.share.error import ErrorCode
from common.share.stream_param import StreamGenerationParams
from common.elements.agent_event import <PERSON>rrorEvent, FinishEvent, CloseEvent, RecordEvent, TitleEvent
from fastapi.responses import StreamingResponse

from common.share.utils import detect_language
from infra.adapter.user_agent_adapter import UserAgentAdapter
from infra.adapter.agent_list_adapter import AgentListAdapter
from infra.adapter.user_count_adapter import UserCountAdapter
from infra.adapter.user_session_adapter import UserSessionAdapter
from infra.domain.user_agent_entity import UserAgentVersion
from infra.domain.user_count_entity import UserCount
from infra.domain.user_session_entity import UserSession
from infra.endpoint import chat_asyn_task
from infra.endpoint.agent import Agent<PERSON>anager
from datetime import datetime

from infra.endpoint.chat_asyn_task import Chat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ger
from infra.memory.memory import factory
from infra.memory.chat_es_operator import Cha<PERSON><PERSON><PERSON><PERSON><PERSON>
from openai import OpenAI


class ChatManager:
    class ChatException(Exception):
        def __init__(self, error_code, sub_account_uin):
            self.error_code = error_code
            self.sub_account_uin = sub_account_uin

    def __init__(self, ctx):
        self.mysql_pool = global_metadata_db_pool()
        self.ctx = ctx
        self.logger = get_trace_logger(ctx, "chat")

    async def handle_chat(self, chat_config, background_tasks, response):
        ctx = self.ctx
        try:
            # 阶段一：聊天前准备
            chat_context, session_title_task, session_title = await self.prepare_chat(chat_config, background_tasks)
            # 阶段二：聊天中，流式响应
            ret = self.generate_stream(chat_context, background_tasks, session_title_task, session_title)
            return StreamingResponse(ret, media_type="text/event-stream")
        except Exception as e:
            if isinstance(e, self.ChatException):
                self.logger.error(f"处理聊天请求时发生ChatException: {e}, traceback: {traceback.format_exc()}")
                ret = generate_error_stream(e.error_code, e.sub_account_uin)
            else:
                self.logger.error(f"处理聊天请求时发生Exception: {e}, traceback: {traceback.format_exc()}")
                if response:
                    response.status_code = 500
                ret = generate_error_stream(ErrorCode.InternalError, getattr(ctx, "sub_account_uin", ""))
            return StreamingResponse(ret, media_type="text/event-stream")

    async def prepare_chat(self, chat_config, background_tasks):
        ctx = self.ctx
        req_context = chat_config.get("Context")
        app_id = ctx.app_id
        sub_account_uin = ctx.sub_account_uin
        trace_id = ctx.trace_id
        session_id = ctx.session_id
        question = chat_config.get("Question")
        old_record_id = chat_config.get("OldRecordId")
        knowledge_base_ids = chat_config.get("KnowledgeBaseIds")
        record_id = str(uuid.uuid4())
        chat_config["RecordId"] = record_id
        self.logger.info(f"开始准备聊天 - chat_config: {chat_config}, ctx: {ctx}")

        # 1. 解析 context
        mcp_instance, db_table, eg_instance, db_info = self.parse_context(req_context)

        # 2. 处理旧记录删除
        if old_record_id:
            background_tasks.add_task(self.delete_chat_record, ctx, old_record_id)

        # 3. 用户计数校验
        user_count_adapter = UserCountAdapter(self.mysql_pool)
        user_count = user_count_adapter.get_statistics(ctx)
        self.check_user_count(user_count, ctx)

        # 4. 校验当前session_id，存储chat_req
        session_title_task, session_title = self.check_and_save_session(ctx, chat_config)

        # 5. 获取/创建 UserInfo
        user_agent_info = self.get_user_agent(ctx)

        # 6. 获取 session title 和历史会话，保存运行中的record_id
        history_memory = await self.get_chat_history(ctx, user_agent_info, question)

        # 7. 统计计数异步任务
        background_tasks.add_task(self.insert_user_count_record, ctx, app_id, sub_account_uin, session_id, record_id)

        # 8. 构造参数，获取 agent actor
        params = StreamGenerationParams(
            ctx=ctx,
            question=question,
            agent_id=user_agent_info["AgentID"],
            history_memory=history_memory,
            mcp_instance=mcp_instance,
            eg_instance=eg_instance,
            db_table=db_table,
            record_id=record_id,
            req_context=req_context,
            db_info=db_info,
            knowledge_base_ids=knowledge_base_ids,
        )
        self.logger.info(
            f"生成params成功 - SubAccountUin: {sub_account_uin}, SessionID: {session_id}, trace_id: {trace_id}")
        return params, session_title_task, session_title

    def parse_context(self, req_context):
        mcp_instance = None
        db_info = ""
        db_table = []
        eg_instance = {}
        tc_catalog_name = ""
        mcp_type = ""
        if req_context:
            parsed_data = json.loads(req_context)
            mcp_instance = parsed_data.get("MCP")
            if isinstance(mcp_instance, dict):
                mcp_instance = [mcp_instance]
            elif not isinstance(mcp_instance, list):
                mcp_instance = []

            if len(mcp_instance) > 0:
                tc_catalog_name = mcp_instance[0].get("TCCatalogName", "")
                mcp_type = mcp_instance[0].get("Type", "")

            db_table = parsed_data.get("DbTable", [])
            result_dict = {
                "DbTable": db_table,
                "TCCatalogName": tc_catalog_name,
                "Type": mcp_type
            }
            db_info = json.dumps(result_dict, ensure_ascii=False)
            eg_instance = parsed_data.get("EG", {})
            scenario = parsed_data.get("Scenario", "")
            if scenario == "dana":
                mcp_list_config = appConfig.automic.mcp.example.get("data", {}).get("MCP", [])
                mcp_instance = [mcp_tool for mcp_tool in mcp_list_config if mcp_tool["Type"] == "es_sql"]
        return mcp_instance, db_table, eg_instance, db_info

    async def delete_chat_record(self, ctx, old_record_id):
        trace_id = ctx.trace_id
        try:
            sub_account_uin = ctx.sub_account_uin
            session_id = ctx.session_id
            logger.info(f"开始删除 delete_chat_record - SubAccountUin: {sub_account_uin}, SessionID: {session_id}, "
                        f"old_record_id :{old_record_id}, trace_id: {trace_id}")
            chat_es_operator = ChatESOperator.get_instance(ctx.app_id)
            await chat_es_operator.async_delete_chat_record(sub_account_uin, session_id, old_record_id)
            logger.info(f"成功删除 delete_chat_record, trace_id: {trace_id}")
        except Exception as delete_chat_e:
            logger.error(f"删除 delete_chat_record error: {delete_chat_e},trace_id: {trace_id}")

    def check_user_count(self, user_count, ctx):
        if user_count:
            total_count = user_count['total_count']
            if total_count > appConfig.common.count_limit.user_count_limit:
                logger.error(f"app_id: {ctx.app_id} 记录数: {user_count['total_count']}, 默认阈值：{appConfig.common.count_limit.user_count_limit}")
                raise self.ChatException(ErrorCode.UserCountLimitExceeded, ctx.sub_account_uin)
            sub_account_count = user_count['sub_account_count']
            if sub_account_count > appConfig.common.count_limit.sub_user_count_limit:
                logger.error(f"子账号 {ctx.sub_account_uin} 记录数: {user_count['sub_account_count']}, 默认阈值：{appConfig.common.count_limit.sub_user_count_limit}")
                raise self.ChatException(ErrorCode.SubUserCountLimitExceeded, ctx.sub_account_uin)
            session_count = user_count['session_count']
            if session_count > appConfig.common.count_limit.sub_user_count_session_limit:
                logger.error(f"子账号 {ctx.sub_account_uin} 会话 {ctx.session_id} 记录数: {user_count['session_count']}, 默认阈值：{appConfig.common.count_limit.sub_user_count_session_limit}")
                raise self.ChatException(ErrorCode.SessionCountLimitExceeded, ctx.sub_account_uin)

    def get_user_agent(self, ctx):
        user_agent_adapter = UserAgentAdapter(self.mysql_pool)
        user_agent_version = user_agent_adapter.get_latest_by_user(ctx)
        user_agent_info = {}
        if not user_agent_version:
            agent_list_adapter = AgentListAdapter(self.mysql_pool)
            agent_list = agent_list_adapter.get_all(ctx)
            if not agent_list:
                logger.error(f"agent_list中没有可用Agent - SubAccountUin: {ctx.sub_account_uin}, trace_id: {ctx.trace_id}")
                raise self.ChatException(ErrorCode.NotFoundError, ctx.sub_account_uin)
            first_agent = agent_list[0]
            default_agent = UserAgentVersion(
                app_id=ctx.app_id,
                sub_account_uin=ctx.sub_account_uin,
                agent_id=first_agent.agent_id,
                agent_name=first_agent.agent_name,
                agent_version=first_agent.agent_version,
                description=first_agent.description
            )
            success = user_agent_adapter.add_version(ctx, default_agent)
            if not success:
                logger.error(f"创建默认Agent失败 - SubAccountUin: {ctx.sub_account_uin}, trace_id: {ctx.trace_id}")
                raise self.ChatException(ErrorCode.ChatAgentCreateError, ctx.sub_account_uin)
            user_agent_info = {
                "AgentID": str(first_agent.agent_id),
                "AgentName": first_agent.agent_name,
                "Version": first_agent.agent_version,
            }
        else:
            user_agent_info = {
                "AgentID": str(user_agent_version.agent_id),
                "AgentName": user_agent_version.agent_name,
                "Version": user_agent_version.agent_version
            }
        return user_agent_info

    async def get_chat_history(self, ctx, user_agent_info, question):
        mm = await factory.get_memory_manager(ctx.app_id)
        history_memory = await mm.search_memory(question, ctx.sub_account_uin, user_agent_info["AgentID"], ctx.session_id)
        return history_memory

    def insert_user_count_record(self, ctx, app_id, sub_account_uin, session_id, record_id):
        try:
            user_count_adapter = UserCountAdapter(self.mysql_pool)
            user_count_info = UserCount(
                app_id=app_id,
                sub_account_uin=sub_account_uin,
                session_id=session_id,
                record_id=record_id,
                create_time=datetime.now()
            )
            user_count_success = user_count_adapter.create(ctx, user_count_info)
            if not user_count_success:
                logger.error(f"插入用户计数记录失败 - SubAccountUin: {sub_account_uin}, SessionID: {session_id}")
            else:
                logger.info(f"成功插入用户计数记录 - SubAccountUin: {sub_account_uin}, SessionID: {session_id}")
        except Exception as user_count_e:
            logger.error(f"插入用户计数记录异常: {user_count_e}", exc_info=True)

    async def generate_stream(self, params, background_tasks, session_title_task, session_title):
        """生成流式响应的辅助函数
        参数:
            agent_actor: Agent实例(Ray Actor或本地Agent)
        """
        model = appConfig.memory.llm.model_name
        ctx = self.ctx
        agent_actor = await AgentManager.get_or_create_agent(ctx, model, params=params)
        # 用于存储所有的chunk
        full_response = []
        task_list = ""
        studio_jupyter = {}
        final_summary = ""
        cell_id = ""
        error_context = ""
        text_response = ""
        complete_think = ""
        think = []
        complete_response = []
        try:
            heartbeat_interval = appConfig.common.time_out.chat_heart_time_out
            chat_time_out = appConfig.common.time_out.chat_time_out
            model = appConfig.common.llm.model_name

            self.logger.info(
                f"generate_stream start heartbeat_interval:{heartbeat_interval}, chat_time_out:{chat_time_out}")

            self.logger.info(f"generate_stream RecordEvent: {RecordEvent(content=params.record_id).to_sse_format()}")
            yield RecordEvent(content=params.record_id).to_sse_format()
            chat_gen = start_chat(agent_actor, model, params)
            queue = asyncio.Queue()

            async def producer():
                try:
                    async for chat_chunk in chat_gen:
                        await queue.put(chat_chunk)
                finally:
                    await queue.put(None)  # 结束信号

            producer_task = asyncio.create_task(producer())
            timeout = False
            last_chunk_time = time.time()
            start_time = time.time()
            self.logger.info(f"generate_stream check heartbeat start")
            while True:
                # 逐个yield数据块
                try:
                    chunk = await asyncio.wait_for(queue.get(), timeout=heartbeat_interval)
                except asyncio.TimeoutError:
                    now = time.time()
                    self.logger.info(f"Timeout: now={now}, last_chunk_time={last_chunk_time}, diff={now - last_chunk_time}")
                    # 超过90秒没收到新chunk，报错
                    if now - start_time > chat_time_out and not timeout:
                        self.logger.error(f"generate_stream timeout")
                        error_info = ErrorEvent(ErrorCode.TimeoutError)
                        self.logger.info(f"generate_stream error_info: {error_info.content}")
                        error_context = json.dumps(error_info.content, ensure_ascii=False)
                        # 超时调用stop接口
                        actor_name = f"{ctx.sub_account_uin}_{ctx.session_id}"
                        enpoints.record_error("/chat", ErrorCode.TimeoutError.value, ctx.sub_account_uin)
                        # 尝试停止agent
                        await AgentManager.stop_agent(actor_name)
                        yield error_info.to_sse_format()
                        timeout = True
                    yield ": heartbeat\n\n"
                    yield RecordEvent(content=params.record_id).to_sse_format()

                    continue

                if chunk is None:
                    break

                last_chunk_time = time.time()

                # 你的业务逻辑
                self.logger.info(
                    f"generate_stream event_type: {chunk.event_type}, content: {chunk.content}")

                if chunk.event_type == "task_list":
                    task_list_dict = chunk.content
                    task_list = json.dumps(task_list_dict, ensure_ascii=False)
                elif chunk.event_type == "think":
                    think.append(chunk.content)
                elif chunk.event_type == "final_summary":
                    final_summary += add_append_summary(chunk.to_dict())
                    cell_id = chunk.to_dict().get('cell_id', '')
                elif chunk.event_type == "text":
                    text_response = chunk.content
                elif chunk.event_type == "studio_jupyter":
                    updated_studio_jupyter(studio_jupyter, chunk.to_str())
                elif chunk.event_type == "error":
                    if hasattr(chunk, "err_code") and ErrorCode.GuardrailNotSafe == chunk.err_code:
                        think = []
                        full_response = []
                    error_info = chunk.content
                    self.logger.info(f"generate_stream error_info: {error_info}")
                    error_context = json.dumps(error_info, ensure_ascii=False)
                elif chunk.event_type == "record_meta":
                    self.logger.info(f"heart beat record_meta: {chunk.content}")
                    continue
                elif chunk.content:
                    full_response.append(chunk.content)  # 将chunk添加到列表中
                yield chunk.to_sse_format()  # 逐个返回给客户端

            if session_title_task:
                session_title = await session_title_task
            yield TitleEvent(content=session_title).to_sse_format()
            # 在所有chunk都处理完后，拼接完整的响应
            final_summary_result = {
                "v": final_summary,
                "cell_id": cell_id
            }
            final_summary = json.dumps(final_summary_result, ensure_ascii=False)
            self.logger.info(
                f"generate_stream full_response: {full_response}, final_summary:{final_summary}, "
                f"text_response: {text_response}, studio_jupyter: {studio_jupyter},")
            for chunk in full_response:
                if isinstance(chunk, dict):
                    # 如果是字典，可以选择转换为字符串
                    complete_response.append(str(chunk))  # 或者使用 json.dumps(chunk)
                elif isinstance(chunk, str):
                    complete_response.append(chunk)
            complete_think = ''.join(think)
            complete_response = ''.join(complete_response)
            self.logger.info(f"complete_response: {complete_response}")
            self.logger.info(f"task_list: {task_list}")

        except Exception as e:
            self.logger.error(f"生成流式响应失败: {e}", exc_info=True)
            error_info = ErrorEvent(
                err_code=ErrorCode.InternalError,
            ).to_sse_format()
            yield error_info
            enpoints.record_error("/chat", ErrorCode.InternalError.value, ctx.sub_account_uin)
        finally:
            # 阶段三：流式结束后的后台任务
            task_manager = ChatAsyncTaskManager(ctx)
            task_manager.add_tasks(
                background_tasks=background_tasks,
                params=params,
                complete_response=complete_response,
                complete_think=complete_think,
                task_list=task_list,
                final_summary=final_summary,
                studio_jupyter=studio_jupyter,
                error_context=error_context,
                mysql_pool=self.mysql_pool
            )
            yield FinishEvent().to_sse_format()
            yield CloseEvent().to_sse_format()

    def check_and_save_session(self, ctx, chat_config):
        session_adapter = UserSessionAdapter(self.mysql_pool)
        user_session = session_adapter.get_by_id(ctx)
        session_title_task = None
        session_title = ""
        if user_session:
            if user_session.run_record:
                self.logger.warning(f"check_run_session is running, session_id: {ctx.session_id}")
                raise self.ChatException(ErrorCode.SessionIsRunning, ctx.sub_account_uin)
            session_title = user_session.session_title
            self.save_session_info(chat_config, ctx, session_adapter)
        else:
            session_title_task = asyncio.create_task(self.save_session_info_and_summary(chat_config, ctx, session_adapter))
        return session_title_task, session_title

    def save_session_info(self, chat_config, ctx, session_adapter):
        chat_config_json = json.dumps(chat_config, ensure_ascii=False)
        self.logger.info(f"保存会话信息 - SubAccountUin: {ctx.sub_account_uin}, SessionID: {ctx.session_id}, "
                    f"chat_config_json: {chat_config_json}")

        try:
            success = session_adapter.update_session(ctx, chat_config_json)
            if not success:
                self.logger.error(f"保存会话信息失败 - SubAccountUin: {ctx.sub_account_uin}, SessionID: {ctx.session_id}")
            self.logger.info(
                f"保存会话信息成功 - SubAccountUin: {ctx.sub_account_uin}, SessionID: {ctx.session_id}")
        except Exception as save_session_title_e:
            self.logger.error(f"保存会话信息异常: {save_session_title_e}")
            raise self.ChatException(ErrorCode.DatabaseError, ctx.sub_account_uin)

    async def save_session_info_and_summary(self, chat_config, ctx, session_adapter):
        question = chat_config.get("Question")
        session_title = self.get_summary(question)
        chat_config_json = json.dumps(chat_config, ensure_ascii=False)
        self.logger.info(f"保存会话标题 - SubAccountUin: {ctx.sub_account_uin}, SessionID: {ctx.session_id}, "
                    f"SessionTitle: {session_title}, chat_config_json: {chat_config_json}")

        try:
            session_entity = UserSession(
                app_id=ctx.app_id,
                sub_account_uin=ctx.sub_account_uin,
                session_id=ctx.session_id,
                session_title=session_title,
                run_record=chat_config_json
            )
            success = session_adapter.create(ctx, session_entity)
            if not success:
                self.logger.error(f"保存会话标题失败 - SubAccountUin: {ctx.sub_account_uin}, SessionID: {ctx.session_id}")
            self.logger.info(
                f"保存会话标题成功 - SubAccountUin: {ctx.sub_account_uin}, SessionID: {ctx.session_id}, SessionTitle: {session_title}")
            return session_title
        except Exception as save_session_title_e:
            self.logger.error(f"保存会话标题异常: {save_session_title_e}")
            raise self.ChatException(ErrorCode.DatabaseError, ctx.sub_account_uin)

    def get_summary(self, question: str):
        self.logger.info(f"get_summary question: {question}")

        lang = detect_language(question)
        if is_short_question(question, lang):
            return question
        try:
            self.logger.info(f"get_summary start !")
            client = OpenAI(
                api_key=appConfig.common.llm.api_key,
                base_url=appConfig.common.llm.base_url,
            )

            if lang == "English":
                prompt = (
                    "Summarize the following sentence in a concise phrase (no more than 8 words). "
                    "Only output the summary, do not include any extra words.\n"
                    "Example:\n"
                    "Sentence: The weather is great today, perfect for a walk.\n"
                    "Summary: Great weather, perfect for a walk.\n"
                    "----\n"
                    f"Sentence: {question}\n"
                    "Summary:"
                )
            else:
                prompt = (
                    "请将下面的句子总结成不超过15个字的简短句子，"
                    "只输出总结内容，不要带“总结”或其他多余文字。\n"
                    "示例：\n"
                    "句子：今天的天气非常好，阳光明媚，适合出去散步。\n"
                    "总结：今天天气晴朗，适合散步。\n"
                    "----\n"
                    f"句子：{question}\n"
                    "总结："
                )

            choices = client.chat.completions.create(
                model=appConfig.common.llm.model_name,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                stream=False
            )
            content_value = choices.choices[0].message.content.strip()
            self.logger.info(f"get_summary, content_value: {content_value}")
            return content_value
        except Exception as e:
            self.logger.error(f"Failed to create summary LLM: {str(e)}")
            raise

    async def continue_chat(self, continue_config, response):
        session_adapter = UserSessionAdapter(self.mysql_pool)
        ctx = self.ctx
        user_session = session_adapter.get_by_id(ctx)
        #RecordId = continue_config.get("RecordId")
        if user_session:
            if not user_session.run_record:
                self.logger.error(f"check_run_session is running, {ctx.sub_account_uin}, session: {ctx.session_id}")
                ret = generate_error_stream(ErrorCode.SessionIsRunning, ctx.sub_account_uin)
                return StreamingResponse(ret, media_type="text/event-stream")

            run_record = json.loads(user_session.run_record)
            record_id = run_record.get("RecordId")
            try:
                chunks = AgentManager.get_chunks_for_resume(ctx, record_id)
                self.logger.info(f"continue_chat chunks: {chunks}")
                ret = self.continue_chunk_stream(chunks, user_session.session_title, record_id)
                return StreamingResponse(ret, media_type="text/event-stream")
            except Exception as e:
                self.logger.error(f"continue_chat 生成流式响应失败: {e}", exc_info=True)
        else:
            self.logger.error(f"check_run_session not found, {ctx.sub_account_uin}, session: {ctx.session_id}")
            ret = generate_error_stream(ErrorCode.NotFoundError, ctx.sub_account_uin)
            return StreamingResponse(ret, media_type="text/event-stream")

    async def continue_chunk_stream(self, chunks, title, record_id):
        self.logger.info(f"continue_chunk_stream start : {chunks}")
        heartbeat_interval = appConfig.common.time_out.chat_heart_time_out
        chat_time_out = appConfig.common.time_out.chat_time_out
        start_time = time.time()
        last_chunk_time = start_time
        timeout = False

        queue = asyncio.Queue()
        yield RecordEvent(content=record_id).to_sse_format()
        async def producer():
            try:
                async for chunk in chunks:
                    await queue.put(chunk)
            finally:
                await queue.put(None)  # 结束信号

        producer_task = asyncio.create_task(producer())
        try:
            while True:
                try:
                    chunk = await asyncio.wait_for(queue.get(), timeout=heartbeat_interval)
                except asyncio.TimeoutError:
                    now = time.time()
                    self.logger.info(f"continue_chunk_stream heartbeat timeout: now={now}, last_chunk_time={last_chunk_time}, diff={now - last_chunk_time}")
                    # 超时判断
                    if now - start_time > chat_time_out and not timeout:
                        self.logger.error(f"continue_chunk_stream timeout")
                        error_info = ErrorEvent(ErrorCode.TimeoutError)
                        self.logger.info(f"continue_chunk_stream error_info: {error_info.content}")
                        yield error_info.to_sse_format()
                        break
                    # 发送心跳
                    yield ": heartbeat\n\n"
                    yield RecordEvent(content=record_id).to_sse_format()
                    continue

                if chunk is None:
                    break

                last_chunk_time = time.time()
                self.logger.info(f"chunk_stream chunk :{chunk}")
                yield chunk.to_sse_format()  # 逐个返回给客户端

        except Exception as e:
            self.logger.error(f"continue_chunk_stream error: {e}", exc_info=True)
            yield ErrorEvent(err_code=ErrorCode.InternalError).to_sse_format()
        finally:
            yield TitleEvent(content=title).to_sse_format()
            yield FinishEvent().to_sse_format()
            yield CloseEvent().to_sse_format()


async def start_chat(agent_actor, model, params: StreamGenerationParams):
    logger.info(f"start_chat agent_actor : {agent_actor}")
    async for chunk in AgentManager.chat(model, params):
        yield chunk


def is_short_question(question: str, lang: str) -> bool:
    if lang == "English":
        # 英文按单词数
        return len(question.strip().split()) < 9
    else:
        # 中文按汉字数
        return len(question) < 16


async def generate_error_stream(error_code, sub_account_uin):
    """
    生成错误信息的流式响应。

    :param sub_account_uin:
    :param error_code: 包含错误信息的字典
    :yield: JSON 格式的错误信息字符串
    """
    # 将错误数据转换为 JSON 字符串
    logger.info(f"generate_error_stream error_data: {error_code}")

    error_info = ErrorEvent(error_code)
    enpoints.record_error("/chat", error_code.value, sub_account_uin)
    logger.info(f"generate_stream error_info: {error_info.content}")
    yield error_info.to_sse_format()
    # 发送结束标志
    yield FinishEvent().to_sse_format()
    yield CloseEvent().to_sse_format()


def updated_studio_jupyter(studio_jupyter, jupyter):
    try:
        jupyter_dict = json.loads(jupyter)
        jupyter_id = jupyter_dict.get("v", {}).get("id")

        if jupyter_id is None:
            logger.error("jupyter_id not found in the data!")
        else:
            studio_jupyter[jupyter_id] = jupyter_dict  # 存储字典，而不是原始字符串

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse jupyter JSON: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


def add_append_summary(summary_dict):
    return str(summary_dict.get('v', ''))
