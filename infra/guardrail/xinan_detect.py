import httpx
import asyncio
import time
from common.logger.logger import logger
from common.share.config import appConfig

class XinanDetect:
    def __init__(self, sessionid, requestid, prompt, product_source="Intellix"):
        self.url = appConfig.guardrails.xinan_detect_input_url
        self.sessionid = sessionid
        self.requestid = requestid
        self.prompt = prompt
        self.input_appid = appConfig.guardrails.xinan_detect_input_appid
        self.output_appid = appConfig.guardrails.xinan_detect_output_appid
        self.product_source = product_source
        # self.prompt_res=0

    async def check_text_safety_input(self, content: str):
        payload = {
            "Comm": {
                "uiAppID": self.input_appid,
                "strMsgID": self.requestid,
                "uiSendTime": int(time.time())
            },
            "Content": {
                "strSessionID": self.sessionid,
                "strRequestID": self.requestid,
                "Msg": {
                    "uiMsgNum": 1,
                    "MsgMap": {
                        "strEnvironment": "Prod",
                        "strProductSource": self.product_source
                    },
                    "MsgNum_0": {
                        "strMediaType": "Text",
                        "strDataOri": content
                    }
                }
            }
        }

        try:
            async with httpx.AsyncClient() as client:
                logger.info("Input Xinan detect start")
                response = await client.post(self.url, json=payload)
                response.raise_for_status()
                return response.json()
        except httpx.RequestError as e:
            return {"error": str(e)}
        
    async def check_text_safety_output(self, content):
        payload = {
            "Comm": {
                "uiAppID": self.output_appid,
                "strMsgID": self.requestid,
                "uiSendTime": int(time.time())
            },
            "Content": {
                "strSessionID": self.sessionid,
                "strRequestID": self.requestid,
                "Msg": {
                    "uiMsgNum": 1,
                    "MsgMazp": {
                        "strPrompt": self.prompt,
                        # "uiPrompt_Pol_Result_Code": prompt_res,                       
                        "strEnvironment": "Test",
                        "strProductSource": self.product_source
                    },
                    "MsgNum_0": {
                        "strMediaType": "Text",
                        "strDataOri": content
                    }
                }
            }
        }

        try:
            async with httpx.AsyncClient() as client:
                logger.info("Input Xinan detect start")
                response = await client.post(self.url, json=payload)
                response.raise_for_status()
                return response.json()
        except httpx.RequestError as e:
            return {"error": str(e)}
   
    
    def parse_labels(self, response_json):
        try:
            self.prompt_res = response_json.get('Res', {}).get('uiResultCode', 0)
            first_label = response_json.get('Res', {}).get('strResultFirstLabel', '{}')
            labels = eval(first_label)  # Convert string to dictionary
            political_level = labels.get('Political', {}).get('Level', None)
            redone_level = labels.get('RedOne', {}).get('Level', None)
            return {
                "Political_Level": political_level,
                "RedOne_Level": redone_level,
                "Prompt_Res": self.prompt_res
            }
        except Exception as e:
            return {"error": str(e)}

# Example usage:
async def main():
    sessionid = "1749470276"
    requestid = "1749470276"
    msgid = "1749470276"
    detector = XinanDetect(sessionid, requestid, msgid)
    text_to_check = "1"
    result = await detector.check_text_safety_input(text_to_check)
    labels = detector.parse_labels(result)
    print("Parsed Labels:", labels)

if __name__ == "__main__":
    asyncio.run(main())