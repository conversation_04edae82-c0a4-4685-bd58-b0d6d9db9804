import langid
import logging

# 配置日志记录
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.ERROR)

class LanguageIdentifier:
    def __init__(self, languages=None):
        """
        Initialize the LanguageIdentifier with optional language constraints.
        :param languages: List of language codes to constrain the identification (e.g., ['zh', 'en']).
        """
        if languages:
            langid.set_languages(languages)

    def identify_language(self, text):
        """
        Identify the language of the given text using langid.
        :param text: The text to classify.
        :return: A tuple containing the language code and confidence score.
        """
        try:
            return langid.classify(text)[0]
        except Exception as e:
            logger.error(f"Error identifying language for text: {text}. Exception: {e}")
            # 返回默认语言或 "unknown" 以确保程序继续运行
            return "unknown"

if __name__ == "__main__":
    identifier = LanguageIdentifier(languages=['zh', 'en'])
    print(identifier.identify_language("This is a test"))
    print(identifier.identify_language("这是一个测试"))
    print(identifier.identify_language("simple test for 中文"))
