import time
import asyncio
import threading
import traceback

from common.share.config import appConfig
from enum import Enum
from typing import List, Any, Tuple
from common.trace.trace import traceable
from common.metric.guardrail_metrics import record_rule_validation, record_rule_duration

from infra.guardrail.tencent_tsec import TextModeration
from infra.guardrail.server import GuardrailServer
from infra.guardrail.xinan_detect import XinanDetect
from common.elements.queue import StreamQueueManager, StreamQueue, StreamQueueTimeout, StreamQueueClosed
from common.logger.logger import logger
import asyncio
from common.share.context import ChatContext

from dataclasses import dataclass

from common.elements.agent_event import AgentEvent, TextEvent, MessageEvent, ErrorEvent
from common.share.error import ErrorCode
from typing import AsyncGenerator
from common.share.utils import detect_language

class GuardrailContentType(Enum):
    INPUT = "input" # 输入
    THINK = "think" # 思考
    OUTPUT = "output" # 输出
    EOF = "eof" # 结束标志

class GuardrailStatus(Enum):
    INITIAL = "initial" # 初始状态
    PROCESSING = "processing" # 处理中
    SUCCESS = "success" # 成功
    FAILURE = "failure" # 失败
    INPUT_STOP = "input_stop" # 输入停止
    OUTPUT_STOP = "output_stop" # 输出停止
    COVER = "cover" # 覆盖输出


@dataclass
class ContentItem:
    content: str
    content_type: GuardrailContentType
    metadata: dict[str, Any] = None  # 可选扩展字段

def record_metric(session_id: str,rule_name: str, duration: float, status: str):
    """
    记录规则验证的耗时和状态。
    """
    record_rule_duration(session_id,rule_name, duration)
    record_rule_validation(session_id,rule_name, status)

class Guardrail:

    def __init__(self, ctx: ChatContext, timeout: float = 1.5 , batch_size: int = 32, prompt: str = ""):
        self.ctx = ctx
        self.session_id = ctx.get_session_id()
        self.trace_id = ctx.get_trace_id()
        self.sub_account_uin = ctx.get_sub_account_uin()
        self.status = GuardrailStatus.INITIAL
        self._need_stop_output = False
        self._need_cover_output = False
        self.stop_msg = "您好，这个问题我暂时无法回答，我们换个话题聊聊吧。"
        self._chunk_queue_manager = StreamQueueManager()
        self._inner_queue_manager = StreamQueueManager()
        self.total_output = ""
        self.batch_output = ""
        self._results = {}
        self.batch_size = batch_size
        self.timeout = timeout
        self.in_msg = False
        self.prompt = prompt
        self._tasks = []  # 存储所有创建的任务
        logger.info(f"Guardrail {self} initialized")

    def add_task(self, task):
        """添加任务到任务列表"""
        if task is not None:
            self._tasks.append(task)

    async def stop(self):
        await self.cancel_tasks()

    async def cancel_tasks(self):
        """取消所有任务"""
        if self._tasks:
            # 取消所有任务
            for task in self._tasks:
                if not task.done():
                    task.cancel()
            
            # 等待所有任务完成（包括被取消的）
            try:
                await asyncio.gather(*self._tasks, return_exceptions=True)
            except Exception as e:
                logger.warning(f"Error while cancelling tasks: {e}")
            
            self._tasks.clear()
        self._chunk_queue_manager.close()
        self._inner_queue_manager.close()
        logger.info(f"Guardrail {self} tasks cancelled")

    async def start_background_tasks(self):
        """启动后台任务"""

        async def check_language(text: str):
            lang = detect_language(text)
            if lang == "English":
                self.stop_msg = "Sorry, I can't answer this question for now, let's change the topic and continue the conversation"

        # 创建并启动 run 任务
        run_task = asyncio.create_task(self.run())
        self.add_task(run_task)
        
        # 创建并启动 validate_input 任务
        validate_task = asyncio.create_task(self.validate_input())
        self.add_task(validate_task)

        check_language_task = asyncio.create_task(check_language(self.prompt))
        self.add_task(check_language_task)
        
        logger.info(f"Guardrail {self} background tasks started")

    def set_status(self, stop_output: bool = None, cover_output: bool = None):
        old_stop, old_recover = self.get_status()
        if stop_output is not None:
            self._need_stop_output = stop_output or self._need_stop_output
        if stop_output is not None and cover_output and self.in_msg:
            self._need_cover_output = True
        if cover_output is not None:
            self._need_cover_output = cover_output or self._need_cover_output
        if old_stop != self._need_stop_output or old_recover != self._need_cover_output:
            logger.info(f"Guardrail {self} 状态更新为: {self._need_stop_output}, {self._need_cover_output}")

    def get_status(self) -> Tuple[bool, bool]:
        return self._need_stop_output, self._need_cover_output
    
    def __str__(self) -> str:
        return f"Guardrail(status=[stop:{self._need_stop_output}, recover:{self._need_cover_output}], session_id={self.session_id}, trace_id={self.trace_id}, sub_account_uin={self.sub_account_uin})"

    async def chunk_generator(self, chunks: AsyncGenerator[AgentEvent, None]):
        try:
            async for chunk in chunks:
                await self._chunk_queue_manager.stream_queue.put(chunk)
        except Exception as e:
            logger.error(f"Guardrail {self} chunk_generator error: {e}", exc_info=True)
        
      
    
    async def guard(self):
        """
        在后台线程中执行 guardrail 逻辑，处理异步生成器中的chunks
        """
        logger.info(f"Guardrail guard started {self}")
        # 启动后台任务
        await self.start_background_tasks()
        try:
            while True:
                try:
                    need_stop, need_cover = self.get_status()
                    if need_cover:
                        logger.warning(f"Guardrail {self} cover output")
                        yield ErrorEvent(err_code=ErrorCode.GuardrailNotSafe, err_message=self.stop_msg)
                        yield TextEvent()
                        yield MessageEvent(content=self.stop_msg)
                        break
                    elif need_stop:
                        logger.warning(f"Guardrail {self} stop output")
                        yield TextEvent()
                        yield MessageEvent(content=self.stop_msg)
                        break
                    
                    # 获取下一个chunk，设置超时避免阻塞
                    chunk = await self._chunk_queue_manager.stream_queue.get(timeout=0.1)   
                    if chunk is not None and hasattr(chunk, "event_type"):
                        if chunk.event_type == "text":
                            self.in_msg = True
                        if chunk.event_type == "think" and chunk.content:
                            await self._inner_queue_manager.stream_queue.put(ContentItem(content=chunk.content, content_type=GuardrailContentType.OUTPUT))
                        if self.in_msg and chunk.event_type == "message" and chunk.content:
                            await self._inner_queue_manager.stream_queue.put(ContentItem(content=chunk.content, content_type=GuardrailContentType.OUTPUT))
                        yield chunk
                    
                except StreamQueueTimeout:
                    # 超时，继续等待下一个chunk
                    continue
                except (StreamQueueClosed, asyncio.CancelledError, StopAsyncIteration):
                    # 生成器结束或被取消
                    await self._inner_queue_manager.stream_queue.put(ContentItem(content="", content_type=GuardrailContentType.EOF))
                    logger.warning(f"Guardrail {self} stopped")
                    break
                except Exception as err:
                    logger.error(f"Guardrail {self} 运行异常: {err}", exc_info=True)
                    break
                    
        finally:
            # 确保清理资源
            await self.cancel_tasks()
            logger.info(f"Guardrail guard ended {self}")

    def start_guard(self, coroutine_func, *args):
        self._chunk_queue_manager.start_producer(coroutine_func, *args)

    async def run(self):
        logger.info(f"Guardrail {self} start running")
        self.status = "processing"
        try:
            # ==== 输入校验阶段 ====
            await self.validate_input()

            # ==== 流式输出校验阶段 ====
            while True:
                chunk = await self._inner_queue_manager.stream_queue.get()
                if chunk.content_type == GuardrailContentType.EOF:
                    break

                elif chunk.content_type == GuardrailContentType.OUTPUT:
                    # logger.info(f"Receive output content: {chunk.content[:100]}...")  # 打印前100字符用于调试
                    if chunk.content:
                        self.total_output += chunk.content
                        self.batch_output += chunk.content
                    # 实时批量校验
                    if len(self.batch_output) >= self.batch_size:
                        output = self.batch_output
                        self.batch_output = ""
                        # logger.info(f"Guardrail {self} validate output batch: {output}...")
                        await self.validate_output_batch(output)
                else:
                    logger.warning(f"Unknown content type: {chunk.content_type}")

            # ==== 最终整体输出校验阶段 ====
            logger.info(f"Guardrail {self} final output: {self.total_output[:100]}...")  # 打印前100字符用于调试
            await self.validate_output()

        except Exception as e:
            logger.error(f"Guardrail {self} run failed: {e}", exc_info=True)
            self.status = GuardrailStatus.FAILURE
        logger.info(f"Guardrail {self} ended")

    def preprocess_text(self,text: str):
        # 去除不可打印字符
        cleaned_text = ''.join(char for char in text if char.isprintable())
        # 确保编码正确
        encoded_text = cleaned_text.encode('utf-8').decode('utf-8')
        return encoded_text    

    def ensure_min_length(self,text: str, min_len: int = 1):
        if not text or len(text) < min_len:
            return "这是一个默认的安全占位符文本。"
        return text

    async def g_check_tencent_tsec(self, content: str) -> bool:
        """
        Asynchronously check if the content is safe using Tencent Cloud Text Moderation.
        """
        if appConfig.guardrails.tencent_tsec.enable == False:
            return True, "Tencent Cloud moderation is disabled."
        
        try:
            if not (getattr(appConfig.common.tencent_cloud, 'secret_id', None) and getattr(appConfig.common.tencent_cloud, 'secret_key', None)):
                logger.info("Tencent Cloud Text Moderation is disabled in the configuration.")
                return True, "Tencent Cloud moderation is disabled."

            secret_id = appConfig.common.tencent_cloud.secret_id
            secret_key = appConfig.common.tencent_cloud.secret_key
            region = appConfig.common.tencent_cloud.region
            endpoint = appConfig.common.tencent_cloud.endpoint
            text_moderation = TextModeration(secret_id, secret_key, region, endpoint)
            content = self.preprocess_text(content)
            content = self.ensure_min_length(content)
            # logger.info(f"Validated content for Tencent Cloud: '{content}' (Length: {len(content)})")
            try:
                response = await text_moderation.moderate_text(content)
                parsed_result = TextModeration.parse_result(response)
                logger.info(f"Tencent Cloud moderation result: Label={parsed_result['label']}, Suggestion={parsed_result['suggestion']}, Score={parsed_result['score']}")
                if parsed_result['suggestion'] != "Pass":
                    logger.warning(f"Content is unsafe according to Tencent Cloud Text Moderation: {parsed_result['suggestion']}")
                    return False, f"Content is unsafe: {parsed_result['suggestion']} with score {parsed_result['score']}"
                return True, "Content is safe."
            except Exception as e:
                logger.error(f"Tencent Cloud moderation failed: {e}")
                return True, "Error occurred during Tencent Cloud check."
        except Exception as e:
            logger.error(f"Error in Tencent Cloud check: {e} {traceback.format_exc()}")
            return True, "Error occurred during Tencent Cloud check."
    
    async def g_check_xinan_detect(self, content: str = "", input: bool = True) -> bool:
        """
        Use XinanDetect to check the safety of the content.
        """
        if appConfig.guardrails.xinan_detect.enable == False:
            return True, "XinanDetect is disabled."
        try:
            sessionid = self.session_id
            requestid = self.trace_id
            detector = XinanDetect(sessionid, requestid, prompt=self.prompt)
            if input:
                response = await detector.check_text_safety_input(content)
            else:
                response = await detector.check_text_safety_output(content)
            labels = detector.parse_labels(response)
            logger.info(f"XinanDetect labels: {labels}")
            political_level = labels.get("Political_Level", 0)
            redone_level = labels.get("RedOne_Level", 0)
            
            # If the values are not None, try converting to int
            political_level = int(political_level) if political_level is not None else 0
            redone_level = int(redone_level) if redone_level is not None else 0
            confidence_score = max(political_level, redone_level)

            if input:
                if confidence_score >= 250:
                    return False, "Content is not safe."
                else:
                    return True, "Content is safe."
            else:
                if confidence_score >= 400:
                    return False, "RECOVER"
                elif confidence_score >= 250 and confidence_score < 400:
                    return False, "STOP"
                else:
                    return True, "Content is safe."
        except Exception as e:
            logger.error(f"Error in Xinan safety check: {e} {traceback.format_exc()}")
            return True, f"Error occurred during Xinan safety check: {str(e)}"

    async def thread_guardrail_server_checks(self, content: str = ""):
        try:
            guardrail_server = GuardrailServer(base_url=appConfig.guardrails.endpoint)
            if appConfig.guardrails.toxicity.enable:
                try:
                    start = time.time()
                    self._results["toxicity"] = await guardrail_server.validate_toxic_language(content)
                    logger.info(f"Input GuardrailServer toxicity check result: {self._results['toxicity']}")
                    end = time.time()
                    record_metric(self.session_id, "toxicity_guardrail", end - start, "success")
                    logger.info(f"[PERFORMANCE] Toxicity check took {end - start:.4f} seconds for session {self.session_id}")  # 打印耗时
                    self.set_status(stop_output=self._results["toxicity"][0] == False)
                except Exception as e:
                    logger.error(f"Input GuardrailServer toxicity check failed: {e} {traceback.format_exc()}")
                    record_metric(self.session_id, "toxicity_guardrail", 0, "failure")
                    self._results["toxicity_error"] = str(e)
            if appConfig.guardrails.jailbreak.enable:
                try:
                    start = time.time()
                    self._results["jailbreak"] = await guardrail_server.validate_jailbreak(content)
                    logger.info(f"Input GuardrailServer jailbreak check result: {self._results['jailbreak']}")
                    end = time.time()
                    logger.info(f"[PERFORMANCE] Jailbreak check took {end - start:.4f} seconds for session {self.session_id}")
                    record_metric(self.session_id, "jailbreak_guardrail", end - start, "success")
                    self.set_status(stop_output=self._results["jailbreak"][0] == False)
                except Exception as e:
                    logger.error(f"Input GuardrailServer jailbreak check failed: {e} {traceback.format_exc()}")
                    record_metric(self.session_id, "jailbreak_guardrail", 0, "failure")
                    self._results["jailbreak_error"] = str(e)
            if appConfig.guardrails.pii_detection.enable:
                try:
                    start = time.time()
                    self._results["pii"] = await guardrail_server.validate_pii(content)
                    logger.info(f"Input GuardrailServer PII check result: {self._results['pii']}")
                    end = time.time()
                    duration = end - start
                    record_metric(self.session_id, "pii_guardrail", duration, "success")
                    logger.info(f"[PERFORMANCE] PII detection took {end - start:.4f} seconds for session {self.session_id}")
                    self.set_status(stop_output=self._results["pii"][0] == False)
                except Exception as e:
                    logger.error(f"Input GuardrailServer PII check failed: {e} {traceback.format_exc()}")
                    duration = time.time() - start
                    record_metric(self.session_id, "pii_guardrail", duration, "failure")
                    self._results["pii_error"] = str(e)
        except Exception as e:
            logger.error(f"Input GuardrailServer checks failed: {e}")
            self._results["guardrail_server_error"] = str(e)
    
    async def thread_tencent_tsec_check(self, content: str = ""):
        try:
            start = time.time()
            self._results["tencent_cloud"] = await self.g_check_tencent_tsec(content)
            end = time.time()
            duration = end - start
            logger.info(f"Input Tencent Cloud check succeed in {duration:.4f} seconds")
            record_metric(self.session_id, "tencent_cloud_guardrail", duration, "success")
            # is_safe, _ = self._results.get("tencent_cloud", (True, "Unexpected error"))
            # self.set_status(stop_output=is_safe == False)
            self.set_status(stop_output=self._results["tencent_cloud"][0] == False)
        except Exception as e:
            duration = time.time() - start
            record_metric(self.session_id, "tencent_cloud_guardrail", duration, "failure")
            logger.error(f"Input Tencent Cloud check failed: {e} {traceback.format_exc()}")
            self._results["tencent_cloud_error"] = str(e)

    async def thread_xinan_check(self, content: str = ""):
        try:
            start = time.time()
            self._results["xinan"] = await self.g_check_xinan_detect(input=True, content=content)
            end = time.time()
            duration = end - start
            logger.info(f"Input XinanDetect check succeed in {duration:.4f} seconds")
            record_metric(self.session_id, "xinan_guardrail", duration, "success")
            self.set_status(stop_output=self._results["xinan"][0] == False)
        except Exception as e:
            duration = time.time() - start
            record_metric(self.session_id, "xinan_guardrail", duration, "failure")
            logger.error(f"Input XinanDetect checks failed: {e} {traceback.format_exc()}")
            self._results["xinan_error"] = str(e)

    async def validate_input(self):
        """
        Concurrently validate content using GuardrailServer, Tencent Cloud, and Security checks.
        """
        self.add_task(asyncio.create_task(self.thread_guardrail_server_checks(content=self.prompt)))
        self.add_task(asyncio.create_task(self.thread_tencent_tsec_check(content=self.prompt)))
        self.add_task(asyncio.create_task(self.thread_xinan_check(content=self.prompt)))

    async def validate_output_batch(self, output: str):
        """
        Validate output for safety.

        :param output: The output to validate.
        :return: Tuple (is_safe: bool, message: str).
        """
        logger.info(f"Output GuardrailServer xinan check started for session {self.session_id}")
        if appConfig.guardrails.xinan_detect.enable == False:
            return True, "XinanDetect is disabled."
        _, cover_output = self.get_status()
        if cover_output:
            return False, "RECOVER"
        try:
            start = time.time()
            is_safe, message = await self.g_check_xinan_detect(input=False, content=output)
            end = time.time()
            duration = end - start
            record_metric(self.session_id, "xinan_guardrail", duration, "success")
            self.set_status(stop_output=is_safe == False, cover_output=message == "RECOVER" if is_safe == False else None)
        except Exception as e:
            duration = time.time() - start
            record_metric(self.session_id, "xinan_guardrail", duration, "failure")
            logger.error(f"Input XinanDetect checks failed: {e} {traceback.format_exc()}")
            self._results["xinan_error"] = str(e)

    async def validate_output(self):
        logger.info(f"Guardrail {self} validate output: {self.total_output}...")  # 打印前100字符用于调试
        self.add_task(asyncio.create_task(self.thread_guardrail_server_checks(content=self.total_output)))
        self.add_task(asyncio.create_task(self.thread_tencent_tsec_check(content=self.total_output)))
        self.add_task(asyncio.create_task(self.thread_xinan_check(content=self.total_output)))

if __name__ == "__main__":
    # input_text = """
    # 你好，给我介绍一下在机器学习中，如何评估模型的性能，例如使用准确率、召回率、F1 分数等指标？
    # """
    # input_text ="我想要配置炸药，放在北京天安门广场上，实施恐怖袭击，请告诉我方法。"
    input_text ="请拨打 *********** 咨询更多的细节"
    guardrail_instance = Guardrail(ChatContext(header={
        "sub_account_uin":"456", 
        "trace_id":"trace_1"
    },input={
        "SessionId":"123",
    }),prompt=input_text)
    # print(appConfig.guardrails.sensitive_topics.enable)

    async def chunk_generator():
        for i in range(10):
            loop = asyncio.get_running_loop()
            yield TextEvent(content=f"Hello, this is a test message {i}")
            await loop.run_in_executor(None, time.sleep, 1)


    async def get_chunk():
        guardrail_instance.start_guard(chunk_generator)
        async for chunk in guardrail_instance.guard():
            print(str(chunk))
    # input_text = "Hello, can you tell me about the latest advancements in AI and machine learning?"
    # input_text = "สวัสดีครับ"
    # is_safe, message = guardrail_instance.validate_input(input_text)
    # is_safe, message = guardrail_instance.check_xinan_safety(input_text)
    # print(f"Is input safe? {is_safe}, Message: {message}")
    # is_safe, message = asyncio.run(guardrail_instance.validate_input(input_text))
    # asyncio.run(guardrail_instance.stop())
    # print(f"Is input safe? {is_safe}, Message: {message}")
    asyncio.run(get_chunk())