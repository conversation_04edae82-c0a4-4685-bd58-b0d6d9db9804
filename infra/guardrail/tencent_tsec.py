import base64
import json
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.tms.v20201229 import tms_client, models
import asyncio


class TextModeration:
    def __init__(self, secret_id: str, secret_key: str, region: str = "ap-guangzhou", endpoint: str ="tms.tencentcloudapi.com"):
        """
        初始化文本内容安全检测类。

        :param secret_id: 腾讯云 API 的 SecretId。
        :param secret_key: 腾讯云 API 的 SecretKey。
        :param region: API 请求的区域，默认为 "ap-guangzhou"。
        """
        self.cred = credential.Credential(secret_id, secret_key)
        self.http_profile = HttpProfile()
        self.http_profile.endpoint = endpoint
        self.client_profile = ClientProfile()
        self.client_profile.httpProfile = self.http_profile
        self.client = tms_client.TmsClient(self.cred, region, self.client_profile)

    async def moderate_text(self, text: str, biz_type: str = "TencentCloudDefault") -> dict:
        """
        异步检测文本内容的安全性。

        :param text: 要检测的文本内容。
        :param biz_type: 业务类型，默认为 "TencentCloudDefault"。
        :return: 返回检测结果的 JSON 格式。
        """

        b64_text = base64.b64encode(text.encode('utf-8')).decode('utf-8')
        req = models.TextModerationRequest()
        params = {
            "Content": b64_text,
            "BizType": biz_type
        }
        req.from_json_string(json.dumps(params))

        loop = asyncio.get_event_loop()
        resp = await loop.run_in_executor(None, self.client.TextModeration, req)
        return json.loads(resp.to_json_string())

    @staticmethod
    def parse_result(response: dict) -> dict:
        """
        解析腾讯云文本内容安全检测结果，提取主要检测主题、处理建议和评分。

        :param response: 腾讯云返回的 JSON 响应。
        :return: 提取的核心信息，包括主题类别、建议处理方式和评分。
        """
        result = {
            "label": None,
            "suggestion": None,
            "score": None
        }

        # 取 DetailResults 中评分最高的类别（即主要命中主题）
        if "DetailResults" in response:
            detail_results = response["DetailResults"]
            if detail_results:
                main_label_info = max(detail_results, key=lambda x: x.get("Score", 0))
                result["label"] = main_label_info.get("Label")
                result["suggestion"] = main_label_info.get("Suggestion")
                result["score"] = main_label_info.get("Score")

        return result
