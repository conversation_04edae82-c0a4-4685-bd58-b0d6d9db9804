import asyncio
import json
import signal
import sys
import threading
import time
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON>Executor, Future
from queue import Queue
from typing import Callable, Union, Awaitable
import traceback

from common.database.database import MysqlPool
from common.logger.logger import logger
from common.share.config import appConfig
from common.tencentcloud.ai_search_batch_index import process_document_semantic_stream, process_document_rule_stream, \
    DocumentChunkConfig, DocumentChunk
from infra.adapter.distributed_lock_adapter import DistributedLockAdapter
from infra.adapter.knowledge_list_adapter import KnowledgeListAdapter
from infra.adapter.task_list_adapter import TaskListAdapter
from infra.adapter.task_node_adapter import TaskNodeAdapter
from infra.domain.task_list_entity import TaskList

worker_num = appConfig.automic.aisearch.knowledge_base.max_workers
concurrent_task_num = appConfig.automic.aisearch.knowledge_base.max_concurrent_tasks
scan_intervals = appConfig.automic.aisearch.knowledge_base.scan_interval
lock_key = appConfig.automic.aisearch.knowledge_base.lock_name
lock_expires = appConfig.automic.aisearch.knowledge_base.lock_timeout
task_timeout = appConfig.automic.aisearch.knowledge_base.task_timeout  # 单个任务超时时间(秒)
heartbeat_interval = appConfig.automic.aisearch.knowledge_base.heartbeat_interval  # 心跳间隔(秒)
heartbeat_timeout = appConfig.automic.aisearch.knowledge_base.heartbeat_timeout  # 心跳超时(秒)
orphaned_check_interval = appConfig.automic.aisearch.knowledge_base.orphaned_check_interval  # orphaned检查间隔(秒)


class TaskProcessor:
    def __init__(self, task_executor: Union[Callable[[TaskList], None], Callable[[TaskList], Awaitable[None]]],
                 max_concurrent_tasks: int = 10, lock_name: str = "task_processor_global_lock", lock_timeout: int = 30,
                 task_timeout: int = 3600, heartbeat_interval: int = 30, heartbeat_timeout: int = 90,
                 orphaned_check_interval: int = 60, mysql_pool=None):
        self.task_executor = task_executor
        self.semaphore = threading.Semaphore(max_concurrent_tasks)  # 控制最大并发任务数
        self.running_tasks = {}  # 存储任务ID到Future的映射
        self.running = False
        self.task_list_adapter = TaskListAdapter(mysql_pool)
        self.task_node_adapter = TaskNodeAdapter(mysql_pool)
        self.knowledge_list_adapter = KnowledgeListAdapter(mysql_pool)
        # 分布式锁配置
        self.lock_name = lock_name
        self.lock_owner = f"processor-{threading.get_ident()}"  # 唯一标识当前实例
        self.lock_timeout = lock_timeout  # 锁超时时间（秒）
        self.lock_adapter = DistributedLockAdapter(mysql_pool)
        self.mysql_pool = mysql_pool

        # 任务超时配置
        self.task_timeout = task_timeout
        self.timeout_monitor = None  # 超时监控线程

        # 节点标识和心跳配置
        self.node_id = f"node-{threading.get_ident()}-{int(time.time() * 1000)}"  # 唯一节点ID
        self.heartbeat_interval = heartbeat_interval
        self.heartbeat_timeout = heartbeat_timeout
        self.heartbeat_thread = None

        # orphaned任务检查配置
        self.orphaned_check_interval = orphaned_check_interval
        self.orphaned_checker = None

        # 创建事件循环
        self.loop = asyncio.new_event_loop()
        self.loop_thread = threading.Thread(target=self._run_event_loop)
        self.loop_thread.daemon = True
        self.loop_thread.start()

    def _run_event_loop(self):
        asyncio.set_event_loop(self.loop)
        self.loop.run_forever()

    def start(self, scan_interval: int = 3):
        """启动任务处理器"""
        # 在启动时注册节点并重置orphaned任务
        self._register_node()
        self._reset_orphaned_tasks()

        self.running = True
        logger.info(f"任务处理器已启动，扫描间隔: {scan_interval} 秒，最大并发任务数: {self.semaphore._value}")

        # 启动扫描线程
        scan_thread = threading.Thread(target=self._scan_tasks, args=(scan_interval,))
        scan_thread.daemon = True
        scan_thread.start()

        # 启动超时监控线程
        self.timeout_monitor = threading.Thread(target=self._monitor_task_timeouts)
        self.timeout_monitor.daemon = True
        self.timeout_monitor.start()

        # 启动心跳线程
        self.heartbeat_thread = threading.Thread(target=self._send_heartbeat)
        self.heartbeat_thread.daemon = True
        self.heartbeat_thread.start()

        # 启动orphaned任务检查线程
        self.orphaned_checker = threading.Thread(target=self._check_orphaned_tasks_periodically)
        self.orphaned_checker.daemon = True
        self.orphaned_checker.start()

        return scan_thread

    def _register_node(self):
        """注册节点信息到数据库"""
        try:
            logger.info(f"注册节点 {self.node_id}")
            self.task_node_adapter.register_node(self.node_id)
        except Exception as e:
            logger.error(f"注册节点失败: {e}")
            traceback.print_exc()

    def _send_heartbeat(self):
        """定期发送心跳"""
        while self.running:
            try:
                self.task_node_adapter.update_node_heartbeat(self.node_id)
                logger.debug(f"发送心跳: {self.node_id}")
            except Exception as e:
                logger.error(f"发送心跳失败: {e}")
                traceback.print_exc()
            time.sleep(self.heartbeat_interval)

    def _reset_orphaned_tasks(self):
        """重置orphaned任务为待处理状态"""
        try:
            # 获取分布式锁
            if self.lock_adapter.acquire_lock(
                    lock_name=f"{self.lock_name}-reset-orphaned",
                    locked_by=self.lock_owner,
                    timeout=60
            ):
                try:
                    logger.info("正在重置orphaned任务...")
                    reset_count = self.task_list_adapter.reset_orphaned_tasks(self.heartbeat_timeout)
                    logger.info(f"已重置 {reset_count} 个orphaned任务为待处理状态")
                finally:
                    # 释放锁
                    self.lock_adapter.release_lock(
                        lock_name=f"{self.lock_name}-reset-orphaned",
                        locked_by=self.lock_owner
                    )
        except Exception as e:
            logger.error(f"重置orphaned任务失败: {e}")
            traceback.print_exc()

    def _check_orphaned_tasks_periodically(self):
        """定期检查并重置orphaned任务"""
        while self.running:
            try:
                # 获取分布式锁以确保只有一个节点执行检查
                if self.lock_adapter.acquire_lock(
                        lock_name=f"{self.lock_name}-orphaned-check",
                        locked_by=self.lock_owner,
                        timeout=60
                ):
                    try:
                        logger.debug("定期检查orphaned任务...")
                        reset_count = self.task_list_adapter.reset_orphaned_tasks(self.heartbeat_timeout)
                        if reset_count > 0:
                            logger.info(f"已重置 {reset_count} 个orphaned任务为待处理状态")
                    finally:
                        self.lock_adapter.release_lock(
                            lock_name=f"{self.lock_name}-orphaned-check",
                            locked_by=self.lock_owner
                        )
            except Exception as e:
                logger.error(f"检查orphaned任务失败: {e}")
                traceback.print_exc()

            # 等待下一次检查
            time.sleep(self.orphaned_check_interval)

    def stop(self):
        """优雅停止任务处理器，释放所有资源"""
        self.running = False
        # 停止事件循环
        self.loop.call_soon_threadsafe(self.loop.stop)
        self.loop_thread.join()

        # 处理未完成的任务
        for task_id in list(self.running_tasks.keys()):
            try:
                with self.mysql_pool.pool_db:
                    self.task_list_adapter.mark_task_status(task_id, 0, None)
                    logger.info(f"任务 {task_id} 已重置为待处理状态")
            except Exception as e:
                logger.error(f"重置任务 {task_id} 失败: {e}")

        try:
            self.task_node_adapter.mark_node_inactive(self.node_id)
        except Exception as e:
            logger.error(f"标记节点不活跃失败: {e}")

        logger.info("任务处理器已停止")

    def _scan_tasks(self, interval: int):
        """带锁的任务扫描循环"""
        while self.running:
            # 获取分布式锁
            if self.lock_adapter.acquire_lock(
                    lock_name=self.lock_name,
                    locked_by=self.lock_owner,
                    timeout=self.lock_timeout
            ):
                try:
                    # 获取可用槽位
                    available_slots = self._get_available_slots()
                    if available_slots > 0:
                        self._process_pending_tasks(available_slots)
                    else:
                        logger.info("没有可用槽位，跳过任务获取")
                finally:
                    # 释放锁
                    self.lock_adapter.release_lock(
                        lock_name=self.lock_name,
                        locked_by=self.lock_owner
                    )
            else:
                logger.info("其他实例正在处理任务，跳过本次扫描")

            # 等待下一次扫描
            time.sleep(interval)

    def _get_available_slots(self):
        """获取可用并发槽位数量"""
        return self.semaphore._value

    def _process_pending_tasks(self, available_slots: int):
        """获取并处理待执行任务，控制并发数量"""
        pending_tasks = self.task_list_adapter.get_task_list(limit=available_slots)
        if not pending_tasks:
            logger.debug("没有找到待处理的任务")
            return

        logger.info(f"找到 {len(pending_tasks)} 个待处理任务")
        for task in pending_tasks:
            if not self.semaphore.acquire(blocking=False):
                logger.info("并发任务数已达上限，停止获取新任务")
                break

            try:
                with self.mysql_pool.pool_db.atomic():
                    if self.task_list_adapter.mark_task_status(task.task_id, 1, self.node_id):
                        # 提交异步任务到事件循环
                        future = asyncio.run_coroutine_threadsafe(
                            self.task_executor(task),
                            self.loop
                        )
                        self.running_tasks[task.task_id] = {
                            'future': future,
                            'start_time': time.time(),
                            'task': task
                        }
                        future.add_done_callback(lambda f, t=task.task_id: self._handle_task_completion(t))
                        logger.info(f"异步任务 {task.task_id} 已提交到事件循环")
                    else:
                        self.semaphore.release()
                        logger.warning(f"无法标记任务 {task.task_id} 为运行中")
            except Exception as e:
                self.semaphore.release()
                logger.error(f"处理任务 {task.task_id} 时发生异常: {e}")
                traceback.print_exc()
            finally:
                self.mysql_pool.pool_db.manual_close()

    def _handle_task_completion(self, task_id):
        """处理任务完成后的清理工作"""
        if task_id in self.running_tasks:
            task_info = self.running_tasks.pop(task_id)
            self.semaphore.release()
            logger.info(f"任务 {task_id} 已完成，释放信号量")

    def _monitor_task_timeouts(self):
        """监控任务超时"""
        while self.running:
            try:
                current_time = time.time()
                # 复制一份当前运行的任务列表进行检查
                task_futures = list(self.running_tasks.items())

                for task_id, task_info in task_futures:
                    future = task_info['future']
                    file_id = task_info['task'].file_id
                    start_time = task_info['start_time']

                    # 检查任务是否已完成
                    if future.done():
                        continue

                    # 检查任务是否超时
                    if current_time - start_time > self.task_timeout:
                        logger.warning(f"任务 {task_id} 执行超时（超过 {self.task_timeout} 秒）")
                        self._handle_task_timeout(task_id, file_id, future)

            except Exception as e:
                logger.error(f"任务超时监控发生错误: {e}")
                traceback.print_exc()
            time.sleep(10)

    def _handle_task_timeout(self, task_id, file_id, future):
        """处理超时任务"""
        try:
            if not future.done():
                future.cancel()
                logger.info(f"任务 {task_id} 已取消")

            with self.mysql_pool.pool_db.atomic():
                self.task_list_adapter.mark_task_error(task_id, -1, "任务执行超时")
                self.knowledge_list_adapter.update_status(file_id, -1)

            if task_id in self.running_tasks:
                self.running_tasks.pop(task_id)
                self.semaphore.release()
                logger.info(f"任务 {task_id} 超时处理完成，释放信号量")
        except Exception as e:
            logger.error(f"处理任务 {task_id} 超时失败: {e}")
            traceback.print_exc()
        finally:
            self.mysql_pool.pool_db.manual_close()


def start_task_executor(mysql_pool: MysqlPool):
    async def custom_task_executor(task_data: TaskList):
        """自定义任务执行函数，处理文档分块和语义嵌入"""
        task_id = task_data.task_id
        task_list_adapter = TaskListAdapter(mysql_pool)
        knowledge_list_adapter = KnowledgeListAdapter(mysql_pool)
        try:
            logger.info(f"开始执行自定义任务 {task_id}")
            task_params = json.loads(task_data.task_params)
            chunk_type = task_params.get("ChunkType")

            def replace_escapes(s):
                s = s.replace('\\n\\n', '\n\n')
                s = s.replace('\\n', '\n')
                return s
            if chunk_type == 0:
                max_chunk_size = task_params.get("MaxChunkSize", 1000)
                delimiters = [replace_escapes(s) for s in task_params.get("Delimiters")] or ["\n\n", "\n", "。", "！", "？", "，", ""]
                chunk_overlap = task_params.get("ChunkOverlap", 200)
                await process_document_rule_stream(DocumentChunk(
                    TaskId=task_id,
                    FileId=task_data.file_id,
                    FileUrl=task_data.file_url,
                    AppId=task_data.app_id,
                    KnowledgeBaseId=task_data.knowledge_base_id,
                    ChunkConfig=DocumentChunkConfig(
                        MaxChunkSize=max_chunk_size,
                        Delimiters=delimiters,
                        ChunkOverlap=chunk_overlap,
                    )
                ),mysql_pool=mysql_pool,
                )
            elif chunk_type == 1:
                max_chunk_size = task_params.get("MaxChunkSize", 4800)
                await process_document_semantic_stream(DocumentChunk(
                    TaskId=task_id,
                    FileId=task_data.file_id,
                    FileUrl=task_data.file_url,
                    AppId=task_data.app_id,
                    KnowledgeBaseId=task_data.knowledge_base_id,
                    ChunkConfig=DocumentChunkConfig(
                        MaxChunkSize=max_chunk_size
                    )
                ),
                    mysql_pool=mysql_pool,
                )
            else:
                raise ValueError(f"未知的ChunkType: {chunk_type}")

            # 更新任务状态
            try:
                with mysql_pool.pool_db.atomic():
                    task_list_adapter.mark_task_status(task_id, 2)  # 标记为已完成
                    knowledge_list_adapter.update_status(task_data.file_id, 1)  # 更新知识库状态为已完成
            finally:
                mysql_pool.pool_db.manual_close()
            logger.info(f"任务 {task_id} 执行完成")
        except Exception as e:
            try:
                with mysql_pool.pool_db.atomic():
                    task_list_adapter.mark_task_error(task_id, -1, str(e))
                    knowledge_list_adapter.update_status(task_data.file_id, -1)
            finally:
                mysql_pool.pool_db.manual_close()
            logger.error(f"执行任务失败: {e}")

    def handle_exit(signum, frame):
        logger.info("接收到退出信号，执行清理操作...")
        processor.stop()
        sys.exit(0)

    # 创建任务处理器，传入自定义执行函数
    processor = TaskProcessor(
        task_executor=custom_task_executor,
        max_concurrent_tasks=concurrent_task_num,  # 最大并发任务数
        lock_name=lock_key,  # 分布式锁名称
        lock_timeout=lock_expires,
        task_timeout=task_timeout,  # 单个任务超时时间
        heartbeat_interval=heartbeat_interval,  # 心跳间隔
        heartbeat_timeout=heartbeat_timeout,  # 心跳超时
        orphaned_check_interval=orphaned_check_interval,  # orphaned检查间隔
        mysql_pool=mysql_pool,  # 数据库连接池
    )
    try:
        # 启动任务处理器
        processor.start(scan_interval=scan_intervals)
        signal.signal(signal.SIGTERM, handle_exit)
    except KeyboardInterrupt:
        logger.info("接收到Ctrl+C，程序终止")
        processor.stop()


if __name__ == "__main__":
    start_task_executor()
