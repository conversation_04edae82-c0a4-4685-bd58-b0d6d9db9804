import asyncio
import json
import os
import threading
import time
from abc import ABC, abstractmethod
from queue import Queue, Full, Empty
from threading import Lock, Thread
from typing import List, Dict, Any

import ray

from common.database.database import get_metadata_db_pool
from common.logger.logger import logger
from common.share import env
from common.share.config import appConfig
from common.tencentcloud.ai_search_batch_index import (
    process_document_semantic_stream,
    process_document_rule_stream,
    DocumentChunkConfig,
    DocumentChunk
)
from infra.adapter.knowledge_list_adapter import KnowledgeListAdapter
from infra.adapter.task_list_adapter import TaskListAdapter
from infra.domain.task_list_entity import TaskList

# 配置参数
LOCAL_QUEUE_MAX_SIZE = 100  # 本地队列最大容量
MONITOR_INTERVAL = 1  # 监控循环间隔时间
ERROR_RETRY_DELAY = 5  # 错误重试延迟时间 (秒)
MAX_TASK_RETRIES = 3  # 最大任务重试次数
GRACEFUL_SHUTDOWN = True  # 优雅关闭标志
GRACEFUL_SHUTDOWN_TIMEOUT = 30  # 优雅关闭时的最长等待时间 (秒)
# 新增：任务超时配置 (秒)
TASK_TIMEOUT = appConfig.automic.aisearch.knowledge_base.task_timeout  # 示例：每个任务最长执行1小时
# 僵尸任务清理器的检查间隔（秒）
CLEANUP_INTERVAL = 60
# 任务被视为僵尸任务的超时时间（秒）。如果一个处于“运行中”状态的任务，在此时间内没有更新其 `update_time`，则被视为僵尸任务。
STALE_TASK_TIMEOUT = TASK_TIMEOUT + CLEANUP_INTERVAL

_db_pool = None
_db_lock = threading.Lock()  # 用于线程安全的锁

actor_name = f"knowledge_base_executor_{env.INSTANCE_ID}"

def get_global_metadata_db_pool():
    global _db_pool
    # 双重检查锁定模式
    if _db_pool is None:
        with _db_lock:
            # 再次检查，避免在等待锁的过程中其他线程已经初始化
            if _db_pool is None:
                try:
                    _db_pool = get_metadata_db_pool()
                except Exception as e:
                    logger.warning(f"获取数据库连接池失败: {str(e)}")
                    raise
    return _db_pool


EXECUTION_MODE = os.getenv("EXECUTION_MODE", "LOCAL").upper()


def reset_orphaned_task():
    logger.info("正在重置孤立任务...")
    try:
        db_pool = get_global_metadata_db_pool()
        task_list_adapter = TaskListAdapter(db_pool)
        task_list_adapter.reset_tasks(actor_name)
        logger.info("重置孤立任务完成。")
    except Exception as e:
        logger.warning(f"重置孤立任务失败: {str(e)}")

class TaskProcessorBase(ABC):
    def __init__(self, max_concurrency: int = 10):
        super().__init__()
        self.max_concurrency = max_concurrency
        self.is_running = False
        self.monitor_thread = None

    @abstractmethod
    def _execute_task(self, task: TaskList):
        """抽象方法，用于实际执行单个任务"""
        pass

    @abstractmethod
    def _get_available_slots(self) -> int:
        """抽象方法，获取当前可用的任务槽位"""
        pass

    def _fetch_new_tasks(self) -> List[TaskList]:
        """从数据库获取新的待处理任务 (在主进程中执行)"""
        try:
            db_pool = get_global_metadata_db_pool()
            task_list_adapter = TaskListAdapter(db_pool)
            max_fetch_size = appConfig.automic.aisearch.knowledge_base.max_workers
            return task_list_adapter.mark_task_running(max_fetch_size, actor_name)
        except Exception as e:
            logger.warning(f"获取新任务失败: {str(e)}")
            return []

    def _monitor_loop(self):
        """监控循环，定期获取新任务并提交 (在主进程中执行)"""
        while self.is_running:
            try:
                available_slots = self._get_available_slots()

                if available_slots > 0:
                    max_fetch_size = appConfig.automic.aisearch.knowledge_base.max_workers
                    fetch_size = min(available_slots, max_fetch_size)
                    new_tasks = self._fetch_new_tasks()[:fetch_size]

                    for task in new_tasks:
                        success = False
                        attempt = 0
                        while not success and self.is_running and attempt < MAX_TASK_RETRIES:
                            success = self._submit_task(task)
                            if not success:
                                logger.warning(f"任务 {task.task_id} 提交失败，重试 {attempt + 1}/{MAX_TASK_RETRIES}")
                                attempt += 1
                                time.sleep(ERROR_RETRY_DELAY * (2 ** attempt) / 2)
                            else:
                                logger.info(f"成功提交新任务: {task.task_id}")
                                break
                        if not success:
                            logger.error(f"任务 {task.task_id} 提交失败，达到最大重试次数，将标记为错误。")
                            self._update_task_status(task.task_id, -1, "任务提交失败，无法分配执行资源")

                time.sleep(MONITOR_INTERVAL)
            except Exception as e:
                logger.error(f"监控循环异常: {str(e)}")
                time.sleep(ERROR_RETRY_DELAY)

    def _cleanup_loop(self):
        """僵尸任务清理的主循环，定期检查并处理僵尸任务 (在主进程中执行)"""
        logger.info(f"僵尸任务清理器启动，每 {CLEANUP_INTERVAL} 秒检查一次，状态更新超时阈值: {STALE_TASK_TIMEOUT} 秒。")
        db_pool = get_global_metadata_db_pool()
        task_list_adapter = TaskListAdapter(db_pool)
        while self.is_running:
            try:
                max_fetch_size = appConfig.automic.aisearch.knowledge_base.max_workers
                task_list_adapter.mark_task_time_out(max_fetch_size, STALE_TASK_TIMEOUT)
            except Exception as e:
                logger.warning(f"僵尸任务清理器主循环发生异常: {e}")

            time.sleep(CLEANUP_INTERVAL)

    @abstractmethod
    def _submit_task(self, task: TaskList) -> bool:
        """抽象方法，用于将任务提交给执行器"""
        pass

    def _update_task_status(self, task_id, status, error_msg=None):
        """更新任务在数据库中的状态 (在主进程中执行)"""
        try:
            db_pool = get_global_metadata_db_pool()
            task_list_adapter = TaskListAdapter(db_pool)
            if status == -1:
                task_list_adapter.mark_task_error(task_id, -1, error_msg)
            else:
                task_list_adapter.mark_task_status(task_id, status)
            logger.info(f"任务 {task_id} 状态更新为: {status}" + (f", 错误信息: {error_msg}" if error_msg else ""))
        except Exception as update_error:
            logger.error(f"更新任务 {task_id} 状态失败: {str(update_error)}")

    def start(self):
        """启动任务处理器"""
        if self.is_running:
            logger.warning("任务处理器已经在运行")
            return

        self.is_running = True
        self._start_executor()

        self.monitor_thread = Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

        # 启动僵尸任务清理线程
        self.cleanup_thread = Thread(target=self._cleanup_loop)
        self.cleanup_thread.daemon = True
        self.cleanup_thread.start()

        logger.info(f"任务处理器已启动，最大并发数: {self.max_concurrency}")

    @abstractmethod
    def _start_executor(self):
        """抽象方法，启动具体的任务执行器"""
        pass

    def stop(self):
        """停止任务处理器"""
        if not self.is_running:
            logger.warning("任务处理器未在运行")
            return

        self.is_running = False

        if self.monitor_thread:
            logger.info("正在等待监控线程停止...")
            self.monitor_thread.join(timeout=GRACEFUL_SHUTDOWN_TIMEOUT)
            if self.monitor_thread.is_alive():
                logger.warning("监控线程未能及时停止。")

        # 停止僵尸清理线程
        if self.cleanup_thread:
            logger.info("正在等待僵尸任务清理线程停止...")
            self.cleanup_thread.join(timeout=CLEANUP_INTERVAL + 5)  # 给清理线程一些额外时间
            if self.cleanup_thread.is_alive():
                logger.warning("僵尸任务清理线程未能及时停止。")

        self._stop_executor()

        logger.info("任务处理器已停止")

    @abstractmethod
    def _stop_executor(self):
        """抽象方法，停止具体的任务执行器"""
        pass


class LocalTaskProcessor(TaskProcessorBase):
    def __init__(self, max_concurrency: int = 10):
        super().__init__(max_concurrency)
        self.task_queue = Queue(maxsize=LOCAL_QUEUE_MAX_SIZE)
        self.worker_threads = []
        self.loop = asyncio.new_event_loop()
        self.loop_thread = Thread(target=self._run_event_loop)
        self.loop_thread.daemon = True

        self.active_tasks = 0
        self.active_tasks_lock = Lock()
        # Local 模式下，db_pool 由 LocalTaskProcessor 实例持有并传递给其工作线程
        self.db_pool = get_metadata_db_pool()

    def _run_event_loop(self):
        """在单独线程中运行 asyncio 事件循环"""
        asyncio.set_event_loop(self.loop)
        self.loop.run_forever()

    def _worker_loop(self, worker_id: int):
        """本地工作线程的主循环，从队列获取任务并执行"""
        logger.info(f"本地工作线程 {worker_id} 已启动")
        while self.is_running:
            try:
                task = self.task_queue.get(timeout=1)
                if task:
                    with self.active_tasks_lock:
                        self.active_tasks += 1
                    try:
                        future = asyncio.run_coroutine_threadsafe(
                            custom_task_executor_async(task, self.db_pool),  # 使用 LocalTaskProcessor 自己的 db_pool
                            self.loop
                        )
                        future.result()
                    finally:
                        with self.active_tasks_lock:
                            self.active_tasks -= 1
                    self.task_queue.task_done()
            except Empty:
                continue
            except Exception as e:
                logger.error(f"本地工作线程 {worker_id} 处理任务异常: {str(e)}")

    async def _execute_task(self, task: TaskList):
        """LocalTaskProcessor 中实际执行任务的逻辑 (被 _worker_loop 调用)"""
        await custom_task_executor_async(task, self.db_pool)

    def _get_available_slots(self) -> int:
        """计算本地可用的任务槽位"""
        with self.active_tasks_lock:
            running_tasks = self.active_tasks
            queue_size = self.task_queue.qsize()

        return max(0, self.max_concurrency - running_tasks - queue_size)

    def _submit_task(self, task: TaskList) -> bool:
        """尝试将任务放入本地队列"""
        try:
            self.task_queue.put(task, block=False)
            return True
        except Full:
            logger.warning(f"本地任务队列已满，任务 {task.task_id} 提交失败。")
            return False

    def _start_executor(self):
        """启动本地事件循环线程和工作线程"""
        self.loop_thread.start()

        for i in range(self.max_concurrency):
            thread = Thread(target=self._worker_loop, args=(i,))
            thread.daemon = True
            thread.start()
            self.worker_threads.append(thread)
        logger.info(f"已启动 {self.max_concurrency} 个本地工作线程")

    def _stop_executor(self):
        """停止本地执行器，包括事件循环和工作线程"""
        if not self.loop.is_closed():
            self.loop.call_soon_threadsafe(self.loop.stop)

        logger.info("等待本地事件循环线程停止...")
        if self.loop_thread.is_alive():
            self.loop_thread.join(timeout=GRACEFUL_SHUTDOWN_TIMEOUT / 2)
            if self.loop_thread.is_alive():
                logger.warning("本地事件循环线程未能及时停止。")

        if not self.loop.is_closed():
            self.loop.close()

        for i, thread in enumerate(self.worker_threads):
            logger.info(f"等待本地工作线程 {i} 停止...")
            if thread.is_alive():
                thread.join(timeout=GRACEFUL_SHUTDOWN_TIMEOUT / len(self.worker_threads))
                if thread.is_alive():
                    logger.warning(f"本地工作线程 {i} 未能及时停止。")


@ray.remote
class RayTaskExecutor:
    """Ray Actor，负责在一个Ray进程中执行多个异步任务"""

    def __init__(self, worker_id: int, max_concurrent_tasks: int):
        self.worker_id = worker_id
        self.max_workers = max_concurrent_tasks
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.is_running = True
        self.task_counter = 0
        self.success_counter = 0
        self.failure_counter = 0
        self.start_time = time.time()

        # 不在初始化时连接数据库，而是在首次使用时
        self.db_pool = None
        self.db_pool_lock = threading.Lock()

        # 引入内部任务队列和多个消费者任务
        self.task_queue = asyncio.Queue()
        self.consumer_tasks = [asyncio.create_task(self._consume_tasks()) for _ in range(self.max_workers)]

        logger.info(f"Ray工作节点 {worker_id} (内部并发: {self.max_workers}) 已启动")
        # Actor 启动时，立即启动多个消费者任务，它们会持续运行

    def _get_db_pool(self):
        """获取数据库连接池，如果尚未初始化则初始化"""
        # 第一重检查，避免不必要的锁获取
        if self.db_pool is None:
            with self.db_pool_lock:
                # 第二重检查，确保在等待锁的过程中没有其他协程已经初始化了连接池
                if self.db_pool is None:
                    try:
                        logger.info(f"正在初始化数据库连接池，配置: {appConfig.common.metadata_db}")
                        self.db_pool = get_metadata_db_pool()
                        logger.info(f"Ray工作节点 {self.worker_id} 初始化数据库连接池成功")
                    except Exception as e:
                        logger.error(f"Ray工作节点 {self.worker_id} 初始化数据库连接池失败: {str(e)}")
                        raise
        return self.db_pool

    async def _consume_tasks(self):
        """持续从内部队列中取出任务并执行"""
        logger.info(f"Ray工作节点 {self.worker_id} 消费者任务已启动。")
        while self.is_running:
            try:
                task = await self.task_queue.get()  # 从队列获取任务
                if task is None:  # 收到停止信号
                    self.task_queue.task_done()  # 标记停止信号已处理
                    break

                # 更新正在执行的任务数量，这里直接使用 semaphore._value 来反映正在使用的槽位
                await self.execute_task(task)  # 直接 await 执行异步任务
                self.task_queue.task_done()  # 标记此任务已完成
            except Exception as e:
                logger.error(f"Ray工作节点 {self.worker_id} 消费任务时异常: {str(e)}")
            await asyncio.sleep(0.01)  # 短暂休眠，避免紧密循环占用CPU

    async def execute_task(self, task: TaskList):  # 这是异步方法
        """实际执行单个任务的异步方法"""
        if not self.is_running:
            logger.warning(f"Ray工作节点 {self.worker_id} 已停止，任务 {task.task_id} 不再执行。")
            return

        self.task_counter += 1
        try:
            async with self.semaphore:  # 使用信号量控制并发执行的任务数量
                # 使用 Actor 内部的 db_pool
                db_pool = self._get_db_pool()
                await custom_task_executor_async(task, db_pool)
                self.success_counter += 1
        except Exception as e:
            self.failure_counter += 1
            logger.error(f"Ray工作节点 {self.worker_id} 执行任务 {task.task_id} 异常: {str(e)}")
        finally:
            pass  # pending_tasks 的更新现在主要通过队列和信号量反映

    def submit_task(self, task: TaskList) -> bool:
        """从 Ray 外部提交任务到此 Actor 的事件循环 (在 Actor 内部执行)"""
        # 队列容量控制，防止无限增长
        if not self.is_running or self.task_queue.qsize() >= self.max_workers * 2:  # 例如，队列容量是最大并发的两倍
            logger.warning(f"Ray工作节点 {self.worker_id} 内部队列已满或未运行，无法提交任务 {task.task_id}。")
            return False

        try:
            self.task_queue.put_nowait(task)  # 将任务放入内部队列
            logger.info(f"任务 {task.task_id} 已添加到Ray工作节点 {self.worker_id} 的内部队列。")
            return True
        except asyncio.QueueFull:
            logger.warning(f"Ray工作节点 {self.worker_id} 内部队列已满 (put_nowait)，无法提交任务 {task.task_id}。")
            return False
        except Exception as e:
            logger.error(f"Ray工作节点 {self.worker_id} 提交任务到内部队列异常: {str(e)}")
            return False

    def get_metrics(self) -> Dict[str, Any]:
        """获取Actor的运行时指标"""
        uptime = time.time() - self.start_time
        # 正在执行的任务数 = max_workers - 信号量当前值
        currently_executing = self.max_workers - self.semaphore._value
        return {
            "worker_id": self.worker_id,
            "uptime": uptime,
            "total_tasks": self.task_counter,
            "success_tasks": self.success_counter,
            "failed_tasks": self.failure_counter,
            "throughput": self.success_counter / max(uptime, 1),
            "failure_rate": self.failure_counter / max(self.task_counter, 1) if self.task_counter > 0 else 0,
            "pending_tasks": self.task_queue.qsize() + currently_executing,  # 待处理任务 = 队列中的 + 正在执行的
            "available_slots": self.semaphore._value  # 可用槽位就是信号量当前的值
        }

    def stop(self):
        """停止Ray Actor"""
        if not self.is_running:
            return

        self.is_running = False
        # 向队列发送停止信号，并等待消费者任务完成
        try:
            # 发送停止信号给所有消费者
            for _ in range(self.max_workers):
                self.task_queue.put_nowait(None)
        except Exception as e:
            logger.warning(f"Ray工作节点 {self.worker_id} 发送停止信号到队列失败: {str(e)}")

        if self.consumer_tasks:
            logger.info(f"Ray工作节点 {self.worker_id} 正在等待所有消费者任务停止...")
            try:
                # 等待所有消费者任务完成
                ray.get([ray.wrap_future(asyncio.run_coroutine_threadsafe(task, asyncio.get_event_loop())) for task in self.consumer_tasks], timeout=GRACEFUL_SHUTDOWN_TIMEOUT)
            except ray.exceptions.RayActorError:
                logger.warning(f"Ray工作节点 {self.worker_id} 消费者任务可能已随Actor终止。")
            except Exception as e:
                logger.error(f"等待Ray工作节点 {self.worker_id} 消费者任务停止时发生错误: {str(e)}")

        if GRACEFUL_SHUTDOWN:
            logger.info(f"Ray工作节点 {self.worker_id} 正在优雅关闭，等待队列和执行中任务完成...")
            start_wait_time = time.time()
            while (self.task_queue.qsize() > 0 or (self.max_workers - self.semaphore._value) > 0) and \
                    time.time() - start_wait_time < GRACEFUL_SHUTDOWN_TIMEOUT:
                logger.info(
                    f"工作节点 {self.worker_id} 仍有队列任务 {self.task_queue.qsize()} 个，正在执行 {self.max_workers - self.semaphore._value} 个，等待...")
                time.sleep(1)

            if self.task_queue.qsize() > 0 or (self.max_workers - self.semaphore._value) > 0:
                logger.warning(
                    f"Ray工作节点 {self.worker_id} 超时停止，仍有 {self.task_queue.qsize()} 个队列任务和 {self.max_workers - self.semaphore._value} 个执行中任务未完成。")
            else:
                logger.info(f"Ray工作节点 {self.worker_id} 所有任务已完成，准备停止。")

        logger.info(f"Ray工作节点 {self.worker_id} 已停止")


class RayTaskProcessor(TaskProcessorBase):
    """Ray 任务处理器，管理一个或多个 RayTaskExecutor Actor"""

    def __init__(self, max_concurrency: int = 10):
        super().__init__(max_concurrency)
        self.executor = None
        self.worker_metrics = {}
        self.task_distribution = {}

    def _execute_task(self, task: TaskList):
        pass

    def _get_available_slots(self) -> int:
        """获取唯一的 Ray Actor 中可用的任务槽位"""
        if not self.executor:
            return 0
        try:
            metrics = ray.get(self.executor.get_metrics.remote())
            # RayTaskExecutor 内部维护了内部队列和正在执行的任务，其 pending_tasks 已经包含了这些信息
            return max(0, self.max_concurrency - metrics["pending_tasks"])
        except ray.exceptions.RayActorError as e:
            logger.error(f"获取Ray Actor指标失败，Actor可能已挂掉: {str(e)}")
            return 0
        except Exception as e:
            logger.error(f"获取Ray Actor指标异常: {str(e)}")
            return 0

    def _submit_task(self, task: TaskList) -> bool:
        """将任务提交给唯一的 Ray Actor"""
        if not self.executor:
            logger.error(f"Ray Executor 未初始化，无法提交任务 {task.task_id}。")
            return False
        try:
            # 从外部调用 Ray Actor 的方法，依然使用 .remote()，并获取结果
            result = ray.get(self.executor.submit_task.remote(task))

            # 获取 worker_id 的逻辑保持不变
            worker_id = ray.get(self.executor.get_metrics.remote())["worker_id"]
            self.task_distribution[worker_id] = self.task_distribution.get(worker_id, 0) + 1
            return result
        except ray.exceptions.RayActorError as e:
            logger.error(f"任务 {task.task_id} 提交给Ray Actor失败，Actor可能已挂掉: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"任务 {task.task_id} 提交异常: {str(e)}")
            return False

    def _start_executor(self):
        try:
            self.executor = RayTaskExecutor.options(
                name=actor_name,
                namespace="ray",
                max_concurrency=100,
                max_restarts=-1
            ).remote(0, self.max_concurrency)
            logger.info(f"已成功创建单个Ray工作节点 (ID: 0)，总并发数上限为: {self.max_concurrency}")
        except Exception as e:
            logger.error(f"创建 Ray Actor 失败: {e}")
            raise

    def _stop_executor(self):
        """停止 Ray 执行器"""
        if not self.executor:
            return

        logger.info("正在停止 Ray 执行器...")
        try:
            # 调用 Actor 的 stop 方法，它将处理内部的优雅关闭
            ray.get(self.executor.stop.remote(), timeout=GRACEFUL_SHUTDOWN_TIMEOUT * 2)
        except ray.exceptions.RayActorError as e:
            logger.error(f"停止Ray Actor时发生错误，Actor可能已经终止: {str(e)}")
        except Exception as e:
            logger.error(f"停止Ray Actor时发生未知错误: {str(e)}")

        if self.task_distribution:
            logger.info("任务分配统计:")
            for worker_id, count in self.task_distribution.items():
                logger.info(f"  工作节点 {worker_id}: {count} 个任务")


def create_task_processor() -> TaskProcessorBase:
    """根据运行模式创建相应的任务处理器"""
    max_concurrency = appConfig.automic.aisearch.knowledge_base.max_workers  # 最大并发任务数
    if EXECUTION_MODE == "RAY":
        return RayTaskProcessor(max_concurrency)
    else:
        return LocalTaskProcessor(max_concurrency)


async def custom_task_executor_async(task_data: TaskList, db_pool: Any):
    """
    实际执行任务的核心异步逻辑。
    包含任务重试和状态更新。
    """
    task_id = task_data.task_id

    for attempt in range(MAX_TASK_RETRIES):
        try:
            logger.info(f"开始执行任务 {task_id} (尝试 {attempt + 1}/{MAX_TASK_RETRIES})")

            # --- 应用超时机制 ---
            await asyncio.wait_for(
                _execute_task_logic(task_data, db_pool),  # 实际的任务逻辑被封装在 _execute_task_logic 中
                timeout=TASK_TIMEOUT
            )
            # --- 超时机制应用结束 ---

            try:
                task_list_adapter = TaskListAdapter(db_pool)
                knowledge_list_adapter = KnowledgeListAdapter(db_pool)
                with db_pool.pool_db.atomic():
                    task_list_adapter.mark_task_status(task_id, 2)
                    knowledge_list_adapter.update_status(task_data.file_id, 1)
                logger.info(f"任务 {task_id} 执行成功并完成状态更新。")
            except Exception as e:
                logger.error(f"任务 {task_id} 更新最终状态失败: {str(e)}")
                raise

            logger.info(f"任务 {task_id} 执行完成。")
            return

        except asyncio.TimeoutError:  # 捕获超时错误
            current_error = f"任务执行超时，超过 {TASK_TIMEOUT} 秒。"
            logger.warning(
                f"任务 {task_id} 执行超时，尝试重试 ({attempt + 1}/{MAX_TASK_RETRIES}): {current_error}")
            if attempt < MAX_TASK_RETRIES - 1:
                await asyncio.sleep(ERROR_RETRY_DELAY * (2 ** attempt))
            else:
                logger.error(f"任务 {task_id} 因超时达到最大重试次数，最终失败：{current_error}")
                try:
                    task_list_adapter = TaskListAdapter(db_pool)
                    knowledge_list_adapter = KnowledgeListAdapter(db_pool)
                    task_list_adapter.mark_task_error(task_id, -1, current_error)
                    knowledge_list_adapter.update_status(task_data.file_id, -1)
                except Exception as update_error:
                    logger.error(f"无法更新任务 {task_id} 为错误状态: {str(update_error)}")
                raise

        except (ConnectionError, TimeoutError) as e:  # 保持原有的网络连接或外部服务超时错误处理
            current_error = str(e)
            logger.warning(
                f"任务 {task_id} 执行遇到连接或超时错误，尝试重试 ({attempt + 1}/{MAX_TASK_RETRIES}): {current_error}")
            if attempt < MAX_TASK_RETRIES - 1:
                await asyncio.sleep(ERROR_RETRY_DELAY * (2 ** attempt))
            else:
                logger.error(f"任务 {task_id} 达到最大重试次数，最终失败：{current_error}")
                try:
                    task_list_adapter = TaskListAdapter(db_pool)
                    task_list_adapter.mark_task_error(task_id, -1, current_error)
                    knowledge_list_adapter = KnowledgeListAdapter(db_pool)
                    knowledge_list_adapter.update_status(task_data.file_id, -1)
                except Exception as update_error:
                    logger.error(f"无法更新任务 {task_id} 为错误状态: {str(update_error)}")
                raise

        except Exception as e:  # 捕获其他所有异常
            current_error = str(e)
            logger.error(
                f"任务 {task_id} 执行失败 (非连接/超时错误)，尝试重试 ({attempt + 1}/{MAX_TASK_RETRIES}): {current_error}")
            if attempt < MAX_TASK_RETRIES - 1:
                await asyncio.sleep(ERROR_RETRY_DELAY * (2 ** attempt))
            else:
                logger.error(f"任务 {task_id} 达到最大重试次数，最终失败：{current_error}")
                try:
                    task_list_adapter = TaskListAdapter(db_pool)
                    task_list_adapter.mark_task_error(task_id, -1, current_error)
                    knowledge_list_adapter = KnowledgeListAdapter(db_pool)
                    knowledge_list_adapter.update_status(task_data.file_id, -1)
                except Exception as update_error:
                    logger.error(f"无法更新任务 {task_id} 为错误状态: {str(update_error)}")
                raise


# 将实际的任务逻辑封装成一个独立的异步函数，方便 asyncio.wait_for 调用
async def _execute_task_logic(task_data: TaskList, db_pool: Any):
    """
    实际任务处理逻辑的封装，用于被 asyncio.wait_for 包装。
    此函数仅执行业务逻辑，不涉及数据库状态更新（由外部函数处理）。
    """
    task_id = task_data.task_id

    task_params = json.loads(task_data.task_params)
    chunk_type = task_params.get("ChunkType")

    def replace_escapes(s):
        """辅助函数：处理字符串中的转义字符"""
        s = s.replace('\\n\\n', '\n\n')
        s = s.replace('\\n', '\n')
        return s

    if chunk_type == 0:  # 规则分块
        max_chunk_size = task_params.get("MaxChunkSize", 1000)
        delimiters = [replace_escapes(s) for s in task_params.get("Delimiters", [])]
        if not delimiters:
            delimiters = ["\n\n", "\n", "。", "！", "？", "，", ""]
        chunk_overlap = task_params.get("ChunkOverlap", 200)

        await process_document_rule_stream(DocumentChunk(
            TaskId=task_id,
            FileId=task_data.file_id,
            FileUrl=task_data.file_url,
            AppId=task_data.app_id,
            KnowledgeBaseId=task_data.knowledge_base_id,
            ChunkConfig=DocumentChunkConfig(
                MaxChunkSize=max_chunk_size,
                Delimiters=delimiters,
                ChunkOverlap=chunk_overlap,
            )
        ), mysql_pool=db_pool)

    elif chunk_type == 1:  # 语义分块
        max_chunk_size = task_params.get("MaxChunkSize", 4800)

        await process_document_semantic_stream(DocumentChunk(
            TaskId=task_id,
            FileId=task_data.file_id,
            FileUrl=task_data.file_url,
            AppId=task_data.app_id,
            KnowledgeBaseId=task_data.knowledge_base_id,
            ChunkConfig=DocumentChunkConfig(
                MaxChunkSize=max_chunk_size
            )
        ), mysql_pool=db_pool)

    else:
        raise ValueError(f"任务 {task_id} 包含未知的ChunkType: {chunk_type}")


if __name__ == "__main__":
    processor = create_task_processor()
    processor.start()
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("捕获到中断信号，正在停止任务处理器...")
    finally:
        processor.stop()
        if EXECUTION_MODE == "RAY" and ray.is_initialized():
            logger.info("正在关闭 Ray 运行时...")
            ray.shutdown()
