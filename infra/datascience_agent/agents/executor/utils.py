import logging
from typing import Dict, Any, List, Optional, AsyncGenerator, Tu<PERSON>, TYPE_CHECKING


logger = logging.getLogger(__name__)

tool_name_mapping = {
    "generate_sql__generate_sql": "SQL生成工具",
    "nl2code__nl2code": "代码生成工具",
    "jupyter__execute_code": "代码执行工具",

    "aisearch_retrieve__aisearch_retrieve": "知识检索工具",
    "jupyter__load_data_by_sql": "数据加载工具",

    "DLCExecuteQuery": "DLC SQL执行工具",
    "DLCListDatabases": "DLC 获取数据库列表工具",
    "DLCListTables": "DLC 获取数据表信息工具",
    "DLCListEngines": "DLC 获取引擎信息工具",
    "DLCListDatasourceConnections": "DLC 获取数据源工具",
}


# 添加简化版的 trim_messages 函数
def count_tokens_approximately(text: str) -> int:
    """
    简单估算文本的 token 数量
    使用 1 token ≈ 4 字符的简单估算方法
    """
    if not text:
        return 0
    return len(text) // 4


def simple_trim_messages(
        messages: List[Dict[str, Any]],
        max_tokens: int = 20480,
        token_counter=None,
        include_system: bool = True,
        start_on: str = "human"
) -> None:
    """
    简化版的消息裁剪函数，保留 system 消息并控制 token 数量
    优先保留最新的消息，确保开始的消息总是 user 类型
    直接修改传入的消息列表

    Args:
        messages: 消息列表（会被直接修改）
        max_tokens: 最大 token 数量
        token_counter: token 计数器函数（可选）
        include_system: 是否保留 system 消息
        start_on: 从哪种消息类型开始计算（"human" 或 "assistant"）
    """
    if not messages:
        return

    # 分离 system 消息和其他消息
    system_messages = []
    other_messages = []

    for message in messages:
        if message.get("role") == "system":
            system_messages.append(message)
        else:
            other_messages.append(message)

    # 如果没有 token 计数器，使用简单的字符长度估算
    if token_counter is None:
        def simple_token_counter(text: str) -> int:
            # 简单估算：1个token约等于4个字符
            return len(text) // 4

        token_counter = simple_token_counter

    # 计算当前 token 数量
    current_tokens = 0
    kept_messages = []

    # 首先添加 system 消息（如果要求保留）
    if include_system:
        for msg in system_messages:
            content = msg.get("content", "")
            if isinstance(content, str):
                msg_tokens = token_counter(content)
                if current_tokens + msg_tokens <= max_tokens:
                    kept_messages.append(msg)
                    current_tokens += msg_tokens
                else:
                    break

    # 从后往前处理其他消息，优先保留最新的消息
    # 但确保开始的消息是 user 类型
    reversed_messages = list(reversed(other_messages))
    temp_kept_messages = []

    for msg in reversed_messages:
        content = msg.get("content", "")
        if isinstance(content, str):
            msg_tokens = token_counter(content)

            # 如果添加这条消息会超过限制，直接跳过
            if current_tokens + msg_tokens > max_tokens:
                break
            else:
                temp_kept_messages.append(msg)
                current_tokens += msg_tokens
        else:
            # 非字符串内容（如 tool_calls），直接添加
            temp_kept_messages.append(msg)

    # 检查是否以 user 消息开始，如果不是则调整
    if temp_kept_messages:
        # 反转回正确的顺序
        temp_kept_messages.reverse()

        # 确保开始的消息是 user 类型
        if temp_kept_messages[0].get("role") != "user":
            # 找到第一个 user 消息
            first_user_index = -1
            for i, msg in enumerate(temp_kept_messages):
                if msg.get("role") == "user":
                    first_user_index = i
                    break

            if first_user_index > 0:
                # 去掉 user 之前的所有消息，只保留从第一个 user 消息开始的部分
                temp_kept_messages = temp_kept_messages[first_user_index:]
                logger.info(
                    f"消息裁剪: 去掉 user 之前的 {first_user_index} 条消息，保留从第 {first_user_index + 1} 条开始的消息")
            elif first_user_index == -1:
                # 如果没有找到 user 消息，添加一个默认的
                temp_kept_messages = [{
                    "role": "user",
                    "content": "请继续执行任务"
                }]
                logger.info("消息裁剪: 未找到 user 消息，添加默认 user 消息")

        kept_messages.extend(temp_kept_messages)

    # 直接修改传入的消息列表
    messages.clear()
    messages.extend(kept_messages)

    logger.info(f"消息裁剪: 原始 {len(messages)} 条 -> 裁剪后 {len(kept_messages)} 条, tokens: {current_tokens}")

