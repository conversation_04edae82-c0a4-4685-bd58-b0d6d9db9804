import asyncio
import copy
import json
import logging
import uuid
import traceback
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, AsyncGenerator, TYPE_CHECKING, Tuple
from dataclasses import dataclass

from common.elements.agent_event import TaskListEvent, JupyterEvent, ThinkEvent, MessageEvent
from common.share.config import appConfig
from .executor_prompt import (
    create_system_prompt,
    get_dynamic_language_reminder,
)
from .utils import tool_name_mapping, simple_trim_messages, count_tokens_approximately
from .custom_tool import get_id_from_chunk
from infra.metrics.codegen_metrics_tracker import get_metrics_tracker
from infra.metrics.replay_data_collector import get_replay_collector
from infra.metrics.environment_recorder import record_session_environment

if TYPE_CHECKING:
    from infra.datascience_agent.utils.openai_client import OpenAIClient
    from infra.mcp.manager.mcp_manager import MCPManager
    from infra.datascience_agent.state import AgentState

# 设置日志
logger = logging.getLogger(__name__)


@dataclass
class SubtaskStatus:
    """子任务状态管理"""
    idx: int
    desc: str
    status: str = "pending"  # pending, running, finish, error
    cell_ids: List[str] = None
    summary: Optional[str] = None
    error: Optional[str] = None
    success: Optional[bool] = None  # 记录任务是否成功完成
    uid: str = None
    
    def __post_init__(self):
        if self.cell_ids is None:
            self.cell_ids = []
        if self.uid is None:
            self.uid = str(uuid.uuid4())


class FlexibleReactAgent:
    """灵活的React Agent实现，支持动态工具组合，与langgraph集成"""
    
    # 自定义工具描述 - 统一管理
    CUSTOM_TOOL_DESCRIPTIONS = {
        "load_data_and_join_table": "将JSON数据加载到数据库作为临时表，与指定表进行JOIN操作，返回结果表名和前5行示例"
    }
    
    def __init__(self, llm_client: 'OpenAIClient', mcp_manager: 'MCPManager', writer=None):
        self.llm_client = llm_client
        self.mcp_manager = mcp_manager
        self.writer = writer
        self.tools = {}
        self.conversation_history = []
        self.current_cell_id = 0
        self.session_id = str(uuid.uuid4())[:8]
        
        # 新增：存储agent_state以便工具可以访问
        self.agent_state = None
        
        # 添加自定义工具
        from .custom_tool import load_data_and_join_table
        self.custom_tools = {
            "load_data_and_join_table": load_data_and_join_table
        }

        # 针对投诉举报类问题的特殊处理结果
        self.complaint_or_report_history = []
        self.temp_tables = []
        
        # 任务状态管理 - 支持全量管理
        self.all_subtasks: Dict[int, SubtaskStatus] = {}  # 所有子任务状态
        self.current_subtask: Optional[SubtaskStatus] = None  # 当前处理的子任务
        self.overall_task_name: str = "数据分析任务"
        self.overall_task_status: str = "pending"  # pending, running, finish, error
        self.overall_task_id: str = str(uuid.uuid4())
        
        # 依赖包管理 - 用于处理nl2code工具返回的required_packages
        self.pending_required_packages: List[str] = []  # 存储待使用的依赖包列表
        
        # 环境参数缓存 - 用于参数覆盖
        self.env_params_cache: Optional[Dict[str, Any]] = None
        
        # 🆕 统一格式：previous_actions存储格式为 [(code, exec_status, output), ...]
        self.previous_actions: List[Tuple[str, str, str]] = []
        
        # 🆕 数据信息存储 - 保存第一条数据信息供summary使用
        self.data_info = None
        
        # Metrics tracking variables
        self.metrics_tracker = get_metrics_tracker()
        self.current_task_id: Optional[str] = None
        self.codegen_call_count: int = 0
        self.task_start_time: Optional[float] = None
        self.total_input_tokens: int = 0
        self.total_output_tokens: int = 0
        
        # Replay data collection variables
        self.replay_collector = get_replay_collector()
        self.current_session_id: Optional[str] = None
        self.current_user_id: Optional[str] = None
        
        logger.info("FlexibleReactAgent initialized with MCPManager for enhanced ReAct execution.")

    def _format_tool_name_for_frontend(self, tool_name: str) -> str:
        """将工具名称格式化为前端友好的显示名称"""
        # 处理 TCHouseD 系列工具格式转换（只处理需要展示的工具）
        if "TCHouseDExecuteQuery" in tool_name:
            return "tchouse_d_execute_query"
        elif "TCHouseDGetTableSchema" in tool_name:
            return "tchouse_d_get_table_schema"
        
        # 处理 DLCExecuteQuery 格式转换
        elif "DLCExecuteQuery" in tool_name:
            return "dlc_execute_query"
        
        # 对于其他工具，保持原名称
        return tool_name

    async def initialize(self):
        """初始化agent，获取可用工具"""
        logger.info("🚀 初始化Flexible React Agent...")
        try:
            self.tools = await self.mcp_manager.list_tools()
            logger.info(f"✅ 获取到 {len(self.tools)} 个可用工具")
            logger.info(f"🔍 可用工具列表: {self.tools}")
        except Exception as e:
            logger.error(f"❌ 初始化工具失败: {e}")
            self.tools = {}
            # 重新抛出异常，让调用方处理
            raise

    def initialize_subtasks(self, subtasks: List[Dict[str, Any]]):
        """初始化所有子任务状态"""
        self.all_subtasks = {}
        for subtask in subtasks:
            subtask_status = SubtaskStatus(
                idx=subtask.get("idx"),
                desc=subtask.get("desc", "No description"),
                status="pending"
            )
            self.all_subtasks[subtask_status.idx] = subtask_status
        
        self.overall_task_status = "running" if self.all_subtasks else "pending"
        logger.info(f"初始化了 {len(self.all_subtasks)} 个子任务")

    def _update_overall_task_status(self):
        """根据所有子任务状态更新整体任务状态"""
        if not self.all_subtasks:
            self.overall_task_status = "pending"
            return
        
        subtask_statuses = [task.status for task in self.all_subtasks.values()]
        
        if all(status == "finish" for status in subtask_statuses):
            self.overall_task_status = "finish"
        elif any(status == "error" for status in subtask_statuses):
            self.overall_task_status = "error"
        elif any(status == "running" for status in subtask_statuses):
            self.overall_task_status = "running"
        else:
            self.overall_task_status = "running"  # 有pending状态时也算running

    def _create_task_list_event(self) -> TaskListEvent:
        """创建完整的TaskListEvent，包含所有子任务状态"""
        # 更新整体任务状态
        self._update_overall_task_status()
        
        # 构建所有子任务的step_info_list
        step_info_list = []
        for subtask in self.all_subtasks.values():
            step_info = {
                "id": subtask.uid,
                "name": subtask.desc,
                "status": subtask.status,
                "type": "expand",
                "expand": {
                    "title": subtask.desc,
                    "status": subtask.status,
                    "cell_ids": subtask.cell_ids.copy()  # 关联cell_ids
                },
                "order": subtask.idx
            }
            
            # 添加summary或error信息
            if subtask.status == "finish" and subtask.summary:
                step_info["summary"] = subtask.summary
            elif subtask.status == "error" and subtask.error:
                step_info["summary"] = f"错误: {subtask.error}"
            elif subtask.status == "running":
                step_info["summary"] = f"executing: {subtask.desc}..."
            else:
                step_info["summary"] = "waiting..."
            
            step_info_list.append(step_info)
        
        # 按idx排序（使用order字段保证稳定顺序）
        step_info_list.sort(key=lambda x: x.get("order", 0))
        
        # 构建完整的task_list
        task_list = [{
            "id": self.overall_task_id,
            "name": self.overall_task_name,
            "status": self.overall_task_status,
            "step_info_list": step_info_list
        }]
        
        # 🎯 添加调试日志
        logger.debug(f"🎯 创建TaskListEvent - 整体任务: {self.overall_task_name}, 状态: {self.overall_task_status}")
        logger.debug(f"🎯 子任务数量: {len(step_info_list)}")
        for step in step_info_list:
            logger.debug(f"🎯 子任务 {step['id']}: {step['name']} - {step['status']}")
        
        return TaskListEvent(content={"task_list": task_list})

    def _get_all_tools_descriptions(self) -> Dict[str, str]:
        """获取所有工具的描述（MCP + 自定义）"""
        descriptions = {}
        
        # MCP工具
        for tool_name, tool_obj in self.tools.items():
            descriptions[tool_name] = getattr(tool_obj, 'description', 'No description')
        
        # 自定义工具
        descriptions.update(self.CUSTOM_TOOL_DESCRIPTIONS)
        
        return descriptions

    def create_enhanced_system_prompt(self, detected_language: Optional[str] = None, agent_state: 'AgentState' = None) -> str:
        """创建增强的系统提示词"""
        identified_intent_name = agent_state.get('identified_intent_name', '')

        return create_system_prompt(
            detected_language=detected_language or 'zh', identified_intent_name=identified_intent_name
        )

    def _format_tools_for_function_calling(self) -> List[Dict[str, Any]]:
        """将工具转换为OpenAI function calling格式"""
        functions = []
        tools_descriptions = self._get_all_tools_descriptions()
        
        # MCP工具
        for tool_name, tool_obj in self.tools.items():
            parameters = {"type": "object", "properties": {}, "required": []}
            
            if hasattr(tool_obj, 'inputSchema') and tool_obj.inputSchema:
                parameters["properties"] = {
                    param: {
                        "type": info.get('type', 'string'),
                        "description": info.get('description', '')
                    }
                    for param, info in tool_obj.inputSchema.get('properties', {}).items()
                }
                parameters["required"] = tool_obj.inputSchema.get('required', [])
            
            functions.append({
                "type": "function",
                "function": {
                    "name": tool_name,
                    "description": tools_descriptions[tool_name],
                    "parameters": parameters
                }
            })
        
        # 自定义工具
        for tool_name, tool_func in self.custom_tools.items():
            # 手动构建load_data_and_join_table工具的参数
            if tool_name == "load_data_and_join_table":
                functions.append({
                    "type": "function",
                    "function": {
                        "name": tool_name,
                        "description": tools_descriptions[tool_name],
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "json_data": {
                                    "type": "string",
                                    "description": "JSON格式的数据字符串"
                                },
                                "table_a_name": {
                                    "type": "string", 
                                    "description": "要JOIN的已存在的表名"
                                },
                                "join_condition": {
                                    "type": "string",
                                    "description": "JOIN条件，例如 'temp_table.registration_id = feedback.registration_id'"
                                },
                                "mcp_url": {
                                    "type": "string",
                                    "description": "MCP服务的URL"
                                },
                                "database_name": {
                                    "type": "string",
                                    "description": "数据库名，默认为feedback",
                                    "default": "feedback"
                                },
                                "catalog_name": {
                                    "type": "string",
                                    "description": "catalog名称，默认为internal",
                                    "default": "internal"
                                }
                            },
                            "required": ["json_data", "table_a_name", "join_condition", "mcp_url"]
                        }
                    }
                })
        
        # Finish工具
        functions.append({
            "type": "function",
            "function": {
                "name": "finish",
                "description": "任务完成时调用，提供总结",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "answer": {
                            "type": "string",
                            "description": "任务完成的总结(不要包含任何具体的结果，只总结任务完成情况)"
                        },
                        "success": {
                            "type": "boolean",
                            "description": "当前子任务是否成功完成。如果遇到无法解决的错误（如网络错误、数据源不可用等），设置为false以停止后续任务执行",
                            "default": True
                        }
                    },
                    "required": ["answer"]
                }
            }
        })
        
        return functions

    async def process_subtask(self, subtask: Dict[str, Any], agent_state: 'AgentState') -> AsyncGenerator[Dict[str, Any], None]:
        """处理单个子任务 - langgraph接口兼容方法"""
        subtask_idx = subtask.get("idx")
        subtask_description = subtask.get("desc", "No description")
        
        # 存储agent_state以便工具可以访问
        self.agent_state = agent_state
        
        # Start task metrics tracking
        # Get session_id from multiple sources
        ctx = agent_state.get('ctx') if agent_state else None
        session_id = ''
        user_id = ''
        
        # Try to get session_id from ChatContext object first
        if ctx:
            session_id = getattr(ctx, 'session_id', '')
            user_id = getattr(ctx, 'sub_account_uin', '')
        
        # Fallback: try to get session_id directly from agent_state
        if not session_id and agent_state:
            session_id = agent_state.get('session_id', '')
        
        # Store session info for replay collection
        self.current_session_id = session_id
        self.current_user_id = user_id
        
        # Note: max_calls is no longer used since we support multi-k analysis
        
        self.current_task_id = self.metrics_tracker.start_task(
            session_id=session_id,
            user_id=user_id,
            task_type="code_execution",
            task_instruction=subtask_description,
            max_allowed_calls=10  # Max calls for display purposes only
        )
        self.task_start_time = time.time() * 1000  # 记录毫秒时间戳
        self.codegen_call_count = 0
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        
        logger.info(f"🎯 Started metrics tracking for task: {self.current_task_id}")
        
        # 📊 Start replay data collection for complete reproduction
        # Record agent reasoning state before task execution
        try:
            agent_state_snapshot = {
                "subtask_idx": subtask_idx,
                "subtask_description": subtask_description,
                "conversation_history": agent_state.get('conversation_history', []),
                "identified_intent": agent_state.get('identified_intent_name', ''),
                "available_data_sources": agent_state.get('available_data_sources', []),
                "database_schema": agent_state.get('database_schema', {}),
                "previous_actions_count": len(self.previous_actions),
                "detected_language": detected_language
            }
            
            self.replay_collector.record_agent_reasoning(
                session_id=session_id,
                task_id=self.current_task_id,
                subtask_sequence=subtask_idx,
                agent_state_before=agent_state_snapshot,
                agent_state_after={},  # Will be updated later
                tool_selection_reasoning=f"Starting subtask {subtask_idx}: {subtask_description}",
                execution_strategy="flexible_react_agent",
                graph_node="process_subtask_start"
            )
            
            logger.info(f"📊 Started replay data collection for task: {self.current_task_id}")
        except Exception as e:
            logger.warning(f"Failed to record replay data: {e}")
        
        # 📊 Record environment snapshot for this session
        try:
            record_session_environment(session_id)
            logger.info(f"📊 Recorded environment snapshot for session: {session_id}")
        except Exception as e:
            logger.warning(f"Failed to record environment snapshot: {e}")
        
        # 从agent_state中获取检测到的语言信息
        detected_language = agent_state.get('detected_language') if agent_state else None
        if detected_language:
            logger.info(f"🌐 FlexibleReactAgent: 检测到用户语言: {detected_language}")
        else:
            logger.info("🌐 FlexibleReactAgent: 未检测到特定语言，将使用默认语言设置")
        
        logger.info(f"🎯 FlexibleReactAgent: 开始处理子任务 {subtask_idx}: {subtask_description}")
        
        # 🔍 添加调试：显示agent实例和previous_actions状态
        logger.info(f"🔍 Agent实例ID: {id(self)}")
        logger.info(f"🔍 当前previous_actions状态: 共{len(self.previous_actions)}条记录")
        if self.previous_actions:
            for i, (code, status, output) in enumerate(self.previous_actions[-3:]):  # 只显示最近3条
                logger.info(f"🔍   最近记录[{len(self.previous_actions)-3+i}]: {code[:50]}... -> {status}")
        else:
            logger.info("🔍   previous_actions为空")
        
        # 获取或创建当前子任务状态
        if subtask_idx in self.all_subtasks:
            self.current_subtask = self.all_subtasks[subtask_idx]
            self.current_subtask.status = "running"
        else:
            # 如果all_subtasks未初始化，创建单个子任务
            self.current_subtask = SubtaskStatus(
                idx=subtask_idx,
                desc=subtask_description,
                status="running"
            )
            self.all_subtasks[subtask_idx] = self.current_subtask
        
        # 📋 发送初始任务状态 - 子任务开始
        self.current_subtask.status = "running"
        
        # ✅ 修复：直接yield TaskListEvent的内容，不要双重包装
        initial_event = self._create_task_list_event()
        yield {
            "type": "task_list_event",
            "data": initial_event.content
        }
        
        # 确保agent已初始化
        if not self.tools:
            try:
                await self.initialize()
            except Exception as e:
                error_msg = f"重新初始化工具失败: {e}"
                logger.error(f"❌ {error_msg}")
                
                # 更新当前子任务状态为错误
                self.current_subtask.status = "error"
                self.current_subtask.error = error_msg
                
                # Yield error event
                yield {
                    "type": "error_event",
                    "data": {
                        "error": error_msg,
                        "subtask_idx": subtask_idx,
                        "subtask_description": subtask_description,
                        "error_type": "reinitialization_error"
                    }
                }
                
                # 发送最终的任务状态更新
                yield {
                    "type": "task_list_event",
                    "data": self._create_task_list_event().content
                }
                
                # 抛出异常终止执行
                raise RuntimeError(error_msg)
        
        # 获取adv_tool和task_dependencies信息
        adv_tool = subtask.get("adv_tool", "未指定工具")
        task_dependencies = subtask.get("dep", [])
        # 将依赖列表转换为字符串描述
        if task_dependencies:
            dep_desc = f"依赖任务: {', '.join(map(str, task_dependencies))}"
        else:
            dep_desc = "无前置依赖"
        
        # 传递detected_language参数到系统提示词创建方法
        functions = self._format_tools_for_function_calling()

        # 初始化子任务对话
        subtask_message = {
            "role": "user",
            "content": f"""
请按照工具建议顺序依次调用工具，完成如下任务：
-**任务描述**: {subtask_description}
-**工具建议**: {adv_tool}
-**任务依赖**: {task_dependencies}
"""
        }
        # save subtask once
        self.conversation_history.append(subtask_message)

        # init subtask multi-turn conversation
        full_messages = self.conversation_history.copy()

        turn_count = 0
        max_turns = 10
        cell_exec_count = 0
        
        while turn_count < max_turns:
            turn_count += 1
            logger.info(f"🔄 开始第 {turn_count} 轮对话")
            
            try:

                if agent_state.get('stop_requested'):
                    logger.info(f"Stop request detected in FlexibleReactAgent. Halting subtask {subtask_idx}.")
                    self.current_subtask.status = "error"
                    self.current_subtask.error = "Task manually stopped by user."
                    
                    # Complete task metrics tracking for stopped case
                    if self.current_task_id:
                        self.metrics_tracker.finish_task(
                            task_id=self.current_task_id,
                            is_successful=False,
                            total_codegen_calls=self.codegen_call_count,
                            success_within_k_calls=False,
                            total_input_tokens=self.total_input_tokens,
                            total_output_tokens=self.total_output_tokens,
                            start_time_ms=self.task_start_time
                        )
                        logger.info(f"🎯 Completed stopped task tracking: {self.current_task_id}")
                    
                    # 发送最终的任务状态更新
                    yield {
                        "type": "task_list_event",
                        "data": self._create_task_list_event().content
                    }
                    break # 退出循环

                # filter large messages
                filtered_messages = []
                for message in full_messages:
                    message_new = message.copy()
                    if len(message.get("content") or "") > 2048:
                        message_new["content"] = message.get("content")[:2048] + "..."
                    filtered_messages.append(message_new)

                simple_trim_messages(
                    messages=filtered_messages,
                    max_tokens=20480,
                    token_counter=count_tokens_approximately,
                    include_system=True,
                    start_on="human",
                )

                logger.info(f"executor_llm_preprocess: before {len(full_messages)}, after={len(filtered_messages)}")
                logger.info(f"executor_llm_input: {filtered_messages}")

                response = await self.llm_client.generate(
                    messages=filtered_messages,
                    tools=functions,
                    # tool_choice="auto" #似乎不支持
                )

                logger.info(f"executor_llm_output: {response}")
                
                # # 处理思考内容
                # if hasattr(response, 'content') and response.content:
                #     self.current_cell_id += 1
                #     cell_id = str(uuid.uuid4())
                    
                #     # 记录cell_id到当前任务
                #     self.current_subtask.cell_ids.append(cell_id)
                    
                #     yield {
                #         "type": "jupyter_event",
                #         "data": {
                #             "cell_type": "markdown",
                #             "source": [f"{response.content}"],
                #             "outputs": [],
                #             "status": "success",
                #             "cell_id": cell_id,
                #             "execution_count": None
                #         }
                #     }
                    
                #     # 🎯 新增：在思考内容的 markdown 后也发送任务状态更新
                #     logger.info(f"🎯 发送任务状态更新 - 思考内容生成后")
                #     yield {
                #         "type": "task_list_event",
                #         "data": self._create_task_list_event().content
                #     }
                    
                #     # 添加助手回复到子任务历史
                #     full_messages.append({"role": "assistant", "content": response.content})
                
                # 处理工具调用
                if not (hasattr(response, 'tool_calls') and response.tool_calls):
                    # 如果没有工具调用，添加继续提示
                    full_messages.append({
                        "role": "user",
                        "content": "请继续调用工具完成任务直到所有任务成功完成或者发现当前任务无法完成后调用finish工具结束。"
                    })
                else:
                    # 准备assistant消息，包含tool_calls信息
                    assistant_message = {
                        "role": "assistant",
                        "content": response.content if hasattr(response, 'content') else None,
                        "tool_calls": []
                    }
                    
                    # 收集所有工具调用信息
                    tool_calls_data = []
                    for tool_call in response.tool_calls:
                        tool_calls_data.append({
                            "id": tool_call.id,
                            "type": "function",
                            "function": {
                                "name": tool_call.function.name,
                                "arguments": tool_call.function.arguments,
                            }
                        })
                    
                    # 更新assistant消息的tool_calls
                    assistant_message["tool_calls"] = tool_calls_data
                    
                    # 添加assistant消息到历史
                    full_messages.append(assistant_message)

                    # 处理每个工具调用
                    for tool_call in response.tool_calls:
                        function_name = tool_call.function.name
                        try:
                            function_args = json.loads(tool_call.function.arguments)
                        except json.JSONDecodeError:
                            function_args = {}
                            logger.error(f"工具调用参数解析失败: {tool_call.function.arguments}")
                        
                        # 在每个工具调用前检查停止请求
                        if agent_state.get('stop_requested'):
                            logger.info(f"Stop request detected before tool call: {function_name}")
                            self.current_subtask.status = "error"
                            self.current_subtask.error = "Task manually stopped by user."
                            yield {
                                "type": "task_list_event",
                                "data": self._create_task_list_event().content
                            }
                            return
                        
                        logger.info(f"🔧 执行工具: {function_name}")
                        
                        # 🆕 初始化变量，避免UnboundLocalError
                        cell_type = "markdown"  # 默认cell类型
                        source_display = "No content"  # 默认显示内容
                        
                        # 检查是否是finish工具
                        if function_name == "finish":
                            final_answer = function_args.get("answer", "子任务完成")
                            success = function_args.get("success", True)  # 默认成功
                            
                            # 根据success参数更新任务状态
                            if success:
                                self.current_subtask.status = "finish"
                                self.current_subtask.summary = final_answer
                            else:
                                self.current_subtask.status = "error"
                                self.current_subtask.error = final_answer
                            self.current_subtask.success = success  # 记录成功状态
                            
                            self.current_cell_id += 1
                            finish_cell_id = str(uuid.uuid4())
                            self.current_subtask.cell_ids.append(finish_cell_id)
                            
                            yield {
                                "type": "jupyter_event",
                                "data": {
                                    "cell_type": "markdown",
                                    "source": [f"{final_answer}"],
                                    "outputs": [],
                                    # "status": "success" if success else "error",
                                    "cell_id": finish_cell_id,
                                    "execution_count": None,
                                    "metadata": {
                                        "status": "success" if success else "error",
                                    }
                                }
                            }
                            
                            # 🎯 新增：在任务完成的 markdown 后也发送任务状态更新
                            yield {
                                "type": "task_list_event",
                                "data": self._create_task_list_event().content
                            }
                            
                            # # 🆕 只在错误情况下发送FinalSummaryEvent（正常情况下由agent_service.py发送）
                            # if not success:
                            #     try:
                            #         yield {
                            #             "type": "final_summary",
                            #             "data": {
                            #                 "content": final_answer,
                            #                 "cell_id": finish_cell_id,
                            #                 "status": "error"
                            #             }
                            #         }
                            #         logger.info(f"🎯 发送错误FinalSummaryEvent: content={final_answer[:50]}...")
                            #     except Exception as e:
                            #         logger.error(f"❌ 发送FinalSummaryEvent失败: {e}")
                            
                            # Complete task metrics tracking based on success status
                            if self.current_task_id:
                                self.metrics_tracker.finish_task(
                                    task_id=self.current_task_id,
                                    is_successful=success,  # 使用实际的success值
                                    total_codegen_calls=self.codegen_call_count,
                                    total_input_tokens=self.total_input_tokens,
                                    total_output_tokens=self.total_output_tokens,
                                    start_time_ms=self.task_start_time
                                )
                                logger.info(f"🎯 Completed task tracking: {self.current_task_id}, success: {success}, calls: {self.codegen_call_count}")
                            
                            # 更新全局对话历史
                            self.conversation_history = full_messages
                            
                            # 如果任务失败，则停止后续任务
                            if not success:
                                logger.info(f"🚫 子任务 {subtask_idx} 失败，已请求停止后续任务。")
                                # agent_state['stop_requested'] = True  # 设置停止标记
                                yield {
                                    "type": "task_list_event",
                                    "data": self._create_task_list_event().content
                                }
                                # yield {
                                #     "type": "error_event",
                                #     "data": {
                                #         "error": final_answer,  # 使用finish工具的answer参数作为失败原因
                                #         "subtask_idx": subtask_idx,
                                #         "subtask_description": subtask_description,
                                #         "error_type": "task_failed_and_stopped"
                                #     }
                                # }
                                agent_state['stop_requested'] = True 
                            return
                        
                        # 显示工具调用前的状态
                        # 对于nl2code工具和不需要显示的TCHouseD工具，不创建cell_id
                        skip_display = ("nl2code" in function_name or 
                                       ("TCHouseD" in function_name and 
                                        "TCHouseDGetTableSchema" not in function_name and 
                                        "TCHouseDExecuteQuery" not in function_name))
                        
                        if not skip_display:
                            cell_exec_count += 1
                            current_cell_id = str(uuid.uuid4())
                            self.current_subtask.cell_ids.append(current_cell_id)
                        else:
                            # 不需要显示的工具不创建cell_id
                            current_cell_id = None

                        # # before tool call
                        # mapping_key = function_name
                        # if "__DLC" in function_name:
                        #     mapping_key = function_name.split("__")[-1]
                        # tool_name = tool_name_mapping.get(mapping_key, function_name)
                        # yield {
                        #     "type": "jupyter_event",
                        #     "data": {
                        #         "cell_type": "markdown",
                        #         "source": [f"我将调用{tool_name}来执行任务"],
                        #         "outputs": [],
                        #         "status": "success",
                        #         "cell_id": current_cell_id,
                        #         "execution_count": None
                        #     }
                        # }

                        # tool call
                        # cell_exec_count += 1
                        # current_cell_id = str(uuid.uuid4())
                        # self.current_subtask.cell_ids.append(current_cell_id)

                        if "generate_sql" in function_name:
                            cell_type = "markdown"
                            source_display = ""
                            action_name = "调用sql工具"
                            yield {
                                "type": "jupyter_event",
                                "data": {
                                    "cell_type": cell_type,
                                    # "source": [source_display],
                                    # "outputs": [],
                                    # "status": "running",
                                    "cell_id": current_cell_id,
                                    "execution_count": cell_exec_count,
                                    "metadata": {
                                        "tool_name": self._format_tool_name_for_frontend(function_name),
                                        "status": "running",
                                        }
                                }
                            }

                        elif "nl2code" in function_name:
                            # cell_type = "markdown"
                            # source_display = ""
                            # action_name = "调用代码工具"
                            # yield {
                            #     "type": "jupyter_event",
                            #     "data": {
                            #         "cell_type": cell_type,
                            #         # "source": [source_display],
                            #         # "outputs": [],
                            #         "status": "running",
                            #         "cell_id": current_cell_id,
                            #         "execution_count": cell_exec_count,
                            #         "metadata": {
                            #             "tool_name": action_name,
                            #             }
                            #     }
                            # }
                            action_name = "调用代码工具"
                        # # 确定显示格式
                        # cell_type = "code"
                        # source_display = f"# 正在执行{tool_name}..."

                        elif "jupyter__execute_code" in function_name:
                            source_display = function_args.get("code", "No code provided")
                            cell_type = "code"
                            yield {
                                "type": "jupyter_event",
                                "data": {
                                    "cell_type": cell_type,
                                    "source": [source_display],
                                    "outputs": [],
                                    # "status": "running",
                                    "cell_id": current_cell_id,
                                    "execution_count": cell_exec_count,
                                    "metadata": {
                                        "tool_name": self._format_tool_name_for_frontend(function_name),
                                        "status": "running",
                                    }
                                }
                            }

                        elif "DLCExecuteQuery" in function_name or "TCHouseDExecuteQuery" in function_name:
                            # 根据不同工具使用正确的参数名
                            if "TCHouseDExecuteQuery" in function_name:
                                raw_sql = function_args.get("Query", "No SQL provided")
                            else:  # DLCExecuteQuery
                                raw_sql = function_args.get("SparkSQL", "No SQL provided")
                            
                            # 使用sqlparse格式化SQL以提高可读性
                            if raw_sql != "No SQL provided":
                                source_display = self._format_sql_with_line_breaks(raw_sql)
                            else:
                                source_display = raw_sql
                            cell_type = "sql"
                            yield {
                                "type": "jupyter_event",
                                "data": {
                                    "cell_type": cell_type,
                                    "source": [source_display],
                                    "outputs": [],
                                    # "status": "running",
                                    "cell_id": current_cell_id,
                                    "execution_count": cell_exec_count,
                                    "metadata": {
                                        "tool_name": self._format_tool_name_for_frontend(function_name),
                                        "status": "running",
                                    }
                                }
                            }
                        elif "load_data_by_sql" in function_name:
                            raw_sql = function_args.get("sql", "No SQL provided")
                            # 使用sqlparse格式化SQL以提高可读性
                            if raw_sql != "No SQL provided":
                                source_display = self._format_sql_with_line_breaks(raw_sql)
                            else:
                                source_display = raw_sql
                            cell_type = "sql"
                            yield {
                                "type": "jupyter_event",
                                "data": {
                                    "cell_type": cell_type,
                                    "source": [source_display],
                                    "outputs": [],
                                    # "status": "running",
                                    "cell_id": current_cell_id,
                                    "execution_count": cell_exec_count,
                                    "metadata": {
                                        "tool_name": self._format_tool_name_for_frontend(function_name),
                                        "status": "running",
                                    }
                                }
                            }
                        elif "DLCListTables" in function_name:
                            # 简单显示查询表信息的操作（只对DLCListTables）
                            database_name = function_args.get("DatabaseName") or function_args.get("Database", "")
                            source_display = f"查询数据库表信息: {database_name}" if database_name else "查询数据库表信息"
                            cell_type = "sql"
                            yield {
                                "type": "jupyter_event",
                                "data": {
                                    "cell_type": cell_type,
                                    "source": [source_display],
                                    "outputs": [],
                                    # "status": "running",
                                    "cell_id": current_cell_id,
                                    "execution_count": cell_exec_count,
                                    "metadata": {
                                        "tool_name": self._format_tool_name_for_frontend(function_name),
                                        "status": "running",
                                    }
                                }
                            }
                        elif "TCHouseDGetTableSchema" in function_name:
                            # 只展示特定的TCHouseD工具
                            database_name = function_args.get("DatabaseName") or function_args.get("Database", "")
                            source_display = f"查询数据库表结构: {database_name}" if database_name else "查询数据库表结构"
                            cell_type = "sql"
                            yield {
                                "type": "jupyter_event",
                                "data": {
                                    "cell_type": cell_type,
                                    "source": [source_display],
                                    "outputs": [],
                                    # "status": "running",
                                    "cell_id": current_cell_id,
                                    "execution_count": cell_exec_count,
                                    "metadata": {
                                        "tool_name": self._format_tool_name_for_frontend(function_name),
                                        "status": "running",
                                    }
                                }
                            }

                        yield {
                            "type": "task_list_event",
                            "data": self._create_task_list_event().content
                        }

                        # 执行工具
                        tool_result = await self._call_tool(function_name, function_args)

                        # 显示执行结果
                        status = "success" if tool_result["success"] else "error"
                        outputs = self._format_tool_outputs(tool_result, function_name)

                        if "generate_sql" in function_name:
                            # 正确提取reasoning内容
                            source_display = "No result"
                            try:
                                # 从工具结果中提取文本内容并解析JSON
                                text_content = self._extract_text_from_result(tool_result.get("result"))
                                if text_content:
                                    result_json = json.loads(text_content)
                                    if isinstance(result_json, dict) and "reasoning" in result_json:
                                        source_display = result_json["reasoning"].strip()
                            except Exception as e:
                                logger.warning(f"提取reasoning内容失败: {e}")
                                source_display = "Failed to extract reasoning content"

                            yield {
                                "type": "jupyter_event",
                                "data": {
                                    "cell_type": "markdown",
                                    "source": [source_display],
                                    # "status": status,   
                                    "cell_id": current_cell_id,
                                    "execution_count": cell_exec_count,
                                    "metadata": {
                                        "tool_name": self._format_tool_name_for_frontend(function_name), # code工具/sql工具
                                        "status": status,
                                    }
                                }
                            }
                        elif "nl2code" in function_name:
                            # nl2code 工具不输出任何内容（按照用户需求）
                            pass 
                        elif "jupyter__execute_code" in function_name:
                            yield {
                                "type": "jupyter_event",
                                "data": {
                                    "cell_type": cell_type,
                                    "source": [source_display],
                                    "outputs": outputs,
                                    # "status": status,
                                    "cell_id": current_cell_id,
                                    "execution_count": cell_exec_count,
                                    "metadata": {
                                        "tool_name": self._format_tool_name_for_frontend(function_name),
                                        "status": status,
                                    }
                                }
                            }
                        elif "load_data_by_sql" in function_name or "DLCExecuteQuery" in function_name or "TCHouseDExecuteQuery" in function_name:
                            yield {
                                "type": "jupyter_event",
                                "data": {
                                    "cell_type": cell_type,
                                    "source": [source_display],
                                    "outputs": outputs,
                                    # "status": status,   
                                    "cell_id": current_cell_id,
                                    "execution_count": cell_exec_count,
                                    "metadata": {
                                        "tool_name": self._format_tool_name_for_frontend(function_name),
                                        "status": status,
                                    }
                                }
                            }
                        elif "TCHouseDGetTableSchema" in function_name:
                            # 对于DLC工具和指定的TCHouseD工具，显示执行结果
                            yield {
                                "type": "jupyter_event",
                                "data": {
                                    "cell_type": cell_type,
                                    "source": [source_display],
                                    "outputs": outputs,
                                    # "status": status,   
                                    "cell_id": current_cell_id,
                                    "execution_count": cell_exec_count,
                                    "metadata": {
                                        "tool_name": self._format_tool_name_for_frontend(function_name),
                                        "status": status,
                                    }
                                }
                            }
                        
                        elif current_cell_id is not None:
                            # 只对有cell_id的工具（需要显示的工具）进行yield
                            yield {
                                "type": "jupyter_event",
                                "data": {
                                    "cell_type": cell_type,
                                    "source": [source_display],
                                    "outputs": outputs,
                                    # "status": status,   
                                    "cell_id": current_cell_id,
                                    "execution_count": cell_exec_count,
                                    "metadata": {
                                        "status": status,
                                    }
                                }
                            }
                        logger.info(f"🎯 jupyter_event_content_to_node: {function_name}, data={outputs}")
                        
                        # 构建记忆中的工具内容
                        if not tool_result["success"]:
                            # 失败时在记忆中保存完整错误信息供agent分析
                            error_info = tool_result.get("error", "Unknown error")
                            traceback_info = tool_result.get("traceback", "")
                            tool_content = f"工具执行失败: {error_info}"
                            if traceback_info:
                                tool_content += f"\n\n{traceback_info}"
                        else:
                            # 成功时正常提取结果
                            tool_content = self._extract_text_from_result(tool_result.get("result"))
                            if not tool_content:
                                tool_content = str(tool_result.get("result", "No result"))

                        if "DLCListTables" in function_name:
                            # TODO table schema is too long, use formated output
                            tool_content = str(outputs)

                        elif "jupyter__execute_code" in function_name:
                            # remove image in messages
                            filtered_output = []
                            for output in outputs:
                                output_new = copy.deepcopy(output)

                                if output_new.get('output_type', '') == 'display_data':
                                    data = output_new.get('data', {})
                                    keys_to_remove = [key for key in data if key.startswith("image/")]
                                    for key in keys_to_remove:
                                        del output_new["data"][key]

                                filtered_output.append(output_new)
                            tool_content = str(filtered_output)
                        elif "aisearch" in function_name:
                            logger.info(f"检索完成时的意图识别结果: {agent_state.get('identified_intent_name')}")
                            if tool_result["success"] and agent_state.get("identified_intent_name")=="complaint_or_report":
                                tool_content = get_id_from_chunk(tool_content)
                                self.complaint_or_report_history.append([function_name, tool_content])

                        # 如果有参数覆盖信息，简单添加到内容前面
                        if tool_result.get("parameter_overrides"):
                            override_info = "参数自动调整: " + "; ".join(tool_result["parameter_overrides"])
                            tool_content = f"{override_info}\n\n{tool_content}" if tool_content else override_info
                        
                        full_messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "content": tool_content,
                        })
                        
                        # 🎯 增加任务状态更新频率：每次工具执行后都发送状态更新
                        # 这样前端能实时看到任务进度变化
                        logger.info(f"🎯 发送任务状态更新 - 工具: {function_name}, 成功: {tool_result['success']}")
                        yield {
                            "type": "task_list_event",
                            "data": self._create_task_list_event().content
                        }
                        
                        # 如果工具执行失败，继续循环让LLM尝试其他方法
                        if not tool_result["success"]:
                            logger.warning(f"⚠️ 工具 {function_name} 执行失败: {tool_result['error']}")

            except Exception as e:
                logger.error(f"❌ 执行子任务时出错: {e}, {traceback.format_exc()}")
                
                # 更新任务状态为错误
                self.current_subtask.status = "error"
                self.current_subtask.error = str(e)
                
                full_messages.append({"role": "tool", "content": f"执行工具时出错: {e}"})
                
                self.current_cell_id += 1
                error_cell_id = str(uuid.uuid4())
                self.current_subtask.cell_ids.append(error_cell_id)
                
                yield {
                    "type": "jupyter_event",
                    "data": {
                        "cell_type": "markdown",
                        "source": [f"**错误:** {str(e)}"],
                        "outputs": [],
                        # "status": "error",
                        "cell_id": error_cell_id,
                        "execution_count": None,
                        "metadata": {
                            "status": "error",
                        }
                    }
                }
                
                # 🎯 在错误 markdown 后发送任务状态更新
                yield {
                    "type": "task_list_event",
                    "data": self._create_task_list_event().content
                }
                
                break  # 出错时退出循环

        # 达到最大轮次或出错退出
        if turn_count >= max_turns:
            logger.warning(f"⚠️ 子任务 {subtask_idx} 达到最大执行轮次")
            
            # 更新任务状态为错误
            self.current_subtask.status = "error"
            self.current_subtask.error = "Reached maximum execution turns"
        
        # Complete task metrics tracking
        if self.current_task_id:
            is_successful = self.current_subtask and self.current_subtask.status == "finish"
            self.metrics_tracker.finish_task(
                task_id=self.current_task_id,
                is_successful=is_successful,
                total_codegen_calls=self.codegen_call_count,
                total_input_tokens=self.total_input_tokens,
                total_output_tokens=self.total_output_tokens,
                start_time_ms=self.task_start_time
            )
            logger.info(f"🎯 Completed task tracking: {self.current_task_id}, success: {is_successful}, calls: {self.codegen_call_count}")
        
        # 更新全局对话历史（即使是失败的情况）
        self.conversation_history = full_messages
        
        # 发送最终状态
        yield {
            "type": "task_list_event",
            "data": self._create_task_list_event().content
        }

    async def _call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """调用工具"""
        try:
            logger.info(f"准备调用工具: {tool_name}, 原始参数: {parameters}")
            
            # 🎯 新增：参数覆盖逻辑
            overridden_params, override_records = self._override_tool_parameters(tool_name, parameters)

            # 如果有参数被覆盖，记录到日志和对话历史
            if override_records:
                override_msg = f"工具 {tool_name} 参数已根据环境配置自动调整:\n" + "\n".join([f"• {record}" for record in override_records])
                logger.info(f"🔧 {override_msg}")
                
                # 将参数覆盖信息添加到对话历史，便于LLM了解参数调整情况
                self.conversation_history.append({
                    "role": "user",
                    "content": f"工具参数提示: {override_msg}"
                })

            if tool_name in self.custom_tools:
                custom_result = await self.custom_tools[tool_name].ainvoke(input=overridden_params)
                logger.info(f"🔍 自定义工具调用结果: {custom_result}")
                
                # 将自定义工具的结果格式转换为统一的工具结果格式
                success = custom_result.get("status", '') == 'success'
                if success:
                    self.complaint_or_report_history.append([tool_name, custom_result])
                    if table_name:=custom_result.get("result_table_name"):
                        self.temp_tables.append(table_name)
                    return {
                        "success": True,
                        "result": custom_result,
                        "error": None
                    }
                else:
                    return {
                        "success": False,
                        "result": None,
                        "error": custom_result.get("error", "Unknown error")
                    }
            else:
                # 🆕 新增：为nl2code工具自动添加previous_actions参数
                if "nl2code" in tool_name or "generate_code" in tool_name:
                    if self.previous_actions:
                        overridden_params["params"]["previous_actions"] = self.previous_actions
                        override_records.append(f"自动添加了 {len(self.previous_actions)} 条历史代码执行记录")
                        logger.info(f"🔧 为 {tool_name} 自动添加了 {len(self.previous_actions)} 条 previous_actions")

                
                # 特殊处理jupyter__execute_code工具 - 自动添加required_packages参数
                if tool_name == "jupyter__execute_code" and self.pending_required_packages:
                    logger.info(f"🔍 为jupyter__execute_code添加required_packages: {self.pending_required_packages}")
                    overridden_params = overridden_params.copy()  # 避免修改原始参数
                    overridden_params["required_packages"] = self.pending_required_packages
                    # 使用完毕后清空待使用的依赖包列表
                    self.pending_required_packages = []
                
                # 🆕 收集代码执行历史 - 在工具调用后
                jupyter_code_to_record = None
                if "jupyter__execute_code" in tool_name:
                    jupyter_code_to_record = overridden_params.get('code', '')

                # Track nl2code call metrics
                call_id = None
                call_start_time = time.time()
                if "nl2code" in tool_name and self.current_task_id:
                    self.codegen_call_count += 1
                    # Estimate input tokens from parameters
                    param_text = json.dumps(overridden_params, ensure_ascii=False)
                    input_tokens = count_tokens_approximately(param_text)
                    self.total_input_tokens += input_tokens
                    
                    call_id = self.metrics_tracker.track_codegen_call(
                        task_id=self.current_task_id,
                        call_sequence=self.codegen_call_count,
                        tool_name=tool_name,
                        input_tokens=input_tokens,
                        action_manager_enabled=bool(self.previous_actions),
                        scenario_detected=overridden_params.get("params", {}).get("scenario", "general")
                    )
                    logger.info(f"🎯 Started tracking nl2code call: {call_id}")
                    
                    # 📊 Record codegen context for replay
                    try:
                        # Build available tools list
                        available_tools = []
                        if hasattr(self, 'tools') and self.tools:
                            available_tools = [{"name": name, "description": str(tool)} for name, tool in self.tools.items()]
                        
                        # Extract execution environment variables
                        env_vars = {}
                        if hasattr(self, 'agent_state') and self.agent_state:
                            env_vars = {
                                "session_id": self.current_session_id,
                                "user_id": self.current_user_id,
                                "conversation_history_length": len(self.agent_state.get('conversation_history', [])),
                                "database_schema_available": bool(self.agent_state.get('database_schema', {})),
                                "data_sources_count": len(self.agent_state.get('available_data_sources', []))
                            }
                        
                        self.replay_collector.record_codegen_context(
                            task_id=self.current_task_id,
                            call_id=call_id,
                            call_sequence=self.codegen_call_count,
                            full_prompt=param_text,  # Complete prompt sent to tool
                            model_name=tool_name,
                            session_id=self.current_session_id,  # 添加session_id参数
                            model_parameters=overridden_params,
                            previous_code_history=[action[0] for action in self.previous_actions],
                            execution_environment_vars=env_vars,
                            available_tools=available_tools,
                            data_schema_info=self.agent_state.get('database_schema', {}) if hasattr(self, 'agent_state') else {},
                            action_manager_state={"enabled": bool(self.previous_actions), "actions_count": len(self.previous_actions)},
                            scenario_detection_result={"detected_scenario": overridden_params.get("params", {}).get("scenario", "general")}
                        )
                        logger.info(f"📊 Recorded codegen context for call: {call_id}")
                    except Exception as e:
                        logger.warning(f"Failed to record codegen context: {e}")
                
                # 🎯 新增：aisearch工具调用前发送reference_event
                if "aisearch" in tool_name:
                    logger.info("🔍 aisearch工具调用开始，发送reference_event")
                    if self.writer:
                        try:
                            reference_event_data = {
                                "source": "knowledge_base",
                                "status": "running", 
                                "name": "正在搜索知识库"
                            }
                            self.writer({
                                "reference_event": {
                                    "data": reference_event_data
                                }
                            })
                            logger.debug("✅ 成功发送aisearch开始reference_event")
                        except Exception as e:
                            logger.error(f"❌ 发送aisearch开始reference_event失败: {e}")
                
                logger.info(f"🔍 调用MCP工具: {tool_name}, 最终参数: {overridden_params}")
                print(f"🔍 调用MCP工具: {tool_name}, 最终参数: {overridden_params}")
                result = await self.mcp_manager.call_tool(tool_name, arguments=overridden_params)
                logger.info(f"full_result: {tool_name},{result}")
                
                # 🎯 新增：aisearch工具调用后发送reference_event（无论成功还是失败）
                if "aisearch" in tool_name:
                    aisearch_success = False
                    aisearch_count = 0
                    aisearch_error = None
                    reference_list = []
                    
                    try:
                        if hasattr(result, 'content') and result.content:
                            content = result.content[0]
                            if hasattr(content, 'text'):
                                result_text = content.text
                                if "aisearch" in result_text:
                                    aisearch_result = json.loads(result_text)
                                    aisearch_items = aisearch_result.get("aisearch", [])
                                    if aisearch_items:
                                        aisearch_success = True
                                        # 构建召回的reference_list，限制chunk长度
                                        for item in aisearch_items:
                                            try:
                                                reference_list.append({
                                                    "knowledge_base_id": item.get('knowledge_base_id', ''),
                                                    "knowledge_base_name": item.get('knowledge_base_name', ''),
                                                    "file_id": item.get('file_id', ''),
                                                    "file_name": item.get('file_name', ''),
                                                    "file_url": item.get('file_url', ''),
                                                    "chunk_id": item.get('chunk_id', ''),
                                                    "chunk": (item.get('content', '') or '')[:200],
                                                    "score": item.get('score'),
                                                    "rerank_score": item.get('rerank_score')
                                                })
                                            except Exception:
                                                # 单条解析失败不影响整体
                                                continue
                                        aisearch_count = len(reference_list)
                                        logger.info(f"🔍 aisearch工具调用成功，召回了{aisearch_count}篇文档")
                                    else:
                                        logger.info(f"🔍 aisearch工具调用完成，但未召回任何文档")
                                else:
                                    aisearch_error = "返回结果格式异常"
                            else:
                                aisearch_error = "返回内容为空"
                        else:
                            aisearch_error = "工具调用无返回内容"
                    except Exception as e:
                        aisearch_error = f"解析结果失败: {str(e)}"
                        logger.error(f"❌ 解析aisearch结果失败: {e}")
                    
                    # 发送相应的状态事件
                    if self.writer:
                        try:
                            if aisearch_success:
                                reference_event_data = {
                                    "source": "knowledge_base",
                                    "status": "finish",
                                    "name": "搜索知识库完成",
                                    "count": aisearch_count,
                                    "reference_list": reference_list
                                }
                            else:
                                reference_event_data = {
                                    "source": "knowledge_base", 
                                    "status": "error",
                                    "name": f"搜索知识库失败: {aisearch_error or '未知错误'}"
                                }
                            
                            self.writer({
                                "reference_event": {
                                    "data": reference_event_data
                                }
                            })
                            logger.debug(f"✅ 成功发送aisearch结果reference_event，状态: {reference_event_data['status']}")
                        except Exception as e:
                            logger.error(f"❌ 发送aisearch结果reference_event失败: {e}")
                
                if hasattr(result, 'content'):
                    logger.info(f"🔍 content长度: {len(result.content) if result.content else 0}")
                    if result.content:
                        if hasattr(result.content[0], 'text'):
                            logger.info(f"🔍 content text: {result.content[0].text}")
                
                # 检查 MCP 工具调用的实际执行结果
                success = True
                error_msg = None
                result_json = None
                
                if hasattr(result, 'content') and result.content:
                    # 解析第一个 TextContent 中的 JSON
                    try:
                        content = result.content[0]
                        if hasattr(content, 'text'):
                            result_json = json.loads(content.text)
                            logger.info(f"🔍 解析的JSON结果: {result_json}")
                            
                            # 特殊处理nl2code工具的返回结果
                            if "nl2code" in tool_name or "generate_sql" in tool_name:
                                if isinstance(result_json, dict) and "required_packages" in result_json:
                                    # 存储required_packages供后续jupyter__execute_code使用
                                    self.pending_required_packages = result_json.get("required_packages", [])
                                    logger.info(f"🔍 从{tool_name}获取到required_packages: {self.pending_required_packages}")
                            
                            if result_json.get('error', False):
                                success = False
                                # 提取错误信息
                                outputs = result_json.get('outputs', [])
                                if outputs and 'evalue' in outputs[0]:
                                    error_msg = outputs[0]['evalue']
                                else:
                                    error_msg = "Tool execution failed"
                    except (json.JSONDecodeError, IndexError, AttributeError) as e:
                        logger.warning(f"无法解析工具调用结果: {e}")
                        success = False
                        error_msg = f"Parse error: {str(e)}"
                        result_json = None

                # 🆕 统一处理代码执行历史记录 - jupyter和load_data_by_sql都要记录
                if tool_name == "jupyter__execute_code" and jupyter_code_to_record and jupyter_code_to_record.strip():
                    status = 'success' if success else 'error'
                    
                    # 尝试提取输出信息
                    if result_json and isinstance(result_json, dict):
                        outputs = result_json.get('outputs', [])
                        output_text = self._extract_simple_output_from_outputs(outputs)
                    else:
                        # 如果没有解析成功，使用错误信息或默认信息
                        if error_msg:
                            output_text = f"Error: {error_msg}"
                        else:
                            output_text = "Execution completed but output unavailable"
                    
                    self._add_previous_action(jupyter_code_to_record, status, output_text)
                    logger.info(f"🔍 记录jupyter代码执行历史: code长度={len(jupyter_code_to_record)}, status={status}, 当前总数={len(self.previous_actions)}")
                    
                    # 📊 Record code execution for replay
                    try:
                        # Extract execution details
                        execution_time_ms = int((time.time() - call_start_time) * 1000)
                        
                        # Build environment snapshot before execution
                        env_snapshot = {}
                        if hasattr(self, 'agent_state') and self.agent_state:
                            env_snapshot = {
                                "available_data_sources": self.agent_state.get('available_data_sources', []),
                                "database_schema": self.agent_state.get('database_schema', {}),
                                "conversation_context": len(self.agent_state.get('conversation_history', []))
                            }
                        
                        # Extract created files/visualizations from outputs
                        files_created = []
                        visualizations = []
                        if result_json and isinstance(result_json, dict):
                            outputs = result_json.get('outputs', [])
                            for output in outputs:
                                if output.get('output_type') == 'display_data':
                                    if 'data' in output and 'image/png' in output['data']:
                                        visualizations.append("chart_generated")
                                elif output.get('output_type') == 'stream' and 'saved to' in output.get('text', ''):
                                    # Extract file names from output text
                                    import re
                                    file_matches = re.findall(r'saved to (.+)', output.get('text', ''))
                                    files_created.extend(file_matches)
                        
                        self.replay_collector.record_code_execution(
                            task_id=self.current_task_id,
                            call_id=f"jupyter_{int(time.time()*1000)}",  # Generate unique execution ID
                            code_before_execution=jupyter_code_to_record,
                            environment_snapshot_before=env_snapshot,
                            available_variables={},  # TODO: Could be extracted from jupyter if needed
                            imported_modules=[],  # TODO: Could be parsed from code
                            execution_method="jupyter",
                            execution_success=success,
                            execution_output=output_text,
                            execution_errors=error_msg or "",
                            execution_time_ms=execution_time_ms,
                            files_created=files_created,
                            visualizations_generated=visualizations
                        )
                        logger.info(f"📊 Recorded code execution for jupyter call, success: {success}")
                    except Exception as e:
                        logger.warning(f"Failed to record code execution: {e}")
                
                # 🆕 为 load_data_by_sql 工具记录SQL执行历史
                elif "load_data_by_sql" in tool_name:
                    sql = overridden_params.get('sql', '')
                    if sql.strip():
                        status = 'success' if success else 'error'
                        
                        # 尝试提取输出信息
                        if result_json and isinstance(result_json, dict):
                            outputs = result_json.get('outputs', [])
                            output_text = self._extract_simple_output_from_outputs(outputs)
                        else:
                            output_text = error_msg if error_msg else "SQL executed and data loaded"
                        
                        # 记录SQL而不是生成的代码
                        self._add_previous_action(f"SQL: {sql}", status, output_text)
                        logger.info(f"🔍 记录SQL执行历史: sql长度={len(sql)}, status={status}, 当前总数={len(self.previous_actions)}")


                # Complete nl2code call tracking
                if call_id and "nl2code" in tool_name:
                    call_duration_ms = int((time.time() - call_start_time) * 1000)
                    
                    # Estimate output tokens from result
                    output_tokens = 0
                    code_generated = None
                    if result_json and isinstance(result_json, dict):
                        code_generated = result_json.get('python_code', '')
                        if code_generated:
                            output_tokens = count_tokens_approximately(code_generated)
                            self.total_output_tokens += output_tokens
                    
                    self.metrics_tracker.finish_codegen_call(
                        call_id=call_id,
                        execution_success=success,
                        duration_ms=call_duration_ms,
                        error_message=error_msg
                    )
                    logger.info(f"🎯 Completed tracking nl2code call: {call_id}, success: {success}, duration: {call_duration_ms}ms")

                # 构建返回结果，包含参数覆盖信息
                tool_result = {
                    "success": success,
                    "result": result,
                    "error": error_msg
                }
                
                # 如果有参数覆盖，在结果中记录
                if override_records:
                    tool_result["parameter_overrides"] = override_records
                
                return tool_result
        except Exception as e:
            logger.error(f"❌ 工具调用失败: {tool_name}, error: {e}")
            # 捕获完整的traceback信息
            tb_str = traceback.format_exc()
            logger.error(f"❌ 完整traceback: {tb_str}")
            return {
                "success": False, 
                "result": None, 
                "error": str(e),
                "traceback": tb_str
            }

    def _extract_text_from_result(self, result) -> Optional[str]:
        """从工具结果中提取文本内容"""
        if hasattr(result, 'content') and result.content:
            try:
                content = result.content[0]
                if hasattr(content, 'text'):
                    return content.text
            except (IndexError, AttributeError):
                pass
        return None

    def _format_sql_generation_output(self, result_json: Dict[str, Any]) -> str:
        """格式化SQL生成工具的输出为markdown格式"""
        sql = result_json.get("sql", "").strip()
        reasoning = result_json.get("reasoning", "").strip()
        tables = result_json.get("tables", [])
        
        # 构建markdown输出
        markdown_parts = []
        
        # SQL代码块 - 添加自动换行
        if sql:
            formatted_sql = self._format_sql_with_line_breaks(sql)
            markdown_parts.append(f"```sql\n{formatted_sql}\n```")
        
        # 涉及的表
        if tables and isinstance(tables, list):
            markdown_parts.append("### 涉及的表")
            for table in tables:
                markdown_parts.append(f"- {table}")
        
        # 推理过程
        if reasoning:
            markdown_parts.append("### 推理过程")
            markdown_parts.append(reasoning)
        
        return "\n\n".join(markdown_parts)

    def _format_sql_with_line_breaks(self, sql: str) -> str:
        """为SQL语句添加适当的换行以提高可读性"""
        import sqlparse
        formatted = sqlparse.format(
            sql, 
            reindent=True, 
            keyword_case='upper',
            indent_width=2,
            wrap_after=80
        )
        return formatted.strip()

    def _format_tool_outputs(self, tool_result: Dict[str, Any], tool_name: str) -> List[Dict[str, Any]]:
        """格式化工具输出为Jupyter格式"""
        outputs = []
        
        # 🎯 新增：显示参数覆盖信息
        # if tool_result.get("parameter_overrides"):
        #     override_info = "系统自动调整了以下参数:\n" + "\n".join([f"• {record}" for record in tool_result["parameter_overrides"]])
        #     outputs.append({
        #         "output_type": "stream",
        #         "name": "stdout",
        #         "text": f"[参数调整] {override_info}\n\n"
        #     })
        
        if not tool_result["success"]:
            # 直接输出工具的完整结果，工具已经返回了适合展示的内容
            result = tool_result.get("result")
            outputs.append({
                "output_type": "stream",
                "name": "stderr", 
                "text": "工具执行失败"
            })
            return outputs
        
        result = tool_result["result"]
        
        # 处理自定义工具输出
        if tool_name == "load_data_and_join_table":
            # 自定义工具已经在result中包含了完整的结果
            if isinstance(result, dict):
                # 构建格式化的输出
                output_text = f"✅ 数据JOIN操作成功完成\n\n"
                output_text += f"**结果表名**: {result.get('result_table_name', 'N/A')}\n"
                output_text += f"**总列数**: {result.get('total_columns', 'N/A')}\n"
                output_text += f"**示例行数**: {result.get('sample_rows_count', 'N/A')}\n\n"
                
                # 显示前5行示例数据
                sample_data = []
                if sample_data:
                    output_text += "**前5行数据示例**:\n"
                    if sample_data:
                        headers = list(json.loads(sample_data[0].text).keys()) if sample_data else []
                        table_data = [headers]
                        for row in sample_data:
                            table_data.append([str(row.get(col, '')) for col in headers])
                        outputs.append({"output_type": "csv", "csv_data": table_data})
                    
                    outputs.append({
                        "output_type": "stream",
                        "name": "stdout",
                        "text": output_text
                    })
                else:
                    output_text += "⚠️ 无示例数据"
                    outputs.append({
                        "output_type": "stream", 
                        "name": "stdout",
                        "text": output_text
                    })
            else:
                outputs.append({"output_type": "stream", "text": str(result)})
            return outputs
        
        elif "jupyter__execute_code" in tool_name:
            # 对于jupyter工具，直接返回其outputs
            if hasattr(result, 'content') and result.content:
                try:
                    content = result.content[0]
                    if hasattr(content, 'text'):
                        result_json = json.loads(content.text)
                        jupyter_outputs = result_json.get('outputs', [])
                        outputs.extend(jupyter_outputs)
                        return outputs
                except:
                    pass
            outputs.append({"output_type": "stream", "text": str(result)})
            return outputs
        elif "generate_vertical_ad" in tool_name or "generate_horizontal_ad" in tool_name:
            outputs.append({"output_type": "display_data", "text": str(result)})
            return outputs

        elif "load_data_by_sql" in tool_name:
            # load_data_by_sql 是MCP工具，按标准格式处理
            if hasattr(result, 'content') and result.content:
                try:
                    content = result.content[0]
                    if hasattr(content, 'text'):
                        result_json = json.loads(content.text)
                        # MCP load_data_by_sql返回格式: {"error": bool, "outputs": [...]}
                        if not result_json.get('error', False):
                            jupyter_outputs = result_json.get('outputs', [])
                            try:
                                self.data_info = jupyter_outputs[0]
                                outputs.extend(jupyter_outputs[1:])  
                            except:
                                outputs.extend(jupyter_outputs)  
                        else:
                            # 显示错误信息
                            error_details = result_json.get('details', 'Unknown error')
                            outputs.append({"output_type": "stream", "name": "stderr", "text": f"Error: {error_details}"})
                        return outputs
                except:
                    pass
            outputs.append({"output_type": "stream", "text": str(result)})
            return outputs
        
        elif "generate_sql" in tool_name or "nl2code" in tool_name:
            # 显示生成的SQL或代码 - 提取实际的文本内容
            text_content = self._extract_text_from_result(result)
            if text_content:
                # 特殊处理nl2code工具的新返回格式
                if "nl2code" in tool_name or "generate_sql" in tool_name:
                    try:
                        result_json = json.loads(text_content)
                        
                        # 处理SQL生成工具的返回格式 {sql, reasoning, tables}
                        if isinstance(result_json, dict) and "sql" in result_json:
                            logger.info(f"🔍 检测到SQL生成工具格式，开始格式化输出")
                            formatted_output = self._format_sql_generation_output(result_json)
                            outputs.append({"output_type": "display_data", "data": {"text/markdown": formatted_output}})
                            return outputs
                        
                        # 处理nl2code工具的返回格式 {python_code, required_packages}
                        elif isinstance(result_json, dict) and "python_code" in result_json:
                            # 显示生成的代码
                            display_text = result_json["python_code"]
                            # 如果有依赖包信息，也显示出来
                            if "required_packages" in result_json and result_json["required_packages"]:
                                display_text += f"\n\n# Required packages: {result_json['required_packages']}"
                            outputs.append({"output_type": "execute_result", "data": {"text/plain": display_text}})
                            return outputs
                    except (json.JSONDecodeError, TypeError):
                        pass
                
                outputs.append({"output_type": "execute_result", "data": {"text/plain": text_content}})
                return outputs
            outputs.append({"output_type": "execute_result", "data": {"text/plain": str(result)}})
            return outputs

        elif "TCHouseDExecuteQuery" in tool_name:
            # TCHouseD格式: {"RequestId": "...", "Data": [{"column1": value1, "column2": value2, ...}]}
            text_content = self._extract_text_from_result(result)
            result_all = None

            if text_content:
                try:
                    query_resp = json.loads(text_content)
                    if "Data" in query_resp and isinstance(query_resp["Data"], list) and query_resp["Data"]:
                        # 从第一行数据提取列名
                        first_row = query_resp["Data"][0]
                        if isinstance(first_row, dict):
                            columns = list(first_row.keys())
                            # 构建表格数据：[列名行, 数据行1, 数据行2, ...]
                            result_all = [columns]
                            # 添加所有数据行
                            for row_dict in query_resp["Data"]:
                                if isinstance(row_dict, dict):
                                    row_values = [row_dict.get(col, "") for col in columns]
                                    result_all.append(row_values)
                    
                    logger.info(f"parse TCHouseDExecuteQuery result: {result_all}")
                except (json.JSONDecodeError, KeyError, TypeError) as e:
                    logger.warning(f"解析TCHouseDExecuteQuery结果失败: {e}")
                    result_all = None

            if result_all:
                outputs.append({"output_type": "csv", "csv_data": result_all})
            else:
                outputs.append({"output_type": "stream", "text": str(result)})
            return outputs
            
        elif "DLCExecuteQuery" in tool_name:
            # DLC格式: {"Response": {"TaskInfo": {"ResultSet": "...", "ResultSchema": [{"Name": "..."}, ...]}}}}
            text_content = self._extract_text_from_result(result)
            result_all = None

            if text_content:
                try:
                    query_resp = json.loads(text_content)
                    if "Response" in query_resp and "TaskInfo" in query_resp["Response"]:
                        task_info = query_resp["Response"]["TaskInfo"]
                        if "ResultSchema" in task_info and "ResultSet" in task_info:
                            columns = [col["Name"] for col in task_info["ResultSchema"]]
                            rows = json.loads(task_info["ResultSet"])

                            result_all = [columns]
                            result_all.extend(rows)
                except (json.JSONDecodeError, KeyError, TypeError) as e:
                    logger.warning(f"解析DLCExecuteQuery结果失败: {e}")
                    result_all = None

            if result_all:
                outputs.append({"output_type": "csv", "csv_data": result_all})
            else:
                outputs.append({"output_type": "stream", "text": str(result)})
            return outputs

        elif "DLCListTables" in tool_name:
            # {"Response": {"TableList": [{"TableBaseInfo":{}, "Columns":[]}]
            text_content = self._extract_text_from_result(result)
            result_all = None

            if text_content:
                query_resp = json.loads(text_content)
                if "Response" in query_resp and "TableList" in query_resp["Response"]:
                    table_list = query_resp["Response"]["TableList"]
                    columns = ["Name", "Type", "Nullable", "Precision", "Scale", "Comment"]
                    # [["c1", "c2"], ["row1_c1", "row1_c2"], ...]
                    header = ["TableName"]
                    header.extend(columns)
                    result_all = [header]

                    for table in table_list:
                        if "TableBaseInfo" in table and "Columns" in table:
                            table_name = table["TableBaseInfo"]["TableName"]

                            rows = []
                            for column_ddl in table["Columns"]:
                                row = [column_ddl[c] for c in columns]
                                row.insert(0, table_name)
                                rows.append(row)

                            result_all.extend(rows)

            if result_all:
                outputs.append({"output_type": "csv", "csv_data": result_all})
            else:
                outputs.append({"output_type": "stream", "text": str(result)})

            logger.info(f"parse dlc__DLCListTables result: {result_all}")
            logger.info(f"parse dlc__DLCListTables result: {outputs}")

            return outputs
        elif "TCHouseDGetTableSchema" in tool_name:
            # TCHouseDGetTableSchema工具的适配 - 解析表结构信息
            text_content = self._extract_text_from_result(result)
            result_all = None
            
            if text_content:
                try:
                    query_resp = json.loads(text_content)
                    if "TableSchemas" in query_resp and isinstance(query_resp["TableSchemas"], list):
                        # 解析表结构数据，格式化为表格
                        result_all = [["表名", "建表SQL"]]  # 表头
                        for table_schema in query_resp["TableSchemas"]:
                            if isinstance(table_schema, dict):
                                table_name = table_schema.get("Table", "")
                                create_sql = table_schema.get("CreateSql", "")
                                # 如果SQL太长，截取前500字符并添加省略号
                                if len(create_sql) > 500:
                                    create_sql = create_sql[:500] + "..."
                                result_all.append([table_name, create_sql])
                except (json.JSONDecodeError, KeyError, TypeError) as e:
                    logger.warning(f"解析TCHouseDGetTableSchema结果失败: {e}")
                    result_all = None
            
            if result_all:
                outputs.append({"output_type": "csv", "csv_data": result_all})
            else:
                # 回退到原始文本显示
                if text_content:
                    outputs.append({"output_type": "stream", "text": text_content})
                else:
                    outputs.append({"output_type": "stream", "text": str(result)})
            return outputs
        else:
            # 其他工具的通用显示 - 也尝试提取文本内容
            text_content = self._extract_text_from_result(result)
            if text_content:
                outputs.append({"output_type": "stream", "text": text_content})
            else:
                outputs.append({"output_type": "stream", "text": str(result)})

            return outputs

    def get_env_params_from_mcp_manager(self) -> Dict[str, Any]:
        """从MCPManager获取环境参数，用于工具调用时的参数覆盖"""
        if self.env_params_cache is not None:
            return self.env_params_cache
        
        env_params = {}
        
        try:
            # 从MCPManager的注册信息中提取环境参数
            for server_name, server_info in self.mcp_manager.mcp_servers.items():
                if server_name == "generate_sql" and server_info.get('params'):
                    # 从环境变量中提取参数
                    env_dict = server_info['params'].env or {}
                    
                    # 提取DLC相关参数 - 修复环境变量名映射
                    if env_dict.get('DATA_ENGINE_NAME'):
                        env_params['DataEngineName'] = env_dict['DATA_ENGINE_NAME']
                    
                    # 从DB_TABLE中解析数据源名称、数据库名和表列表
                    if env_dict.get('DB_TABLE'):
                        try:
                            db_table_info = json.loads(env_dict['DB_TABLE'])
                            if isinstance(db_table_info, list) and len(db_table_info) > 0:
                                first_db = db_table_info[0]
                                if isinstance(first_db, dict):
                                    # 提取数据库名
                                    if 'DbName' in first_db:
                                        env_params['DatabaseName'] = first_db['DbName']
                                    # 注意：不提取TableList作为TableNames，因为：
                                    # - TableList 是数据源配置（告诉系统有哪些表可用）
                                    # - TableNames 是用户意图（用户想查询哪些表）
                                    # 这两个概念不应该混淆
                        except (json.JSONDecodeError, KeyError, IndexError) as e:
                            logger.warning(f"解析DB_TABLE失败: {e}")
                    
                    # 如果有单独设置的环境变量，优先使用（向后兼容）
                    if env_dict.get('DATASOURCE_CONNECTION_NAME'):
                        env_params['DatasourceName'] = env_dict['DATASOURCE_CONNECTION_NAME']
                    if env_dict.get('DATASOURCE_NAME'):
                        env_params['DatasourceName'] = env_dict['DATASOURCE_NAME']
                    if env_dict.get('DBNAME'):
                        env_params['DatabaseName'] = env_dict['DBNAME']
                    # 注意：不提取TABLES环境变量，TableNames应该由LLM根据用户意图生成
                    
                    logger.info(f"🔧 从MCPManager提取环境参数: {env_params}")
                    break
            
            # 缓存结果避免重复计算
            self.env_params_cache = env_params
            
        except Exception as e:
            logger.warning(f"⚠️ 提取环境参数失败: {e}")
            self.env_params_cache = {}
        
        return self.env_params_cache

    def _override_tool_parameters(self, tool_name: str, parameters: Dict[str, Any]) -> Tuple[Dict[str, Any], List[str]]:
        """检查并覆盖工具参数，返回覆盖后的参数和修改记录"""

        overridden_params = parameters.copy()
        override_records = []

        # 🆕 从MCPManager.mcp_servers获取TCHouseD URL（正确的取值方法）
        tchouse_d_url = ''
        if self.mcp_manager and hasattr(self.mcp_manager, 'mcp_servers'):
            for server_name, server_info in self.mcp_manager.mcp_servers.items():
                if server_info.get('type') == 'TCHouseD' and server_info.get('url'):
                    tchouse_d_url = server_info['url']
                    break
        
        # 备用方案：从环境变量获取
        if not tchouse_d_url:
            import os
            mcp_url = json.loads(os.getenv('MCP_URL', '{}'))
            tchouse_d_url = mcp_url.get("TCHouseD", '')
        
        logger.info(f"tchouse_d_url: {tchouse_d_url}")

        load_data_and_join_table_override_mappings = {
            # load_data_and_join_table 工具相关参数 - 覆盖所有参数
            "json_data": self.complaint_or_report_history[-1][-1] if self.complaint_or_report_history else '',
            "table_a_name": "feedback.feedback",  # 使用实际存在的表名
            "join_condition": "temp_table.registration_id = feedback.registration_id",  # 修正JOIN条件
            "mcp_url": tchouse_d_url,
            "database_name": "feedback",  # 使用实际的数据库名
            "catalog_name": "internal"
        }

        if tool_name == 'load_data_and_join_table':
            overridden_params = load_data_and_join_table_override_mappings
            logger.info(f"工具 {tool_name} 参数覆盖: {overridden_params}")
            override_records.append(
                f"参数load_data_and_join_table: '{parameters}' → '{overridden_params}' (使用MCPManager配置)"
            )

        if "generate_sql" in tool_name:
            if self.agent_state.get("identified_intent_name")=="complaint_or_report":
                if not self.temp_tables:
                    self.temp_tables = ["feedback"]
                _override_cfg = {'DbName': 'feedback', 'CatalogName': 'internal', 'TableList': self.temp_tables}
                self.mcp_manager.update_generate_sql([_override_cfg])
                logger.info(f"工具 {tool_name} 参数覆盖: {_override_cfg}")
                override_records.append(
                    f"参数generate_sql: '{parameters}' → '{_override_cfg}' (使用固定规则配置)"
                )

        env_params = self.get_env_params_from_mcp_manager()
        logger.info(f"环境参数: {env_params}")
        
        # 定义需要覆盖的参数映射关系 - 根据具体工具的参数要求
        override_mappings = {
            # DLC工具相关参数 - 只覆盖环境配置类参数，不覆盖用户意图类参数
            'DataEngineName': ['DataEngineName'],
            'DatasourceName': ['DatasourceName', 'DatasourceConnectionName', 'Catalog'],  # TCHouseDListTableNames 需要 Catalog
            'DatabaseName': ['DatabaseName', 'Database'],  # TCHouseDListTableNames 需要 Database
            # 注意：TableNames 不应该被覆盖，因为这是用户意图（指定要查询哪些表），不是环境配置
        }
        
        # 检查并覆盖参数
        for env_key, param_keys in override_mappings.items():
            if env_key in env_params:
                env_value = env_params[env_key]
                
                for param_key in param_keys:
                    if param_key in parameters:
                        original_value = parameters[param_key]
                        if original_value != env_value:
                            overridden_params[param_key] = env_value
                            override_records.append(
                                f"参数 {param_key}: '{original_value}' → '{env_value}' (使用环境配置)"
                            )
                            logger.info(f"🔧 工具 {tool_name} 参数覆盖: {param_key} = {env_value}")
        
        return overridden_params, override_records



    def _get_localized_text(self, key: str, detected_language: Optional[str] = None) -> str:
        """获取本地化文本"""
        language = detected_language or getattr(self, '_detected_language', 'zh')
        
        # 多语言文本映射
        texts = {
            'zh': {
                'waiting': '等待中...',
                'running': '正在执行...',
                'error': '错误',
                'tool_execution_error': '工具执行错误',
                'subtask_completed': '子任务已完成',
                'subtask_failed': '子任务执行失败',
                'parameter_adjusted': '参数已自动调整',
                'executing': '正在执行',
                'thinking': '思考',
                'finish': '完成'
            },
            'en': {
                'waiting': 'Waiting...',
                'running': 'Running...',
                'error': 'Error',
                'tool_execution_error': 'Tool Execution Error',
                'subtask_completed': 'Subtask completed',
                'subtask_failed': 'Subtask failed',
                'parameter_adjusted': 'Parameters automatically adjusted',
                'executing': 'Executing',
                'thinking': 'Thinking',
                'finish': 'Finish'
            }
        }
        
        return texts.get(language, texts['zh']).get(key, key)
    
    def _get_detected_language_from_state(self) -> Optional[str]:
        """从agent_state获取检测到的语言"""
        if self.agent_state:
            return self.agent_state.get('detected_language')
        return None

    def _add_previous_action(self, code: str, status: str, output: str):
        """统一添加previous_actions的方法"""
        self.previous_actions.append((code, status, output))
        logger.info(f"🔍 Added previous action #{len(self.previous_actions)}: status={status}")
        logger.info(f"🔍   Code (first 100 chars): {code[:100]}...")
        logger.info(f"🔍   Output (first 100 chars): {output[:100]}...")
        logger.debug(f"Added previous action. Total: {len(self.previous_actions)}")

    def _extract_simple_output_from_outputs(self, outputs: List[Dict[str, Any]]) -> str:
        """从outputs中提取简化的输出信息，用于previous_actions"""
        if not outputs:
            return "No output"
        
        # 遍历每个输出项，处理图像数据
        processed_outputs = []
        for output in outputs:
            if isinstance(output, dict) and 'data' in output and isinstance(output['data'], dict):
                data = output['data']
                # 检查是否包含图像数据
                image_types = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/svg+xml']
                
                simplified_output = output.copy()
                simplified_data = data.copy()
                
                for img_type in image_types:
                    if img_type in simplified_data and isinstance(simplified_data[img_type], str):
                        original_length = len(simplified_data[img_type])
                        simplified_data[img_type] = f"⟪{original_length} characters image data⟫"
                
                simplified_output['data'] = simplified_data
                processed_outputs.append(simplified_output)
            else:
                processed_outputs.append(output)
        
        # 转换为字符串并截断长内容
        result_str = str(processed_outputs)
        if len(result_str) > 2000:
            return result_str[:2000] + "..."
        
        return result_str




