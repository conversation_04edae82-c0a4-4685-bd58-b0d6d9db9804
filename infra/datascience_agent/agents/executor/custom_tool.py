import json
from typing import Dict, Any, List
from langchain_core.tools import tool
import uuid
import logging
from infra.mcp.basic import mcp_call_tool
from infra.mcp.nl2sql.llm.llm import llm_chat

logger = logging.getLogger(__name__)


def get_id_from_chunk(json_data: str) -> list:
    """
    从JSON字符串中提取登记编号ID

    Args:
        json_data: JSON格式的字符串，包含aisearch字段，每个aisearch项是一个字典，
                  其中content字段包含以'|'分隔的数据

    Returns:
        包含registration_id的字典列表的字符串格式，格式如：
        [{"registration_id": "1320102002023053117424237"}, ...]
        注意：只提取由数字组成的registration_id
    """
    result = []
    try:
        data = json.loads(json_data)
        chunks = data.get('aisearch', [])
        for chunk in chunks:
            # 获取content内容
            content = chunk.get('content', '')
            # 按'|'分割并去除两边空格
            parts = [part.strip() for part in content.split('|')]
            # 第一个有效部分就是registration_id
            if len(parts) > 1:  # 确保至少有一个字段
                registration_id = parts[1]  # 第一个'|'后的内容
                # 验证registration_id是否只由数字组成
                if registration_id.isdigit():
                    result.append({"registration_id": registration_id})
        result = json.dumps(result)
    except json.JSONDecodeError as e:
        logger.error(f"JSON数据解析失败: {e}")
        return []
    return result


@tool
async def load_data_and_join_table(
    json_data: str, 
    table_a_name: str, 
    join_condition: str, 
    mcp_url: str,
    database_name: str = "default_db",
    catalog_name: str = "internal"
) -> Dict[str, Any]:
    """
    将JSON数据加载到数据库作为临时表，与指定表进行JOIN操作，返回结果表名和前5行示例
    
    Args:
        json_data: JSON格式的数据字符串
        table_a_name: 要JOIN的已存在的表名
        join_condition: JOIN条件，例如 "a.id = b.id"
        mcp_url: MCP服务的URL
        database_name: 数据库名，默认为default_db
        catalog_name: catalog名称，默认为internal
        
    Returns:
        包含结果表名和前5行示例的字典
    """
    try:
        # 生成临时表名
        temp_table_name = f"temp_json_data_{uuid.uuid4().hex[:8]}"
        result_table_name = f"result_join_{uuid.uuid4().hex[:8]}"
        
        logger.info(f"开始处理JSON数据加载和JOIN操作，临时表名: {temp_table_name}")
        
        # 1. 解析JSON数据
        try:
            data = json.loads(json_data)
            if not isinstance(data, list):
                data = [data]  # 如果是单个对象，转换为列表
        except json.JSONDecodeError as e:
            logger.error(f"JSON数据解析失败: {e}")
            return {"error": f"JSON数据格式错误: {e}"}
        
        if not data:
            return {"error": "JSON数据为空"}
        
        # 2. 获取目标表的样例数据来了解表结构
        logger.info(f"获取表 {table_a_name} 的样例数据")
        
        # 解析表名，如果包含数据库名则分离
        if '.' in table_a_name:
            db_name, actual_table_name = table_a_name.split('.', 1)
        else:
            db_name = database_name
            actual_table_name = table_a_name
            
        sample_args = {
            "Catalog": catalog_name,
            "Database": db_name,
            "Table": actual_table_name,
            "Limit": 5
        }
        
        sample_result = await mcp_call_tool(
            url=mcp_url,
            tool_name="TCHouseDGetTableSample",
            arguments=sample_args
        )
        
        logger.info(f"样例数据获取结果: {sample_result}")
        
        # 3. 使用LLM生成CREATE TABLE和INSERT INTO SQL
        create_insert_sql = await _generate_create_insert_sql(
            json_data=json_data,
            temp_table_name=temp_table_name,
            target_table_sample=sample_result.content if hasattr(sample_result, 'content') else str(sample_result),
            database_name=db_name
        )
        
        if not create_insert_sql:
            return {"error": "SQL生成失败"}
        
        logger.info(f"生成的建表和插入SQL: {create_insert_sql}")
        
        # 4. 执行CREATE TABLE和INSERT INTO操作
        for i, sql in enumerate(create_insert_sql):
            if i == 0:
                print(f"准备执行CREATE TABLE语句: {sql}")
            else:
                print(f"准备执行INSERT语句 批次{i}: {sql[:100]}...")  # 只显示前100个字符
            
            execute_args = {"Query": sql}
            execute_result = await mcp_call_tool(
                url=mcp_url,
                tool_name="TCHouseDExecuteQuery",
                arguments=execute_args
            )
            
            if i == 0:
                print(f"CREATE TABLE执行结果: {execute_result}")
            else:
                print(f"INSERT批次{i}执行结果: {execute_result}")
                
        logger.info(f"成功执行 {len(create_insert_sql)} 条SQL语句（1个CREATE + {len(create_insert_sql)-1}个INSERT批次）")
        
        # 5. 使用LLM生成JOIN SQL
        join_sql = await _generate_join_sql(
            temp_table_name=temp_table_name,
            table_a_name=table_a_name,
            join_condition=join_condition,
            result_table_name=result_table_name,
            target_table_sample=sample_result.content if hasattr(sample_result, 'content') else str(sample_result),
            json_data=json_data,
            database_name=db_name
        )
        
        if not join_sql:
            return {"error": "JOIN SQL生成失败"}
        
        logger.info(f"生成的JOIN SQL: {join_sql}")
        
        # 6. 执行JOIN操作（创建结果表）
        logger.info(f"准备执行JOIN SQL: {join_sql}")
        execute_args = {"Query": join_sql}
        join_result = await mcp_call_tool(
            url=mcp_url,
            tool_name="TCHouseDExecuteQuery",
            arguments=execute_args
        )
        
        logger.info(f"JOIN执行结果: {join_result}")
        
        # 7. 获取结果表的前5行示例
        sample_result_args = {
            "Catalog": catalog_name,
            "Database": database_name,
            "Table": result_table_name,
            "Limit": 5
        }
        
        final_sample = await mcp_call_tool(
            url=mcp_url,
            tool_name="TCHouseDGetTableSample",
            arguments=sample_result_args
        )
        
        # # 8. 清理临时表（可选，取决于数据库策略）
        # cleanup_sql = f"DROP TABLE IF EXISTS {db_name}.{temp_table_name}"
        # print(f"准备执行清理SQL: {cleanup_sql}")
        # cleanup_args = {"Query": cleanup_sql}
        # cleanup_result = await mcp_call_tool(
        #     url=mcp_url,
        #     tool_name="TCHouseDExecuteQuery", 
        #     arguments=cleanup_args
        # )
        # print(f"清理SQL执行结果: {cleanup_result}")
        
        return {
            "result_table_name": result_table_name,
            "sample_data": final_sample.content if hasattr(final_sample, 'content') else str(final_sample),
            "temp_table_created": temp_table_name,
            "join_condition_used": join_condition,
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"执行load_data_and_join_table时发生错误: {e}", exc_info=True)
        return {"error": f"操作失败: {str(e)}"}


async def _generate_create_insert_sql(json_data: str, temp_table_name: str, target_table_sample: str, database_name: str) -> List[str]:
    """使用LLM生成CREATE TABLE和INSERT INTO SQL语句，优化版本：只用一次LLM生成模板，后续自动拼接"""
    
    # 解析JSON数据以获取记录数量
    try:
        data = json.loads(json_data)
        if not isinstance(data, list):
            data = [data]
    except json.JSONDecodeError as e:
        logger.error(f"JSON数据解析失败: {e}")
        return []
    
    logger.info(f"总共需要处理 {len(data)} 条记录")
    
    sql_list = []
    
    # 1. 使用LLM生成CREATE TABLE和一个小样本INSERT语句（10条数据用于模板）
    sample_data = data[:10]  # 只取前10条用于LLM生成模板
    
    llm_prompt = f"""你是一个数据库SQL专家。你需要根据提供的JSON数据生成CREATE TABLE和INSERT INTO语句。

目标数据库: TCHouseD (基于Apache Doris)
数据库名: {database_name}
临时表名: {temp_table_name}

目标表样例数据供参考:
{target_table_sample}

要求：
1. 分析JSON数据结构，推断合适的列类型
2. 生成CREATE TABLE语句创建临时表，必须包含数据库名: CREATE TABLE {database_name}.{temp_table_name}
3. 生成一个INSERT INTO语句插入提供的JSON数据，必须包含数据库名: INSERT INTO {database_name}.{temp_table_name}
4. 返回格式为JSON列表: ["CREATE TABLE...", "INSERT INTO..."]
5. 确保SQL语法符合Apache Doris规范
6. 字符串类型使用VARCHAR，数字类型使用合适的数值类型

正确示例：
-建表语句示例：
CREATE TABLE feedback (`registration_id` varchar(100) COMMENT "临时表";
-插入语句示例：
insert into feedback (`registration_id`)
values  ('xxxxxxxxxxxxxxxxx')

请只返回JSON格式的SQL语句列表，不要包含其他解释。"""

    messages = [
        {"role": "system", "content": llm_prompt},
        {"role": "user", "content": f"请为以下JSON数据生成CREATE TABLE和INSERT INTO语句：\n\n{json.dumps(sample_data)}"}
    ]
    
    try:
        response = llm_chat(messages)
        logger.info(f"LLM CREATE/INSERT模板响应: {response}")
        
        # 清理响应中的markdown格式并解析JSON
        cleaned_response = response.strip()
        if cleaned_response.startswith('```json'):
            cleaned_response = cleaned_response[7:]
        if cleaned_response.startswith('```'):
            cleaned_response = cleaned_response[3:]
        if cleaned_response.endswith('```'):
            cleaned_response = cleaned_response[:-3]
        cleaned_response = cleaned_response.strip()
        
        template_sqls = json.loads(cleaned_response)
        if not isinstance(template_sqls, list) or len(template_sqls) < 2:
            logger.error(f"LLM返回的SQL格式不正确: {response}")
            return []
        
        create_table_sql = template_sqls[0]
        sample_insert_sql = template_sqls[1]
        
        sql_list.append(create_table_sql)
        
        # 2. 从样本INSERT语句中提取模板
        insert_template = _extract_insert_template(sample_insert_sql, database_name, temp_table_name)
        if not insert_template:
            logger.error("无法从样本INSERT语句中提取模板")
            return []
        
        # 3. 使用模板批量生成INSERT语句（每批500条）
        batch_size = 300
        batches = [data[i:i + batch_size] for i in range(0, len(data), batch_size)]
        logger.info(f"将 {len(data)} 条记录分为 {len(batches)} 批次处理，每批最多 {batch_size} 条")
        
        for i, batch in enumerate(batches):
            batch_insert_sql = _generate_batch_insert_sql(batch, insert_template)
            if batch_insert_sql:
                sql_list.append(batch_insert_sql)
                logger.info(f"生成批次{i+1} INSERT语句，包含{len(batch)}条记录")
            else:
                logger.error(f"生成批次{i+1} INSERT语句失败")
                return []
        
        logger.info(f"成功生成 {len(sql_list)} 条SQL语句（1个CREATE + {len(batches)}个INSERT）")
        return sql_list
        
    except Exception as e:
        logger.error(f"生成CREATE/INSERT SQL时出错: {e}")
        return []


def _extract_insert_template(sample_insert_sql: str, database_name: str, temp_table_name: str) -> dict:
    """从样本INSERT语句中提取模板信息"""
    try:
        # 解析INSERT语句的基本结构
        # 期望格式: INSERT INTO database.table (column1, column2, ...) VALUES (value1, value2, ...), (value1, value2, ...), ...
        
        # 提取列名部分
        insert_part = sample_insert_sql.upper()
        if 'INSERT INTO' not in insert_part or 'VALUES' not in insert_part:
            logger.error(f"INSERT语句格式不正确: {sample_insert_sql}")
            return None
        
        # 找到列名部分
        values_pos = insert_part.find('VALUES')
        header_part = sample_insert_sql[:values_pos].strip()
        
        # 提取表名和列名
        if '(' in header_part and ')' in header_part:
            columns_start = header_part.find('(')
            columns_end = header_part.find(')')
            columns_part = header_part[columns_start+1:columns_end]
            
            # 清理列名
            columns = [col.strip().strip('`').strip('"') for col in columns_part.split(',')]
        else:
            # 如果没有明确的列名，假设是按JSON key的顺序
            logger.warning("INSERT语句中没有明确的列名，使用默认推断")
            columns = ["registration_id"]  # 根据实际情况调整
        
        template = {
            "prefix": f"INSERT INTO {database_name}.{temp_table_name}",
            "columns": columns,
            "columns_part": f"({', '.join([f'`{col}`' for col in columns])})" if columns else ""
        }
        
        logger.info(f"提取的INSERT模板: {template}")
        return template
        
    except Exception as e:
        logger.error(f"提取INSERT模板时出错: {e}")
        return None


def _generate_batch_insert_sql(batch_data: list, insert_template: dict) -> str:
    """使用模板生成批量INSERT语句"""
    try:
        prefix = insert_template["prefix"]
        columns = insert_template["columns"]
        columns_part = insert_template["columns_part"]
        
        # 生成VALUES部分
        values_list = []
        for record in batch_data:
            # 根据列名从记录中提取值
            values = []
            for col in columns:
                value = record.get(col, "")
                # 简单的SQL转义，实际应用中可能需要更复杂的处理
                if isinstance(value, str):
                    escaped_value = value.replace("'", "''")  # SQL单引号转义
                    values.append(f"'{escaped_value}'")
                else:
                    values.append(str(value))
            
            values_list.append(f"({', '.join(values)})")
        
        # 组装完整的INSERT语句
        full_sql = f"{prefix} {columns_part} VALUES {', '.join(values_list)}"
        
        return full_sql
        
    except Exception as e:
        logger.error(f"生成批量INSERT语句时出错: {e}")
        return None


async def _generate_join_sql(
    temp_table_name: str, 
    table_a_name: str, 
    join_condition: str,
    result_table_name: str,
    target_table_sample: str,
    json_data: str,
    database_name: str
) -> str:
    """使用LLM生成JOIN SQL语句"""
    
    system_prompt = f"""你是一个数据库SQL专家。你需要生成一个JOIN查询语句，将临时表与目标表进行JOIN，并创建结果表。

**重要说明**：
- 临时表是从JSON数据创建的，只包含registration_id等少量列
- 目标表包含完整的业务数据
- Apache Doris不支持EXCLUDE语法，请使用标准SQL

数据库信息:
- 数据库名: {database_name}
- 临时表名: {database_name}.{temp_table_name} (从JSON数据创建，只有registration_id列)
- 目标表名: {table_a_name} (完整业务表)
- JOIN条件: {join_condition}
- 结果表名: {database_name}.{result_table_name}

目标表样例数据:
{target_table_sample}

要求：
1. 生成CREATE TABLE AS SELECT语句: CREATE TABLE {database_name}.{result_table_name} AS SELECT
2. 主要选择目标表的数据: SELECT f.*
3. 使用提供的JOIN条件进行表连接
4. 确保SQL语法符合Apache Doris规范
5. 不要使用EXCLUDE语法或其他非标准语法
6. 只返回单条CREATE TABLE AS SELECT SQL语句

正确的SQL格式：
CREATE TABLE {database_name}.{result_table_name} AS
SELECT f.*
FROM {database_name}.{temp_table_name} t
JOIN {table_a_name} f ON t.registration_id = f.registration_id

正确示例：
CREATE TABLE feedback.result_join_xxx AS
SELECT f.*
FROM feedback.temp_json_data_xxx t
JOIN feedback.feedback f ON t.registration_id = f.registration_id

请只返回SQL语句，不要包含其他解释。"""

    user_prompt = f"""请生成JOIN SQL语句将 {database_name}.{temp_table_name} 与 {table_a_name} 进行JOIN，条件是 {join_condition}，结果保存到 {database_name}.{result_table_name}

要求：
- 临时表只有registration_id列，用于筛选
- 主要数据来自目标表，请使用 SELECT f.*
- 不要使用EXCLUDE语法"""
    
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]
    
    try:
        response = llm_chat(messages)
        logger.info(f"LLM JOIN响应: {response}")
        
        # 清理响应中的多余内容，提取SQL
        sql = response.strip()
        if sql.startswith('```sql'):
            sql = sql[6:]
        if sql.endswith('```'):
            sql = sql[:-3]
        sql = sql.strip()
        
        return sql
        
    except Exception as e:
        logger.error(f"生成JOIN SQL时出错: {e}")
        return ""

# 工具描述：
#   "TCHouseDExecuteQuery",
#    "title=None",
#    "description=""在 TCHouseD 上执行一个sql查询。TCHouseD是一个基于Apache Doris的数仓产品。目前只支持SELECT/SHOW/DESC/DESCRIBE/EXPLAIN/INSERT INTO 查询。每次只能执行一条SQL，不能包含恶意SQL，防止SQL注入。 要严格控制查询范围，防止数据泄露。当问你doris、Apache Doris、TCHouseD、TCHouse-D有关的时候，可以认为这三个名词含义一致。注意：查询结果会被转换为json格式，请注意查询结果的数据量。目前最大返回行数是1000行，超过会被截断。如果需要查询更多数据，请通过在SQL中添加Offset、Limit多次获取。",
#    "inputSchema="{
#       "properties":{
#          "Query":{
#             "description":"查询sql语句",
#             "type":"string"
#          }
#       },
#       "required":[
#          "Query"
#       ],
#       "type":"object"
#    },


#   "TCHouseDGetTableSample",
#    "title=None",
#    "description=""获取 TCHouseD 某个表的 sample 数据（最多1000条），并返回表的总行数、总字节数等基本信息。TCHouseD是一个基于Apache Doris的数仓产品。当问你doris、Apache Doris、TCHouseD、TCHouse-D有关的时候，可以认为这三个名词含义一致。",
#    "inputSchema="{
#       "properties":{
#          "Catalog":{
#             "description":"catalog名称（可选）, 默认为 internal",
#             "type":"string"
#          },
#          "Database":{
#             "description":"数据库名称",
#             "type":"string"
#          },
#          "Limit":{
#             "description":"采样数据条数，默认为10条，最多1000条",
#             "type":"number"
#          },
#          "Table":{
#             "description":"表名称",
#             "type":"string"
#          }
#       },
#       "required":[
#          "Database",
#          "Table"
#       ],
#       "type":"object"
#    }, 

# 使用示例
async def example_usage(json_data):
    """
    示例：如何使用 load_data_and_join_table 工具
    参考 infra/mcp/jupyter/simple_test.py 中的调用方式
    """
    # 示例JSON数据
    # 1320102002023053117424237   
    # 1320115002023053149333782
    # 1320104002023053183871403
    # 51320100002023053100001560
    # 1320119002023053105746496
    # 1320114002023053175304090
    # 1320104002023053163269173
    # 1320119002023053146697032
    # 1320115002023053102838404
    # 1320104002023053105570321
    # 1320116002023053190644252
    # 51320100002023053100001388
    # 1320115002023053197415621
    # 1320114002023053185470718
    # 1320115002023053108994504
    if not json_data:
        json_data = '''[
            {"registration_id": "1320102002023053117424237"},
            {"registration_id": "1320115002023053149333782"},
            {"registration_id": "1320104002023053183871403"},
            {"registration_id": "51320100002023053100001560"},
            {"registration_id": "1320119002023053105746496"},
            {"registration_id": "1320114002023053175304090"},
            {"registration_id": "1320104002023053163269173"},
            {"registration_id": "1320119002023053146697032"},
            {"registration_id": "1320115002023053102838404"},
            {"registration_id": "1320104002023053105570321"},
            {"registration_id": "1320116002023053190644252"},
            {"registration_id": "51320100002023053100001388"},
            {"registration_id": "1320115002023053197415621"},
            {"registration_id": "1320114002023053185470718"},
            {"registration_id": "1320115002023053108994504"}
        ]'''
    
    # 实际的MCP URL（参考simple_test.py）
    mcp_url = "http://lb-2ohhzn5i-egfmcuxsj56dh1op.clb.cq-tencentclb.com:31235/sse?key=lCPbVK4QrHVkh11mFeTDsjHaR9z%2BqyIlCeYjL%2F762CzKsFEo%2FnHK8eCG7KAH4srOe2KgaxZkqsJ8zrqv31j3Mw%3D%3D"
    
    # 调用工具 - 使用 ainvoke 方法进行异步调用
    result = await load_data_and_join_table.ainvoke({
        "json_data": json_data,
        "table_a_name": "feedback.feedback",  # 使用实际存在的表名
        "join_condition": "temp_table.registration_id = feedback.registration_id",  # 修正JOIN条件
        "mcp_url": mcp_url,
        "database_name": "feedback",  # 使用实际的数据库名 
        "catalog_name": "internal"
    })
    
    print(f"操作结果: {result}")
    return result


async def test_connection(mcp_url: str):
    """
    测试MCP连接和可用工具（参考simple_test.py）
    """
    try:
        print(f"🚀 测试连接到: {mcp_url}")
        
        # 先测试基本的查询
        sample_result = await mcp_call_tool(
            url=mcp_url,
            tool_name="TCHouseDGetTableSample",
            arguments={
                "Catalog": "internal",
                "Database": "feedback", 
                "Table": "feedback",
                "Limit": 3
            }
        )
        
        print(f"✅ TCHouseDGetTableSample 测试成功: {sample_result}")
        
        # 测试查询
        query_result = await mcp_call_tool(
            url=mcp_url,
            tool_name="TCHouseDExecuteQuery",
            arguments={"Query": "SELECT * FROM feedback.feedback LIMIT 3;"}
        )
        
        print(f"✅ TCHouseDExecuteQuery 测试成功: {query_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False


if __name__ == "__main__":
    import asyncio
    
    # 实际的MCP URL
    mcp_url = "http://lb-2ohhzn5i-egfmcuxsj56dh1op.clb.cq-tencentclb.com:31235/sse?key=lCPbVK4QrHVkh11mFeTDsjHaR9z%2BqyIlCeYjL%2F762CzKsFEo%2FnHK8eCG7KAH4srOe2KgaxZkqsJ8zrqv31j3Mw%3D%3D"
    
    async def main():
        print("🔧 开始测试 load_data_and_join_table 工具")
        
        # 先测试连接
        if await test_connection(mcp_url):
            print("\n✅ 连接测试通过，开始运行示例...")
            input_str = """
                {
                  "aisearch": [
                    {
                      "content": "| 1320105002023052226906708 | 举报 | 2023-05-22 09:13:56 | 微信小程序 | 好的(学子路店) |  |  |  | 江苏省南京市建邺区吴侯街158号14-6室 | 现场 | 其他食品-香肠 | 商品（产品）->食品->普通食品->其他食品 | 热狗肠 |  |  |  | 标注虚假生产日期、保质期或者超过保质期的食品、食品添加剂 | 举报问题类别->食品安全违法行为->食品销售环节->销售贮存法律法规禁止销售的食品（食品添加剂）：标注虚假生产日期、保质期或者超过保质期的食品、食品添加剂 |  |  | 诉求：1、依法受理并立案查处，依法将处理结果书面告知投诉举报人；2、责令被投诉举报人依法赔付消费者：3、依法奖励举报人。 事实和理由，本人于2023年5月10日在被投诉举报人处购买了一些食品，后来发现香肠的生产日期是2023年1月2日，保质期是90天 | 消费者 | 建邺区市场监督管理局 | 江苏省市场监督管理局->南京市市场监督管理局->建邺区市场监督管理局 | 业务部门 | 业务人员 | 双闸分局 | 江苏省市场监督管理局->南京市市场监督管理局->建邺区市场监督管理局->双闸分局 | 业务部门 | 2023-07-04 |  | 2023-07-03 11:36:28 | 2023-07-03 11:36:28 | 不立案 | 已办结 | 经查，举报事项不予立案，理由：2023年7月3日，我局工作人员企信通举报人，告知举报人接到您举报后，我局工作人员进行核查，由于你此前反映的相关内容无法进一步提供证据、无法继续配合本局调查，目前认定被举报人违法行为的证据不足。如你(单位)不服本决定，可在收到本告知之日起六十日内依法向南京市建邺区人民政府申请行政复议；也可以在六个月内依法向南京江北新区人民法院提起行政诉讼。 | 全国12315互联网平台 |  |  |",
                      "file_id": "2fdb7c87-bcc8-4678-9af1-a78bdfbd7a84",
                      "knowledge_base_id": "klbase-K7cWPFjg03",
                      "chunk_id": "74f16f71-7eb9-4607-b0e2-05ef33621722",
                      "score": 0.41948128,
                      "rerank_score": 0.8359375,
                      "file_name": "N/A",
                      "file_url": "N/A",
                      "knowledge_base_name": "default"
                    },
                    {
                      "content": "| 1320114002023050976625564 | 投诉 | 2023-05-09 19:36:15 | 微信小程序 | 三江学院一号食堂 |  |  |  | 江苏省南京市雨花台区龙西路10号三江学院主校区 | 现场 | 米面及其制品（自制） | 商品（产品）->食品->普通食品->餐饮食品->米面及其制品（自制） | 玉米 |  | 2023-05-09 00:00:00 | 退赔费用,赔偿损失 | 食品安全 | 投诉问题类别->食品安全 |  | 江苏省南京市雨花台区江苏省南京市雨花台区三江学院一食堂早饭窗口 | 5月9日晚上18:10在三江学院一食堂早餐窗口买了一个茶叶蛋和一根玉米 共计5.5元 玉米发现有黑点 拔开看有一只白色的虫子在蠕动。 | 消费者 | 雨花台区市场监督管理局 | 江苏省市场监督管理局->南京市市场监督管理局->雨花台区市场监督管理局 | 业务部门 | 业务人员 | 铁心桥分局 | 江苏省市场监督管理局->南京市市场监督管理局->雨花台区市场监督管理局->铁心桥分局 | 铁心桥分局12315投诉站 | 2023-05-18 | 2023-07-17 | 2023-05-12 10:15:29 | 2023-05-12 10:16:22 | 已受理 | 已办结 | 5-11 15:05，诉求人表示问题已解决，自行撤诉。 | 全国12315互联网平台 |  |  |",
                      "file_id": "2fdb7c87-bcc8-4678-9af1-a78bdfbd7a84",
                      "knowledge_base_id": "klbase-K7cWPFjg03",
                      "chunk_id": "b2d29fdd-9e0b-4256-9504-45dd42b7335a",
                      "score": 0.4202037,
                      "rerank_score": 0.787109375,
                      "file_name": "N/A",
                      "file_url": "N/A",
                      "knowledge_base_name": "default"
                    },
                    {
                      "content": "| 1320115002023052420560748 | 投诉 | 2023-05-24 08:30:42 | 微信小程序 | 南京晓庄学院(方山校区)崇1楼 |  |  |  | 江苏省南京市江宁区弘景大道3601号南京晓庄学院方山校区内 | 现场 | 米面及其制品（自制） | 商品（产品）->食品->普通食品->餐饮食品->米面及其制品（自制） | 北食堂一楼烧麦 |  | 2023-05-24 00:00:00 | 赔偿损失 | 食品安全 | 投诉问题类别->食品安全 |  | 江苏省南京市江宁区江苏省南京市江宁区弘景大道3601号南京晓庄学院北食堂一楼 | 早上去南京晓庄学院北食堂一楼买烧麦吃，第一口吃出两根钢丝 | 消费者 | 江宁区市场监督管理局 | 江苏省市场监督管理局->南京市市场监督管理局->江宁区市场监督管理局 | 业务部门 | 范骁惠 | 高新园分局 | 江苏省市场监督管理局->南京市市场监督管理局->江宁区市场监督管理局->高新园分局 | 业务部门 | 2023-06-02 | 2023-08-03 | 2023-05-31 15:08:42 | 2023-08-01 16:20:50 | 已受理 | 已办结 | 投诉人已撤诉 | 全国12315互联网平台 |  |  |",
                      "file_id": "2fdb7c87-bcc8-4678-9af1-a78bdfbd7a84",
                      "knowledge_base_id": "klbase-K7cWPFjg03",
                      "chunk_id": "bfa0b276-a009-4270-b098-ccef998f0fb0",
                      "score": 0.42015123,
                      "rerank_score": 0.7744140625,
                      "file_name": "N/A",
                      "file_url": "N/A",
                      "knowledge_base_name": "default"
                    },
                    {
                      "content": "| 1320104002023051164247604 | 投诉 | 2023-05-11 19:59:49 | App | 南京馋猫电子商务有限公司 | 91320104MABREH8W7X | 320104000778955 |  | 南京市秦淮区五福里1号372室 | 网购 | 速冻其他食品 | 商品（产品）->食品->普通食品->速冻食品->速冻其他食品 | 蒸来思  雪花香芋包 | 1878207096935929374 | 2023-05-11 00:00:00 | 赔偿损失,退赔费用,退货,停止侵权、核定侵权责任 | 食品安全 | 投诉问题类别->食品安全 |  |  | 通过淘宝在被投诉举报人处购买到一款食品，收到货食用时发现该其食品的能量、蛋白质、脂肪等营养成分参考值均为虚假标注，使得消费者无从判断其真实营养价值，足以误导消费者，被投诉举报人存在销售不合格食品、侵犯消费者权益的行为，应当依法赔偿。（此投诉件同时包括举报） | 消费者 | 秦淮区市场监督管理局 | 江苏省市场监督管理局->南京市市场监督管理局->秦淮区市场监督管理局 | 业务部门 | 阮泽军 | 双塘分局 | 江苏省市场监督管理局->南京市市场监督管理局->秦淮区市场监督管理局->双塘分局 | 业务部门 | 2023-05-22 | 2023-07-25 | 2023-05-22 10:46:21 | 2023-05-24 14:55:18 | 已受理 | 已办结 | 双方已协调解决，诉求人已撤案 | 全国12315互联网平台 |  | 淘宝网 |",
                      "file_id": "2fdb7c87-bcc8-4678-9af1-a78bdfbd7a84",
                      "knowledge_base_id": "klbase-K7cWPFjg03",
                      "chunk_id": "d5e5fb02-34bb-4589-b679-2116734519d5",
                      "score": 0.41989255,
                      "rerank_score": 0.18701171875,
                      "file_name": "N/A",
                      "file_url": "N/A",
                      "knowledge_base_name": "default"
                    },
                    {
                      "content": "的标签、说明书存在瑕疵但不影响食品安全且不会对消费者造成误导的，由县级以上人民政府食品安全监督管理部门责令改正。我局决定不予立案。如你不服举报处理结果，可以在收到本告知之日起六十日内向南京市人民政府申请行政复议；也可以在六个月内依法向江北新区人民法院提起行政诉讼。  | 全国12315互联网平台 |  |  |",
                      "file_id": "2fdb7c87-bcc8-4678-9af1-a78bdfbd7a84",
                      "knowledge_base_id": "klbase-K7cWPFjg03",
                      "chunk_id": "39c4cb80-4460-4c9c-8a58-09f88b4bb1a1",
                      "score": 0.42019987,
                      "rerank_score": 0.180908203125,
                      "file_name": "N/A",
                      "file_url": "N/A",
                      "knowledge_base_name": "default"
                    }
                  ]
                }
            """
            res = get_id_from_chunk(input_str)
            # 运行完整示例
            print(res)
            await example_usage(res)
        else:
            print("\n❌ 连接测试失败，请检查MCP URL和服务状态")
    
    # 运行测试（需要配置正确的MCP URL和数据库信息）
    asyncio.run(main()) 