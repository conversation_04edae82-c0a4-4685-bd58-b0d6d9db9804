# datascience_agent/agents/executor/executor.py
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional, TYPE_CHECKING, AsyncGenerator
import logging
import uuid

from .flexible_react_agent import FlexibleReactAgent

if TYPE_CHECKING:
    from infra.datascience_agent.utils.openai_client import OpenAIClient
    from infra.mcp.manager.mcp_manager import MCPManager
    from infra.datascience_agent.state import AgentState

logger = logging.getLogger(__name__)


class ExecutorAgent:
    """增强的执行器代理，基于FlexibleReactAgent实现"""
    
    def __init__(self, llm_client: Optional['OpenAIClient'] = None, mcp_manager: Optional['MCPManager'] = None, writer=None):
        """
        初始化ExecutorAgent
        Args:
            llm_client: LLM客户端实例
            mcp_manager: MCP管理器实例  
            writer: 事件写入器
        """
        self.llm_client = llm_client
        self.mcp_manager = mcp_manager
        self.writer = writer
        self.react_agent = None
        
        if llm_client and mcp_manager:
            self.react_agent = FlexibleReactAgent(llm_client, mcp_manager, writer)
        
        logger.info("Enhanced ExecutorAgent initialized with FlexibleReactAgent.")

    async def initialize(self):
        """
        异步初始化ExecutorAgent和底层的FlexibleReactAgent
        """
        if self.react_agent:
            await self.react_agent.initialize()
            logger.info("ExecutorAgent and FlexibleReactAgent initialization completed.")
        else:
            logger.warning("Cannot initialize: FlexibleReactAgent not created due to missing dependencies.")

    async def execute_subtasks(self, structured_plan: Dict[str, Any], agent_state: 'AgentState' = None) -> AsyncGenerator[Dict[str, Any], None]:
        """
        执行结构化计划中的所有子任务
        Args:
            structured_plan: 包含子任务的结构化计划
            agent_state: 完整的agent状态，包含current_user_input等信息
        Returns:
            AsyncGenerator: 事件流
        """
        if not self.react_agent:
            logger.error("FlexibleReactAgent not initialized")
            yield {
                "type": "final_result",
                "data": {
                    "result": "FlexibleReactAgent not initialized",
                    "status": "error"
                }
            }
            return

        subtasks = structured_plan.get('subtasks', [])
        if not subtasks:
            logger.warning("No subtasks found in structured plan")
            yield {
                "type": "final_result",
                "data": {
                    "result": "No subtasks to execute",
                    "status": "completed"
                }
            }
            return

        logger.info(f"Executing {len(subtasks)} subtasks")
        
        # 🎯 设置整体任务名称 - 这是关键修复
        overall_task_name = structured_plan.get('task_name', '数据分析任务')
        self.react_agent.overall_task_name = overall_task_name
        logger.info(f"Set overall task name: {overall_task_name}")
        
        # 初始化所有子任务状态
        self.react_agent.initialize_subtasks(subtasks)
        
        # 使用传入的完整agent_state，如果没有则创建空字典作为备用
        state_to_pass = agent_state if agent_state is not None else {}
        logger.info(f"Passing agent_state to FlexibleReactAgent: has_current_user_input={bool(state_to_pass.get('current_user_input'))}")
        
        # 🌐 添加语言信息的调试日志
        detected_language = state_to_pass.get('detected_language')
        if detected_language:
            logger.info(f"🌐 ExecutorAgent: 传递语言信息到FlexibleReactAgent: {detected_language}")
        else:
            logger.info("🌐 ExecutorAgent: 未检测到语言信息，将使用默认设置")

        # set system role
        system_prompt = self.react_agent.create_enhanced_system_prompt(detected_language, state_to_pass)
        system_message = {"role": "system", "content": system_prompt}
        self.react_agent.conversation_history.append(system_message)

        # add mcp env context
        context_text = "当前数据源相关的变量值如下：\n"
        env_params = self.react_agent.get_env_params_from_mcp_manager()
        for key, value in env_params.items():
            if key == "DatabaseName":
                key = "当前数据库名称（DatabaseName）"
            elif key == "TableNames":
                key = "当前数据表列表（TableNames）"
            context_text += f"{key}: {str(value)}\n"
        
        if context_text:
            datasource_context = {"role": "system", "content": context_text}
            self.react_agent.conversation_history.append(datasource_context)
        
        for subtask in subtasks:
            # 🛑 在执行每个子任务前检查停止请求
            if state_to_pass.get('stop_requested'):
                logger.info(f"🛑 检测到停止请求，不再执行剩余子任务。停止位置：子任务 {subtask.get('idx')}")
                yield {
                    "type": "final_result", 
                    "data": {
                        "result": "后续任务已停止执行，因为前面的子任务失败",
                        "status": "stopped",
                        "stopped_at_subtask": subtask.get('idx')
                    }
                }
                break
                
            logger.info(f"Processing subtask {subtask.get('idx')}: {subtask.get('desc')}")

            try:
                async for event in self.react_agent.process_subtask(subtask, state_to_pass):
                    yield event
            except RuntimeError as e:
                # 捕获工具初始化失败等关键错误
                logger.error(f"❌ Critical error in subtask {subtask.get('idx')}: {e}")
                
                # 发送错误信息
                yield {
                    "type": "error_event",
                    "data": {
                        "error": str(e),
                        "subtask_idx": subtask.get('idx'),
                        "subtask_description": subtask.get('desc'),
                        "error_type": "critical_error"
                    }
                }
                
                # 终止执行
                return
        

