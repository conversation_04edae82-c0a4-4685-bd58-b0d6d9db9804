# datascience_agent/mock_streamer.py
import asyncio
from typing import AsyncIterator, List, Dict, Any, Optional
import uuid
# 确保从您的项目中正确导入 AgentEvent 及其子类
# 例如: from common.elements.agent_event import (
#    AgentEvent, MessageEvent, ThinkEvent, TaskListEvent, 
#    StatusEvent, ErrorEvent, FinalSummaryEvent, JupyterEvent
# )
# 如果不在 common.elements, 请修改为实际路径
# --- 开始: 导入 AgentEvent (请根据您的项目结构修改路径) ---
import json
import traceback

from common.elements.agent_event import (
    AgentEvent, MessageEvent, ThinkEvent, TaskListEvent, 
    ErrorEvent, FinalSummaryEvent, JupyterEvent,
    RecordEvent
)
from common.share.config import appConfig
from infra.mcp.manager.mcp_manager import MCPManager
from common.logger.logger import logger
from common.share.stream_param import StreamGenerationParams
from common.share.context import Context

class TaskManager:
    """Manages task and step states with improved functionality."""
    
    def __init__(self):
        self.tasks: Dict[int, Dict[str, Any]] = {}
        self.current_task_id = 0
        self.task_step_counters: Dict[int, int] = {}  # Track step counters per task
        
    def create_task(self, name: str, description: str = "") -> int:
        """Create a new task and return its ID."""
        self.current_task_id += 1
        task_id = self.current_task_id
        
        self.tasks[task_id] = {
            "id": task_id,
            "name": name,
            "description": description,
            "status": "pending",
            "steps": [],
            "created_at": asyncio.get_event_loop().time()
        }
        self.task_step_counters[task_id] = 0  # Initialize step counter for this task
        return task_id
    
    def add_step(self, task_id: int, name: str, s_type: str = "expand", description: str = "", tool: str = "", cell_ids: Optional[List[str]] = None, summary: str = None) -> int:
        """Add a new step to a task and return its ID."""
        if task_id not in self.tasks:
            raise ValueError(f"Task {task_id} not found")
            
        self.task_step_counters[task_id] += 1
        step_id = self.task_step_counters[task_id]
        
        step = {
            "id": step_id,
            "name": name,
            "desc": description,
            "tool": tool,
            "status": "pending",
            "type": s_type,
            "expand": {
                "title": name,
                "status": "pending",
                "cell_ids": cell_ids or []
            }
        }
        if summary:
            step["summary"] = summary
        if s_type == "text":
            del step["expand"]
            step["status"] = "finish"

        self.tasks[task_id]["steps"].append(step)
        if self.tasks[task_id]["status"] == "pending":
            self.tasks[task_id]["status"] = "running"
        return step_id
    
    def update_step_status(self, task_id: int, step_id: int, status: str, summary: Optional[str] = None):
        """Update a step's status and optional summary."""
        if task_id not in self.tasks:
            raise ValueError(f"Task {task_id} not found")
            
        task = self.tasks[task_id]
        step = next((s for s in task["steps"] if s["id"] == step_id), None)
        if not step:
            raise ValueError(f"Step {step_id} not found in task {task_id}")
            
        step["status"] = status
        if summary:
            step["summary"] = summary
        elif "summary" in step:
            del step["summary"]
        if "expand" in step:
            step["expand"]["status"] = status
        
        # Update task status based on steps
        self._update_task_status(task_id)
    
    def finish_step(self, task_id: int, step_id: int, summary: str = "已完成"):
        """Mark a step as finished with a summary."""
        self.update_step_status(task_id, step_id, "finish", summary)
    
    def _update_task_status(self, task_id: int):
        """Update task status based on its steps' statuses."""
        task = self.tasks[task_id]
        steps = task["steps"]
        
        if any(s["status"] == "error" for s in steps):
            task["status"] = "error"
        elif any(s["status"] == "running" for s in steps):
            task["status"] = "running"
    
    def finish_task(self, task_id: int):
        """Mark a task as finished."""
        if task_id not in self.tasks:
            raise ValueError(f"Task {task_id} not found")
        self.tasks[task_id]["status"] = "finish"
    
    def get_task_list_event(self) -> TaskListEvent:
        """Generate a TaskListEvent from current task state."""
        task_list = []
        for task in self.tasks.values():
            task_list.append({
                "id": task["id"],
                "name": task["name"],
                "status": task["status"],
                "step_info_list": task["steps"]
            })
        
        return TaskListEvent(content={"task_list": task_list})

class MockAgentStreamer:
    """
    A class for generating mock Agent event streams for integration testing.
    """
    def __init__(self, ctx: Context, record_id: str = "None"):
        self.session_id = ctx.session_id
        self.record_id = record_id
        self.user_id = "user3"
        mcp_config = appConfig.automic.mcp.example.get("data",{})
        self.mcp_config = mcp_config

        params = StreamGenerationParams(
            ctx=ctx,
            mcp_instance=mcp_config.get('MCP',{}),
            eg_instance=mcp_config.get('EG',{}),
            db_table=mcp_config.get('DB_TABLE',[]),
            record_id=self.record_id,
        )
        self.mcp_manager = MCPManager(params)
        self.task_manager = TaskManager()
                
        self.mock_sql_5 = "SELECT * FROM `data_agent`.`coffee_sales` LIMIT 5"
        self.mock_sql_15 = "SELECT * FROM `data_agent`.`coffee_sales` LIMIT 15"
        self.mock_forecast_code = """import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import matplotlib 
matplotlib.rc('font', family='serif')
# 基于历史销售数据生成未来预测
historical_sales = [1200000, 1350000, 1530000]  # 2021, 2022, 2023年华东区销售额
years = [2021, 2022, 2023]

# 简单线性预测
future_years = [2024, 2025, 2026]
trend = np.polyfit(years, historical_sales, 1)
future_sales = np.polyval(trend, future_years)

# 绘制折线图
plt.figure(figsize=(10, 6))
plt.plot(years, historical_sales, 'o-', label='Historical Sales', linewidth=2, markersize=8)
plt.plot(future_years, future_sales, 's--', label='Future Sales', linewidth=2, markersize=8, alpha=0.7)

plt.xlabel('Year')
plt.ylabel('Sales (RMB)')
plt.title('East China Sales Trend and Future Prediction')
plt.legend()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()
"""
        self.final_answer = "2023年销售额最高的地区是华东区，总销售额为1530000.0。"
        self._record_event_task = None

    async def _execute_jupyter_event(self, queue: asyncio.Queue, event: JupyterEvent, task_id: int, tool_name: str, arguments: dict) -> JupyterEvent:
        """Execute a JupyterEvent and update its output.
        
        Args:
            queue: The event queue
            event: The JupyterEvent to execute
            task_id: The current task ID
            tool_name: The tool name to execute
            arguments: The arguments for the tool
            
        Returns:
            The updated JupyterEvent with execution results
        """
        # Send running event
        event.set_ctx_info(self.session_id, self.record_id, task_id)
        await queue.put(event)
        await asyncio.sleep(1.0)

        # Execute the tool
        rst = await self.mcp_manager.call_tool(tool_name, arguments=arguments)
        
        # Process the result
        if tool_name == "dlc__DLCExecuteQuery":
            response = json.loads(rst.content[0].text)["Response"]
            taskInfo = response.get("TaskInfo",{})
            result_set = json.loads(taskInfo.get("ResultSet","[]"))
            result_schema = taskInfo.get("ResultSchema",[])
            result_schema = [item["Name"] for item in result_schema]
            
            logger.info(f"Execution sql result: {result_set}")
            logger.info(f"Execution sql result: {result_schema}")
            
            result_all = [result_schema]
            result_all.extend(result_set)
            result_all = [{"output_type": "csv", "csv_data": result_all}]
        else:
            output = json.loads(rst.content[0].text)["outputs"]
            error = json.loads(rst.content[0].text)["error"]
            status = "error" if error else "success"
            result_all = output

        # Create success event
        success_event = JupyterEvent(
            cell_type=event.cell_type,
            source=event.source,
            status=status if tool_name != "dlc__DLCExecuteQuery" else "success",
            cell_id=event.id,
            execution_count=event.execution_count,
            outputs=result_all
        )
        success_event.set_ctx_info(self.session_id, self.record_id, task_id)
        await queue.put(success_event)
        
        return success_event

    async def _send_record_events(self, queue: asyncio.Queue):
        """Send record events every 30 seconds."""
        while True:
            await asyncio.sleep(30)
            record_event = RecordEvent(content=self.record_id)
            await queue.put(record_event)

    async def _generate_mock_stream(self, queue: asyncio.Queue) -> AsyncIterator[Any]:
        try:
            """Generate mock event stream asynchronously."""
            # Initial task list
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.5)

            # Task 1: Text Description
            task1_id = self.task_manager.create_task("数据预分析")
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.2)

            # Step 1: Add text description
            step1_id = self.task_manager.add_step(
                task1_id, 
                "制定分析计划", 
                description="思考任务细节：首先分析销售数据，然后生成并执行分析计划。",
                s_type="text"
            )
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.2)
            
            # Step 2: Execute SQL
            cell_id_thought = str(uuid.uuid4())
            cell_id_sql = str(uuid.uuid4())
            step2_id = self.task_manager.add_step(
                task1_id, 
                "分析销售数据", 
                tool="jupyter",
                cell_ids=[cell_id_thought, cell_id_sql]
            )
            self.task_manager.update_step_status(task1_id, step2_id, "running")
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.2)

            event = JupyterEvent(
                cell_type="markdown", 
                source=["我需要分析销售数据来验证分析结果， 我将使用sql语句来分析销售数据。"],
                status="success", 
                cell_id=cell_id_thought
            )
            event.set_ctx_info(self.session_id, self.record_id, task1_id)
            await queue.put(event)
            await asyncio.sleep(0.3)

            sql_event = JupyterEvent(
                cell_type="sql", 
                source=[self.mock_sql_5],
                status="running", 
                cell_id=cell_id_sql, 
                execution_count=2
            )
            await self._execute_jupyter_event(
                queue,
                sql_event,
                task1_id,
                "dlc__DLCExecuteQuery",
                {
                    "SparkSQL": self.mock_sql_5,
                    "DatabaseName": "data_agent",
                    "DatasourceConnectionName": self.mcp_config.get('MCP',{}).get('DatasourceConnectionName', "unkown"),
                    "DataEngineName": self.mcp_config.get('MCP',{}).get('DataEngineName', "unkown")
                }
            )
            
            self.task_manager.finish_step(task1_id, step2_id, "完成5条销售数据采样")
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.2)
            
            cell_id = str(uuid.uuid4())
            step3_id = self.task_manager.add_step(
                task1_id, 
                "展示分析结果", 
                description="展示分析结果",
                s_type="expand",
                cell_ids=[cell_id],
                summary="等待中..."
            )
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.2)

            # Create markdown event for text description
            event = JupyterEvent(
                cell_type="markdown", 
                source=["**分析结果：**\n\n2023年的销售数据显示，华东地区的销售额最高，达到了153万元。这主要得益于该地区完善的销售网络和较高的客户购买力。\n\n主要发现：\n1. 华东地区销售额领先\n2. 同比增长13.3%\n3. 客户满意度高"],
                status="success", 
                cell_id=cell_id
            )
            event.set_ctx_info(self.session_id, self.record_id, task1_id)
            await queue.put(event)
            await asyncio.sleep(0.3)

            self.task_manager.finish_step(task1_id, step3_id, summary=None)
            self.task_manager.finish_task(task1_id)
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.8)

            # Task 2: Pandas Analysis
            task2_id = self.task_manager.create_task("按地区统计销售额")
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.2)

            # Step 1: Query sample data
            cell_id = str(uuid.uuid4())
            step1_id = self.task_manager.add_step(
                task2_id, 
                "各地区采样数据展示", 
                tool="jupyter",
                cell_ids=[cell_id]
            )
            self.task_manager.update_step_status(task2_id, step1_id, "running")
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.2)

            # Create pandas analysis code
            pandas_code = """import pandas as pd
import numpy as np

# 创建示例数据
data = {
    'region': ['华东', '华南', '华北', '西南', '西北'],
    'sales_2023': [1530000, 1280000, 980000, 750000, 620000],
    'sales_2022': [1350000, 1150000, 890000, 680000, 580000]
}

df = pd.DataFrame(data)

# 计算同比增长率
df['growth_rate'] = (df['sales_2023'] - df['sales_2022']) / df['sales_2022'] * 100

# 格式化输出
df['sales_2023'] = df['sales_2023'].apply(lambda x: f'{x:,.0f}')
df['sales_2022'] = df['sales_2022'].apply(lambda x: f'{x:,.0f}')
df['growth_rate'] = df['growth_rate'].apply(lambda x: f'{x:.1f}%')

# 显示结果
print(df)
print(50*"=")
df.head()
"""
            pandas_event = JupyterEvent(
                cell_type="code", 
                source=[pandas_code],
                status="running", 
                cell_id=cell_id, 
                execution_count=1
            )
            await self._execute_jupyter_event(
                queue,
                pandas_event,
                task2_id,
                "jupyter__execute_code",
                {"code": pandas_code}
            )
            
            self.task_manager.finish_step(task2_id, step1_id, "提取采样数据成功")
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(1.0)

            # Step 2: Generate SQL
            cell_id_thought = str(uuid.uuid4())
            cell_id_code = str(uuid.uuid4())
            step2_id = self.task_manager.add_step(
                task2_id, 
                "生成销售额查询SQL", 
                tool="generate_sql",
                cell_ids=[cell_id_thought, cell_id_code]
            )
            self.task_manager.update_step_status(task2_id, step2_id, "running")
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.2)
            
            # Create markdown event
            event = JupyterEvent(
                cell_type="markdown", 
                source=["我需要通过一个NL2SQL工具来生成销售额查询SQL。", "因为SQL语句已经生成，这里我先为您展示一个代码执行示例，该示例构造一个错误的执行语句，通过 DLC Kernel 执行代码，并展示该语句的执行结果。"],
                status="success", 
                cell_id=cell_id_thought
            )
            event.set_ctx_info(self.session_id, self.record_id, task2_id)
            await queue.put(event)
            await asyncio.sleep(0.3)

            # Execute code
            code = "print('Hello, World!');a"
            code_event = JupyterEvent(
                cell_type="code", 
                source=[code],
                status="running", 
                cell_id=cell_id_code
            )
            await self._execute_jupyter_event(
                queue,
                code_event,
                task2_id,
                "jupyter__execute_code",
                {"code": code}
            )
            
            self.task_manager.finish_step(task2_id, step2_id, summary=None)
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.8)

            # Step 3: Execute SQL
            cell_id_thought = str(uuid.uuid4())
            cell_id_sql = str(uuid.uuid4())
            step3_id = self.task_manager.add_step(
                task2_id, 
                "执行SQL", 
                tool="jupyter",
                cell_ids=[cell_id_thought, cell_id_sql]
            )
            self.task_manager.update_step_status(task2_id, step3_id, "running")
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.2)

            event = JupyterEvent(
                cell_type="markdown", 
                source=["现在我需要使用 DLC MCP 执行生成的 SQL。"],
                status="success", 
                cell_id=cell_id_thought
            )
            event.set_ctx_info(self.session_id, self.record_id, task2_id)
            await queue.put(event)
            await asyncio.sleep(0.3)

            sql_event = JupyterEvent(
                cell_type="sql", 
                source=[self.mock_sql_15],
                status="running", 
                cell_id=cell_id_sql, 
                execution_count=3
            )
            await self._execute_jupyter_event(
                queue,
                sql_event,
                task2_id,
                "dlc__DLCExecuteQuery",
                {
                    "SparkSQL": self.mock_sql_15,
                    "DatabaseName": "data_agent",
                    "DatasourceConnectionName": self.mcp_config.get('MCP',{}).get('DatasourceConnectionName', "unkown"),
                    "DataEngineName": self.mcp_config.get('MCP',{}).get('DataEngineName', "unkown")
                }
            )
            
            self.task_manager.finish_step(task2_id, step3_id)
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.2)

            # Step 4: Generate forecast plot
            cell_id_thought = str(uuid.uuid4())
            cell_id_plot = str(uuid.uuid4())
            step4_id = self.task_manager.add_step(
                task2_id, 
                "展示销量统计结果", 
                tool="jupyter",
                cell_ids=[cell_id_thought, cell_id_plot]
            )
            self.task_manager.update_step_status(task2_id, step4_id, "running")
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(0.2)

            event = JupyterEvent(
                cell_type="markdown", 
                source=["现在我需要使用 Jupyter 展示销量统计结果。"],
                status="success", 
                cell_id=cell_id_thought
            )
            event.set_ctx_info(self.session_id, self.record_id, task2_id)
            await queue.put(event)
            await asyncio.sleep(0.3)

            plot_event = JupyterEvent(
                cell_type="code", 
                source=[self.mock_forecast_code],
                status="running", 
                cell_id=cell_id_plot, 
                execution_count=4,
                outputs=[]
            )
            await self._execute_jupyter_event(
                queue,
                plot_event,
                task2_id,
                "jupyter__execute_code",
                {"code": self.mock_forecast_code}
            )
            
            self.task_manager.finish_step(task2_id, step4_id, "销量数据分析完毕")
            self.task_manager.finish_task(task2_id)
            await queue.put(self.task_manager.get_task_list_event())
            await asyncio.sleep(1.0)

            # Final summary
            await queue.put(FinalSummaryEvent(content=self.final_answer, cell_id=cell_id_plot))
            await asyncio.sleep(0.1)
        except Exception as e:
            logger.error(f"Error in generate_mock_stream: {str(e)}", exc_info=True)
            raise

    async def generate_mock_stream(self) -> AsyncIterator[Any]:
        """Generate mock event stream asynchronously with record events."""
        try:
            queue = asyncio.Queue()
            record_task = asyncio.create_task(self._send_record_events(queue))
            mock_stream_task = asyncio.create_task(self._generate_mock_stream(queue))
            
            try:
                while True:
                    if not queue.empty():
                        event = await queue.get()
                        yield event
                    elif mock_stream_task.done():
                        logger.info(f"Mock stream task done: {self.record_id}")
                        break
                    await asyncio.sleep(0.1)
            finally:
                # Cancel both tasks
                record_task.cancel()
                mock_stream_task.cancel()
                try:
                    logger.info(f"Mock stream task finish: {self.record_id}")
                    await record_task
                    await mock_stream_task
                except asyncio.CancelledError:
                    pass
        except Exception as e:
            logger.error(f"Error in generate_mock_stream: {str(e)}", exc_info=True)
            raise