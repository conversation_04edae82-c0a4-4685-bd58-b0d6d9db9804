# datascience_agent/agents/planner/planner.py
import json
import logging
import re
import asyncio
from typing import List, Dict, Any, Optional

from .planner_schemas import Plan, SubTask
from .planner_prompts import (
    PLANNER_SYSTEM_PROMPT_TEMPLATE,
    EMPTY_RAG_EXAMPLES,
    EMPTY_FEEDBACK,
    PLANNER_SYSTEM_PROMPT_TEMPLATE_COMPLAINT,
    COMPLAINT_OR_REPORT_EXAMPLES
)
from common.logger.logger import logger


def _format_database_schema_for_prompt(database_schema: Dict[str, Any]) -> str:
    """格式化数据库schema为提示词使用的字符串"""
    if not database_schema:
        return "无可用数据集信息"
    
    return json.dumps(database_schema, indent=2, ensure_ascii=False)


def _strip_markdown_code_block(raw_text: str) -> str:
    """去除markdown代码块标记"""
    match = re.search(r"```(?:json)?\s*\n(.*?)\n\s*```", raw_text, re.DOTALL)
    if match:
        return match.group(1).strip()
    return raw_text.strip()


async def create_plan(
    llm_client: Any,
    intent_name: str,
    intent_entities: Dict[str, Any],
    user_query: str,
    database_schema: Optional[Dict[str, Any]] = None,
    rag_examples: Optional[List[Dict[str, Any]]] = None,
    feedback_on_failed_plan: Optional[Dict[str, Any]] = None,
    user_language: str = 'zh',
    ctx: Optional[Any] = None,
    think_event_callback: Optional[Any] = None  # 保留参数以兼容，但不使用
) -> Plan:
    """
    创建执行计划（简化版本，只支持无思考模式）
    
    Args:
        llm_client: LLM客户端
        intent_name: 识别的意图名称
        intent_entities: 提取的实体参数
        user_query: 用户原始查询
        database_schema: 数据库模式
        rag_examples: RAG示例
        feedback_on_failed_plan: 失败反馈
        user_language: 用户语言（'zh' 或 'en'）
        ctx: 上下文对象（保留兼容）
        think_event_callback: 思考事件回调（保留兼容，但不使用）
    
    Returns:
        Plan: 验证后的执行计划
    """
    logger.info(f"PLANNER: Creating plan for intent '{intent_name}' (language={user_language})")
    if database_schema:
        logger.info(f"PLANNER: Active database schema: {database_schema}")

    is_complaint_or_report = (intent_name == "complaint_or_report")
    default_rag_examples = COMPLAINT_OR_REPORT_EXAMPLES if is_complaint_or_report else EMPTY_RAG_EXAMPLES
    
    # 准备提示词参数
    rag_examples_str = json.dumps(rag_examples, indent=2, ensure_ascii=False) if rag_examples else default_rag_examples
    feedback_str = json.dumps(feedback_on_failed_plan, indent=2, ensure_ascii=False) if feedback_on_failed_plan else EMPTY_FEEDBACK
    
    # 处理database_schema，转换为可读的字符串格式
    if database_schema and isinstance(database_schema, dict):
        dataset_schema_str = _format_database_schema_for_prompt(database_schema)
    else:
        dataset_schema_str = "N/A"
    
    # 使用系统模式提示词模板
    prompt_template = PLANNER_SYSTEM_PROMPT_TEMPLATE_COMPLAINT if is_complaint_or_report else PLANNER_SYSTEM_PROMPT_TEMPLATE
    
    # 根据语言调整提示词
    if user_language == 'en':
        language_instruction = "\n\n**IMPORTANT: The user is communicating in English. You MUST generate the plan and all descriptions in English.**"
        prompt_template = prompt_template + language_instruction
    else:
        language_instruction = "\n\n**重要提醒：用户使用中文交流，你必须用中文生成计划和所有描述。**"
        prompt_template = prompt_template + language_instruction

    if is_complaint_or_report:
        prompt = prompt_template.format(
            intent_name=intent_name,
            user_query=user_query,
            complaint_or_report_examples_str=rag_examples_str
        )
    else:
        prompt = prompt_template.format(
            intent_name=intent_name,
            intent_entities_str=json.dumps(intent_entities, indent=2, ensure_ascii=False),
            dataset_schema=dataset_schema_str,
            user_query=user_query,
            rag_examples_str=rag_examples_str,
            feedback_str=feedback_str
        )
    
    messages_for_llm = [{"role": "system", "content": prompt}]
    logger.info(f"planner_llm_input: {messages_for_llm}")
    
    try:
        # 调用LLM生成计划
        raw_llm_output = await _call_llm(llm_client, messages_for_llm)
        logger.debug(f"planner_llm_output: {raw_llm_output}")
        
        # 解析JSON输出
        json_string = _strip_markdown_code_block(raw_llm_output)
        
        if not json_string:
            raise json.JSONDecodeError("Empty JSON output", raw_llm_output, 0)
        
        return _parse_plan_from_json(json_string, intent_name, database_schema, user_query)
        
    except Exception as e:
        logger.error(f"PLANNER: Plan creation failed: {e}", exc_info=True)
        return _create_fallback_plan(str(e), database_schema, user_query)


async def _call_llm(llm_client: Any, messages_for_llm: List[Dict[str, Any]]) -> str:
    """调用LLM获取响应"""
    try:
        # 优先使用generate方法
        if hasattr(llm_client, 'generate'):
            response_obj = await llm_client.generate(messages_for_llm)
            
            # 处理不同格式的响应对象
            if hasattr(response_obj, 'content'):
                return response_obj.content
            elif hasattr(response_obj, 'message') and hasattr(response_obj.message, 'content'):
                return response_obj.message.content
            else:
                return str(response_obj)
        
        # 如果没有generate方法，使用流式接口并收集所有内容
        elif hasattr(llm_client, 'generate_stream'):
            full_content = []
            async for chunk in llm_client.generate_stream(messages_for_llm):
                if chunk.choices and chunk.choices[0].delta and hasattr(chunk.choices[0].delta, 'content'):
                    if chunk.choices[0].delta.content:
                        full_content.append(chunk.choices[0].delta.content)
            return ''.join(full_content)
        
        else:
            raise AttributeError("LLM client has no generate or generate_stream method")
            
    except Exception as e:
        logger.error(f"PLANNER: LLM call failed: {e}")
        raise


def _parse_plan_from_json(
    json_string: str,
    intent_name: str,
    database_schema: Optional[Dict[str, Any]],
    user_query: str
) -> Plan:
    """从JSON字符串解析Plan对象"""
    try:
        # 解析JSON
        parsed_data = json.loads(json_string)
        
        # 构建plan数据
        plan_data = {}
        
        # 处理可能的嵌套结构
        if "plan" in parsed_data and isinstance(parsed_data["plan"], dict):
            parsed_data = parsed_data["plan"]
        
        # 提取基本信息
        plan_data["task_name"] = parsed_data.get("name") or parsed_data.get("task_name", intent_name)
        plan_data["dataset_id"] = parsed_data.get("dataset_id", 
                                                database_schema.get('dataset_id') if isinstance(database_schema, dict) else None)
        
        # 处理任务列表 - 只支持标准的subtasks格式
        if "subtasks" in parsed_data:
            plan_data["subtasks"] = parsed_data["subtasks"]
        else:
            plan_data["subtasks"] = []
            logger.warning("No subtasks found in plan output")
        
        # 创建Plan对象
        validated_plan = Plan(**plan_data)
        validated_plan.raw_user_query_for_context = user_query
        
        logger.info(f"PLANNER: Successfully created plan '{validated_plan.task_name}' with {len(validated_plan.subtasks)} tasks")
        
        return validated_plan
        
    except json.JSONDecodeError as e:
        logger.error(f"PLANNER: JSON parsing error: {e}")
        raise


def _create_fallback_plan(error_msg: str, database_schema: Optional[Dict[str, Any]], user_query: str) -> Plan:
    """创建失败时的备用计划"""
    return Plan(
        task_name="规划失败",
        subtasks=[SubTask(
            idx=1,
            dep=[],
            desc=f"创建计划时出错：{error_msg}",
            adv_tool="需要技术支持"
        )],
        dataset_id=database_schema.get('dataset_id') if isinstance(database_schema, dict) else None,
        raw_user_query_for_context=user_query
    )


def create_plan_sync(
    llm_client: Any,
    intent_name: str,
    intent_entities: Dict[str, Any],
    user_query: str,
    database_schema: Optional[Dict[str, Any]] = None,
    rag_examples: Optional[List[Dict[str, Any]]] = None,
    feedback_on_failed_plan: Optional[Dict[str, Any]] = None,
    user_language: str = 'zh',
    ctx: Optional[Any] = None
) -> Plan:
    """
    创建执行计划（同步版本，保持向后兼容）
    """
    return asyncio.run(create_plan(
        llm_client=llm_client,
        intent_name=intent_name,
        intent_entities=intent_entities,
        user_query=user_query,
        database_schema=database_schema,
        rag_examples=rag_examples,
        feedback_on_failed_plan=feedback_on_failed_plan,
        user_language=user_language,
        ctx=ctx,
        think_event_callback=None
    ))
