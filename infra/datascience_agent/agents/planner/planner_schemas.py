from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

class SubTask(BaseModel):
    idx: int = Field(..., description="Unique identifier for the subtask.")
    dep: List[int] = Field(default_factory=list, description="List of indices of prerequisite subtasks.")
    desc: str = Field(..., description="Detailed description of the subtask to be performed.")
    adv_tool: Optional[str] = Field(None, description="Suggested advanced tool or method for executing this subtask (e.g., 'Code Interpreter', 'AutoML Library', 'Data Visualization Expert Model').")
    # You could add more fields like 'estimated_duration', 'required_inputs', 'expected_outputs'

class Plan(BaseModel):
    task_name: str = Field(..., description="The overall high-level task name, e.g., 'AutoML-prediction', 'Data_Analysis_Visualization'.")
    dataset_id: Optional[str] = Field(None, description="The ID of the dataset to be used for this plan.")
    subtasks: List[SubTask] = Field(..., description="A list of ordered subtasks to achieve the overall task.")
    raw_user_query_for_context: Optional[str] = Field(None, description="The original user query or intent description that led to this plan.")