# 改进后的 intent_schemas.py
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field

class TaskInfo(BaseModel):
    category: str  # 改为category: "data_science", "nl_database_query", "document_query", "small_talk"
    specific_type: str = ""  # 具体任务类型
    description: str = ""

class IntentState(BaseModel):
    layer: int = 1  # 改为layer: 1=必要信息识别, 2=补充信息收集, 3=完成确认
    conversation_count: int = 0  # 当前层的对话轮次
    task: Optional[TaskInfo] = None
    essential_info: Dict[str, Any] = Field(default_factory=dict)  # 必要信息
    supplementary_info: Dict[str, Any] = Field(default_factory=dict)  # 补充信息
    metadata: Dict[str, Any] = Field(default_factory=dict)

class IntentSlot(BaseModel):
    dataset: Optional[Any] = None  # 直接存储从state传入的字典数据
    content: IntentState = Field(default_factory=IntentState)

    def update_slot(self, updates: Dict[str, Any]) -> None:
        """更新槽位内容"""
        if "layer" in updates:
            self.content.layer = updates["layer"]
            if updates["layer"] == 2:  # 进入第二层时重置对话计数
                self.content.conversation_count = 0
        
        if "conversation_count" in updates:
            self.content.conversation_count = updates["conversation_count"]

        # 任务信息更新
        if "task_category" in updates or "task_specific_type" in updates or "task_description" in updates:
            if not self.content.task:
                self.content.task = TaskInfo(
                    category=updates.get("task_category", ""),
                    specific_type=updates.get("task_specific_type", ""),
                    description=updates.get("task_description", "")
                )
            else:
                if "task_category" in updates:
                    self.content.task.category = updates["task_category"]
                if "task_specific_type" in updates:
                    self.content.task.specific_type = updates["task_specific_type"]
                if "task_description" in updates:
                    self.content.task.description = updates["task_description"]
        
        # 信息收集更新
        if "essential_info" in updates and isinstance(updates["essential_info"], dict):
            self.content.essential_info.update(updates["essential_info"])
        
        if "supplementary_info" in updates and isinstance(updates["supplementary_info"], dict):
            self.content.supplementary_info.update(updates["supplementary_info"])
        
        if "metadata" in updates and isinstance(updates["metadata"], dict):
            self.content.metadata.update(updates["metadata"])

    def reset_for_new_intent(self):
        """重置为新的意图识别"""
        self.content = IntentState()

class IntentUpdateToolArgs(BaseModel):
    """LLM更新意图状态的参数"""
    layer: Optional[int] = Field(None, description="对话层次 (1=必要信息, 2=补充信息, 3=完成)")
    conversation_count: Optional[int] = Field(None, description="当前层的对话轮次")
    task_category: Optional[str] = Field(None, description="任务大类: data_science, nl_database_query, document_query, small_talk")
    task_specific_type: Optional[str] = Field(None, description="具体任务类型")
    task_description: Optional[str] = Field(None, description="整合所有对话历史上下文后完整的任务描述")
    essential_info: Optional[Dict[str, Any]] = Field(None, description="必要信息")
    supplementary_info: Optional[Dict[str, Any]] = Field(None, description="补充信息")
    metadata: Optional[Dict[str, Any]] = Field(None, description="其他元数据")
