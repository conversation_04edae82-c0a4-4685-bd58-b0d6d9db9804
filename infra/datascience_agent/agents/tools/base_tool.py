# Create new file: agents/tools/base_tool.py
from pydantic import BaseModel
from typing import Dict, Any, Optional, Type

class ToolInputSchema(BaseModel):
    pass

class ToolOutputSchema(BaseModel):
    status: str # "success" or "failure"
    observation: Dict[str, Any]
    error_message: Optional[str] = None

class BaseTool:
    name: str
    description: str
    args_schema: Type[ToolInputSchema] = ToolInputSchema # Default empty schema

    def __init__(self):
        # Ensure name and description are set by subclasses
        if not hasattr(self, 'name') or not hasattr(self, 'description'):
            raise NotImplementedError("Tool subclasses must define 'name' and 'description'")

    def _execute(self, **kwargs: Any) -> ToolOutputSchema:
        # This is the internal method that subclasses will implement
        raise NotImplementedError

    def run(self, tool_input: Dict[str, Any]) -> ToolOutputSchema:
        """
        Public method to run the tool with validation.
        Input is a dictionary which will be validated against args_schema.
        """
        try:
            validated_input = self.args_schema(**tool_input)
            return self._execute(**validated_input.dict())
        except Exception as e:
            # Catch validation errors or errors during _execute
            return ToolOutputSchema(
                status="failure",
                observation={"error": f"Error executing tool {self.name}: {str(e)}"},
                error_message=f"Error executing tool {self.name}: {str(e)}"
            )