# datascience_agent/graph_orchestrator.py
from langgraph.graph import StateGraph, <PERSON><PERSON>
from functools import partial # Make sure partial is imported
import logging
from typing import TYPE_CHECKING

from .state import AgentState
from .graph_nodes import (
    get_user_input_node,
    intent_recognition_node,
    planner_node, # planner_node is imported
    executor_node,
    present_output_node
)

if TYPE_CHECKING:
    from .utils.openai_client import OpenAIClient
    from .agents.executor.executor import ExecutorAgent # executor_agent is imported from the correct path by user

logger = logging.getLogger(__name__)


def should_plan_or_clarify_or_end(state: AgentState) -> str:
    logger.debug(f"Router: should_plan_or_clarify_or_end? Intent: {state.get('identified_intent_name')}, Needs Clarify: {state.get('needs_clarification')}")
    if state.get('stop_requested'):
        logger.info("Routing to: presentOutput (due to stop request)")
        return "present_output"
    if state.get('identified_intent_name') == "end_conversation":
        logger.info("Routing to: end_conversation_branch (effectively presentOutput then END)")
        return "end_conversation_branch"
    if state.get('needs_clarification'):
        logger.info("Routing to: clarify_branch (presentOutput)")
        return "clarify_branch"
    if state.get('identified_intent_name') and state.get('identified_intent_name') not in ["unknown_intent", "error_no_input", "error_intent_processing", "unknown_intent_at_stage3"]:
        logger.info("Routing to: plan_branch (planner)")
        return "plan_branch"

    logger.warning("Routing to: present_output_due_to_unclear_intent (presentOutput)")
    return "present_output_due_to_unclear_intent"

def after_planning_route(state: AgentState) -> str:
    logger.debug(f"Router: after_planning_route? Plan: {True if state.get('current_plan') else False}, Error: {state.get('execution_error')}")
    if state.get('stop_requested'):
        logger.info("Routing to: present_output (due to stop request after planning)")
        return "present_output"
    if state.get('execution_error'): # Check if planning itself set an error
        logger.info("Routing to: present_output (due to planning error)")
        return "present_output"
    current_plan = state.get('current_plan')
    if current_plan and isinstance(current_plan, dict) and current_plan.get('subtasks'):
        logger.info("Routing to: execute_branch (executor)")
        return "execute_branch"
    else: # No plan, empty subtasks, or plan is for "End Conversation" which might not have subtasks
        logger.info("Routing to: present_output (no plan, empty subtasks, or plan intentionally empty for execution)")
        return "present_output"

def build_graph(llm_client: 'OpenAIClient', executor_agent: 'ExecutorAgent'):
    workflow = StateGraph(AgentState)

    intent_recognizer_with_client = partial(intent_recognition_node, llm_client=llm_client)
    planner_with_client = partial(planner_node, llm_client=llm_client)
    # Ensure executor_node gets the llm_client (ExecutorAgent needs it)
    executor_node_with_client = partial(executor_node, llm_client=llm_client)

    workflow.add_node("userInput", get_user_input_node)
    workflow.add_node("intentRecognizer", intent_recognizer_with_client)
    workflow.add_node("planner", planner_with_client)
    workflow.add_node("executor", executor_node_with_client) # Use the partial function
    workflow.add_node("presentOutput", present_output_node)

    workflow.set_entry_point("userInput")

    workflow.add_edge("userInput", "intentRecognizer")

    workflow.add_conditional_edges(
        "intentRecognizer",
        should_plan_or_clarify_or_end,
        {
            "plan_branch": "planner",
            "clarify_branch": "presentOutput",
            "end_conversation_branch": "presentOutput",
            "present_output_due_to_unclear_intent": "presentOutput"
        }
    )

    workflow.add_conditional_edges(
        "planner",
        after_planning_route,
        {
            "execute_branch": "executor",
            "present_output": "presentOutput"
        }
    )

    workflow.add_edge("executor", "presentOutput")
    workflow.add_edge("presentOutput", END) # Explicitly end the graph

    app = workflow.compile()
    logger.info("StateGraph compiled successfully.")
    return app