# datascience_agent/graph_nodes.py
import logging
import json
import datetime
import uuid
from typing import TYPE_CHECKING, Any, Dict, Optional, List
from unicodedata import category

from .state import AgentState, get_task_category
from .summary_prompt import all_prompt
from .agents.intent_recognizer.intent_recognizer import Intent<PERSON><PERSON>ognizer
from .agents.planner.planner import create_plan
from .agents.planner.planner_schemas import Plan, SubTask
from .agents.planner.planner_prompts import EMPTY_RAG_EXAMPLES
from infra.mcp.manager.mcp_manager import MC<PERSON>anager
from common.elements.agent_event import JupyterEvent
from langgraph.config import get_stream_writer  # 🎯 导入 LangGraph StreamWriter

if TYPE_CHECKING:
    from .utils.openai_client import OpenAIClient

logger = logging.getLogger(__name__)

async def generate_execution_summary(
    state: AgentState,
    llm_client: 'OpenAIClient',
    jupyter_events: List[Dict[str, Any]],
) -> str:
    slot_state = state.get('intent_recognizer_slot_state', {})
    content = slot_state.get('content', {})
    task_info = content.get('task', {})

    # 获取完整的任务描述，优先使用意图识别器整合的结果
    if isinstance(task_info, dict):
        complete_task_description = task_info.get('description', '')
    else:
        complete_task_description = getattr(task_info, 'description', '') if task_info else ''

    language = state.get('detected_language', 'zh')
    task_category = get_task_category(state)

    try:
        # 构建总结提示词
        failed = _is_execution_failed(state, jupyter_events)
        system_prompt = _get_error_system_prompt(language) if failed else _get_summary_system_prompt(task_category, language)
        summary_prompt = _build_summary_prompt(jupyter_events, complete_task_description, language)

        # logger.info(f"summary_llm_system: {log_clean_multiline(system_prompt)}, language: {language}, task_category: {task_category}")
        # logger.info(f"summary_llm_user: {summary_prompt}")
        messages=[
                {"role": "system", "content": system_prompt},
                {"role": "assistant", "content": summary_prompt},
                {"role": "user", "content": complete_task_description}
        ]
        logger.info(f"summary_llm_input: {messages}")
        # 调用LLM生成总结
        response = await llm_client.generate(
            messages=messages,
            model=None
        )
        
        # 从ChatCompletionMessage对象中提取内容
        summary_content = response.content if response.content else "任务执行完成。"

        # strip markdown flag
        if summary_content.startswith("```"):
            # 去除开头的 markdown 代码块标记
            if summary_content.lower().startswith("```markdown"):
                summary_content = summary_content[11:].lstrip()  # 移除 ```markdown
            else:
                summary_content = summary_content[3:].lstrip()   # 移除 ```
        
        if summary_content.endswith("```"):
            # 去除结尾的 markdown 代码块标记
            summary_content = summary_content[:-3].rstrip()

        logger.info(f"summary_llm_output: {summary_content}")
        return summary_content.strip()
        
    except Exception as e:
        logger.error(f"Error generating execution summary: {e}", exc_info=True)
        fallback_msg = "任务执行完成。" if language == 'zh' else "Task execution completed."
        return fallback_msg


# 在generate_execution_summary函数后添加流式版本
async def generate_execution_summary_stream(
    state: AgentState,
    llm_client: 'OpenAIClient',
    jupyter_events: List[Dict[str, Any]],
    writer=None,
    data_info=None,
) -> str:
    """流式生成执行总结，实时通过writer发送summary片段"""
    slot_state = state.get('intent_recognizer_slot_state', {})
    content = slot_state.get('content', {})
    task_info = content.get('task', {})

    # 获取完整的任务描述，优先使用意图识别器整合的结果
    if isinstance(task_info, dict):
        complete_task_description = task_info.get('description', '')
    else:
        complete_task_description = getattr(task_info, 'description', '') if task_info else ''

    language = state.get('detected_language', 'zh')
    task_category = get_task_category(state)

    # 🆔 获取最后一个成功的jupyter cell的cell_id，而不是生成新的UUID
    summary_cell_id = None
    if jupyter_events:
        # 倒序遍历，找到第一个cell_type是sql或code且成功的cell_id
        for event in reversed(jupyter_events):
            cell_type = event.get("cell_type", "")
            # 从metadata中获取status字段
            cell_status = event.get("status", "") or event.get("metadata", {}).get("status", "")
            if cell_type in ['sql', 'code'] and cell_status == 'success':
                summary_cell_id = event.get("cell_id")
                logger.info(f"Found last successful {cell_type} cell_id for summary: {summary_cell_id}")
                break
    
    # 如果没有找到成功的cell，生成一个fallback UUID
    if not summary_cell_id:
        logger.info(f"No successful cell found")
    else:
        logger.info(f"Using existing successful cell_id for summary: {summary_cell_id}")

    try:
        # 构建总结提示词
        failed = _is_execution_failed(state, jupyter_events)
        system_prompt = _get_error_system_prompt(language) if failed else _get_summary_system_prompt(task_category, language)
        summary_prompt = _build_summary_prompt(jupyter_events, complete_task_description, language, data_info)

        messages=[
                {"role": "system", "content": system_prompt},
                {"role": "assistant", "content": summary_prompt},
                {"role": "user", "content": complete_task_description}
        ]
        logger.info(f"summary_llm_stream_input: {messages}")
        
        # 🎯 使用流式调用
        collected_content = ""
        async for chunk in llm_client.generate_stream(
            messages=messages,
            model=None
        ):
            if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                chunk_content = chunk.choices[0].delta.content
                collected_content += chunk_content
                
                # 🎯 只发送当前chunk，前端自己拼接
                if writer:
                    try:
                        writer({
                            "summary_stream_event": {
                                "data": {
                                    "chunk": chunk_content,  # 只发送当前chunk
                                    "cell_id": summary_cell_id  # 🆔 使用真实的cell_id
                                }
                            }
                        })
                        logger.debug(f"✅ Sent summary chunk: {chunk_content[:50]}...")
                    except Exception as e:
                        logger.error(f"❌ Failed to send summary chunk: {e}")

        # 清理markdown标记
        summary_content = collected_content
        if summary_content.startswith("```"):
            if summary_content.lower().startswith("```markdown"):
                summary_content = summary_content[11:].lstrip()
            else:
                summary_content = summary_content[3:].lstrip()
        
        if summary_content.endswith("```"):
            summary_content = summary_content[:-3].rstrip()
        
        logger.info(f"✅ Summary generation completed. Content length: {len(summary_content)}, Cell ID: {summary_cell_id}")
        return summary_content
        
    except Exception as e:
        error_msg = f"Failed to generate execution summary: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        # 即使出错也要通过writer发送错误信息
        if writer:
            try:
                writer({
                    "summary_stream_event": {
                        "data": {
                            "chunk": f"生成总结时出现错误: {str(e)}",
                            "cell_id": summary_cell_id
                        }
                    }
                })
            except Exception as write_error:
                logger.error(f"❌ Failed to send error summary chunk: {write_error}")
        
        return error_msg


def _get_summary_system_prompt(task_category:str, language: str) -> str:
    """获取总结系统提示词"""
    return all_prompt[language][task_category]

#     if language == 'zh':
#         return """你是一个专业的数据分析专家。基于提供的执行结果，生成简洁的数据分析总结。
#
# 核心要求：
# 1. 核心：简要总结执行结果比如如果是 数据分析就简要总结数据分析步骤和结果，如果是数据科学就简要总结数据科学步骤和结果。
# 2. 包含查询得到的具体数值、统计结果
# 3. 语言简洁专业，避免客套话和引导语
# 4. 控制在150字以内
# 5. 只提供分析结果，不要尝试对话问是否需要更多信息
#
# 输出格式：直接给出数据分析结果，不要有"根据分析"、"让我知道"等开头和结尾。"""
#     else:
#         return """You are a professional data analysis report generator. Generate concise data analysis summaries based on provided execution results.
#
# Core requirements:
# 1. State specific data findings and numerical results directly
# 2. Highlight key data insights and trends
# 3. Include specific values and statistical results from queries
# 4. Use concise, professional language without pleasantries
# 5. Keep under 150 words
# 6. Provide analysis results only, no follow-up questions
#
# Output format: Provide data analysis results directly, without phrases like "based on analysis" or "let me know"."""


def _build_summary_prompt(
    jupyter_events: List[Dict[str, Any]],
    complete_task_description: str,
    language: str,
    data_info=None
) -> str:
    """构建总结提示词"""
    image_count = 0
    sql_count = 0
    code_count = 0
    err_count = 0

    to_summary_text = ""
    for i, event in enumerate(jupyter_events):
        cell_type = event.get('cell_type', '')
        source = event.get('source', [])
        outputs = event.get('outputs', [])
        status = event.get('metadata', {}).get('status', '')

        if status not in ["success", "error"]:
            continue

        if status == 'error':
            err_count += 1

        if cell_type in ['code', 'sql'] and source:
            if cell_type == 'sql':
                sql_count += 1
            else:
                code_count += 1

        source_text = ''.join(source)

        # collect output
        output_text = ""
        for output in outputs:
            if isinstance(output, dict):
                if output.get('output_type') == 'stream':
                    text_content = output.get('text', [])
                    if text_content:
                        result_text = ""
                        # 限制单个结果的长度
                        if "aisearch" in text_content[:20]:
                            aisearch_result = json.loads(text_content)
                            for j, item in enumerate(aisearch_result["aisearch"]):
                                content = item['content'][:5000]
                                rerank_score = item.get('rerank_score', 0)
                                score = item.get('score', 0)
                                chunk_id = item.get('chunk_id', '')
                                file_name = item.get('file_name', 'N/A')
                                result_text += f"检索召回结果{j + 1}：重排序评分{rerank_score}，原始评分{score}, chunk_id：{chunk_id}, 文件名：{file_name}, 内容：{content}\n"
                        else:
                            result_text = ''.join(text_content) if isinstance(text_content, list) else str(text_content)

                        if result_text.strip():
                            output_text += result_text.strip() + "\n"

                elif output.get('output_type') == 'csv':
                    csv_data = output['csv_data']
                    if isinstance(csv_data, list) and len(csv_data) > 0:
                        # 转换CSV数据为可读格式
                        if len(csv_data) > 1:  # 有标题和数据
                            headers = csv_data[0] if isinstance(csv_data[0], list) else [str(csv_data[0])]
                            rows = csv_data[1:1000]  # 最多总结前1000行数据

                            result_summary = f"查询结果（共{len(csv_data) - 1}行)，分析前{len(rows)}行\n"
                            result_summary += f"列名: {', '.join(headers)}\n"

                            for i, row in enumerate(rows):
                                if isinstance(row, list):
                                    row_str = ', '.join([str(cell) for cell in row])
                                    result_summary += f"{row_str}\n"

                            output_text += result_summary + "\n"

                elif output.get('output_type') == 'execute_result':
                    data = output.get('data', {})
                    if 'text/plain' in data:
                        result_text = data['text/plain']
                        if isinstance(result_text, list):
                            result_text = ''.join(result_text)
                        if str(result_text).strip():
                            output_text += str(result_text).strip() + "\n"

                elif output.get('output_type') == 'display_data':
                    data = output.get('data', {})
                    if any(key.startswith('image/') for key in data.keys()):
                        image_count += 1

                else:
                    output_text = str(output)

        to_summary_text += f"\n{output_text}"

    # 🆕 添加data_info信息
    data_info_text = ""
    if data_info:
        try:
            logger.info(f"🔍 Summary中包含data_info")
            data_info_text = f"\n数据加载信息: {str(data_info)}\n"
        except Exception as e:
            logger.warning(f"⚠️ 处理data_info时出错: {e}")

    if language == 'zh':
        prompt_text = f"""
执行统计: 训练原始数据信息{data_info_text}执行了 {sql_count} 个SQL查询，执行了 {code_count} 个代码单元，生成了 {image_count} 个可视化图表。
执行结果: {to_summary_text}\n"""

        # if actual_results:
        #     prompt_parts.append("\n具体执行结果:")
        #     for i, result in enumerate(actual_results[:3]):  # 最多显示前个结果
        #         # 限制单个结果的长度
        #         if "aisearch" in result[:20]:
        #             aisearch_result = json.loads(result)
        #             for j, item in enumerate(aisearch_result["aisearch"]):
        #                 content = item['content'][:5000]
        #                 rerank_score = item.get('rerank_score', 0)
        #                 score = item.get('score', 0)
        #                 prompt_parts.append(f"召回内容{j + 1}：重排序评分{rerank_score}，原始评分{score}, 内容：{content}")
        #
        #         display_result = result[:5000] + "..." if len(result) > 5000 else result
        #         prompt_parts.append(f"结果{i + 1}: {display_result}")

    else:
        prompt_text = f"""
Task Stat: Executed {sql_count} SQL queries，Executed {code_count} Code cells，Generated {image_count} images，Raised {err_count} Errors"),
Task Result: {to_summary_text}{data_info_text}\n
"""
        
    # TODO flacroxing control length
    max_length = 32 * 1024 * 4
    if len(prompt_text) > max_length:
        prompt_text = prompt_text[:max_length] + "\n内容过多，省略了一部分执行结果..."

    return prompt_text


def _send_final_reference_event(summary_content: str, jupyter_events: List[Dict[str, Any]], writer) -> None:
    """发送最终的reference_event"""
    try:
        reference_list = _extract_reference_list_from_summary(summary_content, jupyter_events) if jupyter_events else []
        
        if reference_list:
            # 按文档去重，计算唯一文档数量
            unique_files = set()
            for ref in reference_list:
                file_id = ref.get('file_id', '')
                file_name = ref.get('file_name', '')
                # 优先使用file_id，如果没有则使用file_name作为去重标识
                unique_key = file_id if file_id else file_name
                if unique_key:
                    unique_files.add(unique_key)
            
            unique_doc_count = len(unique_files)
            name = f"引用了{unique_doc_count}篇文章"
            logger.info(f"引用统计: 总chunk数量={len(reference_list)}, 唯一文档数量={unique_doc_count}")
        elif jupyter_events:
            name = "任务完成，但未找到有效引用"
        else:
            name = "任务完成，但未产生执行结果"
        
        reference_event_data = {
            "source": "knowledge_base",
            "status": "finish",
            "name": name,
            "reference_list": reference_list
        }
        
        writer({
            "reference_event": {
                "data": reference_event_data
            }
        })
        logger.debug(f"✅ 成功发送最终reference_event: {name}，引用数量: {len(reference_list)}")
        
    except Exception as e:
        logger.error(f"❌ 发送最终reference_event失败: {e}")


def _extract_reference_list_from_summary(summary_content: str, jupyter_events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """从summary中的citation标签提取实际引用的reference信息"""
    import re
    
    reference_list = []
    
    try:
        # 1. 从summary中提取所有citation的chunk_id
        citation_pattern = r'<reference-block\s+chunk_id="([^"]+)"[^>]*>'
        raw_cited_chunk_ids = re.findall(citation_pattern, summary_content)
        
        if not raw_cited_chunk_ids:
            logger.info("Summary中未发现citation标签，无需提取reference信息")
            return reference_list
        
        # 2. 处理可能包含逗号分隔的多个chunk_id的情况
        cited_chunk_ids = []
        for chunk_id_str in raw_cited_chunk_ids:
            # 如果chunk_id包含逗号，则按逗号分割
            if ',' in chunk_id_str:
                individual_ids = [cid.strip() for cid in chunk_id_str.split(',') if cid.strip()]
                cited_chunk_ids.extend(individual_ids)
                logger.info(f"发现逗号分隔的chunk_id: {chunk_id_str}，分割为: {individual_ids}")
            else:
                cited_chunk_ids.append(chunk_id_str.strip())
        
        # 去除重复的chunk_id
        cited_chunk_ids = list(set(cited_chunk_ids))
        logger.info(f"从summary中提取到{len(cited_chunk_ids)}个citation chunk_id: {cited_chunk_ids}")
        
        # 3. 构建chunk_id到reference信息的映射
        chunk_id_to_ref = {}
        
        for event in jupyter_events:
            outputs = event.get('outputs', [])
            
            for output in outputs:
                if isinstance(output, dict) and output.get('output_type') == 'stream':
                    text_content = output.get('text', [])
                    if text_content:
                        text_content_str = ''.join(text_content) if isinstance(text_content, list) else str(text_content)
                        
                        # 检查是否是aisearch结果
                        if "aisearch" in text_content_str[:20]:
                            try:
                                aisearch_result = json.loads(text_content_str)
                                aisearch_items = aisearch_result.get("aisearch", [])
                                
                                for item in aisearch_items:
                                    chunk_id = item.get('chunk_id', '')
                                    if chunk_id:
                                        chunk_id_to_ref[chunk_id] = {
                                            "knowledge_base_id": item.get('knowledge_base_id', ''),
                                            "knowledge_base_name": item.get('knowledge_base_name', ''),
                                            "file_id": item.get('file_id', ''),
                                            "file_name": item.get('file_name', ''),
                                            "file_url": item.get('file_url', ''),
                                            "chunk_id": chunk_id,
                                            "chunk": item.get('content', '')[:200]  # 限制chunk内容长度
                                        }
                                        
                            except json.JSONDecodeError as e:
                                logger.error(f"解析aisearch结果失败: {e}")
                            except Exception as e:
                                logger.error(f"构建chunk_id映射失败: {e}")
        
        # 4. 根据citation中的chunk_id提取对应的reference信息
        for chunk_id in cited_chunk_ids:
            if chunk_id in chunk_id_to_ref:
                reference_list.append(chunk_id_to_ref[chunk_id])
                logger.debug(f"找到引用的chunk_id: {chunk_id}")
            else:
                logger.warning(f"未找到chunk_id对应的reference信息: {chunk_id}")
        
        # 5. 去重（防止同一个chunk_id被多次引用）
        seen_chunk_ids = set()
        unique_reference_list = []
        for ref in reference_list:
            chunk_id = ref.get('chunk_id')
            if chunk_id not in seen_chunk_ids:
                unique_reference_list.append(ref)
                seen_chunk_ids.add(chunk_id)
        
        logger.info(f"最终提取到{len(unique_reference_list)}个唯一的reference信息")
        return unique_reference_list
                                
    except Exception as e:
        logger.error(f"从summary中提取reference信息失败: {e}")
        return reference_list


"""
def _build_summary_prompt_dep(
    jupyter_events: List[Dict[str, Any]],
    complete_task_description: str,
    language: str
) -> str:
    # 构建总结提示词

    # 提取关键信息
    code_cells = []
    sql_queries = []
    actual_results = []  # 存储实际的数据结果
    error_messages = []
    generated_images = []

    for event in jupyter_events:
        cell_type = event.get('cell_type', '')
        source = event.get('source', [])
        outputs = event.get('outputs', [])
        status = event.get('status', '')

        # 收集代码/SQL
        if cell_type in ['code', 'sql'] and source:
            source_text = ''.join(source)
            if cell_type == 'sql':
                sql_queries.append(source_text)
            else:
                code_cells.append(source_text)
        
        # 提取具体的执行结果数据
        if outputs and status == 'success':
            for output in outputs:
                if isinstance(output, dict):
                    # 检查是否有图像
                    if output.get('output_type') == 'display_data':
                        data = output.get('data', {})
                        if any(key.startswith('image/') for key in data.keys()):
                            generated_images.append("生成了可视化图表")
                    
                    # 提取具体的文本结果
                    elif output.get('output_type') == 'stream':
                        text_content = output.get('text', [])
                        if text_content:
                            result_text = ''.join(text_content) if isinstance(text_content, list) else str(text_content)
                            if result_text.strip():
                                actual_results.append(result_text.strip())
                    
                    # 提取CSV数据结果
                    elif output.get('output_type') == 'csv':
                        csv_data = output['csv_data']
                        if isinstance(csv_data, list) and len(csv_data) > 0:
                            # 转换CSV数据为可读格式
                            if len(csv_data) > 1:  # 有标题和数据
                                headers = csv_data[0] if isinstance(csv_data[0], list) else [str(csv_data[0])]
                                rows = csv_data[1:1000]  # 最多总结前1000行数据
                                
                                result_summary = f"查询结果（共{len(csv_data)-1}行)，分析前{len(rows)}行\n"
                                result_summary += f"列名: {', '.join(headers)}\n"
                                
                                for i, row in enumerate(rows):
                                    if isinstance(row, list):
                                        row_str = ', '.join([str(cell) for cell in row])
                                        result_summary += f"{row_str}\n"
                                
                                actual_results.append(result_summary)
                    
                    # 提取execute_result类型的数据
                    elif output.get('output_type') == 'execute_result':
                        data = output.get('data', {})
                        if 'text/plain' in data:
                            result_text = data['text/plain']
                            if isinstance(result_text, list):
                                result_text = ''.join(result_text)
                            if str(result_text).strip():
                                actual_results.append(str(result_text).strip())
        
        # 收集错误信息
        if status == 'error':
            error_messages.append(f"执行错误: {cell_type}单元")
    
    # 构建提示词
    if language == 'zh':
        prompt_parts = [
            f"任务描述: {complete_task_description}",
            "\n分析执行情况："
        ]
        
        if sql_queries:
            prompt_parts.append(f"执行了 {len(sql_queries)} 个SQL查询")
            # 添加SQL查询的简要信息
            for i, sql in enumerate(sql_queries[:2]):  # 最多显示前2个查询
                prompt_parts.append(f"SQL{i+1}: {sql[:100]}..." if len(sql) > 100 else f"SQL{i+1}: {sql}")
        
        if code_cells:
            prompt_parts.append(f"执行了 {len(code_cells)} 个代码单元")
            
        if generated_images:
            prompt_parts.append(f"生成了 {len(generated_images)} 个可视化图表")
            
        if actual_results:
            prompt_parts.append("\n具体执行结果:")
            for i, result in enumerate(actual_results[:3]):  # 最多显示前个结果
                # 限制单个结果的长度
                if "aisearch" in result[:20]:
                    aisearch_result = json.loads(result)
                    for j, item in enumerate(aisearch_result["aisearch"]):
                        content = item['content'][:5000]
                        rerank_score = item.get('rerank_score', 0)
                        score = item.get('score', 0)
                        chunk_id = item.get('chunk_id', '')
                        file_name = item.get('file_name', 'N/A')
                        prompt_parts.append(f"召回内容{j+1}：重排序评分{rerank_score}，原始评分{score}, chunk_id：{chunk_id}, 文件名：{file_name}, 内容：{content}")

                display_result = result[:5000] + "..." if len(result) > 5000 else result
                prompt_parts.append(f"结果{i+1}: {display_result}")
                
        if error_messages:
            prompt_parts.append(f"\n遇到了 {len(error_messages)} 个错误")
            
        # prompt_parts.append("\n请基于上述具体的执行结果，生成一个简洁的数据分析总结报告。要求：")
        # prompt_parts.append("1. 直接说明具体发现的数据和数字")
        # prompt_parts.append("2. 突出关键的数据洞察")
        # prompt_parts.append("3. 只提供分析结果，不要有引导性语言")
        # prompt_parts.append("4. 控制在150字以内")
        
    else:
        prompt_parts = [
            f"Task Description: {complete_task_description}",
            "\nExecution analysis:"
        ]
        
        if sql_queries:
            prompt_parts.append(f"Executed {len(sql_queries)} SQL queries")
            for i, sql in enumerate(sql_queries[:2]):
                prompt_parts.append(f"SQL{i+1}: {sql[:100]}..." if len(sql) > 100 else f"SQL{i+1}: {sql}")
        
        if code_cells:
            prompt_parts.append(f"Executed {len(code_cells)} code cells")
            
        if generated_images:
            prompt_parts.append(f"Generated {len(generated_images)} visualizations")
            
        if actual_results:
            prompt_parts.append("\nActual results:")
            for i, result in enumerate(actual_results):
                display_result = result[:5000] + "..." if len(result) > 5000 else result
                prompt_parts.append(f"Result{i+1}: {display_result}")
                
        if error_messages:
            prompt_parts.append(f"\nEncountered {len(error_messages)} errors")
            
        # prompt_parts.append("\nGenerate a concise data analysis summary based on the actual results above. Requirements:")
        # prompt_parts.append("1. Include specific data and numbers found")
        # prompt_parts.append("2. Highlight key data insights")
        # prompt_parts.append("3. Provide analysis results only, no introductory language")
        # prompt_parts.append("4. Keep under 150 words")

    # TODO flacroxing control length
    max_length = 32 * 1024
    prompt = '\n'.join(prompt_parts)
    if len(prompt) > max_length:
        prompt = prompt[:max_length] + "\n内容过多，省略了一部分内容..."
    
    return prompt
"""

def _is_execution_failed(state: AgentState, jupyter_events: List[Dict[str, Any]]) -> bool:
    # 明确的执行错误
    if state.get('execution_error'):
        return True
    # 以最后一个带状态的事件为准
    for ev in reversed(jupyter_events or []):
        status = ev.get('metadata', {}).get('status') or ev.get('status')
        if status in ('success', 'error'):
            return status == 'error'
    return False

def _get_error_system_prompt(language: str) -> str:
    return all_prompt[language]["error_analysis"]

def get_user_input_node(state: AgentState) -> AgentState:
    logger.info("--- Node: User Input ---")
    # ----- MCPManager DEBUG LOG -----
    logger.debug(f"Node get_user_input_node START: MCPManager in state is present: {bool(state.get('mcp_manager'))} | ID: {id(state.get('mcp_manager')) if state.get('mcp_manager') else 'N/A'}")

    # 🔧 移除final_output_for_user处理逻辑（已通过AgentService._execute_graph_stream处理）
    # 注释掉冗余的final_output_for_user处理逻辑（所有最终输出都通过AgentService处理）
    # if state.get('final_output_for_user') is not None:
    #     if not state.get('conversation_history') or \
    #        state['conversation_history'][-1].get("content") != state['final_output_for_user'] or \
    #        state['conversation_history'][-1].get("role") != "assistant":
    #         state['conversation_history'].append({"role": "assistant", "content": state['final_output_for_user']})
    #         logger.info(f"📝 userInput node: Added final_output_for_user to conversation_history")

    # 🧹 移除重复的状态清理逻辑（现在由 AgentService 统一处理）
    # 注释：这些清理逻辑已经移动到 AgentService._cleanup_temporary_state() 中
    # state['user_feedback_on_last_result'] = None
    # state['execution_error'] = None
    # state['final_output_for_user'] = None
    # state['final_summary_content'] = None
    # state['jupyter_events'] = []

    logger.debug(f"State after userInput node: User input '{state.get('current_user_input')}', History length: {len(state.get('conversation_history', []))}")
    # ----- MCPManager DEBUG LOG -----
    logger.debug(f"Node get_user_input_node END: MCPManager in state is present: {bool(state.get('mcp_manager'))} | ID: {id(state.get('mcp_manager')) if state.get('mcp_manager') else 'N/A'}")
    return state

async def intent_recognition_node(state: AgentState, llm_client: 'OpenAIClient') -> AgentState:
    """
    意图识别节点 - 使用已经由 AgentService 识别好的意图
    
    这个节点不再进行意图识别，而是：
    1. 处理特殊情况（如退出命令）
    2. 处理表格选择逻辑（如果表格数量超过20个）
    3. 使用已经识别好的意图状态
    4. 准备后续的规划阶段
    """
    logger.info("--- Node: Intent Recognition (Using Pre-identified Intent) ---")
    logger.debug(f"Node intent_recognition_node START: MCPManager in state is present: {bool(state.get('mcp_manager'))} | ID: {id(state.get('mcp_manager')) if state.get('mcp_manager') else 'N/A'}")
    
    user_input = state.get('current_user_input')
    if not user_input:
        logger.error("IntentRecognizer Node: No user input found in state.")
        state['identified_intent_name'] = "error_no_input"
        state['final_output_for_user'] = "It seems I didn't receive any input. Could you please type your request?"
        state['needs_clarification'] = False
        return state

    # 处理退出命令
    if user_input.lower() in ["quit", "exit", "bye", "end chat", "再见"]:
        logger.info("User requested to end conversation via keyword in intent_recognition_node.")
        state['identified_intent_name'] = "end_conversation"
        state['final_output_for_user'] = "Okay, ending our conversation. Goodbye!"
        state['needs_clarification'] = False
        return state

#     # --- 表格选择逻辑 ---
#     if state.get('needs_table_selection', False):
#         all_tables = state.get('all_available_tables', [])
#         if all_tables:
#             # 检查用户是否在回应表格选择
#             user_input_lower = user_input.lower().strip()
            
#             # 简单的表格选择逻辑 - 检查用户输入中是否包含表格名称
#             selected_tables = []
#             for table in all_tables:
#                 if table.lower() in user_input_lower:
#                     selected_tables.append(table)
            
#             # 如果用户明确选择了一些表格
#             if selected_tables:
#                 state['selected_tables'] = selected_tables
#                 state['needs_table_selection'] = False
#                 state['available_data_sources'] = [{"table_name": table} for table in selected_tables]
#                 logger.info(f"User selected tables: {selected_tables}")
#                 state['final_output_for_user'] = f"好的，我将使用以下表格进行分析：{', '.join(selected_tables)}。请告诉我您想要进行什么分析？"
#                 state['needs_clarification'] = True  # 需要用户进一步说明分析需求
#                 return state
            
#             # 如果用户没有明确选择表格，继续询问
#             else:
#                 # 限制显示的表格数量以避免输出过长
#                 display_tables = all_tables[:20] if len(all_tables) > 20 else all_tables
#                 table_list_str = "\n".join([f"- {table}" for table in display_tables])
                
#                 if len(all_tables) > 20:
#                     table_list_str += f"\n\n（还有 {len(all_tables) - 20} 个表格未显示）"
                
#                 state['final_output_for_user'] = f"""数据库中有 {len(all_tables)} 个表格，请选择您需要分析的表格：

# {table_list_str}

# 请告诉我您想要使用哪些表格，例如："我想分析 orders 和 customers 表格"。"""
                
#                 state['needs_clarification'] = True
#                 state['clarification_question'] = state['final_output_for_user']
#                 return state

    # 使用已经识别好的意图状态
    current_slot_dict = state.get('intent_recognizer_slot_state', {})
    content_from_slot = current_slot_dict.get('content', {})
    current_stage_from_slot = content_from_slot.get('layer', 1)
    task_info_from_slot = content_from_slot.get('task')
    metadata_from_slot = content_from_slot.get('metadata', {})
    task_type_from_dict_check = task_info_from_slot.get('category') if task_info_from_slot and isinstance(task_info_from_slot, dict) else None

    # 检查意图识别是否完成
    if current_stage_from_slot == 3:
        logger.info(f"Intent recognition complete (Stage {current_stage_from_slot}). Task: {task_type_from_dict_check if task_type_from_dict_check else 'N/A'}")
        state['needs_clarification'] = False
        # state['clarification_question'] = None
        state['identified_intent_name'] = task_type_from_dict_check if task_type_from_dict_check else 'unknown_intent_at_stage3'
        state['identified_intent_entities'] = metadata_from_slot
    else:
        logger.info(f"Intent recognition needs further clarification (Stage {current_stage_from_slot}).")
        state['needs_clarification'] = True
        # state['clarification_question'] = state.get('final_output_for_user')  # 使用已经生成的问题
        state['identified_intent_name'] = None
        state['identified_intent_entities'] = None
    logger.info(f"Intent recognition complete (Stage {current_stage_from_slot}). Task: {task_type_from_dict_check if task_type_from_dict_check else 'N/A'}")
    return state

async def planner_node(state: AgentState, llm_client: 'OpenAIClient') -> AgentState:
    logger.info("--- Node: Planner ---")

    # ----- MCPManager DEBUG LOG -----
    logger.debug(f"Node planner_node START: MCPManager in state is present: {bool(state.get('mcp_manager'))} | ID: {id(state.get('mcp_manager')) if state.get('mcp_manager') else 'N/A'}")

    # 从状态中获取意图识别结果
    intent_name = state.get('identified_intent_name')
    intent_entities = state.get('identified_intent_entities', {})
    
    if not intent_name:
        logger.error("No intent name found in state")
        state['execution_error'] = "意图识别失败，无法进行规划"
        return state
    
    # 直接使用意图识别器生成的完整任务描述
    slot_state = state.get('intent_recognizer_slot_state', {})
    content = slot_state.get('content', {})
    task_info = content.get('task', {})
    
    # 获取完整的任务描述，优先使用意图识别器整合的结果
    if isinstance(task_info, dict):
        complete_task_description = task_info.get('description', '')
    else:
        complete_task_description = getattr(task_info, 'description', '') if task_info else ''
    
    # 如果意图识别器没有生成完整描述，退化使用原始用户输入
    if not complete_task_description:
        complete_task_description = state.get('current_user_input', '')
        logger.warning(f"PLANNER: 意图识别器未生成完整任务描述，使用原始用户输入: '{complete_task_description}'")
    else:
        logger.info(f"PLANNER: 使用意图识别器生成的完整任务描述: '{complete_task_description}'")
    

    # 获取其他必要信息
    active_dataset_id = state.get('active_dataset_id')
    database_schema = state.get('database_schema')  # 获取数据库schema
    detected_language = state.get('detected_language', 'zh')
    

    # 🧹 移除重复的状态初始化（现在由 AgentService 统一处理）
    # state['execution_error'] = None
    # state['jupyter_events'] = [] # Initialize for new plan

    feedback_on_failed_plan = state.get('feedback_for_planner')
    rag_examples = ''

    # 如果存在MCPManager，可以考虑获取RAG示例
    if state.get('mcp_manager'):
        logger.info("MCPManager is available, could retrieve RAG examples if needed")

    try:
        # 创建执行计划
        plan = await create_plan(
            llm_client=llm_client,
            intent_name=intent_name,
            intent_entities=intent_entities,
            user_query=complete_task_description,  # 使用完整的任务描述
            database_schema=database_schema,  # 传递database_schema而不是active_dataset_id
            rag_examples=rag_examples,
            feedback_on_failed_plan=feedback_on_failed_plan,
            user_language=detected_language,
            ctx=state.get('ctx'),
            think_event_callback=None
        )
        
        if plan and plan.subtasks:
            state['current_plan'] = plan.model_dump()
            logger.info(f"Plan created successfully with {len(plan.subtasks)} subtasks")
            for i, subtask in enumerate(plan.subtasks):
                logger.info(f"  SubTask {i+1}: {subtask.desc} (Tool: {subtask.adv_tool or 'N/A'})")
        else:
            logger.warning("Plan creation returned empty or invalid plan")
            state['execution_error'] = "规划生成失败，请重试"
            
    except Exception as e:
        logger.error(f"Plan creation failed: {e}", exc_info=True)
        state['execution_error'] = f"规划失败: {str(e)}"


    return state

async def executor_node(state: AgentState, llm_client: 'OpenAIClient') -> AgentState:
    logger.info("--- Node: Executor (React Agent Implementation) ---")
    # ----- MCPManager DEBUG LOG -----
    logger.debug(f"Node executor_node START: MCPManager in state is present: {bool(state.get('mcp_manager'))} | ID: {id(state.get('mcp_manager')) if state.get('mcp_manager') else 'N/A'}")

    # 🛑 检查停止请求
    if state.get('stop_requested'):
        logger.info("🛑 Stop request detected in executor_node")
        state['final_output_for_user'] = "任务已根据您的请求停止。"
        state['stop_requested'] = False
        return state

    # 🎯 获取 LangGraph 的 stream writer
    writer = None
    try:
        writer = get_stream_writer()
        logger.debug("Successfully obtained LangGraph stream writer for executor")
    except Exception as e:
        logger.warning(f"Could not obtain LangGraph stream writer: {e}")

    structured_plan = state.get('current_plan')
    mcp_manager = state.get('mcp_manager')

    if not structured_plan:
        logger.warning("No structured plan found in state")
        return state

    if not structured_plan.get('subtasks') or len(structured_plan['subtasks']) == 0:
        logger.warning("No subtasks found in structured plan")
        return state

    if not mcp_manager:
        logger.error("MCPManager not found in state")
        return state

    try:
        # 使用新的ExecutorAgent
        from infra.datascience_agent.agents.executor.executor import ExecutorAgent
        
        executor_agent = ExecutorAgent(llm_client, mcp_manager, writer)
        await executor_agent.initialize()
        
        # 收集执行结果和事件
        jupyter_events = []
        
        logger.info(f"Starting execution of {len(structured_plan['subtasks'])} subtasks")
        
        # 执行所有子任务
        async for event in executor_agent.execute_subtasks(structured_plan, state):
            event_type = event.get('type')
            event_data = event.get('data', {})
            
            logger.debug(f"Executor node: Processing event type: {event_type}")
            
            # 🧹 简化：只实时转发task_list_event，不再收集到state
            if event_type == 'task_list_event':
                logger.info(f"Executor node: ✅ Real-time forwarding task_list_event")
                
                # 🎯 使用 LangGraph writer 发送实时 TaskList 事件
                if writer:
                    try:
                        logger.debug(f"Executor node: 🎯 Event data details: {event_data}")
                        # ✅ 修复格式：_handle_custom_events期望的格式
                        writer({
                            "task_list_event": {
                                "data": event_data
                            }
                        })
                        logger.debug(f"Executor node: ✅ Successfully sent real-time TaskListEvent via Writer")
                    except Exception as e:
                        logger.error(f"Executor node: ❌ Failed to send TaskListEvent via Writer: {e}")
                else:
                    logger.warning(f"Executor node: ❌ Writer not available for real-time TaskList streaming")
                
            elif event_type == 'jupyter_event':
                # 收集Jupyter事件
                jupyter_events.append(event_data)
                logger.info(f"jupyter_event_content_from_executor: {event_data}")
                
                
                # 🎯 使用 LangGraph writer 发送实时 Jupyter 事件
                if writer:
                    try:
                        logger.debug(f"Executor node: 🎯 About to send JupyterEvent via Writer: {event_data}")
                        writer({
                            "jupyter_event": event_data
                        })
                        logger.debug(f"Executor node: ✅ Successfully sent real-time JupyterEvent via Writer: {event_data.get('cell_id')}")
                    except Exception as e:
                        logger.error(f"Executor node: ❌ Failed to send JupyterEvent via Writer: {e}")
                else:
                    logger.warning(f"Executor node: ❌ Writer not available for real-time streaming")
                      
            elif event_type == 'reference_event':
                # 🎯 新增：处理reference_event并通过writer转发
                logger.info(f"Executor node: Reference event received: {event_data}")
                if writer:
                    try:
                        writer({
                            "reference_event": {
                                "data": event_data
                            }
                        })
                        logger.debug(f"Executor node: ✅ Successfully sent ReferenceEvent via Writer")
                    except Exception as e:
                        logger.error(f"Executor node: ❌ Failed to send ReferenceEvent via Writer: {e}")
                else:
                    logger.warning(f"Executor node: ❌ Writer not available for ReferenceEvent streaming")
                      
            elif event_type == 'error_event':
                # 🎯 新增：处理error_event并通过writer发送
                logger.error(f"Executor node: Error event received: {event_data}")
                if writer:
                    try:
                        writer({
                            "error_event": {
                                "data": event_data
                            }
                        })
                        logger.debug(f"Executor node: ✅ Successfully sent ErrorEvent via Writer")
                    except Exception as e:
                        logger.error(f"Executor node: ❌ Failed to send ErrorEvent via Writer: {e}")
                # 在返回错误状态前，通过统一的summary流式生成（内部按失败选择错误提示词）
                try:
                    await generate_execution_summary_stream(
                        state,
                        llm_client,
                        jupyter_events,
                        writer,
                        data_info=None
                    )
                except Exception as e:
                    logger.error(f"Executor node: failed to stream summary on error: {e}")
                # 直接返回错误状态
                return {
                    **state,
                    'status': 'execution_error',
                    'error_message': event_data.get('error', 'Unknown error'),
                    'execution_error': event_data.get('error', 'Unknown error')
                }
            else:
                logger.warning(f"Executor node: ⚠️ Unknown event type: {event_type}")
        
        logger.info(f"Executor completed. Generated {len(jupyter_events)} jupyter events.")
        
        
        summary_content = ""
        failed = _is_execution_failed(state, jupyter_events)
        if jupyter_events:
            try:
                if failed:
                    summary_content = await generate_execution_summary_stream(
                        state, llm_client, jupyter_events, writer, data_info=None
                    )
                    logger.info("Generated and sent error analysis stream")
                else:
                    # 从agent获取data_info（可为空）
                    data_info = None
                    if executor_agent.react_agent and hasattr(executor_agent.react_agent, 'data_info'):
                        data_info = executor_agent.react_agent.data_info
                    summary_content = await generate_execution_summary_stream(
                        state, llm_client, jupyter_events, writer, data_info
                    )
                    logger.info("Generated and sent execution summary stream")
            except Exception as e:
                logger.error(f"Failed to generate final summary stream: {e}")
        else:
            logger.info("Skipping summary generation: no jupyter events")
 
        # 🎯 为document_query任务发送最终reference_event
        task_category = get_task_category(state)
        if task_category == "document_query" and writer and jupyter_events and not failed:
            _send_final_reference_event(summary_content, jupyter_events, writer)
        
        # 返回简化的状态
        return {
            **state,
            'jupyter_events': jupyter_events,
            'final_summary_content': summary_content if summary_content else None,  # 🔧 保存summary内容供conversation_history使用
            'status': 'execution_complete'
        }
    
    except Exception as e:
        logger.error(f"Error in executor node: {e}", exc_info=True)
        
        # 🎯 新增：通过writer发送错误事件
        if writer:
            try:
                error_data = {
                    "error": str(e),
                    "error_type": "executor_initialization_error",
                    "node": "executor"
                }
                writer({
                    "error_event": {
                        "data": error_data
                    }
                })
                logger.debug(f"Executor node: ✅ Successfully sent ErrorEvent via Writer for exception")
            except Exception as writer_e:
                logger.error(f"Executor node: ❌ Failed to send ErrorEvent via Writer: {writer_e}")
        
        return {
            **state,
            'status': 'execution_error',
            'error_message': str(e),
            'execution_error': str(e)
        }

def present_output_node(state: AgentState) -> AgentState:
    logger.info("--- Node: Present Output ---")
    # ----- MCPManager DEBUG LOG -----
    logger.debug(f"Node present_output_node START: MCPManager in state is present: {bool(state.get('mcp_manager'))} | ID: {id(state.get('mcp_manager')) if state.get('mcp_manager') else 'N/A'}")
    
    if state.get('stop_requested'):
        logger.info("Present Output Node: Handling stop request.")
        state['final_output_for_user'] = "任务已根据您的请求终止。"
        state['execution_error'] = None # 清理错误信息，因为这是正常终止
        state['stop_requested'] = False # 重置标志，为下一次对话做准备
        return state
    
    output_message = None

    if state.get('execution_error'):
        output_message = f"An error occurred: {state.get('execution_error')}"
        logger.error(f"Present Output Node: Presenting execution error: '{output_message}'")
    # elif state.get('needs_clarification') and state.get('clarification_question'):
    #     output_message = state.get('clarification_question')
    elif state.get('final_output_for_user') is not None:
        output_message = state.get('final_output_for_user')
    elif state.get('identified_intent_name') == "end_conversation":
        output_message = "Conversation ended."
        logger.info("Present Output Node: Presenting end of conversation message.")
    else:
        logger.warning(f"Present Output Node: No specific message determined, using fallback. Current final_output_for_user: '{state.get('final_output_for_user')}', Error: '{state.get('execution_error')}'")

    state['final_output_for_user'] = output_message

    logger.info(f"Present Output Node: Final determined message for user: '{output_message}'")
    
    # 🧹 移除重复的状态清理逻辑（现在由 AgentService 统一处理）
    # 注释：这些清理逻辑已经移动到 AgentService._cleanup_temporary_state() 中
    # 注意：不清理 conversation_history, detected_language, mcp_manager, ctx 等需要保持的状态
    
    # logger.debug("🔄 Cleaning up task-specific state for next conversation turn")
    
    # # 清理规划和执行相关的临时状态
    # cleanup_keys = [
    #     'current_plan',
    #     'jupyter_events', 
    #     'needs_clarification',
    #     'clarification_question',
    #     'execution_error',
    #     'final_output_for_user',
    #     
    #     'intent_recognizer_slot_state',
    #     'identified_intent_name',
    #     'identified_intent_entities'
    # ]

    
    # for key in cleanup_keys:
    #     if key in state and state[key] is not None:
    #         state[key] = None
            
    # # 重置状态标志
    # state['stop_requested'] = False
    
    # ----- MCPManager DEBUG LOG -----
    logger.debug(f"Node present_output_node END: MCPManager in state is present: {bool(state.get('mcp_manager'))} | ID: {id(state.get('mcp_manager')) if state.get('mcp_manager') else 'N/A'}")
    return state
