# datascience_agent/state.py
from typing import TypedDict, List, Dict, Any, Optional
import asyncio

class AgentState(TypedDict):
    """
    Defines the shared state for the graph.
    """
    stop_requested: Optional[bool]
    # Input and conversation history
    current_user_input: Optional[str]
    conversation_history: List[Dict[str, str]]

    # User feedback related
    user_feedback_on_last_result: Optional[str]
    # is_feedback_request: bool

    # Intent recognition state
    intent_recognizer_slot_state: Optional[Dict[str, Any]]

    # Final identified intent details for planner
    # Todo: 这里应该和slot state 合并 
    identified_intent_name: Optional[str]
    identified_intent_entities: Optional[Dict[str, Any]]

    # Control flags for conversation flow
    needs_clarification: bool
    # clarification_question: Optional[str]

    # Planning results
    current_plan: Optional[Dict[str, Any]] 

    # Execution results
    execution_error: Optional[str]

    # Output to user
    
    # Todo: 这里应该和final_summary content 合并 
    final_output_for_user: Optional[str]

    # Available data sources
    # available_data_sources: Optional[List[Dict[str, Any]]] 
    active_dataset_id: Optional[str]
    
    # Fetched database schema
    database_schema: Optional[Dict[str, List[Dict[str, Any]]]] 

    # 动态表格选择相关字段
    # all_available_tables: Optional[List[str]]  # 数据库中所有可用的表格列表
    # selected_tables: Optional[List[str]]       # 用户选择的表格列表
    # needs_table_selection: Optional[bool]      # 是否需要用户选择表格

    # Fields for FinalSummaryEvent
    final_summary_content: Optional[str] 

    # Support for MCPManager
    mcp_manager: Optional[Any]

    # Context for deep thinking configuration
    ctx: Optional[Any]

    # Language detection
    detected_language: Optional[str]

    # Jupyter events for batch processing (backup mechanism)
    jupyter_events: Optional[List[Dict[str, Any]]]


def get_task_category(state: AgentState) -> Optional[str]:
    """获取任务类别"""
    slot_state = state.get('intent_recognizer_slot_state', {})
    category = slot_state.get('content', {}).get('task', {}).get('category', "")

    return category
