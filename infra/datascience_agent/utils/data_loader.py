# datascience_agent/utils/data_loader.py
import logging
import asyncio
from typing import Dict, Any, List, Optional

from infra.mcp.manager.mcp_manager import MCPManager
from infra.mcp.nl2sql.mcp_client.dlc import DLCListTablesReq, DLCListTablesRsp, DLCExecuteQueryReq, DLCExecuteQueryRsp

# Assuming DLCListTablesReq and DLCListTablesRsp are correctly importable
# If they are in infra.mcp.nl2sql.mcp_client.client, the import would be:


logger = logging.getLogger(__name__)

class DataLoader:
    """
    Loads database schema information using MCP tools.
    """
    def __init__(self, mcp_manager: MCPManager):
        """
        Initializes the DataLoader.
        Args:
            mcp_manager: An instance of MCPManager to interact with MCP tools.
        """
        self.mcp_manager = mcp_manager
        logger.info("DataLoader initialized with MCPManager.")

    def _get_dlc_instance_name(self) -> str:
        """
        获取DLC类型的MCP实例名称
        
        Returns:
            DLC实例的名称，如果未找到则返回默认值
        """
        try:
            # 遍历已注册的MCP服务器，查找DLC类型的服务
            for server_name, server_info in self.mcp_manager.mcp_servers.items():
                # 如果是SSE连接且URL包含DLC特征，认为是DLC服务
                if server_info.get('link') == 'see' and server_info.get('url'):
                    url = server_info.get('url', '')
                    # DLC服务的URL通常包含特定的路径或端口特征
                    if '/sse?' in url and 'auth_token' in url:
                        logger.info(f"Found DLC instance: {server_name}")
                        return server_name
            
            # 如果没有找到特定的DLC实例，使用第一个SSE实例
            for server_name, server_info in self.mcp_manager.mcp_servers.items():
                if server_info.get('link') == 'see':
                    logger.warning(f"Using fallback DLC instance: {server_name}")
                    return server_name
            
            # 最后的fallback
            logger.warning("No DLC instance found, using default: mcp-instance-001")
            return "mcp-instance-001"
            
        except Exception as e:
            logger.error(f"Error getting DLC instance name: {e}")
            return "mcp-instance-001"

    async def get_database_schema_async(self, dataset_id: str) -> Dict[str, Any]:
        """
        异步获取数据库schema信息
        
        Args:
            dataset_id: The dataset identifier to get schema for
            
        Returns:
            Dict containing database schema information
        """
        try:
            logger.info(f"Getting database schema for dataset_id: {dataset_id}")
            
            # 获取已知的表名列表
            known_table_names = self._get_known_table_names()
            
            # Default configuration - these should be retrieved from a configuration service
            # based on the dataset_id
            database_name = "nl2sql_test"  # Should be mapped from dataset_id
            datasource_name = "DataLakeCatalog"  # Should be mapped from dataset_id  
            engine_name = "data-agent-exp-dev"  # Should be mapped from dataset_id
            
            # 获取指定表的schema信息
            list_tables_req = DLCListTablesReq(
                DatabaseName=database_name,
                DatasourceConnectionName=datasource_name,
                TableNames=known_table_names  # 使用已知的表名列表
            )
            
            logger.info(f"Calling MCP tool {self._get_dlc_instance_name()}__DLCListTables with params: {list_tables_req.model_dump()}")
            
            # Call MCP tool to get table schema information
            tables_result = await self.mcp_manager.call_tool(
                f"{self._get_dlc_instance_name()}__DLCListTables", 
                arguments=list_tables_req.model_dump()
            )
            
            logger.info(f"DLCListTables result: {tables_result}")
            
            if not tables_result:
                logger.warning(f"No schema information returned for tables: {known_table_names}")
                return {
                    'tables': {},
                    'database_name': database_name,
                    'datasource_name': datasource_name,
                    'engine_name': engine_name,
                    'table_names': [],
                    'total_tables': 0
                }
            
            # 解析MCP响应结果
            parsed_tables = self._parse_dlc_list_tables_result(tables_result)
            
            # Process table information to build schema
            schema_info = {}
            
            # 准备并行采样任务
            sampling_tasks = []
            table_infos = []
            
            # 收集所有需要采样的表信息
            for table_info in parsed_tables:
                table_name = table_info.TableBaseInfo.TableName
                table_infos.append(table_info)
                
                # 创建采样任务
                sampling_task = self.sample_table_data_async(
                    table_name=table_name,
                    database_name=database_name,
                    datasource_name=datasource_name,
                    engine_name=engine_name,
                    sample_size=10  # 10 rows as requested
                )
                sampling_tasks.append(sampling_task)
            
            # 并行执行所有采样任务
            logger.info(f"Starting parallel sampling for {len(sampling_tasks)} tables")
            sample_results = await asyncio.gather(*sampling_tasks, return_exceptions=True)
            
            # 处理采样结果，构建schema信息
            for table_info, sample_result in zip(table_infos, sample_results):
                table_name = table_info.TableBaseInfo.TableName
                
                # Build column information
                columns = []
                for col in table_info.Columns:
                    columns.append({
                        'name': col.Name,
                        'type': col.Type,
                        'comment': getattr(col, 'Comment', '')
                    })
                
                # 处理采样结果（检查是否有异常）
                if isinstance(sample_result, Exception):
                    logger.error(f"Failed to sample table {table_name}: {sample_result}")
                    sample_result = {
                        'columns': [],
                        'sample_data': [],
                        'row_count': 0
                    }
                
                # Build comprehensive table info
                schema_info[table_name] = {
                    # Basic table metadata
                    'table_name': table_name,
                    'table_comment': getattr(table_info.TableBaseInfo, 'Comment', ''),
                    'column_count': len(columns),
                    'columns': columns,
                    
                    # Sample data
                    'sample_data': {
                        'column_names': sample_result['columns'],
                        'rows': sample_result['sample_data'],
                        'row_count': sample_result['row_count']
                    },
                    
                    # Data preview (formatted for easy reading)
                    'data_preview': self._format_table_preview(table_name, sample_result)
                }
            
            logger.info(f"Successfully retrieved schema with sample data for {len(schema_info)} tables")
            
            return {
                'tables': schema_info,
                'database_name': database_name,
                'datasource_name': datasource_name,
                'engine_name': engine_name,
                'table_names': list(schema_info.keys()),
                'total_tables': len(schema_info)
            }
            
        except Exception as e:
            logger.error(f"Failed to get database schema for dataset_id {dataset_id}: {str(e)}", exc_info=True)
            # Return empty schema on error
            return {
                'tables': {},
                'database_name': '',
                'datasource_name': '',
                'engine_name': '',
                'table_names': [],
                'total_tables': 0
            }

    def get_database_schema(self, dataset_id: str) -> Dict[str, Any]:
        """
        同步版本的数据库schema获取方法，内部调用异步版本
        
        Args:
            dataset_id: The dataset identifier to get schema for
            
        Returns:
            Dict containing database schema information
        """
        try:
            # 检查是否在异步上下文中
            loop = asyncio.get_running_loop()
            # 如果在异步上下文中，需要使用run_in_executor避免嵌套事件循环
            logger.warning("DataLoader.get_database_schema called from async context, this may cause issues")
            # 暂时返回空结果，建议调用者使用异步版本
            return {
                'tables': {},
                'database_name': 'nl2sql_test',
                'datasource_name': 'DataLakeCatalog',
                'engine_name': 'data-agent-exp-dev',
                'table_names': [],
                'total_tables': 0
            }
        except RuntimeError:
            # 不在异步上下文中，可以直接使用asyncio.run
            return asyncio.run(self.get_database_schema_async(dataset_id))

    def _parse_dlc_list_tables_result(self, tables_result: Any) -> List[DLCListTablesRsp]:
        """
        解析MCP工具返回的DLCListTables结果
        
        Args:
            tables_result: MCP工具返回的原始结果
            
        Returns:
            解析后的表列表
        """
        try:
            # 检查返回结果的格式
            if hasattr(tables_result, 'content') and tables_result.content:
                # 使用DLCListTablesRsp的解析方法
                return DLCListTablesRsp.parse_raw_result(tables_result.content)
            elif isinstance(tables_result, list):
                # 如果直接返回表列表
                parsed_tables = []
                for item in tables_result:
                    if hasattr(item, 'TableBaseInfo') and hasattr(item, 'Columns'):
                        parsed_tables.append(item)
                    else:
                        # 尝试从字典构造
                        parsed_tables.append(DLCListTablesRsp.model_validate(item))
                return parsed_tables
            else:
                logger.warning(f"Unexpected tables_result format: {type(tables_result)}")
                return []
        except Exception as e:
            logger.error(f"Failed to parse DLC list tables result: {str(e)}", exc_info=True)
            return []

    def _format_table_preview(self, table_name: str, sample_result: Dict[str, Any]) -> str:
        """
        Format table sample data for easy reading.
        
        Args:
            table_name: Name of the table
            sample_result: Sample data result from sample_table_data
            
        Returns:
            Formatted string preview of the table
        """
        if sample_result['row_count'] == 0:
            return f"Table {table_name}: No data available"
        
        preview = f"Table: {table_name}\n"
        preview += f"Columns ({len(sample_result['columns'])}): {', '.join(sample_result['columns'])}\n"
        preview += f"Sample data ({sample_result['row_count']} rows):\n"
        
        # Add sample rows
        for i, row in enumerate(sample_result['sample_data']):
            row_dict = dict(zip(sample_result['columns'], row))
            preview += f"  Row {i+1}: {row_dict}\n"
            
        return preview

    async def sample_table_data_async(self, 
                                     table_name: str, 
                                     database_name: str, 
                                     datasource_name: str, 
                                     engine_name: str,
                                     sample_size: int = 25) -> Dict[str, Any]:
        """
        异步版本的表数据采样方法
        
        Args:
            table_name: Name of the table to sample
            database_name: Database name
            datasource_name: Datasource connection name  
            engine_name: Data engine name
            sample_size: Number of rows to sample (default: 25)
            
        Returns:
            Dict containing:
            - 'columns': List of column names
            - 'sample_data': List of sample rows
            - 'row_count': Number of sampled rows
        """
        try:
            logger.info(f"Sampling {sample_size} rows from table: {table_name}")
            
            # Construct sampling SQL query
            sampling_sql = f"SELECT * FROM {table_name} LIMIT {sample_size}"
            
            # Prepare DLC query request
            query_req = DLCExecuteQueryReq(
                SparkSQL=sampling_sql,
                DatabaseName=database_name,
                DatasourceConnectionName=datasource_name,
                DataEngineName=engine_name
            )
            
            logger.info(f"Executing sampling query: {sampling_sql}")
            
            # Execute sampling query through MCP - 使用正确的工具名格式
            result = await self.mcp_manager.call_tool(
                f"{self._get_dlc_instance_name()}__DLCExecuteQuery", 
                arguments=query_req.model_dump()
            )
            
            logger.info(f"DLCExecuteQuery result for table {table_name}: {result}")
            
            # 解析查询结果
            parsed_result = self._parse_dlc_execute_query_result(result)
            
            if not parsed_result or not parsed_result.ResultSchema:
                logger.warning(f"No data returned for table {table_name}")
                return {
                    'columns': [],
                    'sample_data': [],
                    'row_count': 0
                }
            
            # Extract column names
            columns = [col.Name for col in parsed_result.ResultSchema]
            
            # Extract sample data
            sample_data = parsed_result.ResultSet if parsed_result.ResultSet else []
            
            logger.info(f"Successfully sampled {len(sample_data)} rows from {table_name}")
            
            return {
                'columns': columns,
                'sample_data': sample_data,
                'row_count': len(sample_data)
            }
            
        except Exception as e:
            logger.error(f"Failed to sample data from table {table_name}: {str(e)}", exc_info=True)
            return {
                'columns': [],
                'sample_data': [],
                'row_count': 0
            }

    def sample_table_data(self, 
                         table_name: str, 
                         database_name: str, 
                         datasource_name: str, 
                         engine_name: str,
                         sample_size: int = 25) -> Dict[str, Any]:
        """
        同步版本的表数据采样方法，内部调用异步版本
        
        Args:
            table_name: Name of the table to sample
            database_name: Database name
            datasource_name: Datasource connection name  
            engine_name: Data engine name
            sample_size: Number of rows to sample (default: 25)
            
        Returns:
            Dict containing:
            - 'columns': List of column names
            - 'sample_data': List of sample rows
            - 'row_count': Number of sampled rows
        """
        try:
            # 检查是否在异步上下文中
            loop = asyncio.get_running_loop()
            # 如果在异步上下文中，返回空结果
            logger.warning(f"DataLoader.sample_table_data called from async context for table {table_name}")
            return {
                'columns': [],
                'sample_data': [],
                'row_count': 0
            }
        except RuntimeError:
            # 不在异步上下文中，可以直接使用asyncio.run
            return asyncio.run(self.sample_table_data_async(
                table_name, database_name, datasource_name, engine_name, sample_size
            ))

    def _parse_dlc_execute_query_result(self, query_result: Any) -> Optional[DLCExecuteQueryRsp]:
        """
        解析MCP工具返回的DLCExecuteQuery结果
        
        Args:
            query_result: MCP工具返回的原始结果
            
        Returns:
            解析后的查询结果
        """
        try:
            # 检查返回结果的格式
            if hasattr(query_result, 'content') and query_result.content:
                # 使用DLCExecuteQueryRsp的解析方法
                return DLCExecuteQueryRsp.parse_raw_result(query_result.content)
            elif isinstance(query_result, dict):
                # 如果直接返回字典格式
                return DLCExecuteQueryRsp.model_validate(query_result)
            else:
                logger.warning(f"Unexpected query_result format: {type(query_result)}")
                return None
        except Exception as e:
            logger.error(f"Failed to parse DLC execute query result: {str(e)}", exc_info=True)
            return None

    def _get_known_table_names(self) -> List[str]:
        """
        获取已知的表名列表
        
        Returns:
            已知表名的列表
        """
        return ['products', 'orders', 'customers']

    def sample_multiple_tables(self, 
                              database_name: str,
                              datasource_name: str, 
                              engine_name: str,
                              sample_size: int = 25) -> Dict[str, Dict[str, Any]]:
        """
        Sample data from all known tables for comprehensive analysis.
        
        Args:
            database_name: Database name
            datasource_name: Datasource connection name
            engine_name: Data engine name
            sample_size: Number of rows to sample per table (default: 25)
            
        Returns:
            Dict mapping table names to their sample data
        """
        results = {}
        table_names = self._get_known_table_names()
        
        for table_name in table_names:
            logger.info(f"Sampling table: {table_name}")
            sample_result = self.sample_table_data(
                table_name=table_name,
                database_name=database_name,
                datasource_name=datasource_name,
                engine_name=engine_name,
                sample_size=sample_size
            )
            results[table_name] = sample_result
            
        logger.info(f"Completed sampling for {len(results)} tables")
        return results

    def get_table_preview(self, 
                         table_name: str,
                         database_name: str,
                         datasource_name: str,
                         engine_name: str) -> str:
        """
        Get a formatted preview of table data for LLM consumption.
        
        Args:
            table_name: Name of the table
            database_name: Database name
            datasource_name: Datasource connection name
            engine_name: Data engine name
            
        Returns:
            Formatted string representation of table sample
        """
        sample_data = self.sample_table_data(
            table_name=table_name,
            database_name=database_name, 
            datasource_name=datasource_name,
            engine_name=engine_name,
            sample_size=25
        )
        
        if sample_data['row_count'] == 0:
            return f"Table {table_name}: No data available"
        
        # Format as readable text for LLM
        preview = f"Table: {table_name}\n"
        preview += f"Columns: {', '.join(sample_data['columns'])}\n"
        preview += f"Sample data ({sample_data['row_count']} rows):\n"
        
        # Add sample rows (limit to first 5 for readability)
        for i, row in enumerate(sample_data['sample_data'][:5]):
            preview += f"Row {i+1}: {dict(zip(sample_data['columns'], row))}\n"
            
        if sample_data['row_count'] > 5:
            preview += f"... and {sample_data['row_count'] - 5} more rows\n"
            
        return preview
