from llm import llm_for_openai
from typing import Optional
from common.logger.logger import logger

def generate_bg_prompt(app_data: dict, user_input: Optional[str] = None) -> str:
    """
    根据App信息和用户需求生成背景提示词

    参数:
        llm: 语言模型调用函数
        app_data: 包含App信息的字典
        user_input: 用户对生成背景图的特殊要求（可选）

    返回:
        优化后的图像提示词字符串
    """
    req_type = "用户特殊要求" if user_input else "自动生成"
    logger.info(f"[背景生成] 模式: {req_type} | 内容: {user_input or '无'}")

    name = app_data.get("name", "应用")
    description = app_data.get("description", "")
    features = "，".join(app_data.get("features", [])) or "功能特点"

    messages = [
        {
            "role": "system",
            "content": """
            你是一位专业的广告设计师，具有较高的审美能力，擅长为应用设计创意广告背景图。
            请严格遵循以下规则：
            1. 考虑用户明确提出的视觉需求
            2. 结合应用的核心功能特点
            3. 不使用logo或文字元素
            4. 避免任何解释性文字，仅提供背景图的描述，控制在200字符内
            """
        },
        {
            "role": "user",
            "content": f"""
            应用名称：{name}
            功能特点：{features}
            应用描述：{description}
            用户需求：{user_input or "请根据应用特点自由发挥"}
            """
        }
    ]

    try:
        response = llm_for_openai(
            messages=messages,
            max_tokens=300
        )

        cleaned_prompt = response.split("：")[-1].replace('"', '').strip()
        logger.info(f"[背景生成] 结果: {cleaned_prompt}")
        return cleaned_prompt

    except Exception as e:
        logger.error(f"[背景生成] 失败: {str(e)}")
        # 降级方案：返回基础描述
        return f"{name}的广告背景图，突出{features}特点"

def generate_usage_prompt(app_data: dict, user_input: Optional[str] = None) -> str:
    """
    生成前景图使用场景提示词（封装LLM调用版）

    参数:
        app_data: {
            "name": str,         # 应用名称
            "description": str,  # 应用描述
            "features": list     # 功能特点
        }
        user_input: 用户特殊需求(可选)

    返回:
        优化后的前景图提示词

    异常:
        返回降级生成的默认提示词
    """
    req_type = "定制需求" if user_input else "自动生成"
    logger.info(f"[前景图生成] 模式: {req_type} | 输入: {user_input or '无特殊要求'}")

    name = app_data.get("name", "该应用")
    features = "、".join(app_data.get("features", [])) or "核心功能"
    description = app_data.get("description", "")

    messages = [
        {
            "role": "system",
            "content": """
                你是一位资深广告创意设计师，擅长创作具视觉冲击力且富有情感共鸣的应用前景图。
                请严格遵循以下规则：
                1. 考虑用户明确提出的视觉需求
                2. 必须结合应用的核心功能特点与目标用户
                3. 不使用logo或文字元素
                4. 避免任何解释性文字，仅提供画面内容的描述，控制在100字符内
            """
        },
        {
            "role": "user",
            "content": f"""
            应用名称：{name}
            功能特点：{features}
            应用描述：{description}
            用户特殊要求：{user_input or "请自由创作"}
            请根据应用的功能特点与目标用户，设计一张具有创意的广告图。
            """
        }
    ]

    try:
        response = llm_for_openai(
            messages=messages
            # temperature=0.7,
        )

        cleaned_prompt = (
            response.split("：")[-1]  # 提取冒号后内容
            .replace('"', '')  # 移除引号
            .replace("'", "")
            .strip()
        )
        logger.info(f"[前景图生成] 结果: {cleaned_prompt}")
        return cleaned_prompt

    except Exception as e:
        logger.error(f"[前景图生成] 异常: {str(e)}")
        # 降级方案
        return f"人物正在使用{name}的{features}功能，表情愉悦"
