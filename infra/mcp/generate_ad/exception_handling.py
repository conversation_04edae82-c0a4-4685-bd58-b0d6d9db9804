from PIL import Image, ImageDraw, ImageFont

def generate_error_placeholder(message: str, font) -> Image.Image:
    """生成错误占位图"""
    img = Image.new('RGB', (1080, 1920), color=(40, 40, 40))
    draw = ImageDraw.Draw(img)

    # 添加错误信息
    text = f"广告生成失败: {message[:100]}"
    draw.text((100, 500), text, font=font, fill=(255, 100, 100))

    # 添加操作指引
    draw.text((100, 600), "请检查：", font=font, fill=(200, 200, 255))
    draw.text((150, 650), "1. API密钥是否有效", font=font, fill=(200, 200, 255))
    draw.text((150, 700), "2. 应用信息是否完整", font=font, fill=(200, 200, 255))
    draw.text((150, 750), "3. 网络连接是否正常", font=font, fill=(200, 200, 255))

    return img
