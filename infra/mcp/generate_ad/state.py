from prompt_polish import *
from core import *

class AdDesignState:
    def __init__(self):
        self.app_info = None  # 存储提取的应用信息
        self.slogan = None  # 存储生成的广告文案
        self.bg_prompt = None  # 存储背景图描述
        self.use_prompt = None  # 存储使用场景图描述
        self.bg_url = None  # 存储生成的背景图像url
        self.use_url = None  # 存储生成的使用场景图像url

        self.ad_type = None  # 存储当前布局类型，默认横版
        self.display_elements = {  # 显示控制
            'icon': True,  # 是否显示图标
            'rating': True,  # 是否显示评分
        }
        self.history = []  # 存储修改历史记录

    def initialize(self, app_info: dict):
        """初始化状态"""
        self.app_info = app_info
        self.ad_type = app_info.get("ad_type", "horizontal")
        # 初始生成广告语和背景描述
        self.slogan = generate_slogan_tool(app_info) or "发现全新体验，立即下载！"
        self.bg_prompt = generate_bg_prompt(app_info)
        self.use_prompt = generate_usage_prompt(app_info)

        # 生成初始图片URL
        self.bg_url = generate_image_url(self.bg_prompt, self.get_size("bg"))
        self.use_url = generate_image_url(self.use_prompt, self.get_size("use"))

    def get_size(self, image_type):
        """获取不同图片类型对应的尺寸"""
        if image_type == "bg":
            return "1280x720" if self.ad_type == "horizontal" else "768x1280"
        elif image_type == "use":
            return "1152x864"
        return "1024x768"  # 默认尺寸

    def to_dict(self):
        """将状态转换为字典"""
        return {
            "app_info": self.app_info,
            "slogan": self.slogan,
            "bg_prompt": self.bg_prompt,
            "use_prompt": self.use_prompt,
            "bg_url": self.bg_url,
            "use_url": self.use_url,
            "display_elements": self.display_elements,
            "history": self.history
        }

    def update_from_dict(self, state_dict):
        """从字典更新状态"""
        for key, value in state_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                logger.error(f"未知状态键: {key}")

class StateManager:
    def __init__(self):
        self.current_state = AdDesignState()
        self.history = []

    def update_state(self, state):
        """更新当前状态，并保存历史"""
        self.history.append(self.current_state.to_dict())
        self.current_state = state

    def restore_state(self, index=-1):
        """恢复历史状态"""
        if self.history:
            self.current_state = self.history[index]
            return True
        return False

    def initialize(self, app_info: dict):
        """初始化状态"""
        self.state.initialize(app_info)

    def reset_state(self):
        """重置状态"""
        self.current_state = AdDesignState()
        self.history = []
