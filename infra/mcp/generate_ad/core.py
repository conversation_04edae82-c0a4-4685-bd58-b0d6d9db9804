from langchain_core.exceptions import LangChainException
from prompt_polish import *
from draw_element import *
from llm import *
from common.logger.logger import logger

def generate_slogan_tool(app_info: dict) -> Optional[str]:
    """生成应用广告语的专用工具"""
    try:
        features = app_info.get("features", [])
        features_str = "，".join(features) if features else "功能强大"

        messages = [{
            "role": "user",
            "content": (
                f"作为专业广告文案，请为{app_info['name']}生成1条精品广告语。\n"
                f"应用介绍：{app_info['description']}\n"
                f"主要特点：{features_str}\n"
                f"格式要求：使用中文且不超过15个词，以感叹号结尾\n"
                f"示例：智能管理您的每一刻，效率提升看得见！"
            )
        }]

        response = llm_for_openai(messages)

        logger.info(f"生成的广告语: {response}")
        return response

    except LangChainException as e:
        logger.error(f"模型调用失败: {str(e)}")
        return "发现全新体验，立即下载！"  # 返回单条默认值
    except Exception as e:
        logger.error(f"未知错误: {str(e)}")
        return None  # 错误时返回空值

def generate_image_url(prompt, size) -> Optional[str]:
    try:
        # 调用函数生成图像url
        result = generate_image_hunyuan(
            prompt=prompt + "写实风格。",
            revise=False,
            size=size
        )
        if not result:
            raise RuntimeError("大模型返回空结果")
        if "data" not in result or not result["data"]:
            raise ValueError("API响应格式错误")

        image_url = result["data"][0].get("url")
        if not image_url:
            raise ValueError("未找到图片URL")
        logger.info(f"获取图片URL: {image_url}")
        if "revised_prompt" in result["data"][0]:
            logger.info(f"修改后的prompt: {result["data"][0].get("revised_prompt")}")
        return image_url

    except Exception as e:
        logger.error(f"背景生成失败: {str(e)}")
        return None

def horizontal_layout_design(state: dict, font) -> Image.Image:
    """版式设计模块"""
    app_data = state["app_info"]

    bg_image = open_image_tool(state["bg_url"], "1280x720")
    use_image = open_image_tool(state["use_url"], "1152x864")
    # 创建绘图上下文并确保为RGBA模式以支持透明度
    canvas = bg_image.copy()

    # 添加半透明白色蒙版（距离四边50像素）
    overlay_width = canvas.width - 100  # 1280 - 100 = 1180
    overlay_height = canvas.height - 100  # 720 - 100 = 620
    overlay = Image.new('RGBA', (overlay_width, overlay_height), (255, 255, 255, 155))  # 半透明白色
    canvas.paste(overlay, (50, 50), overlay)  # 居中放置蒙版

    # 创建新的绘图上下文（蒙版更改后需要重新创建）
    draw = ImageDraw.Draw(canvas)

    # 添加使用场景图
    use_image = use_image.resize((576, 432))
    if use_image.mode != "RGBA":
        use_image = use_image.convert("RGBA")
    canvas.paste(use_image, (150, 150), use_image)

    # 添加应用图标
    if state["display_elements"].get("icon") and app_data.get("icon_url"):
        icon = load_icon(app_data.get("icon_url")).resize((100, 100))
        canvas.paste(icon, (935, 150), icon)
    # 渲染标题
    render_text(
        font,
        draw,
        text=app_data["name"],
        position=(935, 260),
        max_width=100,
        font_size=30
    )
    # 渲染评分
    if state["display_elements"].get("rating") and app_data.get("score"):
        draw_star_rating(draw, app_data["score"], (915, 330), font)

    # 渲染广告语
    render_text(
        font,
        draw,
        text=state["slogan"],
        position=(930, 360),
        max_width=130,
        font_size=18
    )

    return canvas

def vertical_layout_design(state: dict, font) -> Image.Image:
    """版式设计模块"""
    app_data = state["app_info"]
    bg_image = open_image_tool(state["bg_url"], "768x1280")
    use_image = open_image_tool(state["use_url"], "1152x864")

    # 创建绘图上下文并确保为RGBA模式以支持透明度
    canvas = bg_image.copy()

    # # 添加半透明白色蒙版（距离四边50像素）
    overlay_width = canvas.width - 100  # 1280 - 100 = 1180
    overlay_height = canvas.height - 200  # 720 - 100 = 620
    overlay = Image.new('RGBA', (overlay_width, overlay_height), (255, 255, 255, 155))  # 半透明白色
    canvas.paste(overlay, (50, 100), overlay)  # 居中放置蒙版，左上角坐标为(50, 50)

    x = 340
    delta_y = 50

    # # 添加半透明白色蒙版(包围图标&广告语)
    # overlay_width = 400
    # overlay_height = 350
    # overlay = Image.new('RGBA', (overlay_width, overlay_height), (255, 255, 255, 175))  # 半透明白色
    # canvas.paste(overlay, (200, 680+delta_y), overlay)  # 居中放置蒙版

    # 创建新的绘图上下文（蒙版更改后需要重新创建）
    draw = ImageDraw.Draw(canvas)

    # 添加使用场景图
    use_image = use_image.resize((576, 432))
    if use_image.mode != "RGBA":
        use_image = use_image.convert("RGBA")
    canvas.paste(use_image, (100, 200), use_image)

    # 添加应用图标
    if state["display_elements"].get("icon") and app_data.get("icon_url"):
        icon = load_icon(app_data.get("icon_url")).resize((100, 100))
        canvas.paste(icon, (x, 700 + delta_y), icon)

    # 渲染标题
    render_text(
        font,
        draw,
        text=app_data["name"],
        position=(x, 820 + delta_y),
        max_width=100,
        font_size=34
    )

    if state["display_elements"].get("rating") and app_data.get("score"):
        draw_star_rating(draw, app_data["score"], (x - 20, 900 + delta_y), font)

        # 渲染广告语
    render_text(
        font,
        draw,
        text=state["slogan"],
        position=(x - 15, 930 + delta_y),
        max_width=138,
        font_size=28
    )

    return canvas
