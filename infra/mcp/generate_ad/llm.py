import requests
import json
from typing import Dict, Any, Optional
from common.share.config import appConfig
from openai import OpenAI
from common.logger.logger import logger

def generate_image_hunyuan(
        prompt: str,
        model: str = "hunyuan-image",
        revise: bool = True,
        n: int = 1,
        size: str = "1280x720",
        timeout: int = 30,
        verbose: bool = False
) -> Optional[Dict[str, Any]]:
    """
    调用腾讯混元大模型API生成图像

    参数:
    prompt -- 图像生成提示词
    model -- 使用的模型 (默认: 'hunyuan-image')
    revise -- 是否优化提示词 (默认: True)
    n -- 生成图像数量 (默认: 1)
    size -- 图像尺寸 (默认: '1024x1024')
    timeout -- 请求超时秒数 (默认: 30)
    verbose -- 是否打印详细日志 (默认: False)

    返回:
    成功时返回API响应字典，失败时返回None
    """
    # 从环境变量获取API密钥
    hunyuan_config = appConfig.automic.gen_ad.hunyuan
    api_key = hunyuan_config.api_key
    if not api_key:
        logger.error("错误: 未找到HUNYUAN_API_KEY环境变量")
        return None

    url = hunyuan_config.base_url

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    payload = {
        "prompt": prompt,
        "model": model,
        "revise": revise,
        "n": n,
        "size": size,
    }

    if verbose:
        logger.info("请求参数:")
        logger.info(json.dumps(payload, indent=2, ensure_ascii=False))
        logger.info(f"目标URL: {url}")

    try:
        response = requests.post(
            url,
            headers=headers,
            json=payload,
            timeout=timeout
        )

        if verbose:
            logger.info(f"状态码: {response.status_code}")
            logger.info("响应头:")
            for key, value in response.headers.items():
                logger.info(f"  {key}: {value}")

        if response.status_code == 200:
            if verbose:
                logger.info("请求成功!")
            return response.json()
        else:
            logger.error(f"请求失败，状态码: {response.status_code}")
            logger.error(f"错误信息: {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {str(e)}")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {str(e)}")
        logger.error(f"原始响应: {response.text[:200]}...")
        return None

def llm_for_openai(messages: list, temperature: float = 0.7, stream: bool = False, max_tokens: int = 500):
    gen_ad_config = appConfig.automic.gen_ad.llm
    try:
        client = OpenAI(base_url=gen_ad_config.base_url, api_key=gen_ad_config.api_key)
        response = client.chat.completions.create(
            model=gen_ad_config.model_name,
            messages=messages,
            temperature=temperature,
            stream=stream,
            max_tokens=max_tokens
        )
        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"When call llm chat model has error, model:{gen_ad_config.model_name}, error: {e} ")
        raise e