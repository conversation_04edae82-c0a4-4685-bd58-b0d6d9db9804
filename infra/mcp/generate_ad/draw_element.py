from PIL import Image, ImageDraw, ImageFont
import math
import requests
import textwrap
from io import BytesIO
from common.logger.logger import logger

def draw_star_rating(draw, rating, position, font=ImageFont.load_default(), max_stars=5, star_size=30, spacing=5):
    """
    在图片上绘制星级评分

    参数:
        draw (ImageDraw.Draw): PIL的Draw对象
        rating (float): 应用评分(0-5分)
        position (tuple): 星星绘制的起始位置(x, y)
        max_stars (int): 最大星星数量(默认为5)
        star_size (int): 星星大小(像素)
        spacing (int): 星星之间的间距
    """
    try:
        # 确保评分在合理范围内
        rating = max(0, min(rating, max_stars))

        # 计算实心星、半星和空心星的数量
        full_stars = math.floor(rating)
        half_star = 1 if rating - full_stars >= 0.5 else 0
        empty_stars = max_stars - full_stars - half_star

        # 星星颜色
        gold = (255, 215, 0)  # 金色
        gray = (200, 200, 200)  # 灰色

        # 星星绘制位置
        x, y = position

        # 绘制实心星
        for i in range(full_stars):
            draw_star(draw, (x, y), star_size, fill=gold)
            x += star_size + spacing

        # 绘制半星
        if half_star:
            draw_half_star(draw, (x, y), star_size, fill=gold)
            x += star_size + spacing

        # 绘制空心星
        # for i in range(empty_stars):
        #     draw_star(draw, (x, y), star_size, fill=gray, outline=gray)
        #     x += star_size + spacing

        # 在星星右侧添加评分文本
        rating_text = f"{rating:.1f}"
        text_position = (x - 10, y - 15)
        draw.text(text_position, rating_text, fill="black", font=font.font_variant(size=20))

    except:
        print("评分加载失败，跳过评分显示")
        return None


def draw_star(draw, position, size, fill=None, outline=None):
    """
    绘制一个五角星

    参数:
        draw (ImageDraw.Draw): PIL的Draw对象
        position (tuple): 星星中心位置(x, y)
        size (int): 星星大小(外接圆直径)
        fill: 填充颜色
        outline: 边框颜色
    """
    x, y = position
    radius = size // 2
    points = []

    # 计算五角星的五个外顶点和五个内顶点
    for i in range(10):
        angle = math.radians(90 + i * 36)  # 从顶部开始
        r = radius if i % 2 == 0 else radius * 0.4  # 交替外半径和内半径
        points.append((x + r * math.cos(angle), y - r * math.sin(angle)))

    # 绘制星星
    draw.polygon(points, fill=fill, outline=outline)


def draw_half_star(draw, position, size, fill=None):
    """
    绘制完整的左半颗五角星

    参数:
        draw (ImageDraw.Draw): PIL的Draw对象
        position (tuple): 星星中心位置(x, y)
        size (int): 星星大小(外接圆直径)
        fill: 填充颜色
    """
    x, y = position
    radius = size // 2

    # 绘制实心左半星
    points = []
    # 调整循环范围以获取左侧的点
    # 我们需要从第10个点（或等效的-0）开始，到第5个点结束，逆时针绘制
    # 这对应于角度从 90 + 10*36 = 450 (或 90) 到 90 + 5*36 = 270
    # 为了简化，我们可以从角度 270 度（底部）到 90 度（顶部）

    # 内部小圆的半径，通常是外圆半径的某个比例，例如 0.382 for a true pentagram
    inner_radius = radius * 0.382

    # 定义五角星的10个顶点（5个外顶点，5个内顶点）
    star_points = []
    for i in range(10):
        # 偶数索引是外顶点，奇数索引是内顶点
        current_radius = radius if i % 2 == 0 else inner_radius
        angle_rad = math.radians(90 - i * 36)  # 90度是顶部，逆时针旋转
        star_points.append((x + current_radius * math.cos(angle_rad), y - current_radius * math.sin(angle_rad)))

    all_star_points = []
    for i in range(5):
        # Outer points
        angle_outer = math.radians(90 - i * 72)
        all_star_points.append((x + radius * math.cos(angle_outer), y - radius * math.sin(angle_outer)))

        angle_inner = math.radians(90 - (i * 72 + 36))
        all_star_points.append((x + inner_radius * math.cos(angle_inner), y - inner_radius * math.sin(angle_inner)))

    points_clockwise = []
    for i in range(10):
        current_radius = radius if i % 2 == 0 else inner_radius
        angle = math.radians(90 - (i * 36))
        points_clockwise.append((x + current_radius * math.cos(angle), y - current_radius * math.sin(angle)))

    left_half_points = [
        points_clockwise[0],
        points_clockwise[9],
        points_clockwise[8],
        points_clockwise[7],
        points_clockwise[6],
        (x, y)
    ]

    draw.polygon(left_half_points, fill=fill)

    # 绘制空心半星轮廓
    for i in range(len(left_half_points) - 1):
        draw.line([left_half_points[i], left_half_points[i + 1]], fill=(200, 200, 200), width=1)

    draw.line([points_clockwise[0], points_clockwise[9]], fill=(200, 200, 200), width=1)
    draw.line([points_clockwise[9], points_clockwise[8]], fill=(200, 200, 200), width=1)
    draw.line([points_clockwise[8], points_clockwise[7]], fill=(200, 200, 200), width=1)
    draw.line([points_clockwise[7], points_clockwise[6]], fill=(200, 200, 200), width=1)

    # If you want to outline the flat cut edge from top to bottom
    draw.line([points_clockwise[0], (x, y)], fill=(200, 200, 200), width=1)  # Top outer to center
    draw.line([(x, y), points_clockwise[6]], fill=(200, 200, 200), width=1)  # Center to bottom-left outer


def load_icon(url: str) -> Image:
    """加载应用图标"""
    try:
        response = requests.get(url, timeout=10)
        icon = Image.open(BytesIO(response.content))

        # 转换为圆形图标
        mask = Image.new('L', icon.size, 0)
        draw = ImageDraw.Draw(mask)
        draw.ellipse((0, 0, *icon.size), fill=255)
        icon.putalpha(mask)

        return icon
    except:
        print("图标加载失败，跳过图标显示")
        return None


def render_text(font, draw, text: str, position: tuple, max_width: int, font_size: int):
    """智能文本渲染函数"""
    font = font.font_variant(size=font_size)
    lines = textwrap.wrap(text, width=max_width // (font_size // 2))

    y = position[1]
    for line in lines:
        text_width = font.getlength(line)
        x = position[0] + (max_width - text_width) // 2
        # 添加文字阴影
        # draw.text((x-2, y-2), line, font=font, fill="black")
        # draw.text((x+2, y-2), line, font=font, fill="black")
        # draw.text((x-2, y+2), line, font=font, fill="black")
        # draw.text((x+2, y+2), line, font=font, fill="black")
        # 主文字
        draw.text((x, y), line, font=font, fill="black")
        y += font_size + 10


def render_shadow_text(font, draw, text: str, position: tuple, max_width: int, font_size: int):
    """智能文本渲染函数"""
    font = font.font_variant(size=font_size)
    lines = textwrap.wrap(text, width=max_width // (font_size // 2))

    y = position[1]
    for line in lines:
        text_width = font.getlength(line)
        x = position[0] + (max_width - text_width) // 2
        # 添加文字阴影
        draw.text((x - 2, y - 2), line, font=font, fill="black")
        draw.text((x + 2, y - 2), line, font=font, fill="black")
        draw.text((x - 2, y + 2), line, font=font, fill="black")
        draw.text((x + 2, y + 2), line, font=font, fill="black")
        # 主文字
        draw.text((x, y), line, font=font, fill="white")
        y += font_size + 10

def open_image_tool(image_url, size) -> Image.Image:
    """
    根据App信息生成用于推广App的使用场景图
    返回:
        PIL.Image.Image 对象
    """
    # 调用函数生成图像

    if not image_url:
        logger.error("生成图像失败，使用默认灰色背景")
        x, y = map(int, size.split('x'))
        return Image.new("RGB", (x, y), color="#CCCCCC")

    # 下载图片
    response = requests.get(image_url, timeout=30)
    response.raise_for_status()  # 检查HTTP状态

    # 创建PIL图像对象
    image = Image.open(BytesIO(response.content))

    # 转换为RGB模式（确保兼容性）
    if image.mode != "RGB":
        image = image.convert("RGB")
    return image