from mcp.server.fastmcp import FastMCP
from infra.mcp.generate_ad.core import *
from infra.mcp.generate_ad.exception_handling import *
from PIL import Image
from infra.mcp.generate_ad.state import StateManager
from common.logger.logger import logger
import time
import re

FONT_PATH = "font/SourceHanSansSC-Regular-2.otf"
font = ImageFont.truetype(FONT_PATH, 48)
# 全局状态管理器
state_manager = StateManager()

mcp = FastMCP("Generate Ad")
@mcp.tool()
async def generate_horizontal_ad(dummy: Optional[str] = None) -> Image.Image:
    """生成横版广告图"""
    logger.info("正在生成横版广告图...")
    try:
        state = state_manager.current_state

        # 确保状态中有广告类型
        state.ad_type = "horizontal"

        # 如果缺少图片URL，生成新的
        if not state.bg_url:
            state.bg_url = generate_image_url(
                state.bg_prompt,
                state.get_size("bg")
            )

        if not state.use_url:
            state.use_url = generate_image_url(
                state.use_prompt,
                state.get_size("use")
            )

        ad_image = horizontal_layout_design(
            state=state.to_dict(),
            font=font
        )
        timestamp = int(time.time())
        filename = f"images/ad_{timestamp}.jpg"
        ad_image.save(filename, quality=95)
        logger.info(f"广告图已保存至: {filename}")
        return ad_image

    except Exception as e:
        logger.error(f"广告生成失败: {str(e)}")
        return generate_error_placeholder(str(e))

@mcp.tool()
async def generate_vertical_ad(dummy: Optional[str] = None) -> Image.Image:
    """生成竖版广告图"""
    logger.info("正在生成竖版广告图...")
    try:
        state = state_manager.current_state

        # 确保状态中有广告类型
        state.ad_type = "vertical"

        # 如果缺少图片URL，生成新的
        if not state.bg_url:
            state.bg_url = generate_image_url(
                state.bg_prompt,
                state.get_size("bg")
            )

        if not state.use_url:
            state.use_url = generate_image_url(
                state.use_prompt,
                state.get_size("use")
            )

        ad_image = vertical_layout_design(
            state=state.to_dict(),
            font=font
        )
        timestamp = int(time.time())
        filename = f"images/ad_{timestamp}.jpg"
        ad_image.save(filename, quality=95)
        logger.info(f"广告图已保存至: {filename}")
        return ad_image

    except Exception as e:
        logger.error(f"广告生成失败: {str(e)}")
        return generate_error_placeholder(str(e))

@mcp.tool()
async def regenerate_slogan() -> str:
    """重新生成广告语并返回新内容"""
    try:
        new_copy = generate_slogan_tool(state_manager.current_state.app_info) or "发现全新体验，立即下载！"
        state_manager.current_state.slogan = new_copy
        return f"广告语已更新: {new_copy}"
    except Exception as e:
        return f"重新生成广告语失败: {str(e)}"

@mcp.tool()
async def regenerate_bg_image(user_input: Optional[str] = None) -> str:
    """
    重新生成背景图并返回新URL（可结合用户需求）

    参数：
        user_input (str): 从输入中提取用户对生成背景图的要求，若用户无明确要求则输入为空

    """
    try:
        state = state_manager.current_state
        if not state.app_info:
            return "请先提供应用信息"

        # 生成新的背景提示词
        new_prompt = generate_bg_prompt(state.app_info, user_input)

        # 获取尺寸（根据当前广告类型）
        size = state.get_size("bg")

        # 生成新图片URL
        new_url = generate_image_url(new_prompt, size)

        # 更新状态
        state.bg_prompt = new_prompt
        state.bg_url = new_url

        return f"背景图已更新: {new_url}"
    except Exception as e:
        return f"重新生成背景图失败: {str(e)}"

@mcp.tool()
async def regenerate_use_image(user_input: Optional[str] = None) -> str:
    """
    重新生成前景图并返回新URL（可结合用户需求）

    参数：
        user_input (str): 从输入中提取用户对生成前景图的要求，若用户无明确要求则输入为空

    """
    try:
        state = state_manager.current_state
        if not state.app_info:
            return "请先提供应用信息"

        # 生成新的前景图提示词
        new_prompt = generate_usage_prompt(state.app_info, user_input)

        # 生成新图片URL
        new_url = generate_image_url(new_prompt, "1152x864")

        # 更新状态
        state.use_prompt = new_prompt
        state.use_url = new_url

        return f"使用前景图已更新: {new_url}"
    except Exception as e:
        return f"重新生成使用前景图失败: {str(e)}"

@mcp.tool()
async def hide_icon():
    """隐藏广告图中的应用图标"""
    try:
        state = state_manager.current_state
        state.display_elements['icon'] = False
        return "应用图标已隐藏"
    except Exception as e:
        return f"隐藏应用图标失败: {str(e)}"

@mcp.tool()
async def show_icon(user_input: Optional[str] = None) -> str:
    """
    显示广告图中的应用图标

    该函数会尝试以下方式获取图标：
    1. 首先从用户输入文本中提取图标URL
    2. 如果未提供输入文本或提取失败，则从当前状态中获取
    3. 如果都没有图标URL，则返回提示信息

    参数：
        user_input (str): 可选的用户输入文本，用于从中提取图标URL

    返回：
        str: 操作结果信息
    """
    try:
        state = state_manager.current_state

        # 1. 尝试从用户输入中提取图标URL
        if user_input:
            try:
                # 使用正则表达式从文本中提取URL
                url_pattern = r'(https?://[^\s]+\.(?:png|jpg|jpeg|gif|svg))'
                match = re.search(url_pattern, user_input)
                if match:
                    new_url = match.group(1)
                    state.app_info["icon_url"] = new_url
                    logger.info(f"从用户输入中提取到图标URL: {new_url}")
            except Exception as e:
                logger.error(f"从用户输入提取图标URL失败: {str(e)}")

        # 2. 检查当前状态中是否有图标URL
        if "icon_url" not in state.app_info or not state.app_info["icon_url"]:
            return "请先提供应用图标URL (格式示例: https://example.com/icon.png)"

        # 3. 更新显示状态并验证URL有效性
        state.display_elements['icon'] = True

        # 尝试加载图标验证有效性
        try:
            icon = load_icon(state.app_info["icon_url"])
            if not icon:
                raise ValueError("图标URL无效或无法加载")

            return f"应用图标已显示: {state.app_info['icon_url']}"

        except Exception as e:
            state.display_elements['icon'] = False
            return f"图标加载失败，请检查URL有效性: {str(e)}"

    except Exception as e:
        logger.error(f"显示应用图标失败: {str(e)}")
        return f"显示应用图标失败: {str(e)}"

@mcp.tool()
async def hide_score():
    """隐藏广告图中的应用评分"""
    try:
        state = state_manager.current_state
        state.display_elements['rating'] = False
        return "应用评分已隐藏"
    except Exception as e:
        return f"隐藏应用评分失败: {str(e)}"

@mcp.tool()
async def show_score(user_input: Optional[str] = None) -> str:
    """
    显示广告图中的应用评分

    该函数会尝试以下方式获取评分：
    1. 首先从用户输入文本中提取评分
    2. 如果未提供输入文本或提取失败，则从当前状态中获取
    3. 如果都没有评分，则返回提示信息

    参数：
        user_input (str): 可选的用户输入文本，用于从中提取评分

    返回：
        str: 操作结果信息
    """
    try:
        state = state_manager.current_state

        # 1. 尝试从用户输入中提取评分
        if user_input:
            logger.info("提取应用评分")
            try:
                # 使用正则表达式从文本中提取评分
                match = re.search(r'(?:评分|分数|score)[:：]?\s*([0-5](?:\.\d+)?)', user_input)
                if match:
                    new_score = float(match.group(1))
                    if 0 <= new_score <= 5:
                        state.app_info["score"] = new_score
                        print(f"从用户输入中提取到评分: {new_score}")
            except Exception as e:
                logger.error(f"从用户输入提取评分失败: {str(e)}")

        # 2. 检查当前状态中是否有评分
        if "score" not in state.app_info or state.app_info["score"] is None:
            return "请先提供应用评分"

        # 3. 更新显示状态
        state.display_elements['rating'] = True

        # 获取当前评分
        current_score = state.app_info["score"]
        stars = "★" * int(round(current_score)) + "☆" * (5 - int(round(current_score)))

        return f"应用评分已显示: {current_score}分 {stars}"

    except Exception as e:
        logger.error(f"显示应用评分失败: {str(e)}")
        return f"显示应用评分失败: {str(e)}"

@mcp.tool()
async def extract_app_info(name: str, score: float, icon_url: str, features: list, description: str,
                     ad_type: str = "horizontal") -> dict:
    """
    从用户的输入文本中提取应用信息，并初始化状态。

    参数：
        name (str): 应用的名称。
        score (float): 应用的评分。
        icon_url (str): 应用的图标链接。
        features (list): 应用的特点。
        description (str): 应用的详细介绍。
        ad_type (str): 广告类型，默认为"horizontal"，如果用户输入中包含关键字"竖"，则该字段值为"vertical"。
        如果某些字段无法提取，请设置为null。

    """
    app_data = {
        "name": name,
        "score": score,
        "icon_url": icon_url,
        "features": features,
        "description": description,
        "ad_type": ad_type
    }
    state_manager.current_state.initialize(app_data)
    logger.info(f"提取到应用信息并初始化状态: {app_data}")

    return app_data
