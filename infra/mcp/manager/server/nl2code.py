from typing import Dict, Any
from mcp.server.fastmcp import FastMCP
from infra.mcp.codegen.nl2code import core
from common.logger.logger import logger
mcp = FastMCP("nl2code Server")


@mcp.tool()
async def nl2code(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate executable Python code based on user instruction and programming context.

    This function orchestrates the code generation process by:
    1. Validating input parameters
    2. Building execution context from dependencies, variables, and history
    3. Generating Python code using an LLM
    4. Extracting executable code and required packages

    scenario explanation:
        1. General Data Science
           Purpose: Used for general data science tasks without a specific focus.
           Description: This scenario covers exploratory data analysis, basic data manipulation, and general analysis tasks. It provides flexibility to handle mixed or undefined analysis needs, focusing on clear, readable code following Python best practices for data science.
           Keywords: `data`, `analysis`, `exploration`, `basic`, `general`

        2. Summary Statistics
           Purpose: Statistical summaries and descriptive analysis of datasets.
           Description: Summary statistics help uncover key characteristics of data, such as central tendency, variability, and distribution. This scenario focuses on metrics like mean, median, mode, standard deviation, quartiles, and data completeness, aiding data quality assessment and further analysis.
           Keywords: `summary`, `statistics`, `description`, `mean`, `median`, `standard deviation`, `percentiles`, `quartiles`

        3. Distribution Analysis
           Purpose: Data distribution analysis, normality tests, and distribution fitting.
           Description: This scenario examines the shape, spread, and features of data distributions, including normality, skewness, kurtosis, and outliers. Distribution analysis is crucial for choosing the right statistical tests, transformations, and modeling methods. Techniques include visual checks, statistical tests, and fitting distributions.
           Keywords: `distribution`, `histogram`, `density`, `normality`, `skewness`, `kurtosis`, `Shapiro`, `box plot`

        4. Correlation Analysis
           Purpose: Analyze relationships between variables using correlation and association measures.
           Description: This scenario aims to identify relationships between variables, calculate appropriate correlation measures, and test their significance. Visualization techniques like heatmaps and scatter plots, along with significance testing, help understand variable interactions.
           Keywords: `correlation`, `Pearson`, `Spearman`, `heatmap`, `relationship`, `association`, `covariance`

        5. Outlier Detection
           Purpose: Identify outlier data points using statistical and machine learning methods.
           Description: This scenario focuses on detecting outliers through various methods, analyzing their features and impact, and providing advice on handling them. Common methods include Z-scores, IQR (Interquartile Range), and machine learning methods like Isolation Forest.
           Keywords: `outlier`, `anomaly`, `IQR`, `Z-score`, `Isolation Forest`, `box plot`, `extreme`, `unusual`

        6. Data Preprocessing
           Purpose: Data cleaning, transformation, and preparation for analysis.
           Description: This scenario includes data cleaning, handling missing values, type conversions, standardization, encoding, and transformations to ensure the data is ready for analysis.
           Keywords: `preprocessing`, `cleaning`, `missing`, `imputation`, `standardization`, `encoding`, `transformation`

        7. Feature Engineering
           Purpose: Create and transform features to improve model performance.
           Description: Feature engineering involves creating, transforming, and selecting features to enhance machine learning model performance. This scenario includes domain-specific feature creation, mathematical transformations, handling categorical variables, and extracting time-based features.
           Keywords: `features`, `engineering`, `interaction`, `polynomial`, `selection`, `transformation`, `creation`

        8. Machine Learning
           Purpose: Develop, train, evaluate, and optimize models for predictive tasks.
           Description: Machine learning covers the entire lifecycle of predictive model development, from data preparation to deployment. It includes supervised learning (classification/regression), unsupervised learning (clustering), and specialized techniques like time-series forecasting. Key areas include proper data splitting, hyperparameter optimization, model evaluation, and deployment considerations. Advanced topics include distributed training, MLOps integration, and incremental learning.
           Keywords: `machine learning`, `model`, `training`, `prediction`, `sklearn`, `regression`, `classification`

    Args:
        params: Dictionary containing these required keys:
            scenario (str): Data science scenario type (general, summary_stats, distribution_analysis, correlation_analysis, outlier_detection, data_preprocessing, feature_engineering, machine_learning)
            user_instruction (str): Natural language instruction describing desired functionality
            env_dependencies (list): Installed packages/libraries (e.g., ['pandas', 'numpy'])
            global_vars (dict): Existing global variables (e.g., {'df': "pd.DataFrame()"})
            function_headers (list): Available function signatures (e.g., ['def process_data(data):...'])
            previous_actions (list): Tuples of past code attempts and results: Format: [(code_str, exec_status_str, output_str), ...]
            data_type (str): Type of primary data object (e.g., 'DataFrame', 'Array')
            data_schema (str): Schema description of primary data (e.g., 'columns: [id, name]')
            model_name (str): LLM model for code generation (e.g., 'DeepSeek-V3-0324')

    Returns:
        Dictionary with these possible structures:

        Success case:
            {
                "python_code": str  # Executable Python code
                "required_packages": list[str]  # Packages needed for execution
                "detected_scenario": str # scenario_name,
            }

        Error cases:
            {
                "error": "Parameter validation failed",
                "details": list[dict]  # Pydantic validation errors
            }
            OR
            {
                "error": "Code generation failed.",
                "details": str  # Exception message
            }

    The generated code:
        - Uses existing global variables and functions
        - Follows Python data processing conventions
        - Avoids non-executable elements
        - Includes error recovery patterns
        - Maintains Jupyter Notebook compatibility
    """
    try:
        if "scenario" not in params:
            logger.info("no scenario specified, using general scenario")
            params["scenario"] = "general"

        if params["scenario"] not in [
            "general",
            "summary_stats",
            "distribution_analysis",
            "correlation_analysis",
            "outlier_detection",
            "data_preprocessing",
            "feature_engineering",
            "machine_learning"
        ]:
            logger.warning(f"scenario {params['scenario']} not recognized, using general scenario")
            params["scenario"] = "general"

        logger.info(f"nl2code core params: {params}")
        result = core.nl2code_w_pkgs_by_scenario(params)
        logger.info(f"nl2code core result: {result}")

        return result 
    except Exception as e:
        logger.error(f"core.nl2code_w_pkgs error: {str(e)}", exc_info=True)
        return {
            "error": "Code generation failed.",
            "details": str(e)
        }

if __name__ == "__main__":
    mcp.run(transport="stdio")
