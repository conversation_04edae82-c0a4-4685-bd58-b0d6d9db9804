import asyncio
import os
import sys
import time
import json

from typing import Dict, Any, List, Optional
from mcp import ClientSession, StdioServerParameters, types
from mcp.client.stdio import stdio_client
from mcp.shared.exceptions import McpError
from common.metric.enpoints import push_metrics

from common.logger.logger import logger, get_trace_logger
from mcp.client.sse import sse_client
from common.metric.enpoints import HISTOGRAM_CALL_TOOL_LATENCY, COUNTER_CALL_TOOL_TOTAL
from common.share.config import appConfig
from common.share.stream_param import StreamGenerationParams
from common.share.context import ChatContext
from common.share import env
from abc import ABC, abstractmethod

PYTHONPATH = os.getenv('PYTHONPATH', "./")


def working_dir():
    return os.getenv('WORKING_DIR', os.getcwd())


class MCPCallback(ABC):
    @abstractmethod
    async def on_tool_call(self, ctx, tool_name, result):
        """
        MCP工具调用回调
        :param ctx: ChatContext 或其他上下文对象
        :param tool_name: 工具名（如 "jupyter__execute_code"）
        :param result: 工具调用结果
        """
        pass


class CallbackDispatcher(MCPCallback):
    def __init__(self, default_callback=None):
        self.tool_callbacks = {}
        self.prefix_callbacks = {}
        self.default_callback = default_callback

    def register_callback(self, tool_name, callback: MCPCallback):
        self.tool_callbacks[tool_name] = callback

    def register_prefix_callback(self, prefix, callback: MCPCallback):
        self.prefix_callbacks[prefix] = callback

    async def on_tool_call(self, ctx, tool_name, result):
        callback = self.tool_callbacks.get(tool_name)
        if callback is None:
            for prefix, cb in self.prefix_callbacks.items():
                if tool_name.startswith(prefix):
                    callback = cb
                    break
        if callback is None:
            callback = self.default_callback
        if callback:
            await callback.on_tool_call(ctx, tool_name, result)
        else:
            print(f"[CallbackDispatcher] No callback registered for {tool_name}")


class DefaultCallback(MCPCallback):
    async def on_tool_call(self, ctx, tool_name, result):
        logger.info(f"[DefaultCallback] {tool_name} result: {result}")


class MCPManager:
    def __init__(self, params: StreamGenerationParams = None, callback: Optional[MCPCallback] = None):
        self.mcp_servers: Dict[str, Dict[str, Any]] = {}
        self.active_sessions: Dict[str, ClientSession] = {}
        self.callback = self._create_and_register_callbacks()

        # 📊 Initialize replay data collection
        try:
            from infra.metrics.replay_data_collector import get_replay_collector
            self.replay_collector = get_replay_collector()
        except ImportError:
            self.replay_collector = None
            logger.warning("ReplayDataCollector not available")

        self.logger = logger
        logger.info(f"MCPManager initialized, params: {params}")
        # 在初始化时调用异步方法
        if params:
            self.logger = get_trace_logger(params.ctx, "chat")
            self.params = params
            self.register_default_servers(params)

    def _create_and_register_callbacks(self):
        dispatcher = CallbackDispatcher(default_callback=DefaultCallback())
        # 由下游实现回调接口
        # dispatcher.register_prefix_callback("generate_sql", Nl2CodeCallback())
        return dispatcher

    def register_server(
            self,
            server_name: str,
            command: str,
            args: List[str],
            link: str,
            url: str = None,
            env_input: Optional[Dict[str, str]] = None,
            mcp_type: str = None,
    ):
        """注册单个MCP服务器

        Args:
            server_name: 服务器唯一标识
            command: 启动命令（如"python3"）
            args: 命令行参数列表
            link: 协议类型
            url: 服务器地址
            env: 环境变量字典
        """
        self.logger.info(f"register_server: {server_name}, {command}, {args}, {link}, {url}, {env_input}")
        if link == "stdio":
            env_input = env_input or {}
            env_input.update({
                "EXECUTION_MODE": env.EXECUTION_MODE,
                "IMAGE_TAG": env.IMAGE_TAG,
            })
            if env.EXECUTION_MODE == "ray":
                env_input.update({
                    "CONF_PATH": os.getenv("CONF_PATH", ""),
                    "LOG_PATH": os.getenv("LOG_PATH", ""),
                    "WORKING_DIR": os.getenv("WORKING_DIR", ""),
                    "LOGGER_TO_STDOUT": os.getenv("LOGGER_TO_STDOUT", ""),
                    "PYTHONPATH": os.getenv("PYTHONPATH", "") + ":" + command.replace("/bin/python", "/lib/python3.12/site-packages/"),
                })
            self.logger.info(f"env_input: {env_input}")
            self.mcp_servers[server_name] = {
                'params': StdioServerParameters(
                    command=command,
                    args=args,
                    env=env_input
                ),
                'tools': None,
                'link': link,
                'url': url,
                'type': mcp_type,
            }
        else:
            self.mcp_servers[server_name] = {
                'tools': None,
                'link': link,
                'url': url,
                'type': mcp_type,
            }

    def register_default_servers(self, params: StreamGenerationParams):
        """注册一组默认服务器（示例）"""
        # 设置环境变量
        self.logger.info(f"os.getcwd(): {os.getcwd()}, working_dir(): {working_dir()}, params: {params}")

        ctx = params.ctx
        mcp_list = params.mcp_instance
        if isinstance(mcp_list, dict):
            mcp_list = [mcp_list]  # 单个字典转为单元素列表
        elif not isinstance(mcp_list, list):
            mcp_list = []
        data_engine_name = ""
        mcp_type = ""
        is_sampling = True
        mcp_url = {}
        datasource_connection_name = ""  # 添加数据源连接名称变量
        default_servers = []
        self.logger.info(f"register_default_servers mcp_list: {mcp_list}")

        # 一个对话 es_sql和 dlc 只取其一
        # mcp_type , engine , datasource_name 用于nl2sql工具，只能有一个，目前选列表的最后一个
        for item in mcp_list:
            mcp_type = item.get("Type")
            url = item.get("Url")
            is_sampling = item.get("IsSampling", "False")
            if mcp_type and url:
                mcp_url[mcp_type] = url
                self.logger.info(f"register_default_servers request mcp_url: {mcp_url}")
            data_engine_name = item.get("DataEngineName", data_engine_name)
            datasource_connection_name = item.get("DatasourceConnectionName", datasource_connection_name)  # 提取数据源连接名称

            name = item.get('Instance')
            default_see_server = {
                "name": name,
                "command": sys.executable,
                "url": url,
                "link": "see",
                "env": None,
                "args": None,
                "type": mcp_type,
            }
            self.logger.info(f"default_see_server add mcp: {default_see_server}")
            default_servers.append(default_see_server)

        self.logger.info(f"default_servers see: {default_servers}")

        eg = params.eg_instance
        default_servers += [
            {
                "name": "generate_sql",
                "command": sys.executable,
                "args": [f"{working_dir()}/infra/mcp/manager/server/generate_sql.py"],
                "link": "stdio",
                "type": "generate_sql",
                "env": {
                    "PYTHONPATH": f"{working_dir()}:{PYTHONPATH}",
                    "APP_ID": ctx.app_id,
                    "SUB_ACCOUNT_UIN": ctx.sub_account_uin,
                    "TRACE_ID": ctx.trace_id,
                    "DATA_ENGINE_NAME": data_engine_name,
                    "DATASOURCE_CONNECTION_NAME": datasource_connection_name,  # 添加数据源连接名称
                    "DB_TABLE": json.dumps(params.db_table),
                    "IS_SAMPLING": str(is_sampling),
                    "MCP_URL": json.dumps(mcp_url),
                    "TYPE": mcp_type,
                    "RECORD_ID": params.record_id,
                },
                "url": None,
            },
            {
                "name": "nl2code",
                "command": sys.executable,
                "args": [f"{working_dir()}/infra/mcp/manager/server/nl2code.py"],
                "link": "stdio",
                "env": {
                    "PYTHONPATH": f"{working_dir()}:{PYTHONPATH}"
                },
                "url": None,
            },
            {
                "name": "aisearch",
                "command": sys.executable,
                "args": [f"{working_dir()}/infra/mcp/manager/server/aisearch.py"],
                "link": "stdio",
                "type": "aisearch",
                "env": {
                    "PYTHONPATH": f"{working_dir()}:{PYTHONPATH}",
                    "APP_ID": ctx.app_id,
                    "KNOWLEDGE_BASE_IDS": json.dumps(params.knowledge_base_ids),
                },
                "url": None,
            },
            {
                "name": "jupyter",
                "command": sys.executable,
                "args": [f"{working_dir()}/infra/mcp/jupyter/server.py"],
                "link": "stdio",
                "type": "jupyter",
                "env": {
                    "DATA_ENGINE_NAME": data_engine_name,
                    "DATASOURCE_CONNECTION_NAME": datasource_connection_name,  # 添加数据源连接名称
                    "MCP_URL": json.dumps(mcp_url), # 用于jupyter load_data_by_sql 工具
                    "TRACE_ID": ctx.get_trace_id(),
                    "EG_SUBUIN": eg.get('SubUin', ctx.get_sub_account_uin()),
                    "EG_GATEWAY_HOST": eg.get('Url', "unknown"),
                    "KERNEL_ID": eg.get('KernelId', ""),
                    "KERNEL_NAME": eg.get('KernelName', "unknown"),
                    "PYTHONPATH": f"{working_dir()}:{PYTHONPATH}"
                },
                "url": None,
            }
        ]

        self.logger.info(f"default_servers total : {default_servers}")
        for config in default_servers:
            self.register_server(
                server_name=config["name"],
                command=config["command"],
                args=config["args"],
                env_input=config.get("env"),
                link=config["link"],
                url=config["url"],
                mcp_type=config.get("type", "unknown"),
            )
        self.logger.info(f"MCPManager registered, self.mcp_servers: {self.mcp_servers}")

    async def list_tools(self):
        """获取所有工具 返回格式: {"server__tool": tool_info}
        """
        tools = {}
        for server_name, server_info in self.mcp_servers.items():
            self.logger.info(f"Fetching tools for {server_name}...")
            if server_info['tools'] is None:
                server_info['tools'] = await self._fetch_tools(server_name)

            # 转换ListToolsResult为字典格式
            tool_list = server_info['tools'].tools if hasattr(server_info['tools'], 'tools') else []
            # if server_info.get('type', "unkown").lower() == "dlc":
            #     tool_list = [tool for tool in tool_list if tool.name != "DLCListTables"]
            tools.update({
                f"{server_name}__{tool.name}": tool
                for tool in tool_list
            })
            self.logger.info(f"Fetching tools for {server_name} toolscnt: {len(tools)}")
        return tools

    async def _fetch_tools(self, server_name: str):
        """获取单个服务器的工具列表
        返回: ListToolsResult对象
        """
        link = self.mcp_servers[server_name]['link']
        try:
            if link == "stdio":
                params = self.mcp_servers[server_name]['params']
                async with stdio_client(params) as (read, write):
                    async with ClientSession(read, write) as session:
                        await session.initialize()
                        return await session.list_tools()
            else:
                url = self.mcp_servers[server_name]['url']
                async with sse_client(url) as (read, write):
                    async with ClientSession(read, write) as session:
                        await session.initialize()
                        return await session.list_tools()
        except Exception as e:
            self.logger.error(f"获取工具失败 {server_name}: {str(e)}", exc_info=True)
            # WARNING: 获取某一个工具失败，不影响其他工具的获取，注意不要raise异常
            # raise RuntimeError(f"MCP服务 {server_name} 初始化失败: {str(e)}") from e
            COUNTER_CALL_TOOL_TOTAL.labels(server_name=server_name, tool_name="", status="fail").inc()
            push_metrics()
            return {}

    async def call_tool(self, tool_name: str, *args, **kwargs):
        prefixed_name = tool_name
        status = "success"
        self.logger.info(f"Executing tool {prefixed_name} : with params {kwargs}")
        self.logger.info(f"Executing tool mcp_servers: {self.mcp_servers}")
        """执行工具（自动管理会话）
        参数:
            prefixed_name: 格式为"服务名__工具名"
            *args: 位置参数
            **kwargs: 关键字参数
        返回:
            工具执行结果
        """

        # 📊 Start MCP call tracking for replay
        call_start_time = time.time()
        mcp_session_id = f"mcp_{int(call_start_time * 1000)}"
        tool_result = None
        server_name = ""

        try:
            # 验证工具名格式
            err = None
            if '__' not in prefixed_name:
                raise ValueError(f"工具名必须包含'__'分隔符: {prefixed_name}")

            server_name, after_tool_name = prefixed_name.split('__', 1)
            if not server_name or not after_tool_name:
                raise ValueError(f"无效的工具名格式: {prefixed_name}")

            # 检查服务是否注册
            if server_name not in self.mcp_servers:
                raise ValueError(f"服务 {server_name} 未注册")

            # 确保工具列表已加载
            if self.mcp_servers[server_name]['tools'] is None:
                await self._fetch_tools(server_name)

            params = self.mcp_servers[server_name].get('params', None)

            link = self.mcp_servers[server_name]['link']
            if link == "stdio":
                async with stdio_client(params) as (read, write):
                    async with ClientSession(read, write) as session:
                        await session.initialize()
                        tool_result = await session.call_tool(after_tool_name, *args, **kwargs)
                        if isinstance(tool_result, dict) and "error" in tool_result and tool_result["error"]:
                            status = "fail"
                            self.logger.warning(f"Failed to record MCP call, tool_result: {tool_result}")
                        # 📊 Record MCP call chain for replay
                        if self.replay_collector:
                            try:
                                self._record_mcp_call(
                                    mcp_session_id, server_name, after_tool_name,
                                    link, params, kwargs, tool_result, call_start_time
                                )
                            except Exception as e:
                                self.logger.warning(f"Failed to record MCP call: {e}")

                        return tool_result
            else:
                url = self.mcp_servers[server_name]['url']
                async with sse_client(url) as (read, write):
                    async with ClientSession(read, write) as session:
                        await session.initialize()
                        tool_result = await session.call_tool(after_tool_name, *args, **kwargs)

                        # 📊 Record MCP call chain for replay
                        if self.replay_collector:
                            try:
                                self._record_mcp_call(
                                    mcp_session_id, server_name, after_tool_name,
                                    link, {"url": url}, kwargs, tool_result, call_start_time
                                )
                            except Exception as e:
                                self.logger.warning(f"Failed to record MCP call: {e}")

                        return tool_result
        except BaseExceptionGroup as e:
            status = "fail"
            self.logger.warning(f"Executing {id} tool {prefixed_name} original failed: {str(e)}")
            while isinstance(e, ExceptionGroup) or isinstance(e, BaseExceptionGroup):
                e = e.exceptions[0]
            if isinstance(e, McpError):
                self.logger.error(f"Executing {id} tool {prefixed_name} failed: {str(e.error.message)}")
                self.logger.warning(f"Executing {id} tool {prefixed_name} failed: {str(e.error.message)}", exc_info=True)
                err = RuntimeError(f"Executing {id} tool {prefixed_name} failed: {str(e.error.message)}")
            else:
                self.logger.error(f"Executing {id} tool {prefixed_name} failed: {str(e)}", exc_info=True)
                raise e
        except Exception as e:
            status = "fail"
            self.logger.error(f"Executing {id} tool {prefixed_name} failed: {str(e)}", exc_info=True)
            raise RuntimeError(f"Executing {id} tool {prefixed_name} failed: {str(e)}") from e
        finally:
            self.logger.info(f"Executing {id} tool {prefixed_name} finished")
            try:
                duration = time.time() - call_start_time
                # 只保留工具名，不带服务名
                tool_label = prefixed_name.split("__", 1)[-1] if "__" in prefixed_name else prefixed_name

                HISTOGRAM_CALL_TOOL_LATENCY.labels(server_name=server_name, tool_name=tool_label, status=status).observe(duration)
                COUNTER_CALL_TOOL_TOTAL.labels(server_name=server_name, tool_name=tool_label, status=status).inc()
                push_metrics()
                self.logger.info(f"Executing {id} tool {prefixed_name} metrics report finished")
            except Exception as e:
                self.logger.warning(f"Prometheus push failed: {e}")

            if self.callback is not None:
                try:
                    ctx = getattr(self, "params", None)
                    if ctx is not None:
                        ctx = getattr(ctx, "ctx", None)
                    _ = asyncio.create_task(
                        self.callback.on_tool_call(
                            ctx=ctx,
                            tool_name=prefixed_name,
                            result=tool_result
                        )
                    )
                except Exception as e:
                    self.logger.warning(f"MCPCallback on_tool_call failed: {e}")

            if err:
                return {
                    "error": f"tool {prefixed_name} call failed id:{id} ",
                    "details": str(err),
                }

    def _record_mcp_call(self, mcp_session_id: str, server_name: str, tool_name: str,
                         link: str, connection_params: Dict[str, Any], call_kwargs: Dict[str, Any],
                         tool_result: Any, call_start_time: float) -> None:
        """记录MCP工具调用链用于复现"""
        if not self.replay_collector:
            return

        try:
            call_duration_ms = int((time.time() - call_start_time) * 1000)

            # Extract task_id if available from call context
            task_id = call_kwargs.get('task_id', 'unknown')
            if 'arguments' in call_kwargs and isinstance(call_kwargs['arguments'], dict):
                task_id = call_kwargs['arguments'].get('task_id', task_id)

            # Build server info
            server_info = {
                "server_name": server_name,
                "server_type": self.mcp_servers.get(server_name, {}).get('type', 'unknown'),
                "link_type": link
            }

            # Build connection config (sanitize sensitive info)
            connection_config = {}
            if connection_params:
                connection_config = {k: v for k, v in connection_params.items()
                                     if k not in ['password', 'secret', 'token', 'key']}

            # Build call details
            call_details = [{
                "call_id": f"{mcp_session_id}_{tool_name}",
                "tool_name": f"{server_name}__{tool_name}",
                "input_parameters": call_kwargs,
                "output_result": str(tool_result)[:1000] if tool_result else "",  # Truncate large outputs
                "duration_ms": call_duration_ms,
                "timestamp": time.time()
            }]

            # Record the MCP call chain
            self.replay_collector.record_mcp_call_chain(
                task_id=task_id,
                mcp_session_id=mcp_session_id,
                server_info=server_info,
                connection_config=connection_config,
                tool_calls_sequence=[{
                    "tool_name": tool_name,
                    "call_order": 1,
                    "timestamp": call_start_time
                }],
                call_details=call_details,
                tool_state_changes={}
            )

            logger.info(f"📊 Recorded MCP call chain: {server_name}__{tool_name}")

        except Exception as e:
            logger.warning(f"Failed to record MCP call chain: {e}")

    def update_generate_sql(self, new_db_table):
        """
        更新 db_table 并重新注册 generate_sql 工具
        """
        # 更新 params.db_table
        if hasattr(self, "params"):
            self.params.db_table = new_db_table
        self.db_table = new_db_table

        # 取旧的 server 配置
        old = self.mcp_servers.get("generate_sql")
        if not old or "params" not in old:
            self.logger.warning("generate_sql 工具未注册，无法重新注册")
            return

        params = old["params"]
        # 复制 env
        env_dict = dict(getattr(params, "env", {}) or {})
        env_dict["DB_TABLE"] = json.dumps(new_db_table)

        # 重新注册
        self.register_server(
            server_name="generate_sql",
            command=params.command,
            args=params.args,
            env_input=env_dict,
            link="stdio",
            url=None,
            mcp_type="generate_sql"
        )
        # 清空 tools，强制下次 list_tools 重新获取
        self.mcp_servers["generate_sql"]["tools"] = None
        self.logger.info(f"generate_sql 工具已重新注册，db_table: {new_db_table}")
        self.logger.info(f"update_generate_sql self.mcp_servers: {self.mcp_servers}")

    async def run_mvp_server_example(self):
        """示例：MVP服务器交互"""

        tools = await self.list_tools()
        logger.info(f"Available tools: {tools}")

        # p = {
        #     "app_id": "1",
        #     "sub_account_uin": "collinsdeng",
        #     "trace_id": "collinsdeng",
        #     "engine": "data-agent-exp-dev",
        #     "datasource_name": "DataLakeCatalog",
        #     "dbname": "nl2sql_test",
        #     "tables": ["orders", "products", "customers"],
        #     "is_sampling": True,
        #     "mcp_url": {"dlc": "http://lb-2ohhzn5i-egfmcuxsj56dh1op.clb.cq-tencentclb.com:31234/sse?auth_token=SECRET_KEY_20251",
        #                 "es_sql": "http://lb-2ohhzn5i-egfmcuxsj56dh1op.clb.cq-tencentclb.com:31234/sse?auth_token=SECRET_KEY_20251"},
        #     "engine_type": "dlc",
        #     "question": "5月下过单的所有客户名称,以及邮箱是啥？",
        #     "record_id": "5",
        # }
        # result = await self.call_tool(
        #     "generate_sql__generate_sql",  # 使用实际存在的工具名
        #     arguments={"params": p}
        # )
        # logger.info(f"Execution result: {result}")


def run_server_end_to_end():
    mcp_config = appConfig.automic.mcp.example.get("data", {})
    params = StreamGenerationParams(
        ctx=ChatContext(
            input={
                "SessionId": "session_id",
                "ApmTraceId": 0,
                "StartTime": None,
                "EndTime": None,
                "DeepThinking": True,
            },
            header={
                "SubAccountUin": "sub_account_uin",
                "TraceId": "trace_id",
                "AppId": "test_app_id",
                "Uin": "test_uin",
            }
        ),
        mcp_instance=mcp_config.get('MCP', {}),
        eg_instance=mcp_config.get('EG', {}),
        db_table=mcp_config.get('DB_TABLE', []),
        record_id="record_id",
    )
    mcp_manager = MCPManager(params)
    import asyncio
    tools = asyncio.run(mcp_manager.list_tools())
    logger.info(f"Available tools: {tools}")
    rst = asyncio.run(mcp_manager.call_tool(
        "jupyter__execute_code",  # 使用实际存在的工具名
        arguments={"code": "print('Hello, World!')"}
    ))

    logger.info(f"Execution code result: {rst}")
    rst = asyncio.run(mcp_manager.call_tool(
        "nl2code__nl2code",  # 使用实际存在的工具名
        arguments={
            "params": {
                "user_instruction": "生成 python计算两数和的代码",
            }
        }
    ))
    logger.info(f"Execution nl2code result: {rst}")

    # 测试aisearch工具
    rst = asyncio.run(mcp_manager.call_tool(
        "aisearch__aisearch_retrieve",  # 使用aisearch工具
        arguments={
            "params": {
                "question": "如何使用AI搜索进行文档检索",
                "user_id": "test_user_123",
                "app_id": "test_app_456"
            }
        }
    ))
    logger.info(f"Execution aisearch result: {rst}")

    rst = asyncio.run(mcp_manager.call_tool(
        "dlc__DLCExecuteQuery",  # 使用实际存在的工具名
        arguments={"SparkSQL": "SELECT * FROM `data_agent`.`coffee_sales` LIMIT 10",
                   "DatabaseName": "data_agent",
                   "DatasourceConnectionName": mcp_config.get('MCP',[])[0].get('DatasourceConnectionName', "unkown"),
                   "DataEngineName": mcp_config.get('MCP',[])[0].get('DataEngineName', "unkown")}
    ))
    logger.info(f"Execution code result: {rst}")


if __name__ == "__main__":
    async def main():
        mcp_config = {"Mcp": {
            "ProductInstance": "execute_sql",
            "ProductType": "MCP",
            "MCPUrl": "http://127.0.0.1:8000/execute_sql"
        }}
        #manager.list_tools()
        mcp_manager = MCPManager(mcp_config)
        await mcp_manager.run_mvp_server_example()
    run_server_end_to_end()
    # import asyncio
    # asyncio.run(main())

    # tools = manager.get_all_tools()
    # logger.info(f"Tools: {tools}")
#1, see mcp_server 本地参数来控制
#2. 直接给list_tools方法
