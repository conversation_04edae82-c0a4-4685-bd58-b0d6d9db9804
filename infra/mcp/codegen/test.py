import argparse
from infra.mcp.codegen.nl2code.core import (
context_builder, code_generator, parse_last_python_code, 
extract_coding_assistant_content, filter_plt_font_code,
add_data_sampling_for_plots, nl2code, nl2code_w_pkgs_by_scenario
)
from infra.mcp.codegen.nl2code.llm import llm_chat
# from nl2code.core import context_builder, code_generator
# from nl2code.llm import llm_chat


def mock_data_replace_task():
    user_instruction = "修正member_id字段，根据提供的映射字典miss进行替换"

    env_dependencies = ["pyspark==3.3.0", "pandas==1.5.0"]
    global_vars = {"miss": "{422445692328:422445692321}"}
    function_headers = ["spark.sql(query)"]
    previous_actions = [("survey = spark.sql('select member_id, cuisine from testdb.pob_ho_cuisine_sample').toPandas()", "success", "DataFrame loaded with shape (1000,2)")]
    data_type = "DataFrame"
    data_schema = "testdb.pob_ho_cuisine_sample(member_id:bigint, cuisine:string)"

    # 生成代码
    output_code = """
survey_pd['member_id'] = survey_pd['member_id'].replace(miss)
spark.createDataFrame(survey_pd).createOrReplaceTempView("survey_temp")
"""

    return {
        "env_dependencies": env_dependencies,
        "global_vars": global_vars,
        "function_headers": function_headers,
        "previous_actions": previous_actions,
        "data_type": data_type,
        "data_schema": data_schema,
        "user_instruction": user_instruction,
        "output_code": output_code,
    }


def mock_data_filter_task():
    user_instruction = "过滤掉'ไม่ทราบ/ไม่แน่ใจ'的无效菜系记录"

    env_dependencies = ["pyspark==3.3.0", "pandas==1.5.0"]
    global_vars = {}
    function_headers = []
    previous_actions = [("survey = spark.table('survey_temp')", "success", "TempView loaded")]
    data_type = "DataFrame"
    data_schema = "survey_temp(member_id:bigint, cuisine:string)"

    output_code = """
survey = survey.filter(survey.cuisine != 'ไม่ทราบ/ไม่แน่ใจ')
survey.createOrReplaceTempView("survey_cleaned")
"""

    return {
        "env_dependencies": env_dependencies,
        "global_vars": global_vars,
        "function_headers": function_headers,
        "previous_actions": previous_actions,
        "data_type": data_type,
        "data_schema": data_schema,
        "user_instruction": user_instruction,
        "output_code": output_code,
    }


def mock_data_split_task():
    user_instruction = "实现7:2:1比例的数据集分割"

    env_dependencies = ["scikit-learn==1.2.0"]
    global_vars = {"features": "['feature1','feature2']", "label": "'cuisine_map'"}
    function_headers = ["def splitting_data(df_features, type)"]
    previous_actions = [("prep_data = pd.read_parquet('preprocessed.parquet')", "success", "Data loaded")]
    data_type = "DataFrame"
    data_schema = "preprocessed_data(feature1:float, feature2:float, cuisine_map:int)"

    output_code = """
def splitting_data(df_features, type=''):
    X = df_features[features]
    y = df_features[label]
    test_size = 0.1 if type == 'test' else 0.2/(1-0.1)
    return train_test_split(X, y, test_size=test_size, stratify=y)

# 分割数据集(70%训练,20%验证,10%测试)
X, X_test, X['cuisine_map'], y_test = spliting_data(prep_data,features,'test')
X_train, X_val, y_train, y_val = spliting_data(X,X.columns[:-1])
"""
    return {
        "env_dependencies": env_dependencies,
        "global_vars": global_vars,
        "function_headers": function_headers,
        "previous_actions": previous_actions,
        "data_type": data_type,
        "data_schema": data_schema,
        "user_instruction": user_instruction,
        "output_code": output_code,
    }


def mock_ho_task():
    user_instruction = "执行XGBoost超参数优化"

    env_dependencies = ["xgboost==1.7.0", "hyperopt==0.2.7"]
    global_vars = {"space": "{'max_depth': hp.quniform('max_depth', 3, 15, 1),'gamma': hp.uniform('gamma', 0,9),'reg_alpha' : hp.quniform('reg_alpha', 0, 60, 1),'reg_lambda' : hp.uniform('reg_lambda', 0, 1),'colsample_bytree' : hp.uniform('colsample_bytree', 0.5, 1),'min_child_weight' : hp.quniform('min_child_weight', 0, 10, 1),'n_estimators': hp.quniform('n_estimators', 260, 500, 20),'seed': 42}"}
    function_headers = ["def objective(space)"]
    previous_actions = [("X_train, X_val, y_train, y_val = splitting_data(prep_data)", "success", "Data split completed")]
    data_type = "ndarray"
    data_schema = ""

    output_code = """
trials = Trials()
best_hyperparams = fmin(fn=objective, 
                        space=space,
                        algo=tpe.suggest,
                        max_evals=50,
                        trials=trials)
"""

    return {
        "env_dependencies": env_dependencies,
        "global_vars": global_vars,
        "function_headers": function_headers,
        "previous_actions": previous_actions,
        "data_type": data_type,
        "data_schema": data_schema,
        "user_instruction": user_instruction,
        "output_code": output_code,
    }


def mock_xgb_ho_train_task():
    user_instruction = "使用超参数优化得到的最佳参数训练XGBoost模型"

    env_dependencies = ["xgboost==1.7.0", "scikit-learn==1.2.0"]
    global_vars = {
        "best_hyperparams": "{'max_depth': 8, 'gamma': 2.3, 'reg_lambda': 0.4, 'colsample_bytree': 0.8, 'n_estimators': 420}",
        "label_no": "5"
    }
    function_headers = ["xgb.XGBClassifier()"]
    previous_actions = [
        ("best_hyperparams = fmin(fn=objective, space=space, algo=tpe.suggest)", "success", "Best params: max_depth=8, n_estimators=420"),
        ("X_train, y_train = oversample.fit_resample(X_train, y_train)", "success", "Oversampled training set: (5000, 15)")
    ]
    data_type = "ndarray"
    data_schema = "X_train(feature1:float, ..., feature15:float), y_train(cuisine_map:int)"

    output_code = """
xgb_clf = xgb.XGBClassifier(
    objective='multi:softprob',
    num_class=label_no,
    max_depth=int(best_hyperparams['max_depth']),
    gamma=best_hyperparams['gamma'],
    reg_lambda=best_hyperparams['reg_lambda'],
    colsample_bytree=best_hyperparams['colsample_bytree'],
    n_estimators=int(best_hyperparams['n_estimators']),
    seed=42
)
xgb_clf.fit(X_train, y_train, eval_set=[(X_val, y_val)], verbose=0)
"""
    return {
        "env_dependencies": env_dependencies,
        "global_vars": global_vars,
        "function_headers": function_headers,
        "previous_actions": previous_actions,
        "data_type": data_type,
        "data_schema": data_schema,
        "user_instruction": user_instruction,
        "output_code": output_code,
    }


def mock_model_eval_task():
    user_instruction = "在验证集上生成混淆矩阵并输出分类指标"

    env_dependencies = ["seaborn==0.12.0", "scikit-learn==1.2.0"]
    global_vars = {
        "xgb_clf": "训练完成的XGBoost分类器",
        "y_axis_labels": "['cafe_bakery', 'indian_western', 'korean_japanese', 'porkpan_suki_shabu_mala', 'thai']"
    }
    function_headers = ["confusion_matrix()", "classification_report()"]
    previous_actions = [
        ("xgb_clf.fit(X_train, y_train)", "success", "Training completed in 120 iterations")
    ]
    data_type = "ndarray"
    data_schema = "X_val(feature1:float, ..., feature15:float), y_val(cuisine_map:int)"

    output_code = """
y_pred = xgb_clf.predict(X_val)
print(classification_report(y_val, y_pred, target_names=y_axis_labels))

cf_matrix = confusion_matrix(y_val, y_pred)
sns.heatmap(cf_matrix, annot=True, fmt='d', 
            xticklabels=y_axis_labels,
            yticklabels=y_axis_labels)
plt.title('Validation Set Confusion Matrix')
plt.show()
"""
    return {
        "env_dependencies": env_dependencies,
        "global_vars": global_vars,
        "function_headers": function_headers,
        "previous_actions": previous_actions,
        "data_type": data_type,
        "data_schema": data_schema,
        "user_instruction": user_instruction,
        "output_code": output_code,
    }


def mock_model_param_eval_task():
    user_instruction = "分析模型参数并绘制特征重要性图、计算决策树复杂度"

    env_dependencies = ["matplotlib==3.6.0"]
    global_vars = {"xgb_clf": "训练完成的XGBoost分类器"}
    function_headers = ["feature_importances_"]
    previous_actions = [
        ("xgb_clf.fit(X_train, y_train)", "success", "Model training completed")
    ]
    data_type = "model"
    data_schema = ""

    output_code = """
# 特征重要性分析
sorted_idx = xgb_clf.feature_importances_.argsort()
plt.barh(xgb_clf.feature_names_in_[sorted_idx], 
         xgb_clf.feature_importances_[sorted_idx])
plt.title("Feature Importance Ranking")

# 决策树复杂度分析
trees = xgb_clf.get_booster().get_dump()
total_nodes = sum(len(re.findall(r':\\w+', tree)) for tree in trees)
print(f"Total decision nodes: {total_nodes}")
"""
    return {
        "env_dependencies": env_dependencies,
        "global_vars": global_vars,
        "function_headers": function_headers,
        "previous_actions": previous_actions,
        "data_type": data_type,
        "data_schema": data_schema,
        "user_instruction": user_instruction,
        "output_code": output_code,
    }


def mock_test_eval_task():
    user_instruction = "在测试集上评估模型性能并保存预测结果到predictions.parquet"

    env_dependencies = ["pandas==1.5.0"]
    global_vars = {
        "test_df": "包含customer_num和cuisine_map的DataFrame"
    }
    function_headers = ["classification_report()"]
    previous_actions = [
        ("X_test, y_test = splitting_data(prep_data, 'test')", "success", "Test set shape: (1000, 15)")
    ]
    data_type = "DataFrame"
    data_schema = "test_data(customer_num:bigint, cuisine_map:int)"

    output_code = """
# 测试集预测
y_pred_test = xgb_clf.predict(X_test)
print(classification_report(y_test, y_pred_test))

# 保存预测结果
test_df['pred'] = y_pred_test
test_df[['customer_num', 'cuisine_map', 'pred']].to_parquet('predictions.parquet')
"""
    return {
        "env_dependencies": env_dependencies,
        "global_vars": global_vars,
        "function_headers": function_headers,
        "previous_actions": previous_actions,
        "data_type": data_type,
        "data_schema": data_schema,
        "user_instruction": user_instruction,
        "output_code": output_code,
    }


def mock_data(task_name):
    if not task_name:
        user_instruction = "读取销售额数据，并进行特征处理、模型训练和时序预测，最终评估模型效果，并返回预测结果。"
        env_dependencies = []
        global_vars = {}
        function_headers = []
        previous_actions = []
        data_type = ""
        data_schema = ""
        output_code = ""

        return {
            "env_dependencies": env_dependencies,
            "global_vars": global_vars,
            "function_headers": function_headers,
            "previous_actions": previous_actions,
            "data_type": data_type,
            "data_schema": data_schema,
            "user_instruction": user_instruction,
            "output_code": output_code,
        }
    elif task_name == "data_filter":
        return mock_data_filter_task()
    elif task_name == "data_replace":
        return mock_data_replace_task()
    elif task_name == "data_split":
        return mock_data_split_task()
    elif task_name == "ho":
        return mock_ho_task()
    elif task_name == "xgb_ho_train":
        return mock_xgb_ho_train_task()
    elif task_name == "model_eval":
        return mock_model_eval_task()
    elif task_name == "model_param_eval":
        return mock_model_param_eval_task()
    elif task_name == "test_eval":
        return mock_test_eval_task()


    
if __name__ == "__main__":
    args_parser = argparse.ArgumentParser()
    args_parser.add_argument('--task_name', type=str, default='')
    args_parser.add_argument('--model_name', type=str, default='DeepSeek-V3-0324')

    args = args_parser.parse_args()

    task_name = args.task_name
    model_name = args.model_name

    data = mock_data(task_name)
    output_code = data.pop("output_code")
    # breakpoint()
    # result = nl2code(data)
    result = nl2code_w_pkgs_by_scenario(data)

    if "error" in result:
        print(result)
        exit(1)
    python_code = result["python_code"]

    # user_instruction = data.pop("user_instruction")
    # output_code = data.pop("output_code")
    # context = context_builder(**data)
    # response = code_generator(context, user_instruction, model_name)
    
    # python_code = parse_last_python_code(response)
    # python_code = extract_coding_assistant_content(python_code)

    # # Filter font-related code from extracted content
    # python_code = filter_plt_font_code(python_code)
    # # Add data sampling for large datasets
    # python_code = add_data_sampling_for_plots(python_code)

    print("#####gt code:\n", output_code)
    print("#####pred code:\n", python_code)