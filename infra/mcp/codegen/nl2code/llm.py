import requests
import json

from openai import OpenAI

from common.logger.logger import logger
from common.share.config import appConfig

def llm_chat(messages: list, model: str, temperature: float = 0.0, stream: bool = False, max_tokens: int = 4096):
    token = appConfig.common.llm.api_key
    url = appConfig.common.llm.base_url
    header = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    body = {
        "model": appConfig.common.llm.model_name,
        "messages": messages,
        "temperature": temperature,
        "stream": stream,
        "max_tokens": max_tokens,
    }
    ret = requests.post(url, headers=header, data=json.dumps(body))

    if ret.status_code != 200:
        raise RuntimeError(f'{ret.json()}: {body}')
    ret = ret.json()
    text = ret["choices"][0]["message"]["content"]
    token_usage = ret["usage"]

    return text, token_usage


def llm_for_openai(messages: list, model: str, temperature: float = 0.0, stream: bool = False, max_tokens: int = 4096):
    nl2code_config = appConfig.common.llm
    try:
        client = OpenAI(base_url=nl2code_config.base_url, api_key=nl2code_config.api_key)
        response = client.chat.completions.create(
            model=nl2code_config.model_name,
            messages=messages,
            temperature=temperature,
            stream=stream,
            max_tokens=max_tokens
            # response_format={"type": "json_object"}
        )
        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"When call llm chat model has error, model:{nl2code_config.model_name}, error: {e} ")
        raise e
