"""
Data Science Scenario Management for nl2code service.

This module defines different data science scenarios and their specific
constraints, descriptions, and code generation guidelines.
"""

from enum import Enum
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Set
import re


class DataScienceScenario(Enum):
    """Data science task scenarios with specific characteristics."""
    
    GENERAL = "general"
    SUMMARY_STATS = "summary_stats"
    DISTRIBUTION_ANALYSIS = "distribution_analysis"
    CORRELATION_ANALYSIS = "correlation_analysis"
    OUTLIER_DETECTION = "outlier_detection"
    DATA_PREPROCESSING = "data_preprocessing"
    FEATURE_ENGINEERING = "feature_engineering"
    MACHINE_LEARNING = "machine_learning"


@dataclass
class ScenarioConfig:
    """Configuration for a specific data science scenario."""
    
    name: str
    description: str
    objectives: List[str]
    constraints: List[str]
    preferred_libraries: List[str] = field(default_factory=list)
    discouraged_libraries: List[str] = field(default_factory=list)
    common_patterns: List[str] = field(default_factory=list)
    code_templates: Dict[str, str] = field(default_factory=dict)
    best_practices: List[str] = field(default_factory=list)
    typical_outputs: List[str] = field(default_factory=list)
    keywords: Set[str] = field(default_factory=set)
    
    # Enhanced fields based on task planning concepts
    explanation: str = ""
    technical_concepts: List[str] = field(default_factory=list)
    key_techniques: List[str] = field(default_factory=list)
    advanced_patterns: List[str] = field(default_factory=list)
    production_considerations: List[str] = field(default_factory=list)
    evaluation_methods: List[str] = field(default_factory=list)


class DataScienceScenarioManager:
    """Manager for data science scenarios and their configurations."""
    
    def __init__(self):
        self.scenarios = self._initialize_scenarios()
    
    def _initialize_scenarios(self) -> Dict[DataScienceScenario, ScenarioConfig]:
        """Initialize all data science scenario configurations."""
        
        scenarios = {}
        
        # General scenario - flexible approach
        scenarios[DataScienceScenario.GENERAL] = ScenarioConfig(
            name="General Data Science",
            description="General-purpose data science tasks without specific focus",
            explanation="""
            General data science tasks encompass exploratory data analysis, basic data manipulation, 
            and fundamental analytical operations. This scenario provides flexibility for mixed or 
            undefined analysis requirements, emphasizing clean, readable code and standard practices.
            """,
            objectives=[
                "Perform basic data operations and analysis",
                "Generate readable and well-structured code",
                "Follow general Python data science best practices"
            ],
            constraints=[
                "Use standard libraries (pandas, numpy, matplotlib)",
                "Include basic error handling where appropriate",
                "Generate clear and documented code"
            ],
            preferred_libraries=["pandas", "numpy", "matplotlib", "seaborn"],
            common_patterns=[
                "Data loading and inspection",
                "Basic visualization",
                "Simple data transformations"
            ],
            best_practices=[
                "Use descriptive variable names",
                "Add comments for complex operations",
                "Handle missing values appropriately"
            ],
            technical_concepts=[
                "DataFrame operations and indexing",
                "Basic statistical operations",
                "Data type conversions and handling",
                "Simple aggregations and grouping"
            ],
            key_techniques=[
                "df.head(), df.info(), df.describe() for exploration",
                "Basic filtering and selection operations",
                "Simple plotting with matplotlib/seaborn",
                "Basic data cleaning operations"
            ],
            advanced_patterns=[
                "Method chaining for data transformations",
                "Conditional logic in data processing",
                "Basic exception handling for data operations",
                "Memory-efficient data loading techniques"
            ],
            production_considerations=[
                "Code modularity and reusability",
                "Basic logging and error handling",
                "Documentation and code comments",
                "Version control readiness"
            ],
            evaluation_methods=[
                "Code review and readability assessment",
                "Basic functionality testing",
                "Performance considerations for data size",
                "Output validation and sanity checks"
            ],
            keywords={"data", "analysis", "explore", "basic", "general"}
        )
        
        # Summary Statistics scenario
        scenarios[DataScienceScenario.SUMMARY_STATS] = ScenarioConfig(
            name="Summary Statistics",
            description="Statistical summary and descriptive analysis of datasets",
            explanation="""
            Summary statistics provide essential insights into data characteristics through descriptive measures.
            This scenario focuses on calculating central tendency, variability, and distribution shape measures
            to understand data quality, identify potential issues, and inform subsequent analysis decisions.
            Key concepts include mean, median, mode, standard deviation, quartiles, and data completeness metrics.
            """,
            objectives=[
                "Calculate comprehensive descriptive statistics",
                "Identify data characteristics and patterns", 
                "Provide statistical insights about data distribution",
                "Assess data quality and completeness"
            ],
            constraints=[
                "Focus on statistical measures (mean, median, std, etc.)",
                "Handle different data types appropriately",
                "Include percentiles and quartile analysis",
                "Consider missing value statistics",
                "Provide contextual interpretation of statistics"
            ],
            preferred_libraries=["pandas", "numpy", "scipy.stats", "matplotlib"],
            discouraged_libraries=["sklearn", "tensorflow", "pytorch"],
            common_patterns=[
                "df.describe()",
                "df.info()",
                "Statistical aggregations by groups",
                "Missing value analysis"
            ],
            technical_concepts=[
                "Central tendency measures (mean, median, mode)",
                "Variability measures (std, variance, range, IQR)",
                "Distribution shape (skewness, kurtosis)",
                "Quantiles and percentiles",
                "Data completeness and quality metrics"
            ],
            key_techniques=[
                "df.describe() for numerical summaries",
                "df.value_counts() for categorical analysis",
                "Grouped statistics with groupby().describe()",
                "Custom aggregation functions",
                "Missing value pattern analysis"
            ],
            advanced_patterns=[
                "Multi-level statistical summaries",
                "Weighted statistics for sampling adjustments",
                "Bootstrap confidence intervals for statistics",
                "Robust statistics for outlier presence",
                "Time-based rolling statistics"
            ],
            production_considerations=[
                "Efficient computation for large datasets",
                "Memory management for statistical calculations",
                "Automated statistical reporting pipelines",
                "Statistical validation and data quality monitoring"
            ],
            evaluation_methods=[
                "Statistical significance testing",
                "Comparison with historical baselines",
                "Cross-validation of summary statistics",
                "Business rule validation for data ranges"
            ],
            code_templates={
                "basic_summary": """
# Basic statistical summary
summary_stats = df.describe()
print("Dataset Summary Statistics:")
print(summary_stats)

# Missing values analysis
missing_info = df.isnull().sum()
print("\\nMissing Values:")
print(missing_info[missing_info > 0])
""",
                "grouped_summary": """
# Summary statistics by group
grouped_stats = df.groupby('{group_col}').describe()
print("Summary Statistics by {group_col}:")
print(grouped_stats)
"""
            },
            best_practices=[
                "Always check for missing values",
                "Consider data types when calculating statistics",
                "Include both central tendency and variability measures",
                "Format statistical output for readability",
                "Provide business context for statistical interpretations"
            ],
            typical_outputs=["Statistical tables", "Descriptive metrics", "Data quality reports"],
            keywords={"summary", "statistics", "describe", "mean", "median", "std", "percentile", "quartile"}
        )
        
        # Distribution Analysis scenario
        scenarios[DataScienceScenario.DISTRIBUTION_ANALYSIS] = ScenarioConfig(
            name="Distribution Analysis",
            description="Analysis of data distributions, normality tests, and distribution fitting",
            explanation="""
            Distribution analysis examines the shape, spread, and characteristics of data distributions.
            This includes identifying normality, skewness, bimodality, and outliers. Understanding
            distributions is crucial for selecting appropriate statistical tests, transformations, and
            modeling approaches. Key techniques include visual inspection, statistical tests, and
            distribution fitting to theoretical distributions.
            """,
            objectives=[
                "Analyze the distribution of variables",
                "Test for normality and other distribution properties",
                "Visualize distributions effectively",
                "Compare distributions across groups",
                "Identify distribution anomalies and patterns"
            ],
            constraints=[
                "Use appropriate visualization for distribution types",
                "Include statistical tests for distribution properties",
                "Handle different variable types (continuous, categorical)",
                "Consider sample size when interpreting results",
                "Account for multiple testing corrections"
            ],
            preferred_libraries=["pandas", "numpy", "matplotlib", "seaborn", "scipy.stats", "plotly"],
            common_patterns=[
                "Histograms and density plots",
                "Box plots and violin plots", 
                "Q-Q plots for normality testing",
                "Distribution fitting and testing"
            ],
            technical_concepts=[
                "Normal distribution and Central Limit Theorem",
                "Skewness and kurtosis measures",
                "Bimodality and multimodality detection",
                "Distribution transformations (log, Box-Cox)",
                "Empirical vs theoretical distributions"
            ],
            key_techniques=[
                "Histogram binning strategies and density estimation",
                "Shapiro-Wilk and Anderson-Darling normality tests",
                "Q-Q plots and P-P plots for distribution comparison",
                "Kernel density estimation for smooth distributions",
                "Distribution parameter estimation (MLE, MOM)"
            ],
            advanced_patterns=[
                "Mixture model fitting for multimodal distributions",
                "Non-parametric distribution testing",
                "Bootstrap distribution estimation",
                "Bayesian distribution inference",
                "Time-varying distribution analysis"
            ],
            production_considerations=[
                "Computational efficiency for large datasets",
                "Automated distribution monitoring",
                "Distribution drift detection in production",
                "Robust distribution estimation methods"
            ],
            evaluation_methods=[
                "Goodness-of-fit tests (KS, Chi-square, AD)",
                "Cross-validation for distribution parameters",
                "Visual diagnostics and residual analysis",
                "Information criteria for model selection (AIC, BIC)"
            ],
            code_templates={
                "distribution_plot": """
# Distribution visualization
fig, axes = plt.subplots(2, 2, figsize=(12, 10))

# Histogram
axes[0,0].hist(df['{column}'], bins=30, alpha=0.7, edgecolor='black')
axes[0,0].set_title('Histogram of {column}')

# Box plot
axes[0,1].boxplot(df['{column}'].dropna())
axes[0,1].set_title('Box Plot of {column}')

# Density plot
df['{column}'].plot.density(ax=axes[1,0])
axes[1,0].set_title('Density Plot of {column}')

# Q-Q plot for normality
from scipy.stats import probplot
probplot(df['{column}'].dropna(), dist="norm", plot=axes[1,1])
axes[1,1].set_title('Q-Q Plot of {column}')

plt.tight_layout()
plt.show()
""",
                "normality_test": """
# Normality testing
from scipy.stats import shapiro, normaltest, kstest

data = df['{column}'].dropna()

# Shapiro-Wilk test
shapiro_stat, shapiro_p = shapiro(data)
print(f"Shapiro-Wilk Test: statistic={shapiro_stat:.4f}, p-value={shapiro_p:.4f}")

# D'Agostino's normality test
dagostino_stat, dagostino_p = normaltest(data)
print(f"D'Agostino Test: statistic={dagostino_stat:.4f}, p-value={dagostino_p:.4f}")
"""
            },
            best_practices=[
                "Always visualize before statistical testing",
                "Consider sample size limitations for normality tests",
                "Use multiple visualization methods for comprehensive analysis",
                "Report both visual and statistical evidence"
            ],
            typical_outputs=["Distribution plots", "Normality test results", "Distribution parameters"],
            keywords={"distribution", "histogram", "density", "normal", "skew", "kurtosis", "shapiro", "boxplot"}
        )
        
        # Correlation Analysis scenario
        scenarios[DataScienceScenario.CORRELATION_ANALYSIS] = ScenarioConfig(
            name="Correlation Analysis",
            description="Analysis of relationships between variables using correlation and association measures",
            objectives=[
                "Identify relationships between variables",
                "Calculate appropriate correlation measures",
                "Visualize correlation patterns",
                "Test correlation significance"
            ],
            constraints=[
                "Choose appropriate correlation measure for data types",
                "Consider linear vs non-linear relationships",
                "Handle missing values in correlation analysis",
                "Test statistical significance of correlations"
            ],
            preferred_libraries=["pandas", "numpy", "matplotlib", "seaborn", "scipy.stats"],
            common_patterns=[
                "Correlation matrix calculation",
                "Heatmap visualization",
                "Pairwise scatter plots",
                "Correlation significance testing"
            ],
            code_templates={
                "correlation_matrix": """
# Calculate correlation matrix
correlation_matrix = df.select_dtypes(include=[np.number]).corr()

# Visualize correlation heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(correlation_matrix, 
            annot=True, 
            cmap='coolwarm', 
            center=0,
            square=True,
            fmt='.2f')
plt.title('Correlation Matrix')
plt.tight_layout()
plt.show()

# Find strong correlations
strong_corr = correlation_matrix[abs(correlation_matrix) > 0.7]
print("Strong correlations (|r| > 0.7):")
print(strong_corr.stack().sort_values(ascending=False))
""",
                "pairwise_analysis": """
# Pairwise correlation analysis
from scipy.stats import pearsonr, spearmanr

def analyze_correlation(df, col1, col2):
    # Remove missing values
    data = df[[col1, col2]].dropna()
    
    # Pearson correlation
    r_pearson, p_pearson = pearsonr(data[col1], data[col2])
    
    # Spearman correlation
    r_spearman, p_spearman = spearmanr(data[col1], data[col2])
    
    print(f"Correlation between {col1} and {col2}:")
    print(f"  Pearson r: {r_pearson:.3f} (p={p_pearson:.3f})")
    print(f"  Spearman ρ: {r_spearman:.3f} (p={p_spearman:.3f})")
    
    return r_pearson, p_pearson, r_spearman, p_spearman
"""
            },
            best_practices=[
                "Use Pearson for linear relationships, Spearman for monotonic",
                "Always check scatter plots before interpreting correlations",
                "Consider partial correlations for confounding variables",
                "Test statistical significance, especially with small samples"
            ],
            typical_outputs=["Correlation matrices", "Heatmaps", "Scatter plots", "Significance tests"],
            keywords={"correlation", "pearson", "spearman", "heatmap", "relationship", "association", "covariance"}
        )
        
        # Outlier Detection scenario
        scenarios[DataScienceScenario.OUTLIER_DETECTION] = ScenarioConfig(
            name="Outlier Detection",
            description="Identification and analysis of anomalous data points using statistical and ML methods",
            objectives=[
                "Identify outliers using multiple detection methods",
                "Analyze the nature and impact of outliers",
                "Provide recommendations for outlier treatment",
                "Visualize outliers in context"
            ],
            constraints=[
                "Use multiple outlier detection methods for validation",
                "Consider domain knowledge when interpreting outliers",
                "Distinguish between errors and valid extreme values",
                "Document outlier detection methodology"
            ],
            preferred_libraries=["pandas", "numpy", "matplotlib", "seaborn", "scipy.stats", "sklearn"],
            common_patterns=[
                "Statistical outlier detection (Z-score, IQR)",
                "Box plots for visual identification", 
                "Isolation Forest and other ML methods",
                "Multivariate outlier detection"
            ],
            code_templates={
                "statistical_outliers": """
# Statistical outlier detection
def detect_outliers_iqr(df, column):
    Q1 = df[column].quantile(0.25)
    Q3 = df[column].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    
    outliers = df[(df[column] < lower_bound) | (df[column] > upper_bound)]
    return outliers, lower_bound, upper_bound

def detect_outliers_zscore(df, column, threshold=3):
    z_scores = np.abs((df[column] - df[column].mean()) / df[column].std())
    outliers = df[z_scores > threshold]
    return outliers

# Apply outlier detection
outliers_iqr, lower, upper = detect_outliers_iqr(df, '{column}')
outliers_zscore = detect_outliers_zscore(df, '{column}')

print(f"IQR method found {len(outliers_iqr)} outliers")
print(f"Z-score method found {len(outliers_zscore)} outliers")
""",
                "outlier_visualization": """
# Outlier visualization
fig, axes = plt.subplots(1, 3, figsize=(15, 5))

# Box plot
axes[0].boxplot(df['{column}'].dropna())
axes[0].set_title('Box Plot - Outliers Visible')
axes[0].set_ylabel('{column}')

# Histogram with outliers highlighted
axes[1].hist(df['{column}'], bins=30, alpha=0.7, label='All data')
if len(outliers_iqr) > 0:
    axes[1].hist(outliers_iqr['{column}'], bins=10, alpha=0.7, 
                color='red', label='Outliers (IQR)')
axes[1].legend()
axes[1].set_title('Histogram with Outliers')

# Scatter plot (if index represents time/order)
axes[2].scatter(df.index, df['{column}'], alpha=0.6, label='Normal')
if len(outliers_iqr) > 0:
    axes[2].scatter(outliers_iqr.index, outliers_iqr['{column}'], 
                   color='red', label='Outliers', s=50)
axes[2].legend()
axes[2].set_title('Data Points with Outliers')

plt.tight_layout()
plt.show()
"""
            },
            best_practices=[
                "Use multiple detection methods for robust identification",
                "Always visualize outliers before making decisions",
                "Consider the business context of outliers",
                "Document outlier treatment rationale"
            ],
            typical_outputs=["Outlier lists", "Detection plots", "Treatment recommendations"],
            keywords={"outlier", "anomaly", "iqr", "zscore", "isolation", "boxplot", "extreme", "unusual"}
        )
        
        # Data Preprocessing scenario
        scenarios[DataScienceScenario.DATA_PREPROCESSING] = ScenarioConfig(
            name="Data Preprocessing",
            description="Comprehensive data cleaning, transformation, and preparation for analysis",
            objectives=[
                "Clean and validate data quality",
                "Handle missing values appropriately",
                "Transform data for analysis requirements",
                "Prepare data for downstream tasks"
            ],
            constraints=[
                "Preserve data integrity during transformations",
                "Document all preprocessing steps",
                "Handle different data types appropriately",
                "Consider the impact on analysis validity"
            ],
            preferred_libraries=["pandas", "numpy", "sklearn.preprocessing", "scipy"],
            common_patterns=[
                "Missing value imputation",
                "Data type conversions",
                "Scaling and normalization",
                "Encoding categorical variables"
            ],
            code_templates={
                "missing_value_handling": """
# Missing value analysis and handling
print("Missing Values Summary:")
missing_summary = df.isnull().sum()
print(missing_summary[missing_summary > 0])

# Handle missing values based on data type and percentage
for column in df.columns:
    missing_pct = df[column].isnull().sum() / len(df) * 100
    
    if missing_pct > 0:
        print(f"\\n{column}: {missing_pct:.1f}% missing")
        
        if missing_pct > 50:
            print(f"  Recommendation: Consider dropping {column} (>50% missing)")
        elif df[column].dtype in ['object']:
            # Categorical - use mode or 'Unknown'
            df[column] = df[column].fillna(df[column].mode()[0])
            print(f"  Filled with mode: {df[column].mode()[0]}")
        else:
            # Numerical - use median
            df[column] = df[column].fillna(df[column].median())
            print(f"  Filled with median: {df[column].median()}")
""",
                "data_transformation": """
# Data transformation pipeline
from sklearn.preprocessing import StandardScaler, LabelEncoder

# Numerical columns scaling
numerical_cols = df.select_dtypes(include=[np.number]).columns
scaler = StandardScaler()
df[numerical_cols] = scaler.fit_transform(df[numerical_cols])

# Categorical encoding
categorical_cols = df.select_dtypes(include=['object']).columns
for col in categorical_cols:
    if df[col].nunique() < 10:  # For low cardinality
        # One-hot encoding
        df = pd.get_dummies(df, columns=[col], prefix=col)
    else:  # For high cardinality
        # Label encoding
        le = LabelEncoder()
        df[col] = le.fit_transform(df[col].astype(str))

print("Preprocessing completed:")
print(f"Final shape: {df.shape}")
print(f"Data types:\\n{df.dtypes}")
"""
            },
            best_practices=[
                "Always understand data before preprocessing",
                "Keep track of preprocessing steps for reproducibility",
                "Validate transformations don't introduce bias",
                "Consider the downstream analysis requirements"
            ],
            typical_outputs=["Cleaned datasets", "Transformation pipelines", "Data quality reports"],
            keywords={"preprocessing", "cleaning", "missing", "imputation", "scaling", "encoding", "transformation"}
        )
        
        # Feature Engineering scenario
        scenarios[DataScienceScenario.FEATURE_ENGINEERING] = ScenarioConfig(
            name="Feature Engineering",
            description="Creation and transformation of features to improve model performance",
            explanation="""
            Feature engineering is the process of creating, transforming, and selecting features to improve
            machine learning model performance. This includes domain-specific feature creation, mathematical
            transformations, handling categorical variables, and temporal feature extraction. Advanced patterns
            from task planning examples include multi-level aggregations, pivot-based features, sliding window
            temporal features, and hierarchical feature engineering with business rule validation.
            """,
            objectives=[
                "Create meaningful features from existing data",
                "Transform features for better model performance",
                "Handle feature interactions and non-linearities",
                "Select most relevant features",
                "Implement scalable feature pipelines"
            ],
            constraints=[
                "Avoid data leakage in feature creation",
                "Consider feature interpretability",
                "Validate feature importance and relevance",
                "Handle computational complexity",
                "Ensure feature consistency across environments"
            ],
            preferred_libraries=["pandas", "numpy", "sklearn.feature_selection", "sklearn.preprocessing", "category_encoders"],
            common_patterns=[
                "Feature creation from existing variables",
                "Polynomial and interaction features",
                "Feature selection techniques",
                "Dimensionality reduction"
            ],
            technical_concepts=[
                "Domain-specific feature engineering principles",
                "Mathematical transformations (log, polynomial, trigonometric)",
                "Categorical encoding strategies (one-hot, label, target encoding)",
                "Temporal feature extraction (lags, rolling windows, seasonality)",
                "Text feature engineering (TF-IDF, embeddings, N-grams)",
                "Feature scaling and normalization techniques"
            ],
            key_techniques=[
                "Date/time decomposition into cyclic and trend features",
                "Aggregation features with multiple time windows",
                "Ratio and interaction features for domain insights",
                "Polynomial features for capturing non-linearities",
                "Feature selection with statistical tests and tree-based importance",
                "Binning and discretization for continuous variables"
            ],
            advanced_patterns=[
                "Multi-level hierarchical feature aggregation (item→category→group)",
                "Pivot table features for long-to-wide transformations",
                "Sliding window temporal features with lag optimization",
                "Target encoding with cross-validation to prevent overfitting",
                "Feature interaction mining with automated discovery",
                "Business rule-based feature filtering and validation",
                "Version-controlled feature stores for consistency",
                "Incremental feature computation for streaming data"
            ],
            production_considerations=[
                "Feature pipeline versioning and backward compatibility",
                "Efficient computation for real-time inference",
                "Feature drift monitoring and alerting",
                "A/B testing for feature impact assessment",
                "Memory optimization for high-dimensional features",
                "Feature documentation and lineage tracking",
                "Cross-team feature sharing and governance",
                "Automated feature quality monitoring"
            ],
            evaluation_methods=[
                "Feature importance ranking with multiple algorithms",
                "Univariate statistical tests (chi-square, ANOVA, mutual information)",
                "Correlation analysis and multicollinearity detection",
                "Permutation importance for model-agnostic evaluation",
                "Feature ablation studies for impact assessment",
                "Business metric correlation for feature validation",
                "Cross-validation stability of feature importance",
                "Feature engineering ROI measurement"
            ],
            code_templates={
                "feature_creation": """
# Feature engineering pipeline
# Date/time features
if 'date' in df.columns:
    df['date'] = pd.to_datetime(df['date'])
    df['year'] = df['date'].dt.year
    df['month'] = df['date'].dt.month
    df['day_of_week'] = df['date'].dt.dayofweek
    df['is_weekend'] = df['day_of_week'].isin([5, 6]).astype(int)

# Numerical feature transformations
for col in df.select_dtypes(include=[np.number]).columns:
    # Log transformation for skewed data
    if df[col].min() > 0:  # Only for positive values
        df[f'{col}_log'] = np.log1p(df[col])
    
    # Binning
    df[f'{col}_binned'] = pd.cut(df[col], bins=5, labels=['low', 'med_low', 'med', 'med_high', 'high'])

# Interaction features (example for two numerical columns)
numerical_cols = df.select_dtypes(include=[np.number]).columns[:2]
if len(numerical_cols) >= 2:
    col1, col2 = numerical_cols[0], numerical_cols[1]
    df[f'{col1}_{col2}_interaction'] = df[col1] * df[col2]
    df[f'{col1}_{col2}_ratio'] = df[col1] / (df[col2] + 1e-8)  # Add small epsilon to avoid division by zero

print(f"Feature engineering completed. New shape: {df.shape}")
""",
                "feature_selection": """
# Feature selection
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression

# Prepare features and target
X = df.drop(columns=['{target_column}'])
y = df['{target_column}']

# Statistical feature selection
selector_stat = SelectKBest(score_func=f_regression, k=10)
X_selected_stat = selector_stat.fit_transform(X, y)
selected_features_stat = X.columns[selector_stat.get_support()]

# Mutual information feature selection
selector_mi = SelectKBest(score_func=mutual_info_regression, k=10)
X_selected_mi = selector_mi.fit_transform(X, y)
selected_features_mi = X.columns[selector_mi.get_support()]

print("Top features (Statistical):")
print(selected_features_stat.tolist())
print("\\nTop features (Mutual Information):")
print(selected_features_mi.tolist())
"""
            },
            best_practices=[
                "Create domain-meaningful features",
                "Validate feature importance before using in models",
                "Consider feature scaling after engineering",
                "Document feature creation logic for reproducibility"
            ],
            typical_outputs=["Engineered features", "Feature importance rankings", "Selected feature sets"],
            keywords={"feature", "engineering", "interaction", "polynomial", "selection", "transformation", "creation"}
        )
        
        # Machine Learning scenario
        scenarios[DataScienceScenario.MACHINE_LEARNING] = ScenarioConfig(
            name="Machine Learning",
            description="Model development, training, evaluation, and optimization for predictive tasks",
            explanation="""
            Machine learning encompasses the full lifecycle of predictive model development, from data preparation
            through model deployment. This includes supervised learning (classification/regression), unsupervised
            learning (clustering), and specialized techniques like time series forecasting. Key focus areas include
            proper data splitting, hyperparameter optimization, model evaluation, and production considerations.
            Based on task planning examples, advanced patterns include distributed training, MLOps integration,
            and incremental learning approaches.
            """,
            objectives=[
                "Develop appropriate ML models for the task",
                "Train and validate model performance",
                "Optimize hyperparameters systematically",
                "Evaluate model generalization and robustness",
                "Implement production-ready ML pipelines"
            ],
            constraints=[
                "Use appropriate train/validation/test splits",
                "Apply proper cross-validation techniques",
                "Avoid overfitting and data leakage",
                "Use relevant evaluation metrics",
                "Ensure reproducible results with random seeds",
                "Handle class imbalance appropriately"
            ],
            preferred_libraries=["sklearn", "pandas", "numpy", "matplotlib", "seaborn", "xgboost", "hyperopt", "mlflow"],
            discouraged_libraries=[],
            common_patterns=[
                "Data splitting and cross-validation",
                "Model training and evaluation",
                "Hyperparameter tuning",
                "Model comparison and selection"
            ],
            technical_concepts=[
                "Supervised vs unsupervised learning paradigms",
                "Bias-variance tradeoff and model complexity",
                "Regularization techniques (L1, L2, elastic net)",
                "Ensemble methods (bagging, boosting, stacking)",
                "Feature importance and model interpretability",
                "Cross-validation strategies and data leakage prevention"
            ],
            key_techniques=[
                "train_test_split with stratification for balanced splits",
                "GridSearchCV and RandomizedSearchCV for hyperparameter tuning",
                "Pipeline construction for reproducible workflows",
                "Feature scaling and preprocessing integration",
                "Model evaluation with multiple metrics",
                "Learning curves for diagnosing overfitting/underfitting"
            ],
            advanced_patterns=[
                "Hyperopt for Bayesian hyperparameter optimization",
                "XGBoost with early stopping and custom objectives",
                "Distributed training with Spark MLlib",
                "MLflow for experiment tracking and model versioning",
                "Incremental learning for streaming data",
                "Model stacking and ensemble techniques",
                "SMOTE and advanced resampling for imbalanced data",
                "Feature selection with statistical methods and business rules"
            ],
            production_considerations=[
                "Model versioning and experiment tracking with MLflow",
                "Scalable inference pipelines for batch and real-time scoring",
                "Model monitoring and drift detection",
                "A/B testing frameworks for model deployment",
                "Feature store integration for consistent preprocessing",
                "Containerization and deployment orchestration",
                "Model interpretability and explainability requirements",
                "Performance optimization for inference latency"
            ],
            evaluation_methods=[
                "Multi-metric evaluation (accuracy, precision, recall, F1, AUC)",
                "Confusion matrices and classification reports",
                "Cross-validation with temporal splits for time series",
                "Hold-out validation sets for final model assessment",
                "Learning curves and validation curves",
                "Feature importance analysis and SHAP values",
                "Model calibration and reliability assessment",
                "Business metric alignment and ROI evaluation"
            ],
            code_templates={
                "model_pipeline": """
# Machine Learning Pipeline
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score

# Prepare data
X = df.drop(columns=['{target_column}'])
y = df['{target_column}']

# Split data
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Train multiple models
models = {{
    'Linear Regression': LinearRegression(),
    'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42)
}}

results = {{}}
for name, model in models.items():
    # Train model
    model.fit(X_train, y_train)
    
    # Predictions
    y_pred = model.predict(X_test)
    
    # Evaluation
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    # Cross-validation
    cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='r2')
    
    results[name] = {{
        'MSE': mse,
        'R²': r2,
        'CV R² (mean)': cv_scores.mean(),
        'CV R² (std)': cv_scores.std()
    }}

# Display results
print("Model Performance Comparison:")
for model_name, metrics in results.items():
    print(f"\\n{model_name}:")
    for metric, value in metrics.items():
        print(f"  {metric}: {value:.4f}")
""",
                "hyperparameter_tuning": """
# Hyperparameter optimization
from sklearn.model_selection import GridSearchCV
from sklearn.ensemble import RandomForestRegressor

# Define parameter grid
param_grid = {{
    'n_estimators': [50, 100, 200],
    'max_depth': [None, 10, 20],
    'min_samples_split': [2, 5, 10]
}}

# Grid search with cross-validation
rf = RandomForestRegressor(random_state=42)
grid_search = GridSearchCV(rf, param_grid, cv=5, scoring='r2', n_jobs=-1)
grid_search.fit(X_train, y_train)

print("Best parameters:")
print(grid_search.best_params_)
print(f"Best cross-validation score: {grid_search.best_score_:.4f}")

# Evaluate best model
best_model = grid_search.best_estimator_
y_pred_best = best_model.predict(X_test)
print(f"Test R²: {r2_score(y_test, y_pred_best):.4f}")
"""
            },
            best_practices=[
                "Always use proper train/validation/test splits",
                "Apply cross-validation for robust evaluation",
                "Use appropriate metrics for your problem type",
                "Check for overfitting and validate generalization"
            ],
            typical_outputs=["Trained models", "Performance metrics", "Hyperparameter results"],
            keywords={"machine", "learning", "model", "train", "predict", "sklearn", "regression", "classification"}
        )
        
        return scenarios
    
    def get_scenario_config(self, scenario: DataScienceScenario) -> ScenarioConfig:
        """Get configuration for a specific scenario."""
        return self.scenarios[scenario]
    
    def detect_scenario_from_instruction(self, instruction: str) -> DataScienceScenario:
        """Automatically detect the most likely scenario from user instruction."""
        instruction_lower = instruction.lower()
        
        # Score each scenario based on keyword matches
        scenario_scores = {}
        
        for scenario, config in self.scenarios.items():
            score = 0
            
            # Check for direct keyword matches
            for keyword in config.keywords:
                if keyword in instruction_lower:
                    score += 1
            
            # Check for pattern matches in instruction
            for pattern in config.common_patterns:
                if any(word in instruction_lower for word in pattern.lower().split()):
                    score += 0.5
            
            scenario_scores[scenario] = score
        
        # Return scenario with highest score, default to GENERAL
        best_scenario = max(scenario_scores.items(), key=lambda x: x[1])
        
        if best_scenario[1] > 0:
            return best_scenario[0]
        else:
            return DataScienceScenario.GENERAL
    
    def get_scenario_prompt_enhancement(self, scenario: DataScienceScenario) -> str:
        """Get scenario-specific prompt enhancements for code generation."""
        config = self.scenarios[scenario]
        
        prompt_enhancement = f"""
SCENARIO: {config.name}
DESCRIPTION: {config.description}

{config.explanation.strip() if config.explanation else ""}

OBJECTIVES:
{chr(10).join(f"• {obj}" for obj in config.objectives)}

CONSTRAINTS:
{chr(10).join(f"• {constraint}" for constraint in config.constraints)}

PREFERRED LIBRARIES: {', '.join(config.preferred_libraries)}
"""
        
        if config.discouraged_libraries:
            prompt_enhancement += f"AVOID LIBRARIES: {', '.join(config.discouraged_libraries)}\n"
        
        if config.technical_concepts:
            prompt_enhancement += f"""
TECHNICAL CONCEPTS:
{chr(10).join(f"• {concept}" for concept in config.technical_concepts)}
"""
        
        if config.key_techniques:
            prompt_enhancement += f"""
KEY TECHNIQUES:
{chr(10).join(f"• {technique}" for technique in config.key_techniques)}
"""
        
        if config.advanced_patterns:
            prompt_enhancement += f"""
ADVANCED PATTERNS:
{chr(10).join(f"• {pattern}" for pattern in config.advanced_patterns[:5])}  # Limit to 5 for prompt length
"""
        
        if config.best_practices:
            prompt_enhancement += f"""
BEST PRACTICES:
{chr(10).join(f"• {practice}" for practice in config.best_practices)}
"""
        
        if config.evaluation_methods:
            prompt_enhancement += f"""
EVALUATION METHODS:
{chr(10).join(f"• {method}" for method in config.evaluation_methods[:3])}  # Limit to 3 for prompt length
"""
        
        return prompt_enhancement
    
    def get_code_template(self, scenario: DataScienceScenario, template_name: str) -> Optional[str]:
        """Get a specific code template for a scenario."""
        config = self.scenarios[scenario]
        return config.code_templates.get(template_name)
    
    def list_available_scenarios(self) -> List[str]:
        """List all available scenarios."""
        return [scenario.value for scenario in DataScienceScenario]
    
    def get_scenario_by_name(self, name: str) -> Optional[DataScienceScenario]:
        """Get scenario enum by name string."""
        try:
            return DataScienceScenario(name)
        except ValueError:
            return None


# Global scenario manager instance
scenario_manager = DataScienceScenarioManager()


# Convenience functions
def get_scenario_config(scenario_name: str) -> Optional[ScenarioConfig]:
    """Convenience function to get scenario config by name."""
    scenario = scenario_manager.get_scenario_by_name(scenario_name)
    if scenario:
        return scenario_manager.get_scenario_config(scenario)
    return None


def detect_scenario(instruction: str) -> str:
    """Convenience function to detect scenario from instruction."""
    scenario = scenario_manager.detect_scenario_from_instruction(instruction)
    return scenario.value


def get_scenario_prompt(scenario_name: str) -> str:
    """Convenience function to get scenario-specific prompt enhancement."""
    scenario = scenario_manager.get_scenario_by_name(scenario_name)
    if scenario:
        return scenario_manager.get_scenario_prompt_enhancement(scenario)
    return ""