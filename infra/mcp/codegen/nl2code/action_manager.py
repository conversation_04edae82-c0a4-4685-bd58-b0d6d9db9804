"""
Previous Action Management Module for nl2code service.

This module intelligently manages the previous_actions context to optimize
code generation by:
1. Filtering out irrelevant or failed actions
2. Summarizing repetitive operations
3. Prioritizing actions based on current task relevance
4. Managing context length to avoid token limits
5. Learning from execution patterns

The goal is to provide the most useful execution history context while
keeping it concise and relevant.
"""

import re
import json
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import hashlib


class ActionStatus(Enum):
    """Status of a previous action."""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    PARTIAL = "partial"


class ActionType(Enum):
    """Type classification of actions."""
    DATA_LOADING = "data_loading"
    DATA_PROCESSING = "data_processing"
    VISUALIZATION = "visualization"
    MODEL_TRAINING = "model_training"
    ANALYSIS = "analysis"
    UTILITY = "utility"
    UNKNOWN = "unknown"


class RelevanceScore(Enum):
    """Relevance score for action prioritization."""
    CRITICAL = 5    # Essential for current task
    HIGH = 4        # Very relevant
    MEDIUM = 3      # Moderately relevant
    LOW = 2         # Minimally relevant
    IRRELEVANT = 1  # Can be removed


@dataclass
class ActionRecord:
    """Enhanced representation of a previous action."""
    
    # Original data
    code: str
    exec_status: str
    output: str
    
    # Enhanced metadata
    action_type: ActionType = ActionType.UNKNOWN
    status: ActionStatus = ActionStatus.SUCCESS
    relevance_score: RelevanceScore = RelevanceScore.MEDIUM
    
    # Analysis results
    variables_created: Set[str] = field(default_factory=set)
    variables_used: Set[str] = field(default_factory=set)
    libraries_used: Set[str] = field(default_factory=set)
    keywords: Set[str] = field(default_factory=set)
    
    # Management metadata
    timestamp: Optional[datetime] = None
    code_hash: Optional[str] = None
    can_summarize: bool = True
    is_redundant: bool = False
    
    def __post_init__(self):
        """Initialize computed fields."""
        if self.timestamp is None:
            self.timestamp = datetime.now()
        
        if self.code_hash is None:
            self.code_hash = hashlib.md5(self.code.encode()).hexdigest()[:8]
        
        # Parse execution status
        if "error" in self.exec_status.lower() or "fail" in self.exec_status.lower():
            self.status = ActionStatus.ERROR
        elif "warning" in self.exec_status.lower():
            self.status = ActionStatus.WARNING
        elif "success" in self.exec_status.lower():
            self.status = ActionStatus.SUCCESS
        
        # Analyze code content
        self._analyze_code()
    
    def _analyze_code(self):
        """Analyze code content to extract metadata."""
        lines = self.code.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            # Extract variable assignments
            var_assignment = re.search(r'^(\w+)\s*=', line)
            if var_assignment:
                self.variables_created.add(var_assignment.group(1))
            
            # Extract variable usage
            variables_in_line = re.findall(r'\b(\w+)\b', line)
            for var in variables_in_line:
                if not var.isdigit() and len(var) > 1:
                    self.variables_used.add(var)
            
            # Extract library imports
            import_match = re.match(r'import\s+(\w+)', line)
            if import_match:
                self.libraries_used.add(import_match.group(1))
            
            from_import_match = re.match(r'from\s+(\w+)', line)
            if from_import_match:
                self.libraries_used.add(from_import_match.group(1))
        
        # Classify action type based on code content
        self._classify_action_type()
        
        # Extract keywords from code
        self._extract_keywords()
    
    def _classify_action_type(self):
        """Classify the action type based on code content."""
        code_lower = self.code.lower()
        
        # Data loading patterns
        if any(pattern in code_lower for pattern in [
            'read_csv', 'read_excel', 'read_json', 'load_data', 'pd.read',
            'spark.read', 'load(', 'open('
        ]):
            self.action_type = ActionType.DATA_LOADING
        
        # Visualization patterns
        elif any(pattern in code_lower for pattern in [
            'plot(', 'plt.', 'sns.', 'seaborn', 'matplotlib', 'show()',
            'figure(', 'subplot(', 'plotly'
        ]):
            self.action_type = ActionType.VISUALIZATION
        
        # Model training patterns
        elif any(pattern in code_lower for pattern in [
            'fit(', 'train(', 'model.', 'sklearn', 'tensorflow', 'pytorch',
            'xgboost', 'lightgbm', 'predict(', '.score('
        ]):
            self.action_type = ActionType.MODEL_TRAINING
        
        # Data processing patterns
        elif any(pattern in code_lower for pattern in [
            'groupby', 'merge(', 'join(', 'transform(', 'apply(',
            'dropna(', 'fillna(', 'reset_index(', 'pivot'
        ]):
            self.action_type = ActionType.DATA_PROCESSING
        
        # Analysis patterns
        elif any(pattern in code_lower for pattern in [
            'describe(', 'info()', 'value_counts(', 'corr()', 'mean(',
            'std(', 'count()', 'nunique()', 'summary'
        ]):
            self.action_type = ActionType.ANALYSIS
        
        # Utility patterns
        elif any(pattern in code_lower for pattern in [
            'print(', 'len(', 'type(', 'shape', 'head(', 'tail(',
            'columns', 'dtypes'
        ]):
            self.action_type = ActionType.UTILITY
    
    def _extract_keywords(self):
        """Extract important keywords from code and output."""
        # Extract from code
        code_words = re.findall(r'\b[a-zA-Z_]\w*\b', self.code)
        
        # Extract from output (if available)
        output_words = []
        if self.output:
            output_words = re.findall(r'\b[a-zA-Z_]\w*\b', self.output)
        
        # Combine and filter important keywords
        all_words = set(code_words + output_words)
        
        # Filter out common/unimportant words
        stop_words = {
            'and', 'or', 'not', 'in', 'is', 'if', 'else', 'for', 'while',
            'def', 'class', 'import', 'from', 'as', 'with', 'try', 'except',
            'print', 'len', 'str', 'int', 'float', 'list', 'dict', 'set',
            'true', 'false', 'none', 'self'
        }
        
        self.keywords = {word.lower() for word in all_words 
                        if len(word) > 2 and word.lower() not in stop_words}


@dataclass
class ActionManagerConfig:
    """Configuration for Previous Action Manager."""
    
    # Basic limits
    max_actions: int = 20
    max_tokens: int = 2000
    
    # Filtering controls
    filter_failed_actions: bool = True
    filter_redundant_actions: bool = True
    keep_error_context: bool = True  # Keep errors that led to successful fixes
    
    # Relevance scoring controls
    enable_relevance_scoring: bool = True
    relevance_boost_keywords: List[str] = field(default_factory=list)  # Boost actions containing these keywords
    relevance_penalty_keywords: List[str] = field(default_factory=list)  # Penalize actions containing these keywords
    min_relevance_threshold: int = 1  # Minimum relevance score to keep (1-5)
    
    # Summarization controls
    enable_summarization: bool = True
    summarization_threshold: int = 3  # Summarize groups of 3+ similar actions
    preserve_critical_actions: bool = True  # Never summarize critical actions
    
    # Action type preferences
    prioritize_action_types: List[str] = field(default_factory=list)  # Boost these action types
    deprioritize_action_types: List[str] = field(default_factory=list)  # Lower priority for these types
    
    # Context management
    maintain_execution_order: bool = True  # Keep chronological order for dependencies
    include_execution_stats: bool = False  # Include execution timing/stats info
    
    # Advanced controls
    enable_dependency_analysis: bool = True  # Analyze variable dependencies
    variable_importance_boost: float = 1.5  # Boost factor for actions creating important variables
    instruction_keyword_boost: float = 2.0  # Boost factor for instruction-relevant actions
    
    # Debug and reporting
    enable_debug_logging: bool = False
    generate_optimization_report: bool = True


class PreviousActionManager:
    """Main manager for previous actions optimization."""
    
    def __init__(self, config: Optional[ActionManagerConfig] = None, **kwargs):
        # Support both config object and individual parameters for backward compatibility
        if config is None:
            config = ActionManagerConfig(**kwargs)
        elif kwargs:
            # Merge config with additional kwargs
            config_dict = config.__dict__.copy()
            config_dict.update(kwargs)
            config = ActionManagerConfig(**config_dict)
            
        self.config = config
        self.max_actions = config.max_actions  # Keep for backward compatibility
        self.max_tokens = config.max_tokens
        self.action_patterns = self._initialize_patterns()
        self.debug_info = []
    
    def _initialize_patterns(self) -> Dict[str, Any]:
        """Initialize patterns for action analysis."""
        return {
            "redundant_patterns": [
                r"print\(\w+\.head\(\)\)",
                r"print\(\w+\.shape\)",
                r"print\(\w+\.info\(\)\)",
                r"\w+\.describe\(\)"
            ],
            "error_keywords": [
                "error", "exception", "traceback", "failed", "not found"
            ],
            "success_indicators": [
                "completed", "success", "finished", "done", "shape:"
            ]
        }
    
    def process_actions(self, 
                       raw_actions: List[Tuple[str, str, str]], 
                       current_instruction: str = "",
                       context_vars: Dict[str, str] = None) -> List[Tuple[str, str, str]]:
        """
        Process and optimize previous actions for better context.
        
        Args:
            raw_actions: List of (code, exec_status, output) tuples
            current_instruction: Current user instruction for relevance scoring
            context_vars: Current context variables for dependency analysis
            
        Returns:
            Optimized list of previous actions
        """
        if not raw_actions:
            return []
        
        # Convert to ActionRecord objects
        action_records = [
            ActionRecord(code=code, exec_status=status, output=output)
            for code, status, output in raw_actions
        ]
        
        # Apply optimization strategies
        optimized_records = self._apply_optimization_strategies(
            action_records, current_instruction, context_vars or {}
        )
        
        # Convert back to original format
        return [
            (record.code, record.exec_status, record.output)
            for record in optimized_records
        ]
    
    def _apply_optimization_strategies(self, 
                                     records: List[ActionRecord],
                                     current_instruction: str,
                                     context_vars: Dict[str, str]) -> List[ActionRecord]:
        """Apply various optimization strategies based on configuration."""
        
        if self.config.enable_debug_logging:
            self.debug_info.append(f"Starting optimization with {len(records)} actions")
        
        # Strategy 1: Remove failed actions (if enabled)
        if self.config.filter_failed_actions:
            records = self._filter_failed_actions(records)
            if self.config.enable_debug_logging:
                self.debug_info.append(f"After filtering failed actions: {len(records)} actions")
        
        # Strategy 2: Remove redundant actions (if enabled)
        if self.config.filter_redundant_actions:
            records = self._remove_redundant_actions(records)
            if self.config.enable_debug_logging:
                self.debug_info.append(f"After filtering redundant actions: {len(records)} actions")
        
        # Strategy 3: Score relevance based on current instruction (if enabled)
        if self.config.enable_relevance_scoring:
            records = self._score_relevance(records, current_instruction, context_vars)
            if self.config.enable_debug_logging:
                self.debug_info.append(f"Relevance scoring completed")
        
        # Strategy 4: Apply action type preferences
        records = self._apply_action_type_preferences(records)
        
        # Strategy 5: Filter by minimum relevance threshold
        if self.config.enable_relevance_scoring and self.config.min_relevance_threshold > 1:
            records = self._filter_by_relevance_threshold(records)
            if self.config.enable_debug_logging:
                self.debug_info.append(f"After relevance filtering: {len(records)} actions")
        
        # Strategy 6: Prioritize and limit actions
        records = self._prioritize_and_limit(records)
        if self.config.enable_debug_logging:
            self.debug_info.append(f"After prioritization and limiting: {len(records)} actions")
        
        # Strategy 7: Summarize if still too long (if enabled)
        if self.config.enable_summarization:
            records = self._summarize_if_needed(records)
            if self.config.enable_debug_logging:
                self.debug_info.append(f"After summarization: {len(records)} actions")
        
        return records
    
    def _filter_failed_actions(self, records: List[ActionRecord]) -> List[ActionRecord]:
        """Remove failed actions that don't provide learning value."""
        filtered = []
        
        for record in records:
            if record.status == ActionStatus.ERROR:
                if self.config.keep_error_context:
                    # Keep errors if they led to a successful retry
                    next_success = any(
                        r.status == ActionStatus.SUCCESS and 
                        len(r.variables_created & record.variables_created) > 0
                        for r in records[records.index(record)+1:]
                    )
                    if next_success:
                        # Summarize the error instead of keeping full detail
                        record.code = f"# Previous attempt failed: {record.exec_status}"
                        record.output = "Error (see comment)"
                        filtered.append(record)
                # If keep_error_context is False, skip all error actions
            else:
                filtered.append(record)
        
        return filtered
    
    def _remove_redundant_actions(self, records: List[ActionRecord]) -> List[ActionRecord]:
        """Remove redundant actions like repeated data inspection."""
        seen_patterns = set()
        filtered = []
        
        for record in records:
            # Create a pattern signature for the action
            pattern_signature = self._create_pattern_signature(record)
            
            # Check if this pattern was seen recently
            if pattern_signature in seen_patterns:
                # Skip utility actions that are repetitive
                if record.action_type == ActionType.UTILITY:
                    continue
                # Keep other types but mark as potentially redundant
                record.is_redundant = True
            
            seen_patterns.add(pattern_signature)
            filtered.append(record)
            
            # Only keep track of recent patterns
            if len(seen_patterns) > 10:
                seen_patterns.clear()
        
        return filtered
    
    def _create_pattern_signature(self, record: ActionRecord) -> str:
        """Create a signature for pattern matching."""
        # Normalize code by removing variable names and keeping structure
        normalized = re.sub(r'\b\w+\b', 'VAR', record.code)
        normalized = re.sub(r'\d+', 'NUM', normalized)
        normalized = re.sub(r'\s+', ' ', normalized.strip())
        
        return f"{record.action_type.value}:{normalized[:50]}"
    
    def _score_relevance(self, 
                        records: List[ActionRecord],
                        current_instruction: str,
                        context_vars: Dict[str, str]) -> List[ActionRecord]:
        """Score relevance of each action to current task."""
        
        instruction_keywords = set(re.findall(r'\b[a-zA-Z_]\w*\b', current_instruction.lower()))
        context_var_names = set(context_vars.keys())
        
        # Convert boost/penalty keywords to lowercase for comparison
        boost_keywords = set(word.lower() for word in self.config.relevance_boost_keywords)
        penalty_keywords = set(word.lower() for word in self.config.relevance_penalty_keywords)
        
        for record in records:
            base_score = RelevanceScore.MEDIUM
            
            # High relevance if creates variables used in context
            if record.variables_created & context_var_names:
                if self.config.enable_dependency_analysis:
                    base_score = RelevanceScore.HIGH
                    # Apply variable importance boost
                    boost_factor = self.config.variable_importance_boost
                else:
                    base_score = RelevanceScore.HIGH
            
            # High relevance if keywords match current instruction
            elif record.keywords & instruction_keywords:
                base_score = RelevanceScore.HIGH
                if self.config.enable_dependency_analysis:
                    # Apply instruction keyword boost
                    boost_factor = self.config.instruction_keyword_boost
            
            # Critical if it's a data loading action (usually foundation)
            elif record.action_type == ActionType.DATA_LOADING:
                base_score = RelevanceScore.CRITICAL
            
            # Low relevance for utility actions unless they created important vars
            elif (record.action_type == ActionType.UTILITY and 
                  not record.variables_created & context_var_names):
                base_score = RelevanceScore.LOW
            
            # Irrelevant if failed and didn't lead to success
            elif record.status == ActionStatus.ERROR:
                base_score = RelevanceScore.IRRELEVANT
            
            # Apply keyword-based boosts and penalties
            record_text = (record.code + " " + record.output).lower()
            
            # Check for boost keywords
            if boost_keywords and any(keyword in record_text for keyword in boost_keywords):
                base_score = RelevanceScore(min(5, base_score.value + 1))
            
            # Check for penalty keywords  
            if penalty_keywords and any(keyword in record_text for keyword in penalty_keywords):
                base_score = RelevanceScore(max(1, base_score.value - 1))
            
            record.relevance_score = base_score
        
        return records
    
    def _apply_action_type_preferences(self, records: List[ActionRecord]) -> List[ActionRecord]:
        """Apply action type prioritization and depriorization."""
        
        prioritize_types = set(self.config.prioritize_action_types)
        deprioritize_types = set(self.config.deprioritize_action_types)
        
        for record in records:
            action_type_name = record.action_type.value
            
            # Boost prioritized action types
            if action_type_name in prioritize_types:
                current_score = record.relevance_score.value
                record.relevance_score = RelevanceScore(min(5, current_score + 1))
            
            # Lower priority for deprioritized action types
            elif action_type_name in deprioritize_types:
                current_score = record.relevance_score.value
                record.relevance_score = RelevanceScore(max(1, current_score - 1))
        
        return records
    
    def _filter_by_relevance_threshold(self, records: List[ActionRecord]) -> List[ActionRecord]:
        """Filter actions by minimum relevance threshold."""
        
        return [record for record in records 
                if record.relevance_score.value >= self.config.min_relevance_threshold]
    
    def _prioritize_and_limit(self, records: List[ActionRecord]) -> List[ActionRecord]:
        """Prioritize actions and limit to max_actions."""
        
        # Sort by relevance score (descending) and recency
        sorted_records = sorted(
            records,
            key=lambda r: (r.relevance_score.value, records.index(r)),
            reverse=True
        )
        
        # Keep top actions, but ensure we maintain execution order for critical ones
        critical_actions = [r for r in sorted_records if r.relevance_score == RelevanceScore.CRITICAL]
        other_actions = [r for r in sorted_records if r.relevance_score != RelevanceScore.CRITICAL]
        
        # Maintain order for critical actions, limit others
        final_actions = critical_actions[:10]  # At most 10 critical actions
        
        remaining_slots = self.max_actions - len(final_actions)
        if remaining_slots > 0:
            final_actions.extend(other_actions[:remaining_slots])
        
        # Restore chronological order for final list if configured
        if self.config.maintain_execution_order:
            final_actions.sort(key=lambda r: records.index(r))
        
        return final_actions
    
    def _summarize_if_needed(self, records: List[ActionRecord]) -> List[ActionRecord]:
        """Summarize actions if context is still too long."""
        
        # Estimate token count
        estimated_tokens = sum(len(r.code.split()) + len(r.output.split()) for r in records)
        
        if estimated_tokens <= self.max_tokens:
            return records
        
        # Group similar actions for summarization
        summarized = []
        current_group = []
        current_type = None
        
        for record in records:
            # Check if action can be summarized based on configuration
            can_summarize = (record.can_summarize and 
                           (not self.config.preserve_critical_actions or 
                            record.relevance_score != RelevanceScore.CRITICAL))
            
            if (record.action_type == current_type and 
                can_summarize and 
                len(current_group) < self.config.summarization_threshold):
                current_group.append(record)
            else:
                # Process previous group
                if current_group:
                    if len(current_group) > 1:
                        summarized.append(self._create_summary_record(current_group))
                    else:
                        summarized.extend(current_group)
                
                # Start new group
                current_group = [record]
                current_type = record.action_type
        
        # Process final group
        if current_group:
            if len(current_group) > 1:
                summarized.append(self._create_summary_record(current_group))
            else:
                summarized.extend(current_group)
        
        return summarized
    
    def _create_summary_record(self, records: List[ActionRecord]) -> ActionRecord:
        """Create a summary record from multiple similar actions."""
        
        # Combine information from all records
        all_vars_created = set()
        all_vars_used = set()
        all_libraries = set()
        
        for record in records:
            all_vars_created.update(record.variables_created)
            all_vars_used.update(record.variables_used)
            all_libraries.update(record.libraries_used)
        
        # Create summary text
        action_type = records[0].action_type.value
        count = len(records)
        
        summary_code = f"""# Summary: {count} {action_type} operations
# Variables created: {', '.join(sorted(all_vars_created)) if all_vars_created else 'none'}
# Variables used: {', '.join(sorted(all_vars_used)) if all_vars_used else 'none'}
# Libraries: {', '.join(sorted(all_libraries)) if all_libraries else 'none'}"""
        
        summary_output = f"Multiple {action_type} operations completed successfully"
        
        # Create summary record
        summary = ActionRecord(
            code=summary_code,
            exec_status="success",
            output=summary_output,
            action_type=records[0].action_type,
            status=ActionStatus.SUCCESS,
            relevance_score=max(r.relevance_score for r in records)
        )
        
        summary.variables_created = all_vars_created
        summary.variables_used = all_vars_used
        summary.libraries_used = all_libraries
        summary.can_summarize = False  # Don't summarize summaries
        
        return summary
    
    def get_optimization_report(self, 
                               original_actions: List[Tuple[str, str, str]],
                               optimized_actions: List[Tuple[str, str, str]]) -> Dict[str, Any]:
        """Generate a report on the optimization performed."""
        
        original_tokens = sum(len(code.split()) + len(output.split()) 
                             for code, _, output in original_actions)
        optimized_tokens = sum(len(code.split()) + len(output.split()) 
                              for code, _, output in optimized_actions)
        
        report = {
            "original_count": len(original_actions),
            "optimized_count": len(optimized_actions),
            "actions_removed": len(original_actions) - len(optimized_actions),
            "original_tokens": original_tokens,
            "optimized_tokens": optimized_tokens,
            "token_reduction": original_tokens - optimized_tokens,
            "compression_ratio": optimized_tokens / original_tokens if original_tokens > 0 else 1.0,
            "configuration": {
                "max_actions": self.config.max_actions,
                "max_tokens": self.config.max_tokens,
                "filter_failed_actions": self.config.filter_failed_actions,
                "filter_redundant_actions": self.config.filter_redundant_actions,
                "enable_relevance_scoring": self.config.enable_relevance_scoring,
                "enable_summarization": self.config.enable_summarization,
                "min_relevance_threshold": self.config.min_relevance_threshold
            }
        }
        
        if self.config.enable_debug_logging and self.debug_info:
            report["debug_info"] = self.debug_info.copy()
        
        return report
    
    def get_debug_info(self) -> List[str]:
        """Get debug information from the last optimization run."""
        return self.debug_info.copy()
    
    def clear_debug_info(self):
        """Clear debug information."""
        self.debug_info.clear()
    
    def update_config(self, **kwargs):
        """Update configuration parameters."""
        config_dict = self.config.__dict__.copy()
        config_dict.update(kwargs)
        self.config = ActionManagerConfig(**config_dict)
        
        # Update derived attributes
        self.max_actions = self.config.max_actions
        self.max_tokens = self.config.max_tokens


def optimize_previous_actions(actions: List[Tuple[str, str, str]], 
                            current_instruction: str = "",
                            context_vars: Dict[str, str] = None,
                            config: Optional[ActionManagerConfig] = None,
                            **kwargs) -> List[Tuple[str, str, str]]:
    """
    Convenience function to optimize previous actions.
    
    Args:
        actions: List of (code, exec_status, output) tuples
        current_instruction: Current user instruction
        context_vars: Current context variables
        config: ActionManagerConfig object (optional)
        **kwargs: Additional configuration parameters (for backward compatibility)
        
    Returns:
        Optimized list of previous actions
    """
    # Support both config object and individual parameters
    if config is None and kwargs:
        config = ActionManagerConfig(**kwargs)
    elif config is None:
        config = ActionManagerConfig()
    
    manager = PreviousActionManager(config=config)
    return manager.process_actions(actions, current_instruction, context_vars)


# Convenience functions for common configuration patterns
def create_conservative_config() -> ActionManagerConfig:
    """Create a conservative configuration that preserves more context."""
    return ActionManagerConfig(
        max_actions=30,
        max_tokens=3000,
        filter_failed_actions=False,
        filter_redundant_actions=True,
        enable_summarization=False,
        min_relevance_threshold=1,
        preserve_critical_actions=True,
        maintain_execution_order=True
    )


def create_aggressive_config() -> ActionManagerConfig:
    """Create an aggressive configuration that maximizes compression."""
    return ActionManagerConfig(
        max_actions=10,
        max_tokens=1000,
        filter_failed_actions=True,
        filter_redundant_actions=True,
        enable_summarization=True,
        min_relevance_threshold=3,
        preserve_critical_actions=True,
        maintain_execution_order=False,
        summarization_threshold=2
    )


def create_visualization_focused_config() -> ActionManagerConfig:
    """Create a configuration optimized for visualization tasks."""
    return ActionManagerConfig(
        max_actions=15,
        max_tokens=2000,
        prioritize_action_types=["visualization", "data_processing"],
        deprioritize_action_types=["utility", "analysis"],
        relevance_boost_keywords=["plot", "chart", "graph", "visualization", "matplotlib", "seaborn"],
        relevance_penalty_keywords=["model", "train", "fit", "predict"],
        enable_summarization=True,
        preserve_critical_actions=True
    )


def create_ml_focused_config() -> ActionManagerConfig:
    """Create a configuration optimized for machine learning tasks."""
    return ActionManagerConfig(
        max_actions=20,
        max_tokens=2500,
        prioritize_action_types=["model_training", "data_processing"],
        deprioritize_action_types=["utility", "visualization"],
        relevance_boost_keywords=["model", "train", "fit", "predict", "sklearn", "tensorflow", "pytorch"],
        relevance_penalty_keywords=["plot", "chart", "visualization"],
        enable_summarization=True,
        preserve_critical_actions=True
    )


if __name__ == "__main__":
    # Example usage
    sample_actions = [
        ("df = pd.read_csv('data.csv')", "success", "DataFrame loaded with shape (1000, 5)"),
        ("print(df.head())", "success", "   A  B  C  D  E\n0  1  2  3  4  5"),
        ("print(df.shape)", "success", "(1000, 5)"),
        ("df.info()", "success", "Data types: int64(5)"),
        ("df_clean = df.dropna()", "success", "Cleaned data shape: (995, 5)"),
        ("print(df_clean.head())", "success", "   A  B  C  D  E\n0  1  2  3  4  5"),
        ("df_clean.plot()", "success", "Plot displayed"),
        ("model = LinearRegression()", "error", "sklearn not imported"),
        ("from sklearn.linear_model import LinearRegression", "success", ""),
        ("model = LinearRegression()", "success", "Model created"),
        ("model.fit(X, y)", "success", "Model trained"),
    ]
    
    manager = PreviousActionManager()
    optimized = manager.process_actions(
        sample_actions, 
        "Create a machine learning model to predict target variable",
        {"df_clean": "cleaned dataframe", "model": "trained model"}
    )
    
    print("Original actions:", len(sample_actions))
    print("Optimized actions:", len(optimized))
    
    print("\nOptimized actions:")
    for i, (code, status, output) in enumerate(optimized, 1):
        print(f"{i}. {code[:50]}...")
    
    report = manager.get_optimization_report(sample_actions, optimized)
    print(f"\nOptimization report: {report}")