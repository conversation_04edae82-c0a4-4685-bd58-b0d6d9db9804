"""
Python Library Configuration Manager for nl2code service.

This module handles environment-specific configuration issues that can cause
problems when code is executed in different environments. It provides a 
rule-based system to detect, filter, and replace problematic configurations
with more robust alternatives.

Common issues addressed:
- matplotlib font configurations
- seaborn style/theme settings  
- plotly renderer configurations
- tensorflow/pytorch device settings
- jupyter display configurations
- opencv backend settings
- pandas display options
- numpy library configurations
"""

import re
import json
from typing import List, Dict, Any, Optional, Tuple, Union
from enum import Enum
from dataclasses import dataclass, field


class ConfigAction(Enum):
    """Actions to take when a problematic configuration is detected."""
    FILTER = "filter"          # Remove the line entirely
    REPLACE = "replace"        # Replace with a safer alternative
    COMMENT = "comment"        # Comment out with explanation
    WRAP_TRY = "wrap_try"     # Wrap in try-except block


@dataclass
class ConfigRule:
    """Represents a single configuration rule for a specific library issue."""
    
    # Rule identification
    name: str
    library: str
    description: str
    
    # Pattern matching
    patterns: List[str]
    action: ConfigAction
    
    # Replacement/alternative code
    replacement: Optional[str] = None
    comment: Optional[str] = None
    
    # Conditions
    min_severity: int = 1  # 1=low, 2=medium, 3=high
    environment_tags: List[str] = field(default_factory=list)  # e.g., ['jupyter', 'headless', 'docker']
    
    # Additional metadata
    rationale: Optional[str] = None
    documentation_url: Optional[str] = None


class LibraryConfigManager:
    """Main manager for handling library configuration issues."""
    
    def __init__(self):
        self.rules: List[ConfigRule] = []
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """Initialize with commonly problematic configurations."""
        
        # Matplotlib configurations
        matplotlib_rules = [
            ConfigRule(
                name="matplotlib_font_family",
                library="matplotlib",
                description="Font family settings that may not be available",
                patterns=[
                    r"plt\.rcParams\[[\'\"]font\.family[\'\"]",
                    r"matplotlib\.rcParams\[[\'\"]font\.family[\'\"]",
                    r"rcParams\[[\'\"]font\.family[\'\"]"
                ],
                action=ConfigAction.FILTER,
                comment="Font family configuration removed to prevent font warnings",
                rationale="Specified fonts may not be available in different environments"
            ),
            ConfigRule(
                name="matplotlib_font_properties",
                library="matplotlib", 
                description="Font size, weight, style parameters",
                patterns=[
                    r"fontsize\s*=",
                    r"font_size\s*=", 
                    r"fontweight\s*=",
                    r"font_weight\s*=",
                    r"fontstyle\s*=",
                    r"font_style\s*=",
                    r"fontname\s*=",
                    r"font_name\s*="
                ],
                action=ConfigAction.FILTER,
                comment="Font parameter removed to prevent compatibility issues"
            ),
            ConfigRule(
                name="matplotlib_backend",
                library="matplotlib",
                description="Backend settings that may not be available",
                patterns=[
                    r"matplotlib\.use\s*\(",
                    r"plt\.switch_backend\s*\("
                ],
                action=ConfigAction.WRAP_TRY,
                comment="Backend setting wrapped in try-except for compatibility",
                rationale="Some backends may not be available in all environments"
            )
        ]
        
        # Seaborn configurations
        seaborn_rules = [
            ConfigRule(
                name="seaborn_style_set",
                library="seaborn",
                description="Global style settings that may conflict",
                patterns=[
                    r"sns\.set_style\s*\(",
                    r"seaborn\.set_style\s*\(",
                    r"sns\.set\s*\(",
                    r"seaborn\.set\s*\("
                ],
                action=ConfigAction.REPLACE,
                replacement="# Style setting removed for environment compatibility",
                comment="Seaborn style configuration replaced with default",
                rationale="Global style changes can interfere with other plots"
            ),
            ConfigRule(
                name="seaborn_palette_set",
                library="seaborn", 
                description="Color palette settings",
                patterns=[
                    r"sns\.set_palette\s*\(",
                    r"seaborn\.set_palette\s*\("
                ],
                action=ConfigAction.COMMENT,
                comment="Palette setting commented out for compatibility"
            )
        ]
        
        # TensorFlow/PyTorch configurations
        ml_framework_rules = [
            ConfigRule(
                name="tensorflow_gpu_config",
                library="tensorflow",
                description="GPU memory growth and device configurations",
                patterns=[
                    r"tf\.config\.experimental\.set_memory_growth",
                    r"tf\.config\.gpu\.set_experimental_memory_growth",
                    r"tf\.device\s*\(\s*[\'\"]\/gpu",
                    r"tf\.config\.list_physical_devices"
                ],
                action=ConfigAction.WRAP_TRY,
                comment="GPU configuration wrapped in try-except for CPU fallback",
                rationale="GPU may not be available in all environments"
            ),
            ConfigRule(
                name="pytorch_device_config",
                library="pytorch",
                description="CUDA device specifications",
                patterns=[
                    r"\.cuda\(\)",
                    r"\.to\s*\(\s*[\'\"]cuda",
                    r"torch\.device\s*\(\s*[\'\"]cuda"
                ],
                action=ConfigAction.REPLACE,
                replacement="device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')",
                comment="Device selection made conditional on CUDA availability"
            )
        ]
        
        # Plotly configurations
        plotly_rules = [
            ConfigRule(
                name="plotly_renderer",
                library="plotly",
                description="Renderer configurations that may not work",
                patterns=[
                    r"pio\.renderers\.default\s*=",
                    r"plotly\.io\.renderers\.default\s*="
                ],
                action=ConfigAction.COMMENT,
                comment="Renderer setting commented out for environment compatibility",
                rationale="Some renderers may not be available in headless environments"
            )
        ]
        
        # Jupyter/IPython configurations  
        jupyter_rules = [
            ConfigRule(
                name="jupyter_display_config",
                library="jupyter",
                description="Jupyter-specific display configurations",
                patterns=[
                    r"%matplotlib\s+",
                    r"get_ipython\(\)\.magic",
                    r"IPython\.display\."
                ],
                action=ConfigAction.WRAP_TRY,
                comment="Jupyter-specific code wrapped for non-Jupyter compatibility"
            )
        ]
        
        # OpenCV configurations
        opencv_rules = [
            ConfigRule(
                name="opencv_backend",
                library="opencv",
                description="Backend configurations for headless environments",
                patterns=[
                    r"cv2\.setNumThreads",
                    r"cv2\.setUseOptimized"
                ],
                action=ConfigAction.WRAP_TRY,
                comment="OpenCV configuration wrapped for compatibility"
            )
        ]
        
        # Pandas configurations
        pandas_rules = [
            ConfigRule(
                name="pandas_display_options",
                library="pandas",
                description="Display options that may cause issues",
                patterns=[
                    r"pd\.set_option\s*\(\s*[\'\"]display\.",
                    r"pandas\.set_option\s*\(\s*[\'\"]display\."
                ],
                action=ConfigAction.COMMENT,
                comment="Display option commented out for compatibility"
            )
        ]
        
        # Add all rules
        all_rules = (
            matplotlib_rules + seaborn_rules + ml_framework_rules + 
            plotly_rules + jupyter_rules + opencv_rules + pandas_rules
        )
        
        self.rules.extend(all_rules)
    
    def add_rule(self, rule: ConfigRule):
        """Add a custom configuration rule."""
        self.rules.append(rule)
    
    def remove_rule(self, rule_name: str):
        """Remove a rule by name."""
        self.rules = [rule for rule in self.rules if rule.name != rule_name]
    
    def get_rules_for_library(self, library: str) -> List[ConfigRule]:
        """Get all rules for a specific library."""
        return [rule for rule in self.rules if rule.library.lower() == library.lower()]
    
    def detect_config_issues(self, code: str) -> List[Tuple[ConfigRule, str, int]]:
        """
        Detect configuration issues in code.
        
        Returns:
            List of (rule, matched_line, line_number) tuples
        """
        issues = []
        lines = code.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            for rule in self.rules:
                for pattern in rule.patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        issues.append((rule, line.strip(), line_num))
                        break  # Only match first pattern per rule per line
        
        return issues
    
    def apply_config_fixes(self, code: str, severity_threshold: int = 1) -> Tuple[str, List[str]]:
        """
        Apply configuration fixes to code.
        
        Args:
            code: The Python code to process
            severity_threshold: Minimum severity level to process (1-3)
            
        Returns:
            Tuple of (fixed_code, list_of_applied_fixes)
        """
        lines = code.split('\n')
        applied_fixes = []
        
        for line_num, line in enumerate(lines):
            original_line = line
            
            for rule in self.rules:
                if rule.min_severity < severity_threshold:
                    continue
                    
                for pattern in rule.patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        fixed_line, fix_description = self._apply_rule(line, rule, pattern)
                        if fixed_line != line:
                            lines[line_num] = fixed_line
                            applied_fixes.append(f"Line {line_num + 1}: {fix_description}")
                            line = fixed_line  # Update for subsequent patterns
                            break
        
        return '\n'.join(lines), applied_fixes
    
    def _apply_rule(self, line: str, rule: ConfigRule, pattern: str) -> Tuple[str, str]:
        """Apply a specific rule to a line of code."""
        
        if rule.action == ConfigAction.FILTER:
            return "", f"Filtered {rule.library} configuration: {rule.description}"
        
        elif rule.action == ConfigAction.REPLACE:
            if rule.replacement:
                return rule.replacement, f"Replaced {rule.library} configuration with safe alternative"
            else:
                return f"# {rule.comment or 'Configuration replaced'}", f"Replaced {rule.library} configuration"
        
        elif rule.action == ConfigAction.COMMENT:
            comment_text = rule.comment or f"Problematic {rule.library} configuration"
            return f"# {comment_text}: {line.strip()}", f"Commented out {rule.library} configuration"
        
        elif rule.action == ConfigAction.WRAP_TRY:
            indent = len(line) - len(line.lstrip())
            indent_str = " " * indent
            wrapped = f"{indent_str}try:\n{line}\n{indent_str}except Exception:\n{indent_str}    # {rule.comment or 'Configuration may not be available in all environments'}\n{indent_str}    pass"
            return wrapped, f"Wrapped {rule.library} configuration in try-except"
        
        return line, ""
    
    def get_config_report(self, code: str) -> Dict[str, Any]:
        """Generate a detailed report of configuration issues."""
        issues = self.detect_config_issues(code)
        
        report = {
            "total_issues": len(issues),
            "libraries_affected": list(set(issue[0].library for issue in issues)),
            "severity_breakdown": {
                "low": len([i for i in issues if i[0].min_severity == 1]),
                "medium": len([i for i in issues if i[0].min_severity == 2]), 
                "high": len([i for i in issues if i[0].min_severity == 3])
            },
            "issues_by_library": {},
            "detailed_issues": []
        }
        
        # Group by library
        for library in report["libraries_affected"]:
            library_issues = [i for i in issues if i[0].library == library]
            report["issues_by_library"][library] = len(library_issues)
        
        # Detailed issue information
        for rule, line, line_num in issues:
            report["detailed_issues"].append({
                "line_number": line_num,
                "library": rule.library,
                "rule_name": rule.name,
                "description": rule.description,
                "problematic_code": line,
                "severity": rule.min_severity,
                "recommended_action": rule.action.value,
                "rationale": rule.rationale
            })
        
        return report
    
    def export_rules(self, file_path: str):
        """Export current rules to a JSON file."""
        rules_data = []
        for rule in self.rules:
            rule_dict = {
                "name": rule.name,
                "library": rule.library,
                "description": rule.description,
                "patterns": rule.patterns,
                "action": rule.action.value,
                "replacement": rule.replacement,
                "comment": rule.comment,
                "min_severity": rule.min_severity,
                "environment_tags": rule.environment_tags,
                "rationale": rule.rationale,
                "documentation_url": rule.documentation_url
            }
            rules_data.append(rule_dict)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(rules_data, f, indent=2, ensure_ascii=False)
    
    def import_rules(self, file_path: str):
        """Import rules from a JSON file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            rules_data = json.load(f)
        
        for rule_dict in rules_data:
            rule = ConfigRule(
                name=rule_dict["name"],
                library=rule_dict["library"],
                description=rule_dict["description"],
                patterns=rule_dict["patterns"],
                action=ConfigAction(rule_dict["action"]),
                replacement=rule_dict.get("replacement"),
                comment=rule_dict.get("comment"),
                min_severity=rule_dict.get("min_severity", 1),
                environment_tags=rule_dict.get("environment_tags", []),
                rationale=rule_dict.get("rationale"),
                documentation_url=rule_dict.get("documentation_url")
            )
            self.add_rule(rule)


def apply_library_config_fixes(code: str, severity_threshold: int = 1) -> Tuple[str, List[str]]:
    """
    Convenience function to apply library configuration fixes to code.
    
    Args:
        code: Python code to process
        severity_threshold: Minimum severity level (1=low, 2=medium, 3=high)
        
    Returns:
        Tuple of (fixed_code, list_of_applied_fixes)
    """
    manager = LibraryConfigManager()
    return manager.apply_config_fixes(code, severity_threshold)


def get_library_config_report(code: str) -> Dict[str, Any]:
    """
    Convenience function to get a configuration issues report.
    
    Args:
        code: Python code to analyze
        
    Returns:
        Dictionary containing detailed report of configuration issues
    """
    manager = LibraryConfigManager()
    return manager.get_config_report(code)


if __name__ == "__main__":
    # Example usage
    test_code = """
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf

# Problematic configurations
plt.rcParams['font.family'] = 'Arial'
matplotlib.use('TkAgg')
sns.set_style('darkgrid')
tf.config.experimental.set_memory_growth(gpu, True)

# Normal code
plt.plot([1, 2, 3])
plt.show()
"""
    
    manager = LibraryConfigManager()
    
    # Generate report
    report = manager.get_config_report(test_code)
    print("Configuration Issues Report:")
    print(f"Total issues: {report['total_issues']}")
    print(f"Libraries affected: {report['libraries_affected']}")
    
    # Apply fixes
    fixed_code, fixes = manager.apply_config_fixes(test_code)
    print("\nApplied fixes:")
    for fix in fixes:
        print(f"- {fix}")
    
    print("\nFixed code:")
    print(fixed_code)