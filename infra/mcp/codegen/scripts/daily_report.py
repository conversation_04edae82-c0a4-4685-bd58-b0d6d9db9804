#!/usr/bin/env python3
"""
快速生成今日Codegen运营指标报告
"""
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from infra.mcp.codegen.scripts.codegen_metrics_report import CodegenMetricsProcessor, print_report

def main():
    """生成今日报告"""
    today = datetime.now().strftime("%Y%m%d")
    
    print(f"🚀 正在生成今日({today})Codegen运营指标报告...")
    
    try:
        processor = CodegenMetricsProcessor(k=3, use_es=True)
        report = processor.generate_report(today, today)
        
        # 打印报告
        print_report(report)
        
        # 计算关键指标摘要
        print("\n" + "🌟"*20 + " 关键指标摘要 " + "🌟"*20)
        print("📊 多k值功能正确性:")
        for k, correctness in report.functional_correctness_by_k.items():
            print(f"   k={k}: {correctness:.1%}")
        print("📊 多k值执行成功率:")
        for k, success_rate in report.execution_success_rate_by_k.items():
            print(f"   k={k}: {success_rate:.1%}")
        print(f"平均任务耗时: {report.avg_task_duration_ms/1000:.1f}秒")
        print(f"平均调用耗时: {report.avg_call_duration_ms/1000:.1f}秒")
        print(f"平均任务Token: {report.avg_task_tokens:.0f}")
        print(f"平均调用Token: {report.avg_call_tokens:.0f}")
        
        if report.total_tasks == 0:
            print("\n⚠️  今日暂无codegen任务数据")
        
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()