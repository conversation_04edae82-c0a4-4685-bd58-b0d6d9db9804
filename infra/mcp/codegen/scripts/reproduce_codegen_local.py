#!/usr/bin/env python3
"""
本地 Codegen 复现脚本
直接调用 codegen/nl2code 接口，不依赖 HTTP API
"""
import json
import sys
import os
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from infra.metrics.es_storage import ReplayDataESStorage, MetricsESQueryParams
from common.share.config import appConfig

# 创建模拟的 logger 以避免依赖问题
class MockLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def debug(self, msg): print(f"DEBUG: {msg}")

# 模拟依赖模块
sys.modules['common.logger.logger'] = type('MockModule', (), {'logger': MockLogger()})()

# 模拟配置
class MockLLMConfig:
    api_key = os.getenv('DEEPSEEK_API_KEY', 'your-api-key')
    base_url = "https://api.deepseek.com"
    model_name = "DeepSeek-V3-0324"

class MockCommonConfig:
    llm = MockLLMConfig()

class MockConfig:
    common = MockCommonConfig()
    
    def get_config(self, key, default=None):
        configs = {
            'llm.deepseek.api_key': os.getenv('DEEPSEEK_API_KEY', 'your-api-key'),
            'llm.deepseek.base_url': 'https://api.deepseek.com',
            'llm.model_name': 'DeepSeek-V3-0324'
        }
        return configs.get(key, default)

# sys.modules['common.share.config'] = type('MockModule', (), {'appConfig': MockConfig()})()

# 模拟环境变量
# sys.modules['common.share.env'] = type('MockModule', (), {})()

# 现在可以导入 nl2code
try:
    from infra.mcp.codegen.nl2code.core import nl2code
    LOCAL_IMPORT_SUCCESS = True
    print("✅ Successfully imported nl2code locally")
except Exception as e:
    LOCAL_IMPORT_SUCCESS = False
    print(f"❌ Failed to import nl2code locally: {e}")


class LocalCodegenReproducer:
    """本地 Codegen 复现器"""
    
    def __init__(self, reproduction_file: str = None, use_es: bool = True):
        """
        初始化复现器
        
        Args:
            reproduction_file: 复现数据文件路径
            use_es: 是否使用ES存储
        """
        self.reproduction_file = Path(reproduction_file) if reproduction_file else None
        self.reproduction_data = None
        self.results = []
        self.use_es = use_es
        
        # 初始化ES存储
        self.es_storage = None
        if use_es:
            try:
                es_config = appConfig.automic.nl2sql.es
                app_id = appConfig.app_id or "default"
                self.es_storage = ReplayDataESStorage(es_config, app_id)
                print(f"✅ ES存储初始化成功, app_id: {app_id}")
            except Exception as e:
                print(f"⚠️ ES存储初始化失败: {e}, 将使用文件存储")
                self.use_es = False
        
    def load_reproduction_data(self, session_id: str = None) -> None:
        """加载复现数据"""
        if self.use_es and self.es_storage and session_id:
            try:
                print(f"🔍 从ES加载会话 {session_id} 的复现数据...")
                self.reproduction_data = self.es_storage.query_replay_data_by_session(session_id)
                print(f"✅ ES数据加载成功")
                print(f"📊 Total contexts to reproduce: {len(self.reproduction_data.get('codegen_contexts', []))}")
                return
            except Exception as e:
                print(f"⚠️ ES数据加载失败: {e}, 使用文件数据")
        
        if not self.reproduction_file or not self.reproduction_file.exists():
            raise FileNotFoundError(f"Reproduction file not found: {self.reproduction_file}")
        
        with open(self.reproduction_file, 'r', encoding='utf-8') as f:
            self.reproduction_data = json.load(f)
        
        print(f"✅ Loaded reproduction data from: {self.reproduction_file}")
        print(f"📊 Total contexts to reproduce: {len(self.reproduction_data['codegen_contexts'])}")
        
    def extract_params_from_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """从上下文中提取 nl2code 参数"""
        codegen_context = context['codegen_context']
        model_params = codegen_context.get('model_parameters', {}).get('params', {})
        
        # 提取核心参数
        params = {
            'user_instruction': model_params.get('user_instruction', ''),
            'env_dependencies': model_params.get('env_dependencies', []),
            'global_vars': model_params.get('global_vars', {}),
            'function_headers': model_params.get('function_headers', []),
            'previous_actions': model_params.get('previous_actions', []),
            'data_type': model_params.get('data_type', 'DataFrame'),
            'data_schema': model_params.get('data_schema', ''),
            'model_name': model_params.get('model_name', 'DeepSeek-V3-0324')
        }
        
        return params
    
    def call_nl2code_local(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """本地调用 nl2code"""
        if not LOCAL_IMPORT_SUCCESS:
            return {
                "error": "Local import failed",
                "details": "Could not import nl2code module locally"
            }
        
        try:
            # 直接调用本地 nl2code 函数
            result = nl2code(params)
            return result
        except Exception as e:
            return {
                "error": "Local call failed",
                "details": str(e)
            }
    
    def reproduce_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """复现单个上下文"""
        context_id = context['context_id']
        call_sequence = context['call_sequence']
        
        print(f"\n🔄 Reproducing context {context_id} (sequence: {call_sequence})")
        
        # 提取参数
        params = self.extract_params_from_context(context)
        
        print(f"📝 User instruction: {params['user_instruction'][:100]}...")
        print(f"🛠️  Environment dependencies: {params['env_dependencies']}")
        print(f"🔧 Global variables: {list(params['global_vars'].keys())}")
        print(f"📊 Previous actions: {len(params['previous_actions'])} actions")
        
        # 调用 nl2code 生成代码
        try:
            start_time = datetime.now()
            result = self.call_nl2code_local(params)
            duration = (datetime.now() - start_time).total_seconds()
            
            # 记录结果
            reproduction_result = {
                'context_id': context_id,
                'call_sequence': call_sequence,
                'original_context': context,
                'reproduction_params': params,
                'reproduction_result': result,
                'reproduction_status': 'success' if 'python_code' in result else 'failed',
                'reproduction_duration_seconds': duration,
                'reproduction_timestamp': datetime.now().isoformat()
            }
            
            if 'python_code' in result:
                print(f"✅ Code generation successful")
                print(f"📄 Generated code length: {len(result['python_code'])} characters")
                if 'required_packages' in result:
                    print(f"📦 Required packages: {result['required_packages']}")
            else:
                print(f"❌ Code generation failed: {result.get('error', 'Unknown error')}")
                print(f"   Details: {result.get('details', '')}")
                
            return reproduction_result
            
        except Exception as e:
            print(f"❌ Reproduction failed with exception: {e}")
            import traceback
            traceback.print_exc()
            
            reproduction_result = {
                'context_id': context_id,
                'call_sequence': call_sequence,
                'original_context': context,
                'reproduction_params': params,
                'reproduction_result': {'error': str(e)},
                'reproduction_status': 'exception',
                'reproduction_duration_seconds': 0,
                'reproduction_timestamp': datetime.now().isoformat()
            }
            
            return reproduction_result
    
    def reproduce_all_contexts(self) -> None:
        """复现所有上下文"""
        if not self.reproduction_data:
            raise ValueError("No reproduction data loaded")
        
        print(f"\n🚀 Starting local reproduction of {len(self.reproduction_data['codegen_contexts'])} contexts...")
        
        for i, context in enumerate(self.reproduction_data['codegen_contexts'], 1):
            print(f"\n{'='*60}")
            print(f"Context {i}/{len(self.reproduction_data['codegen_contexts'])}")
            print(f"{'='*60}")
            
            result = self.reproduce_context(context)
            self.results.append(result)
        
        print(f"\n🎉 Local reproduction completed!")
        
    def generate_report(self) -> Dict[str, Any]:
        """生成复现报告"""
        if not self.results:
            return {'error': 'No reproduction results available'}
        
        total_contexts = len(self.results)
        successful_contexts = sum(1 for r in self.results if r['reproduction_status'] == 'success')
        failed_contexts = sum(1 for r in self.results if r['reproduction_status'] == 'failed')
        exception_contexts = sum(1 for r in self.results if r['reproduction_status'] == 'exception')
        
        avg_duration = sum(r['reproduction_duration_seconds'] for r in self.results) / total_contexts
        
        report = {
            'reproduction_summary': {
                'reproduction_type': 'local',
                'total_contexts': total_contexts,
                'successful_contexts': successful_contexts,
                'failed_contexts': failed_contexts,
                'exception_contexts': exception_contexts,
                'success_rate': successful_contexts / total_contexts if total_contexts > 0 else 0,
                'average_duration_seconds': avg_duration
            },
            'reproduction_results': self.results,
            'reproduction_metadata': {
                'source_file': str(self.reproduction_file),
                'reproduction_timestamp': datetime.now().isoformat(),
                'original_export_time': self.reproduction_data.get('reproduction_metadata', {}).get('export_time'),
                'local_import_success': LOCAL_IMPORT_SUCCESS
            }
        }
        
        return report
    
    def save_report(self, output_file: str = None) -> None:
        """保存复现报告"""
        if not output_file:
            base_name = self.reproduction_file.stem
            output_file = f"{base_name}_local_reproduction_report.json"
        
        report = self.generate_report()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 Local reproduction report saved to: {output_file}")
        
        # 打印摘要
        summary = report['reproduction_summary']
        print(f"\n📊 Local Reproduction Summary:")
        print(f"   Total contexts: {summary['total_contexts']}")
        print(f"   Successful: {summary['successful_contexts']}")
        print(f"   Failed: {summary['failed_contexts']}")
        print(f"   Exceptions: {summary['exception_contexts']}")
        print(f"   Success rate: {summary['success_rate']:.2%}")
        print(f"   Average duration: {summary['average_duration_seconds']:.2f}s")
    
    def _print_formatted_code(self, code: str, prefix: str) -> None:
        """格式化打印代码"""
        if not code:
            print(f"{prefix}: (empty)")
            return
        
        # 如果代码很短，直接显示
        if len(code) <= 100:
            print(f"{prefix}: {code}")
            return
        
        # 如果代码很长，显示预览和完整版本
        print(f"{prefix} (Preview): {code[:100]}...")
        print(f"{prefix} (Full):")
        
        # 按行缩进显示完整代码
        lines = code.split('\n')
        for line in lines[:20]:  # 最多显示前20行
            print(f"       {line}")
        
        if len(lines) > 20:
            print(f"       ... ({len(lines) - 20} more lines)")
    
    def _print_formatted_output(self, output, prefix: str) -> None:
        """格式化打印输出"""
        if not output:
            print(f"{prefix}: (empty)")
            return
        
        # 尝试解析字符串为 Python 列表（针对 Jupyter 输出）
        if isinstance(output, str) and output.startswith('[{'):
            # 放宽条件：只检查开头，因为字符串可能被截断
            try:
                # 先尝试 JSON 解析
                import json
                parsed_output = json.loads(output)
                if isinstance(parsed_output, list):
                    output = parsed_output
            except (json.JSONDecodeError, ValueError):
                try:
                    # JSON 失败后尝试 eval（针对 Python 字典格式）
                    parsed_output = eval(output)
                    if isinstance(parsed_output, list):
                        output = parsed_output
                except (SyntaxError, ValueError, NameError):
                    # 如果解析失败，检查是否是截断的 Jupyter 输出
                    if len(output) > 1000 and not output.endswith('}]'):
                        # 可能是截断的 Jupyter 输出，使用特殊处理
                        self._print_truncated_jupyter_output(output, prefix)
                        return
                    pass  # 其他情况保持原始字符串
        
        # 处理不同类型的输出
        if isinstance(output, list):
            print(f"{prefix} ({len(output)} items):")
            for i, item in enumerate(output[:5]):  # 最多显示前5项
                if isinstance(item, dict):
                    self._print_jupyter_output_item(item, i+1)
                else:
                    # 处理非字典类型的项
                    item_str = str(item)[:100]
                    print(f"       [{i+1}] {type(item).__name__}: {item_str}...")
            
            if len(output) > 5:
                print(f"       ... ({len(output) - 5} more items)")
            return  # 重要：返回，避免继续执行下面的代码
        
        elif isinstance(output, str):
            # 处理字符串输出
            self._print_text_output(output, prefix)
            return
        
        else:
            # 处理其他类型的输出
            output_str = str(output)
            if len(output_str) <= 200:
                print(f"{prefix}: {output_str}")
            else:
                print(f"{prefix} ({type(output).__name__}): {output_str[:200]}...")
    
    def _print_jupyter_output_item(self, item: dict, index: int) -> None:
        """打印单个 Jupyter notebook 输出项"""
        if 'output_type' in item:
            output_type = item['output_type']
            
            if output_type == 'stream' and 'text' in item:
                # 处理 stream 输出 (stdout, stderr)
                name = item.get('name', 'stream')
                text_content = item['text']
                if isinstance(text_content, list):
                    text = ''.join(text_content)
                else:
                    text = str(text_content)
                
                print(f"       [{index}] {name} ({len(text)} chars):")
                # 显示文本内容的前几行
                lines = text.split('\n')
                for line_idx, line in enumerate(lines[:8]):  # 显示前8行
                    if line.strip():  # 只显示非空行
                        print(f"           {line}")
                if len(lines) > 8:
                    print(f"           ... ({len(lines) - 8} more lines)")
            
            elif output_type == 'display_data' and 'data' in item:
                # 处理 display_data 输出 (图表、HTML等)
                data = item['data']
                data_types = list(data.keys())
                print(f"       [{index}] display_data: {data_types}")
                
                # 如果有文本表示，显示预览
                if 'text/plain' in data:
                    text_repr = data['text/plain']
                    if isinstance(text_repr, str) and len(text_repr) < 100:
                        print(f"           Preview: {text_repr}")
                
                # 如果有图片，显示图片信息
                if 'image/png' in data:
                    png_data = data['image/png']
                    if isinstance(png_data, str):
                        size_info = f"({len(png_data)} chars)" if len(png_data) < 100000 else "(large image)"
                        print(f"           PNG data: {size_info}")
            
            elif output_type == 'execute_result' and 'data' in item:
                # 处理执行结果输出
                data = item['data']
                execution_count = item.get('execution_count', 'N/A')
                print(f"       [{index}] execute_result (#{execution_count}): {list(data.keys())}")
                
                if 'text/plain' in data:
                    text_repr = data['text/plain']
                    if isinstance(text_repr, str):
                        lines = text_repr.split('\n')
                        for line in lines[:3]:  # 显示前3行
                            print(f"           {line}")
                        if len(lines) > 3:
                            print(f"           ... ({len(lines) - 3} more lines)")
            
            else:
                # 处理其他类型的输出
                item_keys = list(item.keys())
                print(f"       [{index}] {output_type}: {item_keys}")
        
        elif 'ename' in item and 'evalue' in item:
            # 处理错误输出
            error_name = item['ename']
            error_value = item['evalue']
            print(f"       [{index}] ❌ {error_name}: {error_value}")
            
            # 如果有 traceback，显示简短预览
            if 'traceback' in item:
                traceback_lines = item['traceback']
                if isinstance(traceback_lines, list) and len(traceback_lines) > 0:
                    # 显示最后几行 traceback（通常是最重要的）
                    print(f"           Traceback ({len(traceback_lines)} lines):")
                    for line in traceback_lines[-3:]:  # 显示最后3行
                        # 清理 ANSI 转义序列
                        clean_line = line.replace('\x1b[0;31m', '').replace('\x1b[0m', '').replace('\x1b[0;32m', '')
                        if clean_line.strip():
                            print(f"           {clean_line[:80]}...")
        
        else:
            # 处理其他字典类型
            item_keys = list(item.keys())
            print(f"       [{index}] dict: {item_keys}")
    
    def _print_truncated_jupyter_output(self, text: str, prefix: str) -> None:
        """处理被截断的 Jupyter 输出"""
        print(f"{prefix} (Truncated Jupyter Output):")
        print(f"       ⚠️  Output appears to be truncated (length: {len(text)} chars)")
        
        # 尝试解析出能识别的 Jupyter 输出模式
        if "'output_type': 'stream'" in text:
            stream_count = text.count("'output_type': 'stream'")
            print(f"       📊 Contains {stream_count} stream output(s)")
        
        if "'output_type': 'display_data'" in text:
            display_count = text.count("'output_type': 'display_data'")
            print(f"       📊 Contains {display_count} display_data output(s)")
        
        if "'ename':" in text:
            error_count = text.count("'ename':")
            print(f"       📊 Contains {error_count} error output(s)")
        
        # 显示部分内容
        print(f"       📝 Preview of first 3000 characters:")
        preview = text[:3000].replace('\\n', '\n')
        lines = preview.split('\n')
        for line in lines[:80]:
            if line.strip():
                print(f"           {line[:800]}")
        
        if len(text) > 3000:
            print(f"           ... (truncated, {len(text) - 3000} more characters)")
    
    def _print_text_output(self, text: str, prefix: str) -> None:
        """打印文本输出"""
        if len(text) <= 2000:
            print(f"{prefix}: {text}")
        else:
            print(f"{prefix} (Preview): {text[:2000]}...")
            print(f"{prefix} (Full):")
            lines = text.split('\n')
            for line in lines[:100]:  # 最多显示前10行
                print(f"       {line}")
            if len(lines) > 100:
                print(f"       ... ({len(lines) - 100} more lines)")
    
    def print_code_comparison(self, context_index: int = 0) -> None:
        """打印代码对比"""
        if not self.results or context_index >= len(self.results):
            print("❌ No results available for comparison")
            return
        
        result = self.results[context_index]
        context = result['original_context']
        params = result['reproduction_params']
        
        print(f"\n{'='*80}")
        print(f"Local Code Comparison for Context {context_index + 1}")
        print(f"Context ID: {result['context_id']}")
        print(f"Call Sequence: {result['call_sequence']}")
        print(f"{'='*80}")
        
        # 打印上下文信息
        print(f"\n📋 Context Information:")
        print(f"   User Instruction: {params['user_instruction']}")
        print(f"   Environment Dependencies: {params['env_dependencies']}")
        print(f"   Global Variables: {list(params['global_vars'].keys())}")
        print(f"   Data Type: {params['data_type']}")
        print(f"   Data Schema: {params['data_schema']}")
        
        # 打印 previous_actions
        previous_actions = params.get('previous_actions', [])
        print(f"\n📚 Previous Actions ({len(previous_actions)} actions):")
        if previous_actions:
            for i, action in enumerate(previous_actions, 1):
                if isinstance(action, list) and len(action) >= 3:
                    code, status, output = action[0], action[1], action[2]
                    print(f"\n   Action {i}:")
                    print(f"     Status: {status}")
                    
                    # 格式化代码显示
                    self._print_formatted_code(code, "     Code")
                    
                    # 格式化输出显示
                    if output:
                        self._print_formatted_output(output, "     Output")
                else:
                    print(f"   Action {i}: {action}")
        else:
            print("   No previous actions")
        
        # 原始生成的代码
        original_code = context['codegen_context'].get('generated_code', '')
        print(f"\n🔍 Original Generated Code:")
        if original_code:
            print(f"```python\n{original_code}\n```")
        else:
            print("   (No original code available)")
        
        # 复现生成的代码
        reproduced_code = result['reproduction_result'].get('python_code', '')
        print(f"\n🔄 Locally Reproduced Generated Code:")
        if reproduced_code:
            print(f"```python\n{reproduced_code}\n```")
        else:
            print("   (No reproduced code available)")
            if 'error' in result['reproduction_result']:
                print(f"   Error: {result['reproduction_result']['error']}")
                if 'details' in result['reproduction_result']:
                    print(f"   Details: {result['reproduction_result']['details']}")
        
        # 比较
        print(f"\n🔍 Comparison Results:")
        if original_code and reproduced_code:
            if original_code == reproduced_code:
                print(f"✅ Code generation is identical!")
            else:
                print(f"⚠️  Code generation differs")
                print(f"   Original length: {len(original_code)} chars")
                print(f"   Reproduced length: {len(reproduced_code)} chars")
                
                # 计算相似度
                original_lines = original_code.split('\n')
                reproduced_lines = reproduced_code.split('\n')
                print(f"   Original lines: {len(original_lines)}")
                print(f"   Reproduced lines: {len(reproduced_lines)}")
                
                # 简单的行级差异统计
                if len(original_lines) > 0 and len(reproduced_lines) > 0:
                    common_lines = 0
                    for line in original_lines:
                        if line in reproduced_lines:
                            common_lines += 1
                    similarity = common_lines / max(len(original_lines), len(reproduced_lines))
                    print(f"   Line similarity: {similarity:.2%}")
        else:
            if not original_code and not reproduced_code:
                print(f"⚠️  Both codes are empty")
            elif not original_code:
                print(f"⚠️  Original code is empty, reproduced code available")
            else:
                print(f"⚠️  Reproduced code is empty, original code available")
        
        # 打印复现状态
        print(f"\n📊 Reproduction Status:")
        print(f"   Status: {result['reproduction_status']}")
        print(f"   Duration: {result['reproduction_duration_seconds']:.2f}s")
        print(f"   Timestamp: {result['reproduction_timestamp']}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="本地 Codegen 复现脚本")
    parser.add_argument("reproduction_file", help="复现数据文件路径")
    parser.add_argument("--output", help="输出报告文件路径")
    parser.add_argument("--compare", type=int, help="显示指定索引的代码对比 (从0开始)")
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    try:
        # 创建复现器
        reproducer = LocalCodegenReproducer(args.reproduction_file)
        
        # 加载数据
        reproducer.load_reproduction_data()
        
        # 复现所有上下文
        reproducer.reproduce_all_contexts()
        
        # 保存报告
        reproducer.save_report(args.output)
        
        # 代码对比
        if args.compare is not None:
            reproducer.print_code_comparison(args.compare)
        
    except Exception as e:
        print(f"❌ Local reproduction failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()