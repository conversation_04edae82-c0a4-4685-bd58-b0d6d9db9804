#!/usr/bin/env python3
"""
离线复现数据分析工具
用于分析收集的复现数据，验证数据完整性，生成复现报告
"""
import json
import sys
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from infra.metrics.es_storage import ReplayDataESStorage, MetricsESQueryParams
from common.share.config import appConfig


@dataclass
class ReplayDataSummary:
    """复现数据摘要"""
    date_range: str
    total_sessions: int
    total_tasks: int
    total_codegen_calls: int
    total_mcp_calls: int
    total_code_executions: int
    
    # 数据完整性指标
    sessions_with_complete_data: int
    tasks_with_complete_context: int
    calls_with_execution_data: int
    
    # 分析统计
    avg_task_duration_ms: float
    avg_codegen_calls_per_task: float
    success_rate: float
    
    # 文件统计
    data_files_found: int
    total_data_size_mb: float


class ReplayDataAnalyzer:
    """复现数据分析器"""
    
    def __init__(self, data_dir: str = "replay_data", use_es: bool = True):
        self.data_dir = Path(data_dir)
        self.use_es = use_es
        self.sessions_data = {}
        self.reasoning_data = defaultdict(list)
        self.codegen_data = defaultdict(list)
        self.execution_data = defaultdict(list)
        self.mcp_data = defaultdict(list)
        self.environment_data = defaultdict(list)
        self.time_events_data = defaultdict(list)
        
        # 初始化ES存储
        self.es_storage = None
        if use_es:
            try:
                es_config = appConfig.automic.nl2sql.es
                app_id = appConfig.app_id or "default"
                self.es_storage = ReplayDataESStorage(es_config, app_id)
                print(f"✅ ES存储初始化成功, app_id: {app_id}")
            except Exception as e:
                print(f"⚠️ ES存储初始化失败: {e}, 将使用文件存储")
                self.use_es = False
    
    def load_data(self, start_date: str, end_date: str) -> None:
        """加载指定日期范围的数据"""
        print(f"🔍 Loading replay data from {start_date} to {end_date}...")
        
        if self.use_es and self.es_storage:
            try:
                print("🔍 从ES获取replay数据...")
                self._load_data_from_es(start_date, end_date)
                print(f"✅ ES数据加载完成!")
                return
            except Exception as e:
                print(f"⚠️ ES数据加载失败: {e}, 使用文件数据")
        
        print("📁 从文件加载replay数据...")
        # 加载各类数据文件
        self._load_sessions_data(start_date, end_date)
        self._load_reasoning_data(start_date, end_date)
        self._load_codegen_data(start_date, end_date)
        self._load_execution_data(start_date, end_date)
        self._load_mcp_data(start_date, end_date)
        self._load_environment_data(start_date, end_date)
        self._load_time_events_data(start_date, end_date)
        
        print(f"✅ Data loading completed!")
    
    def _load_jsonl_files(self, subdir: str, pattern: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """加载JSONL文件"""
        data = []
        file_dir = self.data_dir / subdir
        
        if not file_dir.exists():
            print(f"⚠️  Directory not found: {file_dir}")
            return data
        
        # 生成日期范围内的文件列表
        start_dt = datetime.strptime(start_date, "%Y%m%d")
        end_dt = datetime.strptime(end_date, "%Y%m%d")
        
        current_dt = start_dt
        while current_dt <= end_dt:
            date_str = current_dt.strftime("%Y%m%d")
            filename = pattern.replace("YYYYMMDD", date_str)
            filepath = file_dir / filename
            
            if filepath.exists():
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:

                        content = f.read().strip()
                        if not content:
                            continue
                        
                        # 尝试按行解析
                        if '\n' in content:
                            lines = content.split('\n')
                            for line_no, line in enumerate(lines, 1):
                                line = line.strip()
                                if line:
                                    try:
                                        data.append(json.loads(line))
                                    except json.JSONDecodeError as e:
                                        print(f"⚠️  Skipping malformed JSON at line {line_no} in {filename}: {e}")
                        else:
                            # 单行多个JSON对象的情况
                            try:
                                # 尝试直接解析
                                data.append(json.loads(content))
                            except json.JSONDecodeError:
                                # 尝试分割多个JSON对象
                                json_objects = self._split_json_objects(content)
                                data.extend(json_objects)

                    print(f"📄 Loaded {filename}: {len(data)} records")
                except Exception as e:
                    print(f"❌ Failed to load {filename}: {e}")
            
            current_dt += timedelta(days=1)
        
        return data
    
    def _split_json_objects(self, content: str) -> List[Dict[str, Any]]:
        """分割单行中的多个JSON对象"""
        json_objects = []
        depth = 0
        start = 0
        
        for i, char in enumerate(content):
            if char == '{':
                depth += 1
            elif char == '}':
                depth -= 1
                if depth == 0:
                    # 找到一个完整的JSON对象
                    json_str = content[start:i+1]
                    try:
                        json_obj = json.loads(json_str)
                        json_objects.append(json_obj)
                        start = i + 1
                    except json.JSONDecodeError:
                        continue
        
        return json_objects
    
    def _load_data_from_es(self, start_date: str, end_date: str) -> None:
        """从ES加载数据"""
        start_time = f"{start_date}T00:00:00Z"
        end_time = f"{end_date}T23:59:59Z"
        
        # 构建日期范围查询
        date_query = {
            "must": [],
            "filter": [
                {
                    "range": {
                        "timestamp": {
                            "gte": start_time,
                            "lte": end_time
                        }
                    }
                }
            ]
        }
        
        # 加载会话数据
        print("📄 Loading sessions from ES...")
        query_params = MetricsESQueryParams(
            index_name="sessions",
            query_body=date_query,
            size=10000
        )
        sessions = self.es_storage.query_session_replay(query_params)
        for session in sessions:
            self.sessions_data[session.get('session_id')] = session
        print(f"✅ Loaded {len(sessions)} sessions")
        
        # 加载推理数据
        print("📄 Loading reasoning chains from ES...")
        reasoning_query = {
            "must": [],
            "filter": [
                {
                    "range": {
                        "timestamp": {
                            "gte": start_time,
                            "lte": end_time
                        }
                    }
                }
            ]
        }
        reasoning_response = self.es_storage.sync_client.search(
            index=self.es_storage.reasoning_index,
            body={
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"app_id": self.es_storage.app_id}},
                            *reasoning_query.get("must", [])
                        ],
                        "filter": reasoning_query.get("filter", [])
                    }
                },
                "size": 10000
            }
        )
        reasoning_data = [hit["_source"] for hit in reasoning_response["hits"]["hits"]]
        for reasoning in reasoning_data:
            session_id = reasoning.get('session_id')
            if session_id:
                self.reasoning_data[session_id].append(reasoning)
        print(f"✅ Loaded {len(reasoning_data)} reasoning chains")
        
        # 加载代码生成数据
        print("📄 Loading codegen contexts from ES...")
        codegen_response = self.es_storage.sync_client.search(
            index=self.es_storage.codegen_index,
            body={
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"app_id": self.es_storage.app_id}},
                            *date_query.get("must", [])
                        ],
                        "filter": date_query.get("filter", [])
                    }
                },
                "size": 10000
            }
        )
        codegen_data = [hit["_source"] for hit in codegen_response["hits"]["hits"]]
        for codegen in codegen_data:
            session_id = codegen.get('session_id')
            if session_id:
                self.codegen_data[session_id].append(codegen)
        print(f"✅ Loaded {len(codegen_data)} codegen contexts")
        
        # 加载执行数据
        print("📄 Loading execution data from ES...")
        execution_response = self.es_storage.sync_client.search(
            index=self.es_storage.execution_index,
            body={
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"app_id": self.es_storage.app_id}},
                            *date_query.get("must", [])
                        ],
                        "filter": date_query.get("filter", [])
                    }
                },
                "size": 10000
            }
        )
        execution_data = [hit["_source"] for hit in execution_response["hits"]["hits"]]
        for execution in execution_data:
            task_id = execution.get('task_id')
            if task_id:
                self.execution_data[task_id].append(execution)
        print(f"✅ Loaded {len(execution_data)} execution records")
        
        # 加载时间序列事件
        print("📄 Loading time series events from ES...")
        events_response = self.es_storage.sync_client.search(
            index=self.es_storage.events_index,
            body={
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"app_id": self.es_storage.app_id}},
                            *date_query.get("must", [])
                        ],
                        "filter": date_query.get("filter", [])
                    }
                },
                "sort": [{"timestamp_ms": {"order": "asc"}}],
                "size": 10000
            }
        )
        events_data = [hit["_source"] for hit in events_response["hits"]["hits"]]
        for event in events_data:
            session_id = event.get('session_id')
            if session_id:
                self.time_events_data[session_id].append(event)
        print(f"✅ Loaded {len(events_data)} time series events")
    
    def _load_sessions_data(self, start_date: str, end_date: str) -> None:
        """加载会话数据"""
        sessions_dir = self.data_dir / "sessions"
        if not sessions_dir.exists():
            return
        
        for file_path in sessions_dir.glob("session_*.jsonl"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            session_data = json.loads(line)
                            session_id = session_data.get('session_id')
                            if session_id:
                                self.sessions_data[session_id] = session_data
            except Exception as e:
                print(f"❌ Failed to load session file {file_path}: {e}")
    
    def _load_reasoning_data(self, start_date: str, end_date: str) -> None:
        """加载推理数据"""
        data = self._load_jsonl_files("reasoning", "reasoning_YYYYMMDD.jsonl", start_date, end_date)
        for item in data:
            session_id = item.get('session_id')
            if session_id:
                self.reasoning_data[session_id].append(item)
    
    def _load_codegen_data(self, start_date: str, end_date: str) -> None:
        """加载代码生成数据"""
        data = self._load_jsonl_files("codegen", "codegen_context_YYYYMMDD.jsonl", start_date, end_date)
        for item in data:
            task_id = item.get('task_id')
            if task_id:
                self.codegen_data[task_id].append(item)
    
    def _load_execution_data(self, start_date: str, end_date: str) -> None:
        """加载执行数据"""
        data = self._load_jsonl_files("execution", "code_execution_YYYYMMDD.jsonl", start_date, end_date)
        for item in data:
            task_id = item.get('task_id')
            if task_id:
                self.execution_data[task_id].append(item)
    
    def _load_mcp_data(self, start_date: str, end_date: str) -> None:
        """加载MCP数据"""
        data = self._load_jsonl_files("mcp", "mcp_calls_YYYYMMDD.jsonl", start_date, end_date)
        for item in data:
            task_id = item.get('task_id')
            if task_id:
                self.mcp_data[task_id].append(item)
    
    def _load_environment_data(self, start_date: str, end_date: str) -> None:
        """加载环境数据"""
        data = self._load_jsonl_files("environment", "environment_YYYYMMDD.jsonl", start_date, end_date)
        for item in data:
            session_id = item.get('session_id')
            if session_id:
                self.environment_data[session_id].append(item)
    
    def _load_time_events_data(self, start_date: str, end_date: str) -> None:
        """加载时间事件数据"""
        data = self._load_jsonl_files("time_series", "time_events_YYYYMMDD.jsonl", start_date, end_date)
        for item in data:
            session_id = item.get('session_id')
            if session_id:
                self.time_events_data[session_id].append(item)
    
    def analyze_data_completeness(self) -> Dict[str, Any]:
        """分析数据完整性"""
        print("\\n🔍 Analyzing data completeness...")
        
        completeness = {
            "sessions": {
                "total": len(self.sessions_data),
                "with_conversation_turns": 0,
                "with_environment_data": 0
            },
            "tasks": {
                "total": len(self.codegen_data),
                "with_reasoning_data": 0,
                "with_execution_data": 0,
                "with_mcp_data": 0
            },
            "completeness_score": 0.0
        }
        
        # 分析会话完整性
        for session_id, session in self.sessions_data.items():
            if session.get('conversation_turns'):
                completeness["sessions"]["with_conversation_turns"] += 1
            if session_id in self.environment_data:
                completeness["sessions"]["with_environment_data"] += 1
        
        # 分析任务完整性
        for task_id in self.codegen_data.keys():
            # 查找对应的推理数据
            found_reasoning = False
            for session_id, reasoning_list in self.reasoning_data.items():
                if any(r.get('task_id') == task_id for r in reasoning_list):
                    found_reasoning = True
                    break
            if found_reasoning:
                completeness["tasks"]["with_reasoning_data"] += 1
            
            if task_id in self.execution_data:
                completeness["tasks"]["with_execution_data"] += 1
            if task_id in self.mcp_data:
                completeness["tasks"]["with_mcp_data"] += 1
        
        # 计算完整性得分
        total_checks = 0
        passed_checks = 0
        
        if completeness["sessions"]["total"] > 0:
            total_checks += 2
            passed_checks += completeness["sessions"]["with_conversation_turns"] / completeness["sessions"]["total"]
            passed_checks += completeness["sessions"]["with_environment_data"] / completeness["sessions"]["total"]
        
        if completeness["tasks"]["total"] > 0:
            total_checks += 3
            passed_checks += completeness["tasks"]["with_reasoning_data"] / completeness["tasks"]["total"]
            passed_checks += completeness["tasks"]["with_execution_data"] / completeness["tasks"]["total"]
            passed_checks += completeness["tasks"]["with_mcp_data"] / completeness["tasks"]["total"]
        
        if total_checks > 0:
            completeness["completeness_score"] = passed_checks / total_checks
        
        return completeness
    
    def analyze_task_patterns(self) -> Dict[str, Any]:
        """分析任务模式"""
        print("\\n📊 Analyzing task patterns...")
        
        patterns = {
            "codegen_calls_distribution": defaultdict(int),
            "execution_success_rate": 0.0,
            "common_error_patterns": defaultdict(int),
            "tool_usage_frequency": defaultdict(int),
            "avg_task_duration": 0.0
        }
        
        total_tasks = 0
        successful_executions = 0
        total_executions = 0
        total_duration = 0
        
        # 分析每个任务的模式
        for task_id, codegen_calls in self.codegen_data.items():
            total_tasks += 1
            patterns["codegen_calls_distribution"][len(codegen_calls)] += 1
            
            # 分析执行结果
            if task_id in self.execution_data:
                executions = self.execution_data[task_id]
                for execution in executions:
                    total_executions += 1
                    if execution.get('execution_success', False):
                        successful_executions += 1
                    else:
                        # 记录错误模式
                        error_msg = execution.get('execution_errors', '')
                        if error_msg:
                            # 简化错误消息以识别模式
                            if 'ModuleNotFoundError' in error_msg:
                                patterns["common_error_patterns"]["module_not_found"] += 1
                            elif 'NameError' in error_msg:
                                patterns["common_error_patterns"]["name_error"] += 1
                            elif 'TypeError' in error_msg:
                                patterns["common_error_patterns"]["type_error"] += 1
                            elif 'SyntaxError' in error_msg:
                                patterns["common_error_patterns"]["syntax_error"] += 1
                            else:
                                patterns["common_error_patterns"]["other"] += 1
                    
                    # 累计持续时间
                    duration = execution.get('execution_time_ms', 0)
                    if duration > 0:
                        total_duration += duration
            
            # 分析工具使用
            if task_id in self.mcp_data:
                mcp_calls = self.mcp_data[task_id]
                for call in mcp_calls:
                    for detail in call.get('call_details', []):
                        tool_name = detail.get('tool_name', '')
                        if tool_name:
                            patterns["tool_usage_frequency"][tool_name] += 1
        
        # 计算统计数据
        if total_executions > 0:
            patterns["execution_success_rate"] = successful_executions / total_executions
            patterns["avg_task_duration"] = total_duration / total_executions
        
        return patterns
    
    def generate_summary(self, start_date: str, end_date: str) -> ReplayDataSummary:
        """生成数据摘要"""
        print("\\n📋 Generating data summary...")
        
        # 统计基础数据
        total_sessions = len(self.sessions_data)
        total_codegen_calls = sum(len(calls) for calls in self.codegen_data.values())
        total_mcp_calls = sum(len(calls) for calls in self.mcp_data.values())
        total_code_executions = sum(len(execs) for execs in self.execution_data.values())
        
        # 计算完整性指标
        completeness = self.analyze_data_completeness()
        sessions_with_complete_data = completeness["sessions"]["with_environment_data"]
        tasks_with_complete_context = completeness["tasks"]["with_reasoning_data"]
        calls_with_execution_data = completeness["tasks"]["with_execution_data"]
        
        # 计算性能指标
        patterns = self.analyze_task_patterns()
        avg_task_duration_ms = patterns.get("avg_task_duration", 0.0)
        avg_codegen_calls_per_task = total_codegen_calls / len(self.codegen_data) if self.codegen_data else 0
        success_rate = patterns.get("execution_success_rate", 0.0)
        
        # 计算文件统计
        data_files_found = 0
        total_data_size_mb = 0.0
        
        for subdir in ["sessions", "reasoning", "codegen", "execution", "mcp", "environment", "time_series"]:
            subdir_path = self.data_dir / subdir
            if subdir_path.exists():
                for file_path in subdir_path.glob("*.jsonl"):
                    data_files_found += 1
                    total_data_size_mb += file_path.stat().st_size / (1024 * 1024)
        
        return ReplayDataSummary(
            date_range=f"{start_date} ~ {end_date}",
            total_sessions=total_sessions,
            total_tasks=len(self.codegen_data),
            total_codegen_calls=total_codegen_calls,
            total_mcp_calls=total_mcp_calls,
            total_code_executions=total_code_executions,
            sessions_with_complete_data=sessions_with_complete_data,
            tasks_with_complete_context=tasks_with_complete_context,
            calls_with_execution_data=calls_with_execution_data,
            avg_task_duration_ms=avg_task_duration_ms,
            avg_codegen_calls_per_task=avg_codegen_calls_per_task,
            success_rate=success_rate,
            data_files_found=data_files_found,
            total_data_size_mb=total_data_size_mb
        )
    
    def export_session_replay_data(self, session_id: str, output_file: str) -> bool:
        """导出单个会话的完整复现数据"""
        if session_id not in self.sessions_data:
            print(f"❌ Session {session_id} not found")
            return False
        
        print(f"📦 Exporting replay data for session: {session_id}")
        
        # 收集该会话的所有相关数据
        replay_data = {
            "session_info": self.sessions_data[session_id],
            "environment_snapshots": self.environment_data.get(session_id, []),
            "time_events": self.time_events_data.get(session_id, []),
            "reasoning_chains": self.reasoning_data.get(session_id, []),
            "tasks": {},
            "export_metadata": {
                "export_time": datetime.now().isoformat(),
                "session_id": session_id,
                "total_tasks": 0,
                "total_codegen_calls": 0,
                "total_executions": 0
            }
        }
        
        # 收集任务相关数据
        for reasoning in replay_data["reasoning_chains"]:
            task_id = reasoning.get('task_id')
            if task_id and task_id not in replay_data["tasks"]:
                replay_data["tasks"][task_id] = {
                    "task_id": task_id,
                    "codegen_contexts": self.codegen_data.get(task_id, []),
                    "code_executions": self.execution_data.get(task_id, []),
                    "mcp_calls": self.mcp_data.get(task_id, [])
                }
                
                replay_data["export_metadata"]["total_tasks"] += 1
                replay_data["export_metadata"]["total_codegen_calls"] += len(self.codegen_data.get(task_id, []))
                replay_data["export_metadata"]["total_executions"] += len(self.execution_data.get(task_id, []))
        
        # 写入文件
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(replay_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Exported replay data to: {output_path}")
            print(f"📊 Export summary: {replay_data['export_metadata']['total_tasks']} tasks, "
                  f"{replay_data['export_metadata']['total_codegen_calls']} codegen calls, "
                  f"{replay_data['export_metadata']['total_executions']} executions")
            return True
            
        except Exception as e:
            print(f"❌ Failed to export replay data: {e}")
            return False
    
    def export_codegen_reproduction_data(self, context_id: str = None, task_id: str = None, output_file: str = None) -> bool:
        """导出codegen上下文复现数据"""
        if not context_id and not task_id:
            print("❌ Either context_id or task_id must be provided")
            return False
        
        print(f"🔍 Searching for codegen contexts...")
        
        # 查找匹配的codegen上下文
        matching_contexts = []
        
        if context_id:
            # 根据context_id查找
            for tid, contexts in self.codegen_data.items():
                for ctx in contexts:
                    if ctx.get('context_id') == context_id:
                        matching_contexts.append((tid, ctx))
        elif task_id:
            # 根据task_id查找所有相关的codegen上下文
            contexts = self.codegen_data.get(task_id, [])
            for ctx in contexts:
                matching_contexts.append((task_id, ctx))
        
        if not matching_contexts:
            print(f"❌ No codegen contexts found for {'context_id: ' + context_id if context_id else 'task_id: ' + task_id}")
            return False
        
        print(f"📋 Found {len(matching_contexts)} codegen context(s)")
        
        # 构建复现数据
        reproduction_data = {
            "reproduction_metadata": {
                "export_time": datetime.now().isoformat(),
                "search_criteria": {
                    "context_id": context_id,
                    "task_id": task_id
                },
                "total_contexts": len(matching_contexts)
            },
            "codegen_contexts": []
        }
        
        for tid, context in matching_contexts:
            # 获取相关的执行数据
            execution_records = []
            if tid in self.execution_data:
                for exec_data in self.execution_data[tid]:
                    if (exec_data.get('context_id') == context.get('context_id') or 
                        exec_data.get('call_id') == context.get('call_id')):
                        execution_records.append(exec_data)
            
            # 获取相关的MCP调用数据
            mcp_calls = []
            if tid in self.mcp_data:
                for mcp_data in self.mcp_data[tid]:
                    if (mcp_data.get('context_id') == context.get('context_id') or
                        mcp_data.get('call_id') == context.get('call_id')):
                        mcp_calls.append(mcp_data)
            
            # 获取推理链数据
            reasoning_chain = []
            for session_id, reasoning_list in self.reasoning_data.items():
                for reasoning in reasoning_list:
                    if reasoning.get('task_id') == tid:
                        reasoning_chain.append(reasoning)
            
            context_reproduction = {
                "task_id": tid,
                "context_id": context.get('context_id'),
                "call_id": context.get('call_id'),
                "call_sequence": context.get('call_sequence'),
                "timestamp": context.get('timestamp'),
                "session_id": context.get('session_id'),
                "codegen_context": context,
                "execution_records": execution_records,
                "mcp_calls": mcp_calls,
                "reasoning_chain": reasoning_chain,
                "reproduction_info": {
                    "can_reproduce": len(execution_records) > 0,
                    "has_complete_context": bool(context.get('user_query') and context.get('generated_code')),
                    "execution_count": len(execution_records),
                    "mcp_call_count": len(mcp_calls),
                    "reasoning_steps": len(reasoning_chain)
                }
            }
            
            reproduction_data["codegen_contexts"].append(context_reproduction)
        
        # 生成输出文件名
        if not output_file:
            if context_id:
                output_file = f"codegen_reproduction_{context_id}.json"
            else:
                output_file = f"codegen_reproduction_task_{task_id}.json"
        
        # 写入文件
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(reproduction_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Exported codegen reproduction data to: {output_path}")
            
            # 打印复现可行性统计
            reproducible_count = sum(1 for ctx in reproduction_data["codegen_contexts"] 
                                   if ctx["reproduction_info"]["can_reproduce"])
            complete_context_count = sum(1 for ctx in reproduction_data["codegen_contexts"] 
                                       if ctx["reproduction_info"]["has_complete_context"])
            
            print(f"📊 Reproduction feasibility:")
            print(f"   - Reproducible contexts: {reproducible_count}/{len(matching_contexts)}")
            print(f"   - Complete contexts: {complete_context_count}/{len(matching_contexts)}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to export codegen reproduction data: {e}")
            return False
    
    def list_codegen_contexts(self, task_id: str = None, session_id: str = None) -> List[Dict[str, Any]]:
        """列出codegen上下文"""
        contexts = []
        
        if task_id:
            # 列出特定任务的所有codegen上下文
            task_contexts = self.codegen_data.get(task_id, [])
            contexts.extend(task_contexts)
        elif session_id:
            # 列出特定会话的所有codegen上下文
            for tid, task_contexts in self.codegen_data.items():
                for ctx in task_contexts:
                    if ctx.get('session_id') == session_id:
                        contexts.append(ctx)
        else:
            # 列出所有codegen上下文
            for task_contexts in self.codegen_data.values():
                contexts.extend(task_contexts)
        
        # 按时间戳排序
        contexts.sort(key=lambda x: x.get('timestamp', ''))
        
        return contexts
    
    def print_codegen_contexts_summary(self, contexts: List[Dict[str, Any]]) -> None:
        """打印codegen上下文摘要"""
        if not contexts:
            print("No codegen contexts found")
            return
        
        print(f"\n📋 Found {len(contexts)} codegen context(s):")
        print("-" * 120)
        print(f"{'Context ID':<20} {'Task ID':<15} {'Session ID':<15} {'Sequence':<8} {'Timestamp':<20} {'Query Preview':<40}")
        print("-" * 120)
        
        for ctx in contexts:
            context_id = ctx.get('context_id', 'N/A')[:19]
            task_id = ctx.get('task_id', 'N/A')[:14]
            session_id = ctx.get('session_id', 'N/A')[:14]
            sequence = str(ctx.get('call_sequence', 'N/A'))[:7]
            timestamp = ctx.get('timestamp', 'N/A')[:19]
            query = ctx.get('user_query', 'N/A')[:39]
            
            print(f"{context_id:<20} {task_id:<15} {session_id:<15} {sequence:<8} {timestamp:<20} {query:<40}")
        
        print("-" * 120)
    
    def get_all_task_ids(self) -> List[Dict[str, Any]]:
        """获取所有task_id及其基本信息"""
        task_summaries = []
        
        for task_id in self.codegen_data.keys():
            # 统计该task的基本信息
            codegen_count = len(self.codegen_data[task_id])
            execution_count = len(self.execution_data.get(task_id, []))
            mcp_count = len(self.mcp_data.get(task_id, []))
            
            # 获取第一个codegen上下文的信息用于展示
            first_context = self.codegen_data[task_id][0] if self.codegen_data[task_id] else {}
            session_id = first_context.get('session_id', 'N/A')
            timestamp = first_context.get('timestamp', 'N/A')
            user_query = first_context.get('user_query', 'N/A')
            
            # 查找对应的推理数据
            has_reasoning = False
            for reasoning_list in self.reasoning_data.values():
                if any(r.get('task_id') == task_id for r in reasoning_list):
                    has_reasoning = True
                    break
            
            task_summaries.append({
                'task_id': task_id,
                'session_id': session_id,
                'timestamp': timestamp,
                'user_query': user_query,
                'codegen_count': codegen_count,
                'execution_count': execution_count,
                'mcp_count': mcp_count,
                'has_reasoning': has_reasoning
            })
        
        # 按时间戳排序
        task_summaries.sort(key=lambda x: x.get('timestamp', ''))
        return task_summaries
    
    def print_all_task_ids(self) -> None:
        """打印所有task_id的摘要信息"""
        task_summaries = self.get_all_task_ids()
        
        if not task_summaries:
            print("No tasks found in the data")
            return
        
        print(f"\n🆔 All Task IDs ({len(task_summaries)} total):")
        print("=" * 140)
        print(f"{'Task ID'} {'Session ID'} {'Timestamp':<20} {'CG':<3} {'EX':<3} {'MCP':<4} {'RES':<4} {'Query Preview':<50}")
        print("-" * 140)
        
        for task_info in task_summaries:
            task_id = task_info['task_id']
            session_id = task_info['session_id']
            timestamp = task_info['timestamp'][:19]
            codegen_count = str(task_info['codegen_count'])[:2]
            execution_count = str(task_info['execution_count'])[:2]
            mcp_count = str(task_info['mcp_count'])[:3]
            has_reasoning = "✓" if task_info['has_reasoning'] else "✗"
            query_preview = task_info['user_query'][:49]
            
            print(f"{task_id:<25} {session_id:<15} {timestamp:<20} {codegen_count:<3} {execution_count:<3} {mcp_count:<4} {has_reasoning:<4} {query_preview:<50}")
        
        print("-" * 140)
        print("Legend: CG=Codegen calls, EX=Executions, MCP=MCP calls, RES=Has reasoning data")
        print("\n💡 To export specific task data, use: --export-task <task_id>")
        print("💡 To list task contexts, use: --list-task-contexts <task_id>")


def print_summary(summary: ReplayDataSummary) -> None:
    """打印数据摘要"""
    print("\\n" + "="*80)
    print("📊 Replay Data Summary")
    print("="*80)
    print(f"📅 Date Range: {summary.date_range}")
    print(f"💾 Data Size: {summary.total_data_size_mb:.2f} MB ({summary.data_files_found} files)")
    
    print("\\n" + "-"*60)
    print("📈 Data Volume")
    print("-"*60)
    print(f"🔗 Total Sessions: {summary.total_sessions}")
    print(f"📋 Total Tasks: {summary.total_tasks}")
    print(f"🤖 Total Codegen Calls: {summary.total_codegen_calls}")
    print(f"🔧 Total MCP Calls: {summary.total_mcp_calls}")
    print(f"⚡ Total Code Executions: {summary.total_code_executions}")
    
    print("\\n" + "-"*60)
    print("✅ Data Completeness")
    print("-"*60)
    print(f"Sessions with complete data: {summary.sessions_with_complete_data}/{summary.total_sessions} "
          f"({summary.sessions_with_complete_data/summary.total_sessions*100:.1f}%)" if summary.total_sessions > 0 else "N/A")
    print(f"Tasks with complete context: {summary.tasks_with_complete_context}/{summary.total_tasks} "
          f"({summary.tasks_with_complete_context/summary.total_tasks*100:.1f}%)" if summary.total_tasks > 0 else "N/A")
    print(f"Calls with execution data: {summary.calls_with_execution_data}/{summary.total_tasks} "
          f"({summary.calls_with_execution_data/summary.total_tasks*100:.1f}%)" if summary.total_tasks > 0 else "N/A")
    
    print("\\n" + "-"*60)
    print("📊 Performance Statistics")
    print("-"*60)
    print(f"Avg task duration: {summary.avg_task_duration_ms:.0f}ms")
    print(f"Avg codegen calls per task: {summary.avg_codegen_calls_per_task:.1f}")
    print(f"Success rate: {summary.success_rate:.2%}")
    
    print("\\n" + "="*80)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="离线复现数据分析工具")
    parser.add_argument("--start-date", required=True, help="开始日期 (YYYYMMDD)")
    parser.add_argument("--end-date", required=True, help="结束日期 (YYYYMMDD)")
    parser.add_argument("--data-dir", default="replay_data", help="数据目录")
    parser.add_argument("--export-session", help="导出指定会话的复现数据")
    parser.add_argument("--output", help="输出文件路径")
    parser.add_argument("--today", action="store_true", help="分析今日数据")
    
    # 新增的codegen复现相关参数
    parser.add_argument("--export-codegen", help="导出codegen上下文复现数据 (context_id)")
    parser.add_argument("--export-task", help="导出任务的所有codegen上下文复现数据 (task_id)")
    parser.add_argument("--list-contexts", action="store_true", help="列出所有codegen上下文")
    parser.add_argument("--list-task-contexts", help="列出指定任务的codegen上下文 (task_id)")
    parser.add_argument("--list-session-contexts", help="列出指定会话的codegen上下文 (session_id)")
    
    args = parser.parse_args()
    
    if args.today:
        today = datetime.now().strftime("%Y%m%d")
        start_date = end_date = today
    else:
        start_date = args.start_date
        end_date = args.end_date
    
    try:
        analyzer = ReplayDataAnalyzer(data_dir=args.data_dir)
        analyzer.load_data(start_date, end_date)
        
        if args.export_session:
            # 导出特定会话数据
            output_file = args.output or f"session_replay_{args.export_session}.json"
            analyzer.export_session_replay_data(args.export_session, output_file)
        elif args.export_codegen:
            # 导出特定codegen上下文复现数据
            output_file = args.output or f"codegen_reproduction_{args.export_codegen}.json"
            analyzer.export_codegen_reproduction_data(context_id=args.export_codegen, output_file=output_file)
        elif args.export_task:
            # 导出任务的所有codegen上下文复现数据
            output_file = args.output or f"codegen_reproduction_task_{args.export_task}.json"
            analyzer.export_codegen_reproduction_data(task_id=args.export_task, output_file=output_file)
        elif args.list_contexts:
            # 列出所有codegen上下文
            contexts = analyzer.list_codegen_contexts()
            analyzer.print_codegen_contexts_summary(contexts)
        elif args.list_task_contexts:
            # 列出指定任务的codegen上下文
            contexts = analyzer.list_codegen_contexts(task_id=args.list_task_contexts)
            analyzer.print_codegen_contexts_summary(contexts)
        elif args.list_session_contexts:
            # 列出指定会话的codegen上下文
            contexts = analyzer.list_codegen_contexts(session_id=args.list_session_contexts)
            analyzer.print_codegen_contexts_summary(contexts)
        else:
            # 生成分析报告
            summary = analyzer.generate_summary(start_date, end_date)
            print_summary(summary)
            
            # 显示所有task_id信息，方便用户按task导出数据
            print("\n" + "="*80)
            print("📋 Available Task IDs")
            print("="*80)
            analyzer.print_all_task_ids()
            
            # 保存摘要到文件
            if args.output:
                output_path = Path(args.output)
            else:
                output_path = Path(f"replay_analysis_{start_date}_{end_date}.json")
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump({
                    "summary": summary.__dict__,
                    "completeness": analyzer.analyze_data_completeness(),
                    "patterns": analyzer.analyze_task_patterns()
                }, f, ensure_ascii=False, indent=2)
            
            print(f"\\n📄 Analysis report saved to: {output_path}")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()