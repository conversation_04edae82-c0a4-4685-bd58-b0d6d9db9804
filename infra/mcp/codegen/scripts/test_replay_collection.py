#!/usr/bin/env python3
"""
复现数据收集测试脚本
用于验证复现数据收集功能是否正常工作
"""
import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

import sys
import os

# Add the project root to the path so we can import from infra and common
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from infra.metrics.replay_data_collector import get_replay_collector
from infra.metrics.environment_recorder import get_environment_recorder


def test_replay_data_collection():
    """测试复现数据收集功能"""
    print("🧪 Testing Replay Data Collection...")
    
    # 获取收集器实例
    collector = get_replay_collector()
    env_recorder = get_environment_recorder()
    
    # 测试数据
    test_session_id = f"test_session_{int(time.time())}"
    test_user_id = "test_user"
    test_task_id = f"test_task_{int(time.time())}"
    
    try:
        # 1. 测试用户会话记录
        print("\\n1️⃣ Testing user session recording...")
        collector.start_user_session(
            session_id=test_session_id,
            user_id=test_user_id,
            initial_query="测试查询：请帮我分析销售数据",
            session_context={"test_mode": True, "language": "zh"}
        )
        
        # 添加对话轮次
        collector.add_conversation_turn(
            session_id=test_session_id,
            user_input="请生成一个Python脚本来分析销售数据",
            agent_response="好的，我来帮您生成一个分析销售数据的Python脚本",
            turn_metadata={"turn_type": "code_request"}
        )
        
        collector.add_user_feedback(
            session_id=test_session_id,
            feedback="脚本很好，但是需要添加可视化",
            feedback_type="enhancement"
        )
        
        print("✅ User session recording works!")
        
        # 2. 测试Agent推理记录
        print("\\n2️⃣ Testing agent reasoning recording...")
        reasoning_id = collector.record_agent_reasoning(
            session_id=test_session_id,
            task_id=test_task_id,
            subtask_sequence=1,
            agent_state_before={"intent": "data_analysis", "tools_available": ["nl2code", "jupyter"]},
            agent_state_after={"selected_tool": "nl2code", "reasoning": "选择nl2code生成Python代码"},
            tool_selection_reasoning="用户需要分析销售数据，选择nl2code工具生成Python脚本",
            execution_strategy="先生成代码，再执行验证",
            graph_node="tool_selection",
            graph_edge_condition={"has_data_request": True}
        )
        print(f"✅ Agent reasoning recorded: {reasoning_id}")
        
        # 3. 测试代码生成上下文记录
        print("\\n3️⃣ Testing codegen context recording...")
        context_id = collector.record_codegen_context(
            task_id=test_task_id,
            call_id=f"call_{int(time.time())}",
            call_sequence=1,
            full_prompt="Generate Python code to analyze sales data with pandas",
            model_name="nl2code__nl2code",
            model_parameters={"temperature": 0.1, "max_tokens": 1000},
            previous_code_history=[],
            execution_environment_vars={"session_id": test_session_id},
            available_tools=[{"name": "pandas", "description": "Data analysis library"}],
            data_schema_info={"tables": ["sales"], "columns": ["date", "amount", "product"]},
            generated_code="import pandas as pd\\ndf = pd.read_csv('sales.csv')",
            generation_reasoning="Generated basic pandas code for sales analysis",
            confidence_score=0.85,
            action_manager_state={"enabled": False},
            scenario_detection_result={"scenario": "data_analysis"},
            library_config_applied=["pandas", "matplotlib"]
        )
        print(f"✅ Codegen context recorded: {context_id}")
        
        # 4. 测试代码执行记录
        print("\\n4️⃣ Testing code execution recording...")
        execution_id = collector.record_code_execution(
            task_id=test_task_id,
            call_id=f"exec_{int(time.time())}",
            code_before_execution="import pandas as pd\\ndf = pd.read_csv('sales.csv')\\nprint(df.head())",
            environment_snapshot_before={"variables": [], "imports": []},
            available_variables={"df": "DataFrame"},
            imported_modules=["pandas"],
            execution_method="jupyter",
            execution_success=True,
            execution_output="   date  amount product\\n0  2024-01-01    100   A\\n1  2024-01-02    200   B",
            execution_time_ms=150,
            memory_usage_mb=25.6,
            environment_changes={"new_variables": ["df"]},
            new_variables_created={"df": "DataFrame"},
            files_created=[],
            visualizations_generated=[]
        )
        print(f"✅ Code execution recorded: {execution_id}")
        
        # 5. 测试MCP调用链记录
        print("\\n5️⃣ Testing MCP call chain recording...")
        mcp_id = collector.record_mcp_call_chain(
            task_id=test_task_id,
            mcp_session_id=f"mcp_{int(time.time())}",
            server_info={"server_name": "nl2code", "server_type": "codegen"},
            connection_config={"link_type": "stdio"},
            tool_calls_sequence=[{"tool_name": "nl2code", "call_order": 1}],
            call_details=[{
                "call_id": "call_1",
                "tool_name": "nl2code__nl2code",
                "input_parameters": {"query": "generate sales analysis code"},
                "output_result": "Generated Python code",
                "duration_ms": 1200
            }],
            tool_state_changes={}
        )
        print(f"✅ MCP call chain recorded: {mcp_id}")
        
        # 6. 测试环境快照记录
        print("\\n6️⃣ Testing environment snapshot recording...")
        env_id = env_recorder.record_session_environment(test_session_id)
        print(f"✅ Environment snapshot recorded: {env_id}")
        
        # 7. 完成会话记录
        print("\\n7️⃣ Finishing session recording...")
        collector.finish_user_session(test_session_id, final_satisfaction=4)
        print("✅ Session finished!")
        
        # 8. 检查生成的文件
        print("\\n8️⃣ Checking generated files...")
        data_dir = Path("replay_data")
        files_found = []
        
        for subdir in ["sessions", "reasoning", "codegen", "execution", "mcp", "environment", "time_series"]:
            subdir_path = data_dir / subdir
            if subdir_path.exists():
                for file_path in subdir_path.glob("*.jsonl"):
                    files_found.append(str(file_path))
        
        print(f"📄 Found {len(files_found)} data files:")
        for file_path in files_found:
            print(f"   - {file_path}")
        
        # 9. 验证数据内容
        print("\\n9️⃣ Validating data content...")
        total_records = 0
        
        for file_path in files_found:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    file_records = 0
                    for line in f:
                        if line.strip():
                            json.loads(line)  # 验证JSON格式
                            file_records += 1
                    total_records += file_records
                    print(f"   📊 {Path(file_path).name}: {file_records} records")
            except Exception as e:
                print(f"   ❌ {Path(file_path).name}: validation failed - {e}")
        
        print(f"\\n✅ Total records validated: {total_records}")
        
        # 10. 测试收集器统计
        print("\\n🔟 Testing collector statistics...")
        stats = collector.get_session_stats()
        print(f"📊 Collector stats: {json.dumps(stats, indent=2)}")
        
        print("\\n🎉 All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"\\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_analysis():
    """测试数据分析功能"""
    print("\\n🔍 Testing Replay Data Analysis...")
    
    try:
        from scripts.replay_data_analyzer import ReplayDataAnalyzer
        
        # 创建分析器
        analyzer = ReplayDataAnalyzer()
        
        # 分析今日数据
        today = time.strftime("%Y%m%d")
        analyzer.load_data(today, today)
        
        # 生成摘要
        summary = analyzer.generate_summary(today, today)
        
        print(f"📊 Analysis Summary:")
        print(f"   Sessions: {summary.total_sessions}")
        print(f"   Tasks: {summary.total_tasks}")
        print(f"   Codegen calls: {summary.total_codegen_calls}")
        print(f"   Data files: {summary.data_files_found}")
        print(f"   Data size: {summary.total_data_size_mb:.2f} MB")
        print(f"   Success rate: {summary.success_rate:.2%}")
        
        print("✅ Data analysis works!")
        return True
        
    except Exception as e:
        print(f"❌ Data analysis test failed: {e}")
        return False


def main():
    """主函数"""
    print("🚀 Starting Replay Data Collection Tests...")
    
    # 测试数据收集
    collection_success = test_replay_data_collection()
    
    # 测试数据分析
    analysis_success = test_data_analysis()
    
    if collection_success and analysis_success:
        print("\\n🎉 All tests passed! Replay data collection system is working correctly.")
        return 0
    else:
        print("\\n❌ Some tests failed. Please check the error messages above.")
        return 1


if __name__ == "__main__":
    exit(main())