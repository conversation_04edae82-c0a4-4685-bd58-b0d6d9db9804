#!/usr/bin/env python3
"""
离线复现数据系统演示
展示完整的数据收集、分析和复现流程
"""
import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))


def demo_complete_workflow():
    """演示完整的复现数据工作流程"""
    print("🚀 Replay Data System Complete Demo")
    print("=" * 60)
    
    # 第一步：数据收集演示
    print("\n📊 Step 1: Data Collection Demo")
    print("-" * 40)
    
    try:
        from scripts.test_replay_collection import test_replay_data_collection
        
        print("运行数据收集测试...")
        collection_success = test_replay_data_collection()
        
        if collection_success:
            print("✅ 数据收集测试成功!")
        else:
            print("❌ 数据收集测试失败!")
            return False
            
    except Exception as e:
        print(f"❌ 数据收集演示失败: {e}")
        return False
    
    # 第二步：数据分析演示
    print("\n🔍 Step 2: Data Analysis Demo")
    print("-" * 40)
    
    try:
        from scripts.replay_data_analyzer import ReplayDataAnalyzer, print_summary
        
        # 创建分析器
        analyzer = ReplayDataAnalyzer()
        
        # 分析今日数据
        today = time.strftime("%Y%m%d")
        print(f"分析日期: {today}")
        
        analyzer.load_data(today, today)
        
        # 生成摘要报告
        summary = analyzer.generate_summary(today, today)
        print_summary(summary)
        
        # 保存分析结果
        analysis_file = f"demo_analysis_{today}.json"
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump({
                "summary": summary.__dict__,
                "completeness": analyzer.analyze_data_completeness(),
                "patterns": analyzer.analyze_task_patterns()
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 分析结果已保存到: {analysis_file}")
        
    except Exception as e:
        print(f"❌ 数据分析演示失败: {e}")
        return False
    
    # 第三步：配置演示
    print("\n⚙️ Step 3: Configuration Demo")
    print("-" * 40)
    
    try:
        from common.share.config import appConfig
        
        print("当前复现数据配置:")
        config = appConfig.metrics.replay
        print(f"  启用状态: {config.enabled}")
        print(f"  数据目录: {config.data_dir}")
        print(f"  采样率: {config.sampling_rate}")
        print(f"  保存实际数据: {config.save_actual_data}")
        print(f"  保留天数: {config.rotation_days}")
        
        # 演示如何调整配置
        print("\n配置调整建议:")
        print("  开发环境: sampling_rate = 1.0 (100%)")
        print("  测试环境: sampling_rate = 0.5 (50%)")
        print("  生产环境: sampling_rate = 0.1 (10%)")
        
    except Exception as e:
        print(f"❌ 配置演示失败: {e}")
        return False
    
    # 第四步：文件系统演示
    print("\n📁 Step 4: File System Demo")
    print("-" * 40)
    
    try:
        data_dir = Path("replay_data")
        
        if data_dir.exists():
            print(f"数据目录: {data_dir.absolute()}")
            
            # 统计各子目录的文件
            subdirs = ["sessions", "reasoning", "codegen", "execution", "mcp", "environment", "time_series"]
            total_files = 0
            total_size = 0
            
            for subdir in subdirs:
                subdir_path = data_dir / subdir
                if subdir_path.exists():
                    files = list(subdir_path.glob("*.jsonl"))
                    size = sum(f.stat().st_size for f in files)
                    total_files += len(files)
                    total_size += size
                    
                    print(f"  {subdir}/: {len(files)} 文件, {size/1024:.1f} KB")
            
            print(f"\n总计: {total_files} 文件, {total_size/1024/1024:.2f} MB")
            
            # 显示最新的几个文件
            print("\n最新数据文件:")
            all_files = []
            for subdir in subdirs:
                subdir_path = data_dir / subdir
                if subdir_path.exists():
                    all_files.extend(subdir_path.glob("*.jsonl"))
            
            # 按修改时间排序，显示最新的5个文件
            all_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
            for file_path in all_files[:5]:
                mtime = time.strftime('%H:%M:%S', time.localtime(file_path.stat().st_mtime))
                size_kb = file_path.stat().st_size / 1024
                print(f"  {mtime} - {file_path.name} ({size_kb:.1f} KB)")
        else:
            print("数据目录不存在，请先运行数据收集测试")
        
    except Exception as e:
        print(f"❌ 文件系统演示失败: {e}")
        return False
    
    # 第五步：使用场景演示
    print("\n🎯 Step 5: Usage Scenarios Demo")
    print("-" * 40)
    
    print("主要使用场景:")
    print("1. 🐛 问题调试:")
    print("   - 用户报告codegen失败")
    print("   - 导出该会话的完整数据")
    print("   - 分析失败原因和上下文")
    
    print("2. 📈 效果优化:")
    print("   - 分析成功率趋势")
    print("   - 识别常见错误模式")
    print("   - 优化prompt和策略")
    
    print("3. 🔍 深度分析:")
    print("   - 复现用户场景")
    print("   - A/B测试对比")
    print("   - 新功能验证")
    
    print("4. 📊 运营监控:")
    print("   - 每日数据分析")
    print("   - 性能指标监控")
    print("   - 用户体验评估")
    
    # 第六步：实用命令演示
    print("\n💻 Step 6: Useful Commands Demo")
    print("-" * 40)
    
    print("常用命令:")
    print("# 测试数据收集")
    print("python infra/mcp/codegen/scripts/test_replay_collection.py")
    print()
    print("# 分析今日数据")
    print("python infra/mcp/codegen/scripts/replay_data_analyzer.py --today")
    print()
    print("# 分析指定日期范围")
    print("python infra/mcp/codegen/scripts/replay_data_analyzer.py --start-date 20241201 --end-date 20241208")
    print()
    print("# 导出特定会话数据")
    print("python infra/mcp/codegen/scripts/replay_data_analyzer.py --start-date 20241208 --end-date 20241208 \\")
    print("    --export-session session_123 --output session_123_replay.json")
    print()
    print("# 检查配置")
    print('python -c "from common.share.config import appConfig; print(appConfig.metrics.replay)"')
    
    print("\n🎉 Demo completed successfully!")
    print("\n📖 更多信息请参考: infra/metrics/REPLAY_DATA_USAGE.md")
    
    return True


def demo_production_setup():
    """演示生产环境配置"""
    print("\n🏭 Production Environment Setup Guide")
    print("=" * 50)
    
    print("1. 配置文件调整 (etc/config.yaml):")
    print("""
metrics:
  replay:
    enabled: true
    sampling_rate: 0.1      # 生产环境10%采样
    save_actual_data: false # 不保存实际数据
    rotation_days: 7        # 保留7天
    max_file_size_mb: 50    # 限制文件大小
""")
    
    print("2. 监控脚本设置:")
    print("# 添加到 crontab")
    print("0 2 * * * /path/to/python /path/to/infra/mcp/codegen/scripts/replay_data_analyzer.py --today > /var/log/replay_analysis.log")
    print()
    print("# 每周清理")
    print("0 3 * * 0 find /path/to/replay_data -name '*.jsonl' -mtime +7 -delete")
    
    print("\n3. 磁盘空间监控:")
    print("# 检查数据目录大小")
    print("du -sh replay_data/")
    print()
    print("# 监控文件数量")
    print("find replay_data -name '*.jsonl' | wc -l")
    
    print("\n4. 性能优化:")
    print("- 使用SSD存储数据目录")
    print("- 定期压缩历史数据")
    print("- 监控I/O使用情况")
    print("- 异步写入，避免阻塞主流程")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="离线复现数据系统演示")
    parser.add_argument("--production", action="store_true", help="显示生产环境配置指南")
    
    args = parser.parse_args()
    
    if args.production:
        demo_production_setup()
    else:
        success = demo_complete_workflow()
        if success:
            print("\n✨ 演示完成! 复现数据系统已准备就绪。")
            return 0
        else:
            print("\n❌ 演示过程中遇到问题，请检查错误信息。")
            return 1


if __name__ == "__main__":
    exit(main())