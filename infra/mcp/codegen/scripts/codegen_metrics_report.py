#!/usr/bin/env python3
"""
Codegen运营指标汇总脚本
基于metrics日志生成运营报告
"""
import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

import sys
import os

# Add the project root to the path so we can import from infra and common
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from infra.metrics.codegen_metrics_service import get_metrics_service
from infra.metrics.es_storage import MetricsESStorage, MetricsESQueryParams
from common.share.config import appConfig


@dataclass
class CodegenReport:
    """Codegen运营指标报告"""
    # 基础统计
    report_date: str
    date_range: str
    total_tasks: int
    total_calls: int
    
    # 多k值功能正确性指标
    functional_correctness_by_k: Dict[int, float]  # 在k次调用内成功完成任务的比例
    successful_tasks: int
    failed_tasks: int
    
    # 多k值执行成功率指标  
    execution_success_rate_by_k: Dict[int, float]  # 在k次调用内代码执行成功的比例
    successful_calls: int
    failed_calls: int
    
    # 响应时间指标
    avg_task_duration_ms: float  # 每个代码子任务的平均用时
    avg_call_duration_ms: float  # 每次codegen调用的平均用时
    min_task_duration_ms: float
    max_task_duration_ms: float
    min_call_duration_ms: float
    max_call_duration_ms: float
    
    # Token使用指标
    avg_task_tokens: float  # 每个代码子任务的平均token使用数
    avg_call_tokens: float  # 每次codegen调用的平均token使用数
    total_input_tokens: int
    total_output_tokens: int
    avg_input_tokens_per_call: float
    avg_output_tokens_per_call: float
    
    # 分布统计
    task_duration_distribution: Dict[str, int]  # 任务耗时分布
    call_duration_distribution: Dict[str, int]  # 调用耗时分布
    token_usage_distribution: Dict[str, int]  # Token使用分布
    
    # 详细数据
    top_slow_tasks: List[Dict]  # 最慢的任务
    top_token_consuming_tasks: List[Dict]  # Token消耗最多的任务


class CodegenMetricsProcessor:
    """Codegen指标处理器"""
    
    def __init__(self, data_dir: str = "metrics/codegen", k: int = 3, use_es: bool = True):
        self.data_dir = Path(data_dir)
        self.k = k  # 最大允许的codegen调用次数
        self.service = get_metrics_service()
        self.use_es = use_es
        
        # 初始化ES存储
        self.es_storage = None
        if use_es:
            try:
                es_config = appConfig.automic.nl2sql.es
                app_id = appConfig.app_id or "default"
                self.es_storage = MetricsESStorage(es_config, app_id)
                print(f"✅ ES存储初始化成功, app_id: {app_id}")
            except Exception as e:
                print(f"⚠️ ES存储初始化失败: {e}, 将使用文件存储")
                self.use_es = False
    
    def generate_report(self, start_date: str, end_date: str) -> CodegenReport:
        """生成指定日期范围的运营报告"""
        print(f"🔍 开始生成 {start_date} 到 {end_date} 的Codegen运营报告...")
        
        # 获取k值配置
        from common.share.config import appConfig
        try:
            k_values = appConfig.metrics.codegen.k_values
        except AttributeError:
            k_values = [1, 2, 3, 5]  # 默认值
        
        print(f"📈 使用k值: {k_values}")
        
        # 获取原始数据
        tasks, calls = self._get_data(start_date, end_date)
        
        print(f"📊 数据统计: {len(tasks)} 个任务, {len(calls)} 次调用")
        
        # 基础统计
        completed_tasks = [t for t in tasks if t.get('end_time') is not None]
        completed_calls = [c for c in calls if c.get('end_time') is not None]
        
        # 多k值功能正确性计算
        functional_correctness_by_k = self.service.calculate_functional_correctness_by_k(completed_tasks, k_values)
        successful_tasks = sum(1 for t in completed_tasks if t.get('is_successful', False))
        failed_tasks = len(completed_tasks) - successful_tasks
        
        # 多k值执行成功率计算
        execution_success_rate_by_k = self.service.calculate_execution_success_rate_by_k(completed_tasks, completed_calls, k_values)
        successful_calls = sum(1 for c in completed_calls if c.get('execution_success', False))
        failed_calls = len(completed_calls) - successful_calls
        
        # 响应时间计算
        task_durations = [t.get('duration_ms', 0) for t in completed_tasks if t.get('duration_ms')]
        call_durations = [c.get('duration_ms', 0) for c in completed_calls if c.get('duration_ms')]
        
        avg_task_duration = sum(task_durations) / len(task_durations) if task_durations else 0
        avg_call_duration = sum(call_durations) / len(call_durations) if call_durations else 0
        
        # Token使用计算
        task_tokens = self._calculate_task_tokens(completed_tasks)
        call_tokens = self._calculate_call_tokens(completed_calls)
        
        # 生成分布统计
        task_duration_dist = self._calculate_duration_distribution(task_durations)
        call_duration_dist = self._calculate_duration_distribution(call_durations)
        token_dist = self._calculate_token_distribution(task_tokens)
        
        # 获取详细数据
        top_slow_tasks = self._get_top_slow_tasks(completed_tasks, 5)
        top_token_tasks = self._get_top_token_consuming_tasks(completed_tasks, 5)
        
        # 计算token统计
        total_input = sum(c.get('input_tokens', 0) for c in completed_calls)
        total_output = sum(c.get('output_tokens', 0) for c in completed_calls)
        avg_input_per_call = total_input / len(completed_calls) if completed_calls else 0
        avg_output_per_call = total_output / len(completed_calls) if completed_calls else 0
        
        return CodegenReport(
            report_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            date_range=f"{start_date} ~ {end_date}",
            total_tasks=len(completed_tasks),
            total_calls=len(completed_calls),
            
            functional_correctness_by_k=functional_correctness_by_k,
            successful_tasks=successful_tasks,
            failed_tasks=failed_tasks,
            
            execution_success_rate_by_k=execution_success_rate_by_k,
            successful_calls=successful_calls,
            failed_calls=failed_calls,
            
            avg_task_duration_ms=avg_task_duration,
            avg_call_duration_ms=avg_call_duration,
            min_task_duration_ms=min(task_durations) if task_durations else 0,
            max_task_duration_ms=max(task_durations) if task_durations else 0,
            min_call_duration_ms=min(call_durations) if call_durations else 0,
            max_call_duration_ms=max(call_durations) if call_durations else 0,
            
            avg_task_tokens=sum(task_tokens) / len(task_tokens) if task_tokens else 0,
            avg_call_tokens=sum(call_tokens) / len(call_tokens) if call_tokens else 0,
            total_input_tokens=total_input,
            total_output_tokens=total_output,
            avg_input_tokens_per_call=avg_input_per_call,
            avg_output_tokens_per_call=avg_output_per_call,
            
            task_duration_distribution=task_duration_dist,
            call_duration_distribution=call_duration_dist,
            token_usage_distribution=token_dist,
            
            top_slow_tasks=top_slow_tasks,
            top_token_consuming_tasks=top_token_tasks
        )
    
    def _get_data(self, start_date: str, end_date: str) -> Tuple[List[Dict], List[Dict]]:
        """获取数据，优先从ES获取，失败则从文件获取"""
        if self.use_es and self.es_storage:
            try:
                print("🔍 从ES获取数据...")
                tasks = self._get_es_task_data(start_date, end_date)
                calls = self._get_es_call_data(start_date, end_date)
                print(f"✅ ES数据获取成功: {len(tasks)} 个任务, {len(calls)} 次调用")
                return tasks, calls
            except Exception as e:
                print(f"⚠️ ES数据获取失败: {e}, 使用文件数据")
        
        print("📁 从文件获取数据...")
        tasks = self.service.get_tasks_data(start_date, end_date)
        calls = self.service.get_calls_data(start_date, end_date)
        return tasks, calls
    
    def _get_es_task_data(self, start_date: str, end_date: str) -> List[Dict]:
        """从ES获取任务数据"""
        start_time = f"{start_date}T00:00:00Z"
        end_time = f"{end_date}T23:59:59Z"
        
        query_body = {
            "must": [],
            "filter": [
                {
                    "range": {
                        "start_time": {
                            "gte": start_time,
                            "lte": end_time
                        }
                    }
                }
            ]
        }
        
        query_params = MetricsESQueryParams(
            index_name="task_metrics",
            query_body=query_body,
            size=10000,
            sort=[{"start_time": {"order": "asc"}}]
        )
        
        return self.es_storage.query_task_metrics(query_params)
    
    def _get_es_call_data(self, start_date: str, end_date: str) -> List[Dict]:
        """从ES获取调用数据"""
        start_time = f"{start_date}T00:00:00Z"
        end_time = f"{end_date}T23:59:59Z"
        
        query_body = {
            "must": [],
            "filter": [
                {
                    "range": {
                        "start_time": {
                            "gte": start_time,
                            "lte": end_time
                        }
                    }
                }
            ]
        }
        
        query_params = MetricsESQueryParams(
            index_name="call_metrics",
            query_body=query_body,
            size=10000,
            sort=[{"start_time": {"order": "asc"}}]
        )
        
        return self.es_storage.query_call_metrics(query_params)
    
    def _calculate_execution_success_rate(self, calls: List[Dict]) -> float:
        """计算执行成功率：代码执行成功的比例"""
        if not calls:
            return 0.0
        
        successful_calls = sum(1 for call in calls 
                              if call.get('execution_success', False))
        return successful_calls / len(calls)
    
    def _calculate_task_tokens(self, tasks: List[Dict]) -> List[int]:
        """计算任务token使用量"""
        tokens = []
        for task in tasks:
            total = task.get('total_input_tokens', 0) + task.get('total_output_tokens', 0)
            if total > 0:
                tokens.append(total)
        return tokens
    
    def _calculate_call_tokens(self, calls: List[Dict]) -> List[int]:
        """计算调用token使用量"""
        tokens = []
        for call in calls:
            total = call.get('input_tokens', 0) + call.get('output_tokens', 0)
            if total > 0:
                tokens.append(total)
        return tokens
    
    def _calculate_duration_distribution(self, durations: List[int]) -> Dict[str, int]:
        """计算耗时分布"""
        if not durations:
            return {}
        
        dist = {
            "0-1s": 0,
            "1-5s": 0,
            "5-10s": 0,
            "10-30s": 0,
            "30s+": 0
        }
        
        for duration in durations:
            duration_s = duration / 1000
            if duration_s <= 1:
                dist["0-1s"] += 1
            elif duration_s <= 5:
                dist["1-5s"] += 1
            elif duration_s <= 10:
                dist["5-10s"] += 1
            elif duration_s <= 30:
                dist["10-30s"] += 1
            else:
                dist["30s+"] += 1
        
        return dist
    
    def _calculate_token_distribution(self, tokens: List[int]) -> Dict[str, int]:
        """计算token使用分布"""
        if not tokens:
            return {}
        
        dist = {
            "0-500": 0,
            "500-1000": 0,
            "1000-2000": 0,
            "2000-5000": 0,
            "5000+": 0
        }
        
        for token in tokens:
            if token <= 500:
                dist["0-500"] += 1
            elif token <= 1000:
                dist["500-1000"] += 1
            elif token <= 2000:
                dist["1000-2000"] += 1
            elif token <= 5000:
                dist["2000-5000"] += 1
            else:
                dist["5000+"] += 1
        
        return dist
    
    def _get_top_slow_tasks(self, tasks: List[Dict], limit: int) -> List[Dict]:
        """获取最慢的任务"""
        tasks_with_duration = [t for t in tasks if t.get('duration_ms')]
        sorted_tasks = sorted(tasks_with_duration, 
                             key=lambda x: x.get('duration_ms', 0), reverse=True)
        
        return [{
            'task_id': t.get('task_id', 'unknown'),
            'task_instruction': t.get('task_instruction', 'unknown')[:50] + '...',
            'duration_ms': t.get('duration_ms', 0),
            'duration_s': round(t.get('duration_ms', 0) / 1000, 2),
            'codegen_calls': t.get('total_codegen_calls', 0)
        } for t in sorted_tasks[:limit]]
    
    def _get_top_token_consuming_tasks(self, tasks: List[Dict], limit: int) -> List[Dict]:
        """获取Token消耗最多的任务"""
        tasks_with_tokens = []
        for t in tasks:
            total_tokens = t.get('total_input_tokens', 0) + t.get('total_output_tokens', 0)
            if total_tokens > 0:
                tasks_with_tokens.append((t, total_tokens))
        
        sorted_tasks = sorted(tasks_with_tokens, key=lambda x: x[1], reverse=True)
        
        return [{
            'task_id': t[0].get('task_id', 'unknown'),
            'task_instruction': t[0].get('task_instruction', 'unknown')[:50] + '...',
            'total_tokens': t[1],
            'input_tokens': t[0].get('total_input_tokens', 0),
            'output_tokens': t[0].get('total_output_tokens', 0),
            'codegen_calls': t[0].get('total_codegen_calls', 0)
        } for t in sorted_tasks[:limit]]


def print_report(report: CodegenReport):
    """打印运营报告"""
    print("\n" + "="*80)
    print(f"📊 Codegen运营指标报告")
    print("="*80)
    print(f"🕐 报告生成时间: {report.report_date}")
    print(f"📅 统计时间范围: {report.date_range}")
    print(f"📈 数据总量: {report.total_tasks} 个任务, {report.total_calls} 次调用")
    
    print("\n" + "-"*60)
    print("🎯 功能正确性指标（多k值）")
    print("-"*60)
    for k, correctness in report.functional_correctness_by_k.items():
        print(f"✅ k={k}次调用内功能正确性: {correctness:.2%}")
    print(f"   - 成功任务: {report.successful_tasks} 个")
    print(f"   - 失败任务: {report.failed_tasks} 个")
    
    print("\n" + "-"*60)
    print("⚡ 执行成功率指标（多k值）")
    print("-"*60)
    for k, success_rate in report.execution_success_rate_by_k.items():
        print(f"✅ k={k}次调用内执行成功率: {success_rate:.2%}")
    print(f"   - 成功调用: {report.successful_calls} 次")
    print(f"   - 失败调用: {report.failed_calls} 次")
    
    print("\n" + "-"*60)
    print("⏱️ 响应时间指标")
    print("-"*60)
    print(f"📋 任务平均耗时: {report.avg_task_duration_ms:.0f}ms ({report.avg_task_duration_ms/1000:.2f}s)")
    print(f"   - 最快任务: {report.min_task_duration_ms:.0f}ms")
    print(f"   - 最慢任务: {report.max_task_duration_ms:.0f}ms")
    print(f"🔧 调用平均耗时: {report.avg_call_duration_ms:.0f}ms ({report.avg_call_duration_ms/1000:.2f}s)")
    print(f"   - 最快调用: {report.min_call_duration_ms:.0f}ms")
    print(f"   - 最慢调用: {report.max_call_duration_ms:.0f}ms")
    
    print("\n📊 任务耗时分布:")
    for duration_range, count in report.task_duration_distribution.items():
        percentage = count / report.total_tasks * 100 if report.total_tasks > 0 else 0
        print(f"   {duration_range}: {count} 个任务 ({percentage:.1f}%)")
    
    print("\n" + "-"*60)
    print("🔤 Token使用指标")
    print("-"*60)
    print(f"📋 任务平均Token: {report.avg_task_tokens:.0f}")
    print(f"🔧 调用平均Token: {report.avg_call_tokens:.0f}")
    print(f"📥 总输入Token: {report.total_input_tokens:,}")
    print(f"📤 总输出Token: {report.total_output_tokens:,}")
    print(f"📊 平均输入Token/调用: {report.avg_input_tokens_per_call:.0f}")
    print(f"📊 平均输出Token/调用: {report.avg_output_tokens_per_call:.0f}")
    
    print("\n📊 Token使用分布:")
    for token_range, count in report.token_usage_distribution.items():
        percentage = count / report.total_tasks * 100 if report.total_tasks > 0 else 0
        print(f"   {token_range}: {count} 个任务 ({percentage:.1f}%)")
    
    if report.top_slow_tasks:
        print("\n" + "-"*60)
        print("🐌 最慢的任务 TOP5")
        print("-"*60)
        for i, task in enumerate(report.top_slow_tasks, 1):
            print(f"{i}. {task['task_instruction']}")
            print(f"   耗时: {task['duration_s']}s, 调用次数: {task['codegen_calls']}")
    
    if report.top_token_consuming_tasks:
        print("\n" + "-"*60)
        print("💰 Token消耗最多的任务 TOP5")
        print("-"*60)
        for i, task in enumerate(report.top_token_consuming_tasks, 1):
            print(f"{i}. {task['task_instruction']}")
            print(f"   Token: {task['total_tokens']} (输入:{task['input_tokens']}, 输出:{task['output_tokens']})")
    
    print("\n" + "="*80)


def save_report_json(report: CodegenReport, filename: str):
    """保存报告为JSON文件"""
    report_dict = {
        'report_date': report.report_date,
        'date_range': report.date_range,
        'summary': {
            'total_tasks': report.total_tasks,
            'total_calls': report.total_calls,
            'functional_correctness_by_k': report.functional_correctness_by_k,
            'execution_success_rate_by_k': report.execution_success_rate_by_k,
            'avg_task_duration_ms': report.avg_task_duration_ms,
            'avg_call_duration_ms': report.avg_call_duration_ms,
            'avg_task_tokens': report.avg_task_tokens,
            'avg_call_tokens': report.avg_call_tokens
        },
        'detailed_metrics': {
            'tasks': {
                'successful': report.successful_tasks,
                'failed': report.failed_tasks,
                'duration_distribution': report.task_duration_distribution
            },
            'calls': {
                'successful': report.successful_calls,
                'failed': report.failed_calls,
                'duration_distribution': report.call_duration_distribution
            },
            'tokens': {
                'total_input': report.total_input_tokens,
                'total_output': report.total_output_tokens,
                'usage_distribution': report.token_usage_distribution
            }
        },
        'top_lists': {
            'slowest_tasks': report.top_slow_tasks,
            'highest_token_tasks': report.top_token_consuming_tasks
        }
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(report_dict, f, ensure_ascii=False, indent=2)
    
    print(f"📄 报告已保存到: {filename}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Codegen运营指标汇总脚本")
    parser.add_argument("--start-date", required=True, help="开始日期 (YYYYMMDD)")
    parser.add_argument("--end-date", required=True, help="结束日期 (YYYYMMDD)")
    parser.add_argument("--k", type=int, default=3, help="最大允许的codegen调用次数")
    parser.add_argument("--data-dir", default="metrics/codegen", help="数据目录")
    parser.add_argument("--output", help="输出JSON文件路径")
    parser.add_argument("--today", action="store_true", help="生成今日报告")
    
    args = parser.parse_args()
    
    if args.today:
        today = datetime.now().strftime("%Y%m%d")
        start_date = end_date = today
    else:
        start_date = args.start_date
        end_date = args.end_date
    
    try:
        processor = CodegenMetricsProcessor(data_dir=args.data_dir, k=args.k)
        report = processor.generate_report(start_date, end_date)
        
        # 打印报告
        print_report(report)
        
        # 保存JSON报告
        if args.output:
            save_report_json(report, args.output)
        else:
            output_file = f"codegen_report_{start_date}_{end_date}.json"
            save_report_json(report, output_file)
        
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()