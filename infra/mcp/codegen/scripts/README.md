# Codegen运营指标汇总脚本使用说明

## 概述

本套脚本基于metrics日志数据，自动计算和汇总codegen运营指标，支持生成详细的运营报告。

## 核心指标

### 1. 功能正确性
- **定义**: 在k次codegen调用内成功完成任务的比例
- **计算**: 成功任务数 / 总任务数
- **目标**: ≥95%

### 2. 执行成功率
- **定义**: 代码执行成功的比例
- **计算**: 成功调用数 / 总调用数
- **目标**: ≥98%

### 3. 响应时间
- **任务级**: 每个代码子任务的平均用时
- **调用级**: 每次codegen调用的平均用时
- **目标**: 任务<30s, 调用<10s

### 4. Token使用
- **任务级**: 每个代码子任务的平均token使用数
- **调用级**: 每次codegen调用的平均token使用数
- **监控**: 成本控制和性能优化

## 脚本使用

### 1. 快速今日报告
```bash
# 生成今日运营指标报告
python infra/mcp/codegen/scripts/daily_report.py
```

### 2. 详细报告生成
```bash
# 生成指定日期范围的详细报告
python infra/mcp/codegen/scripts/codegen_metrics_report.py --start-date 20250701 --end-date 20250707

# 生成今日报告并保存JSON
python infra/mcp/codegen/scripts/codegen_metrics_report.py --today --output today_report.json

# 自定义参数
python infra/mcp/codegen/scripts/codegen_metrics_report.py \
  --start-date 20250701 \
  --end-date 20250707 \
  --k 5 \
  --data-dir metrics/codegen \
  --output weekly_report.json
```

### 3. 参数说明
- `--start-date`: 开始日期 (YYYYMMDD格式)
- `--end-date`: 结束日期 (YYYYMMDD格式)
- `--k`: 最大允许的codegen调用次数 (默认3)
- `--data-dir`: metrics数据目录 (默认metrics/codegen)
- `--output`: 输出JSON文件路径
- `--today`: 生成今日报告

## 报告内容

### 控制台输出
- 📊 基础统计信息
- 🎯 功能正确性指标
- ⚡ 执行成功率指标
- ⏱️ 响应时间指标和分布
- 🔤 Token使用指标和分布
- 🐌 最慢任务TOP5
- 💰 Token消耗最多任务TOP5

### JSON输出格式
```json
{
  "report_date": "2025-07-07 17:08:30",
  "date_range": "20250707 ~ 20250707",
  "summary": {
    "total_tasks": 5,
    "total_calls": 4,
    "functional_correctness": 1.0,
    "execution_success_rate": 1.0,
    "avg_task_duration_ms": 8700.25,
    "avg_call_duration_ms": 8700.25,
    "avg_task_tokens": 1323.0,
    "avg_call_tokens": 985.75
  },
  "detailed_metrics": {
    "tasks": { ... },
    "calls": { ... },
    "tokens": { ... }
  },
  "top_lists": {
    "slowest_tasks": [...],
    "highest_token_tasks": [...]
  }
}
```

## 运营监控建议

### 日常监控
```bash
# 每日运行，检查关键指标
python infra/mcp/codegen/scripts/daily_report.py
```

### 周报生成
```bash
# 每周一生成上周报告
python infra/mcp/codegen/scripts/codegen_metrics_report.py \
  --start-date 20250630 \
  --end-date 20250706 \
  --output weekly_report_w27.json
```

### 月报生成
```bash
# 每月1号生成上月报告
python infra/mcp/codegen/scripts/codegen_metrics_report.py \
  --start-date 20250601 \
  --end-date 20250630 \
  --output monthly_report_june.json
```

## 告警阈值建议

| 指标 | 正常 | 警告 | 严重 |
|------|------|------|------|
| 功能正确性 | ≥95% | 90-95% | <90% |
| 执行成功率 | ≥98% | 95-98% | <95% |
| 任务平均耗时 | <30s | 30-60s | >60s |
| 调用平均耗时 | <10s | 10-20s | >20s |
| 平均Token/任务 | <2000 | 2000-5000 | >5000 |

## 自动化集成

### Crontab定时任务
```bash
# 每日8点生成昨日报告
0 8 * * * cd /path/to/project && python infra/mcp/codegen/scripts/daily_report.py >> logs/daily_metrics.log 2>&1

# 每周一9点生成周报
0 9 * * 1 cd /path/to/project && python infra/mcp/codegen/scripts/codegen_metrics_report.py --start-date $(date -d '7 days ago' +%Y%m%d) --end-date $(date -d '1 day ago' +%Y%m%d) --output reports/weekly_$(date +%Y%m%d).json
```

### CI/CD集成
在持续集成流程中加入指标检查，确保代码变更不会显著影响性能指标。

## 故障排查

### 常见问题
1. **无数据**: 检查metrics目录是否存在数据文件
2. **路径错误**: 确保PYTHONPATH正确设置
3. **权限问题**: 检查文件读写权限

### 调试模式
```bash
# 添加详细日志输出
PYTHONPATH=/path/to/project python -v infra/mcp/codegen/scripts/daily_report.py
```

## 扩展开发

脚本采用模块化设计，可以轻松扩展新的指标计算和报告格式。主要组件：

- `CodegenMetricsProcessor`: 核心处理器
- `CodegenReport`: 报告数据结构
- `print_report()`: 控制台输出格式化
- `save_report_json()`: JSON格式保存

如需添加新指标，可继承或扩展这些组件。