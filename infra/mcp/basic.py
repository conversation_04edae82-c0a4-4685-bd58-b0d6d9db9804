from mcp import ClientSession
from common.logger.logger import logger
from mcp.shared.exceptions import McpError
from mcp.client.sse import sse_client
import httpx
import httpcore

async def mcp_call_tool(url: str, tool_name: str, arguments: dict, timeout: float=5, sse_read_timeout:float= 60*5):
    '''
    执行MCP工具调用
    Args:
        url: MCP服务URL
        tool_name: 要调用的工具名称
        arguments: 工具调用参数
        timeout: HTTP请求超时时间
        sse_read_timeout: SSE读取超时时间
    '''
    try:
        async with sse_client(url, timeout=timeout, sse_read_timeout=sse_read_timeout) as (read, write):
            logger.info(f"Initialized session, url: {url}")
            async with ClientSession(read, write) as session:
                await session.initialize()
                logger.info(f"Initialized session, url: {url}")
                return await session.call_tool(tool_name, arguments=arguments)
    except BaseExceptionGroup as e:
        logger.warning(f"Executing {tool_name} original failed: {str(e)}")
        while isinstance(e, ExceptionGroup) or isinstance(e, BaseExceptionGroup):
            e = e.exceptions[0]
        if isinstance(e, McpError) :
            logger.warning(f"Executing {tool_name} failed: {str(e.error.message)}", exc_info=True)
            raise RuntimeError(f"Executing {tool_name} failed: {str(e.error.message)}")
        elif isinstance(e, httpx.TimeoutException) or isinstance(e, httpx.NetworkError):
            logger.warning(f"Executing {tool_name} failed: {str(type(e).__name__)}")
            raise RuntimeError(f"Executing {tool_name} failed: {str(type(e).__name__)}")
        else:
            logger.error(f"Executing {tool_name} failed: {str(e)}", exc_info=True)
            raise e
    except Exception as e:
        logger.error(f"Executing {tool_name} failed: {str(e)}", exc_info=True)
        raise RuntimeError(f"Executing {tool_name} failed: {str(e)}") from e

        
if __name__ == "__main__":
    import asyncio
    # rst = asyncio.run(mcp_call_tool("http://xxx:31234/sse?key=xxx", "DLCListEngines", {}))
    rst = asyncio.run(mcp_call_tool("http://xxx:31235/sse?key=xxx", "TCHouseDListDatabases", {}))
    print(rst)
   