import json
import traceback
from mcp.server import Server
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
import os
import ray
import mcp.types as types
from common.logger.logger import logger, get_logger_with_ctx
from mcp.types import Resource, Tool, TextContent
from infra.jupyter.manager import KernelManager, KernelURN, KernelUtil
from common.share import env
from common.share.config import appConfig
from common.share.enums import MCPEngineTypeEnum
from infra.mcp.jupyter.data_loaders import dlc_spark_data_loader, dlc_mcp_data_loader, tchouse_d_mcp_data_loader
@asynccontextmanager
async def server_lifespan(server: Server) -> AsyncIterator[dict]:
    """Manage server startup and shutdown lifecycle."""
    # Initialize resources on startup
    try:
        logger.info(f"Server variables: eg_sub_uin={os.environ.get('EG_SUBUIN')}, timeout={os.environ.get('EG_TIMEOUT')}, eg_host={os.environ.get('EG_GATEWAY_HOST')}, kernel_id={os.environ.get('KERNEL_ID')}, kernel_name={os.environ.get('KERNEL_NAME')}, trace_id={os.environ.get('TRACE_ID')}, mcp_url={os.environ.get('MCP_URL')}")
        yield {
            "eg_sub_uin": os.environ.get("EG_SUBUIN"),
            "timeout": os.environ.get("EG_TIMEOUT","600"),
            "eg_host": os.environ.get("EG_GATEWAY_HOST","unknown"),
            "kernel_id": os.environ.get("KERNEL_ID",""),
            "kernel_name": os.environ.get("KERNEL_NAME","unknown"),
            "trace_id": os.environ.get("TRACE_ID","unknown"),
            "mcp_url": os.environ.get("MCP_URL","unknown"),
            "data_engine_name": os.environ.get("DATA_ENGINE_NAME","unknown"),
        }
    finally:
        # Clean up on shutdown
        logger.info("Shutting down server")

# Pass lifespan to server
server = Server("intellix-ds-agent", lifespan=server_lifespan)

def get_kernel_urn_by_kwargs(**kwargs) -> KernelURN:
    eg_sub_uin = kwargs.get("eg_sub_uin", "unknown")
    eg_host = kwargs.get("eg_host", "unknown")
    kernel_name = kwargs.get("kernel_name", "unknown")
    kernel_id = kwargs.get("kernel_id", "unknown")
    trace_id = kwargs.get("trace_id", "unknown")
    kernel_urn = KernelURN(
        sub_uin=eg_sub_uin, 
        kernel_host=eg_host, 
        kernel_name=kernel_name, 
        kernel_id=kernel_id if kernel_id != "" else None,
    )
    if eg_host == "unknown" or kernel_name == "unknown" or kernel_name == "":
        kernel_urn.kernel_type = "ipython"
        kernel_urn.kernel_name = "ipython"
        kernel_urn.kernel_id = trace_id
    return kernel_urn

def use_dlc_kernel(kernel_name: str) -> bool:
    return kernel_name != "unknown" and kernel_name != ""

def get_kernel_manager_by_kwargs(**kwargs) -> KernelManager:
    if env.EXECUTION_MODE_RAY:
        ray_address = appConfig.common.ray_config.ray_address  # 指定 Ray 集群的 IP 地址
        logger.info(f"Ray address: {ray_address}")
        ray.init(
            log_to_driver=False,
            namespace="ray",
            address=f"ray://{ray_address}",
            runtime_env={"env_vars": {
                "CONF_PATH": os.getenv("CONF_PATH", ""),
                "LOG_PATH": os.getenv("LOG_PATH", ""),
                "WORKING_DIR": os.getenv("WORKING_DIR", ""),
                "OMP_NUM_THREADS": "5",
                "PYTHONPATH": os.getenv("PYTHONPATH", ""),
                "LOGGER_TO_STDOUT": os.getenv("LOGGER_TO_STDOUT", ""),
            },
            "pip": appConfig.common.ray_config.dependencies,
            },
            ignore_reinit_error=True,
        )
    manager = KernelManager(use_ray=env.EXECUTION_MODE_RAY)
    return manager

def get_code_by_engine_type(engine_type: str, mcp_url: str, kernel_name: str, data_engine_name: str, sql: str) -> (str, str):
    if use_dlc_kernel(kernel_name) and engine_type == MCPEngineTypeEnum.DLC.value:
        return dlc_spark_data_loader(sql), []
    # not use dlc kernel
    if engine_type == MCPEngineTypeEnum.DLC.value and not use_dlc_kernel(kernel_name):
        return dlc_mcp_data_loader(sql, mcp_url, data_engine_name), ["mcp"]
    if engine_type == MCPEngineTypeEnum.TCHouseD.value:
        return tchouse_d_mcp_data_loader(sql, mcp_url), ["mcp"]
    else:
        raise Exception(f"engine_type {engine_type} is not supported")

def tool_execute_code(name: str = "execute_code") -> tuple[str, Tool, callable]:
    tool = Tool(
        name=name,
        description='''
        Execute code in a remote Jupyter kernel and return the output and error information.

        Args:
        code: The code string to execute in the Jupyter kernel.

        Returns:
        {
            "outputs": <list of output results>,
            "error": <is exec error, return true or false>
        }

        Example:
        execute_code(code="print('hello world')")
        execute_code(code="print('hello world')", required_packages=["prophet"])
        ''',
        inputSchema={
            "properties": {
                "code": {
                    "type": "string",
                    "description": "The code to execute in the Jupyter kernel"
                },
                "required_packages": {
                    "type": "array",
                    "description": "The required packages to install in the Jupyter kernel"
                }
            },
            "required": ["code"],
        }
    )
    
    async def execute_code(**kwargs):
        rst = {}
        try:
            trace_id = kwargs.get("trace_id", "unknown")
            sub_uin = kwargs.get("eg_sub_uin", "unknown")
            logger = get_logger_with_ctx(sub_account_uin=sub_uin, trace_id=trace_id, model_name="execute_code")
            logger.info(f"Executing execute_code with parameters: {kwargs}")

            timeout = int(kwargs.get("timeout", 180))
            code = kwargs.get("code", "")
            required_packages = kwargs.get("required_packages", [])
            kernel_urn = get_kernel_urn_by_kwargs(**kwargs) 
            manager = get_kernel_manager_by_kwargs(**kwargs)
            kernel_urn = await manager.start_kernel_with_timeout(trace_id, kernel_urn, timeout=timeout)
            if kernel_urn is None:
                raise Exception(f"failed to start kernel, trace_id: {trace_id}")
            logger.info(f"start kernel success, trace_id: {trace_id}, kernel_urn: {kernel_urn}, required_packages: {required_packages}")
            if required_packages:
                outputs, has_err = KernelUtil.install_requirements(kernel_urn, required_packages)
                if has_err:
                    logger.error(f"failed to install requirements, error: {outputs}")
                    raise Exception(f"failed to install requirements, error: {outputs}")
                for output in outputs:
                    if "not find a version" in json.dumps(output):
                        logger.error(f"failed to install requirements, error: {output}")
                        raise Exception(f"failed to install requirements, error: {output}")
                logger.info(f"installing requirements done for {required_packages} , outputs: {outputs}")
            logger.info(f"execute code, trace_id: {trace_id}, kernel_urn: {kernel_urn}, code: {code}")
            outputs, has_err = KernelUtil.execute_cmd(kernel_urn, code,timeout)
            logger.info(f"execute code done, trace_id: {trace_id}, kernel_urn: {kernel_urn}, code: {code}, has_err: {has_err}")
            rst["error"] = has_err
            rst["outputs"] = outputs 
        except RuntimeError as err:
            logger.error(f"failed to exec_code, error: {str(err.args[0])}")
            rst = {
                "error": True,
                "details": str(err.args[0])
            }
            return [TextContent(type="text",text=json.dumps(rst, ensure_ascii=False))]
        except Exception as err:
            logger.error(f"failed to exec_code, error: {err} {traceback.format_exc()}")
            rst = {
                "error": True,
                "details": str(err.args[0])
            }
            return [TextContent(type="text",text=json.dumps(rst, ensure_ascii=False))]
        return [TextContent(type="text",text=json.dumps(rst, ensure_ascii=False))]
    return name,tool,execute_code

def load_data_by_sql(name: str = "load_data_by_sql") -> tuple[str, Tool, callable]:
    tool = Tool(
        name=name,
        description='''
        Execute an SQL query in a notebook and load the results into a DataFrame variable.

        Args:
        sql: The SQL query to execute in the Jupyter kernel.

        Returns:
        {
            "error": is exec error, return true or false
            "outputs": dataframe df info use notebook outputs when exec this code, print("data loaded");print(f"data shape: {df.shape}");print(f"data types: {df.dtypes.to_dict()}"),print(f"data columns: {df.columns.tolist()}"),print(f"data example:"),df.head()
            "details": error details
        }

        Example:
        load_data_by_sql(sql="select * from test")
        load_data_by_sql(sql="SELECT * FROM nl2sql_test.orders limit 3;", required_packages=["pandas"])
        return:
        {
            "error": false,
            "outputs": [
                [
                  {
                    "output_type": "stream",
                    "name": "stdout",
                    "text": [
                      "data loaded\n",
                      "data shape: (3, 6)\n",
                      "data types: {'customer_id': dtype('int64'), 'order_date': dtype('O'), ...}\n",
                      "data columns: ['customer_id', 'order_date', ...]\n",
                      "data example:\n"
                    ]
                  },
                  {
                    "output_type": "execute_result",
                    "metadata": {},
                    "data": {
                      "text/plain": [
                        "   customer_id  order_date  ...\n",
                        "0            2  2024-02-16 ...\n",
                        "1            8  2023-09-13 ...\n",
                        "2           40  2023-09-29 ...\n"
                      ],
                      "text/html": [
                        "<div>...</div>"
                      ]
                    },
                    "execution_count": 3
                  }
                ]
            ],
            "details": <error details>
        }
        ''',
        inputSchema={
            "properties": {
                "sql": {
                    "type": "string",
                    "description": "The SQL query to execute in the Jupyter kernel"
                }
            },
            "required": ["sql"],
        }
    )
    
    async def load_data_by_sql(**kwargs):
        rst = {
            "error": True,
            "length": 0,
            "column_names": [],
            "column_types": [],
        }
        try:
            trace_id = kwargs.get("trace_id", "unknown")
            sub_uin = kwargs.get("eg_sub_uin", "unknown")
            logger = get_logger_with_ctx(sub_account_uin=sub_uin, trace_id=trace_id, model_name="load_data_by_sql")
            logger.info(f"Executing load_data_by_sql with parameters: {kwargs}")

            kernel_name = kwargs.get("kernel_name", "unknown")
            sql = kwargs.get("sql", "")
            timeout = int(kwargs.get("timeout", 180))
            data_engine_name = kwargs.get("data_engine_name", "unknown")

            mcp_url_input: dict[str, str] = json.loads(kwargs.get("mcp_url", "{}"))
            if mcp_url_input is None or len(mcp_url_input) != 1:
                raise Exception(f"mcp_url is None")
            engine_type, mcp_url = list(mcp_url_input.items())[0]
            logger.info(f"engine_type: {engine_type}, mcp_url: {mcp_url}")
            if engine_type not in MCPEngineTypeEnum:
                raise Exception(f"engine_type {engine_type} is not supported")
         
            kernel_urn = get_kernel_urn_by_kwargs(**kwargs) 
            manager = get_kernel_manager_by_kwargs(**kwargs)
            kernel_urn = await manager.start_kernel_with_timeout(trace_id, kernel_urn, timeout=timeout)
            if kernel_urn is None:
                raise Exception(f"failed to start kernel, trace_id: {trace_id}")
            
            code, required_packages = get_code_by_engine_type(engine_type, mcp_url, kernel_name, data_engine_name, sql)
            logger.info(f"code: {code}, required_packages: {required_packages}")
            if required_packages:
                outputs, has_err = KernelUtil.install_requirements(kernel_urn, required_packages)
                if has_err:
                    logger.error(f"failed to install requirements, error: {outputs}")
                    raise Exception(f"failed to install requirements, error: {outputs}")
                for output in outputs:
                    if "not find a version" in json.dumps(output):
                        logger.error(f"failed to install requirements, error: {output}")
                        raise Exception(f"failed to install requirements, error: {output}")
                logger.info(f"installing requirements done for {required_packages} , outputs: {outputs}")
            
            outputs, has_err = KernelUtil.execute_cmd(kernel_urn, code, timeout)
            if has_err:
                logger.error(f"failed to load_data_by_sql, error: {outputs}")
                raise Exception(f"failed to load_data_by_sql, error: {outputs}")
            logger.info(f"load_data_by_sql done, trace_id: {trace_id}, kernel_urn: {kernel_urn}, code: {code}, has_err: {has_err}, outputs: {outputs}")
            rst["error"] = has_err
            rst["outputs"] = outputs 
        except RuntimeError as err:
            logger.error(f"failed to load_data_by_sql, error: {str(err.args[0])}")
            rst["details"] = str(err.args[0])
            return [TextContent(type="text",text=json.dumps(rst, ensure_ascii=False))]
        except Exception as err:
            logger.error(f"failed to load_data_by_sql, error: {err} {traceback.format_exc()}")
            rst["details"] = str(err.args[0])
            return [TextContent(type="text",text=json.dumps(rst, ensure_ascii=False))]
        return [TextContent(type="text",text=json.dumps(rst, ensure_ascii=False))]
    return name,tool,load_data_by_sql

tool_list = [tool_execute_code(), load_data_by_sql()]
tool_map = {name: tool for name,tool,func in tool_list}
tool_funcs = {name: func for name,tool,func in tool_list}

@server.list_tools()
async def list_tools() -> list:
    return tool_map.values()

@server.call_tool()
async def call_tool(name: str, arguments: dict) -> dict:
    """Call a tool by name."""
    if name not in tool_map:
        raise ValueError(f"Tool {name} not found")
    ctx = server.request_context.lifespan_context
    logger.info(f"Executing tool {name} : with params {arguments} in ctx {ctx}")   

    eg_sub_uin = ctx.get("eg_sub_uin","unknown")
    timeout = int(ctx.get("timeout","180"))
    eg_host = ctx.get("eg_host","unknown")
    kernel_id = ctx.get("kernel_id",None)
    trace_id = ctx.get("trace_id","unknown")
    kernel_name = ctx.get("kernel_name","unknown")
    mcp_url = ctx.get("mcp_url","{}")
    data_engine_name = ctx.get("data_engine_name","unknown")
    func = tool_funcs[name]
    arguments.update({
        "eg_host":eg_host,
        "eg_sub_uin":eg_sub_uin,
        "kernel_id":kernel_id,
        "kernel_name":kernel_name,
        "trace_id":trace_id,
        "mcp_url":mcp_url,
        "data_engine_name":data_engine_name,
        "timeout":timeout})
    return await func(**arguments)


@server.list_resources()
async def list_resources() -> list:
    return []

@server.read_resource()
async def read_resource(url: str) -> str:
    return ""

@server.list_prompts()
async def list_prompts() -> list:
    return [
        types.Prompt(
            name="execute code",
            description="please help me exec code",
            arguments=[],
        )
    ]

@server.get_prompt()
async def handle_get_prompt(
    name: str, arguments: dict[str, str] | None
) -> types.GetPromptResult:
    return types.GetPromptResult(
        description="help me exec code",
        messages=[
            types.PromptMessage(
                role="user",
                content=types.TextContent(type="text", text="please help me exec code"),
            )
        ]
    )

async def run_stdio():
    from mcp.server.stdio import stdio_server
    logger.info("Starting stdio server")
    async with stdio_server() as (read_stream, write_stream):
        try:
            await server.run(
                read_stream,
                write_stream,
                server.create_initialization_options()
            )
        except Exception as e:
            logger.error(f"Server error: {str(e)}", exc_info=True)
            raise

def run_sse(port: int = 8900):
    from mcp.server.sse import SseServerTransport 
    from starlette.applications import Starlette 
    from starlette.routing import Mount, Route
    import uvicorn
    sse = SseServerTransport("/messages/")  # 创建SSE服务器传输实例，路径为"/messages/"
    
    async def handle_sse(request):
        async with sse.connect_sse(
            request.scope, request.receive, request._send
        ) as streams:
            # 建立SSE连接，获取输入输出流
            try:
                await server.run(
                    streams[0], streams[1], server.create_initialization_options()
                )
            except Exception as e:
                logger.error(f"Server error: {str(e)}", exc_info=True)
                raise     

    starlette_app = Starlette(
        debug=True,  # 启用调试模式
        routes=[
            Route("/sse", endpoint=handle_sse),  # 设置/sse路由，处理函数为handle_sse
            Mount("/messages/", app=sse.handle_post_message),  # 挂载/messages/路径，处理POST消息
        ],
    )  # 创建Starlette应用实例，配置路由
    uvicorn.run(starlette_app, host="0.0.0.0", port=port)


if __name__ == "__main__":
    # sync
    import asyncio
    asyncio.run(run_stdio())
    
    # async
    # run_sse()
