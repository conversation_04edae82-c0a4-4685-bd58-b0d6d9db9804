view_df = '''# 预览数据
from IPython.display import display, Markdown
import pandas as pd

# 生成数据概览的markdown
data_info = f"""## 数据概览

### 基本信息
- **数据形状**: {df.shape[0]:,} 行 × {df.shape[1]} 列
- **内存使用**: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB

### 数据类型
"""

# 添加数据类型统计
dtype_counts = df.dtypes.value_counts()
for dtype, count in dtype_counts.items():
    data_info += f"- **{dtype}**: {count} 列\\n"

data_info += f"""

### 列信息
"""

# 添加列名列表，每行显示3-4个
columns = df.columns.tolist()
for i in range(0, len(columns), 4):
    chunk = columns[i:i+4]
    data_info += "- " + " • ".join([f"`{col}`" for col in chunk]) + "\\n"

data_info += """

### 数据样例
"""

display(Markdown(data_info))
df.head()'''

sse_load_data =f'''# 忽略 warning
import warnings
warnings.filterwarnings('ignore')
import pandas as pd
from mcp import ClientSession
from mcp.client.sse import sse_client
import asyncio
def run_async_in_sync_context(func, *args, **kwargs):
    # 检查是否已有运行的事件循环
    try:
        loop = asyncio.get_running_loop()
        # 如果已有运行的事件循环，使用run_coroutine_threadsafe
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(asyncio.run, func(*args, **kwargs))
            return future.result()
    except RuntimeError:
        # 没有运行的事件循环，可以直接使用asyncio.run
        return asyncio.run(func(*args, **kwargs))

async def load_data_by_sql(mcp_url, tool_name, arguments):
    async with sse_client(mcp_url) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            tools = await session.list_tools()
            # print("tools: ", tools)
            rst = await session.call_tool(tool_name, arguments=arguments)
            return rst'''

dlc_spark_data_loader = lambda sql: f"""
# 忽略 warning
import warnings
warnings.filterwarnings('ignore')

# 镜像设置
%pip config set global.index-url https://mirrors.tencentyun.com/pypi/simple/ > /dev/null

# 中文字体

# 数据加载 - 使用预配置的Spark Session，名字为 "spark"
spark.conf.set("spark.sql.session.timeZone", "Asia/Shanghai")

import pandas as pd

df = spark.sql('''
{sql}
''').toPandas()

{view_df}"""

dlc_mcp_data_loader = lambda sql, mcp_url, data_engine_name: f'''
{sse_load_data}

def dlc_rst_to_df():
    rst = run_async_in_sync_context(load_data_by_sql, "{mcp_url}", "DLCExecuteQuery", {{"SparkSQL": "{sql}", "DataEngineName": "{data_engine_name}"}})
    # rst 实际为 content=[TextContent(type='text', text='...json...')]
    # 需要解析 json，提取 ResultSchema 和 ResultSet，转为 DataFrame
    import json

    # 1. 兼容 rst 直接为 dict 或有 content 属性
    if hasattr(rst, "content"):
        # rst.content 可能为 list
        content = rst.content[0] if isinstance(rst.content, list) else rst.content
        # 兼容 TextContent
        text = getattr(content, "text", None) or content
        try:
            data = json.loads(text)
        except Exception:
            return pd.DataFrame()
    elif isinstance(rst, dict):
        data = rst
    else:
        return pd.DataFrame()
    # 2. 兼容 Response 层
    response = data.get("Response", data)
    task_info = response.get("TaskInfo", response)
    # 3. 取 ResultSchema 和 ResultSet
    schema = task_info.get("ResultSchema", None)
    result_set = task_info.get("ResultSet", None)
    if result_set is None or schema is None:
        return pd.DataFrame()
    # 4. ResultSet 可能是字符串，需要 json.loads
    if isinstance(result_set, str):
        try:
            result_set = json.loads(result_set)
        except Exception:
            return pd.DataFrame()
    # 5. 列名
    columns = [col.get("Name", f"col{{i}}") for i, col in enumerate(schema)]
    # 6. 转 DataFrame
    df = pd.DataFrame(result_set, columns=columns)
    # df = df.astype(str)
    return df

df = dlc_rst_to_df()

{view_df}'''

tchouse_d_mcp_data_loader = lambda sql, mcp_url: f'''
{sse_load_data}

def tchouse_d_rst_to_df():
    rst = run_async_in_sync_context(load_data_by_sql, "{mcp_url}", "TCHouseDExecuteQuery", {{"Query": "{sql}"}})
    # rst 转 DataFrame
    import json
    # 1. 兼容 rst 直接为 dict 或有 content 属性
    if hasattr(rst, "content"):
        # rst.content 可能为 list
        content = rst.content[0] if isinstance(rst.content, list) else rst.content
        # 兼容 TextContent
        text = getattr(content, "text", None) or content
        try:
            data = json.loads(text)
        except Exception:
            return pd.DataFrame()
    elif isinstance(rst, dict):
        data = rst
    else:
        return pd.DataFrame()
    # 2. 取 Data 字段
    result_set = data.get("Data", None)
    if result_set is None:
        return pd.DataFrame()
    # 3. 自动推断列名
    if isinstance(result_set, list) and len(result_set) > 0:
        columns = list(result_set[0].keys())
    else:
        columns = []
    # 4. 转 DataFrame
    df = pd.DataFrame(result_set, columns=columns)
    return df

df = tchouse_d_rst_to_df()
{view_df}
'''