import os

from mcp import ClientSession, StdioServerParameters, stdio_client
from common.logger.logger import logger
from common.share import env
from common.share.enums import MCPEngineTypeEnum
import json

def get_params(mcp_type: str, mcp_url: str):
    PYTHONPATH = os.getenv('PYTHONPATH',"./")
    base_params = {
        "MCP_URL": json.dumps({mcp_type: mcp_url}) if mcp_type else '{}',
        "TRACE_ID": "1234567890",
        "EG_SUBUIN": "100041916576",
        "EG_GATEWAY_HOST": "**********:8888",
        "KERNEL_ID": "",
        "DATA_ENGINE_NAME": "data-agent-exp-dev",
        "IMAGE_TAG": env.IMAGE_TAG,
        "EXECUTION_MODE": env.EXECUTION_MODE,
    }
    if mcp_type == MCPEngineTypeEnum.TCHouseD.value:
        return base_params | {
            # "KERNEL_NAME": "unknown",
            "PYTHONPATH": f"{PYTHONPATH}:{os.getcwd()}"
        } 
    elif mcp_type == MCPEngineTypeEnum.DLC.value:
        return base_params | {
            "EG_GATEWAY_HOST": "lb-2ohhzn5i-egfmcuxsj56dh1op.clb.cq-tencentclb.com:8888",
            # "KERNEL_NAME": "rg-io2c6sp3k4",
            "PYTHONPATH": f"{PYTHONPATH}:{os.getcwd()}"
        } 
    else:
        return base_params

async def test_run_stdio(mcp_type: str, mcp_url: str, sql: str = None):
    params = StdioServerParameters(
        command = "python3",
        args = [f"{os.getcwd()}/infra/mcp/jupyter/server.py"],
        env=get_params(mcp_type, mcp_url)
    )
    logger.info(f"start cmd {params}")
    async with stdio_client(params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            tools = await session.list_tools()
            logger.info(f"tools list result: {tools}")
            assert any(tool.name == "execute_code" for tool in tools.tools), "Tool 'execute_code' not found in tools list"
            rst = await session.call_tool("execute_code",arguments={"code":"print('Hello world')"})
            logger.info(f"tools exec result: {rst}")
            if sql:
                rst = await session.call_tool("load_data_by_sql",arguments={"sql":sql})
                logger.info(f"tools exec result: {rst}")
            # assert "Hello world\\n" in rst.content[0].text


if __name__ == "__main__":
    lb = "lb-2ohhzn5i-egfmcuxsj56dh1op.clb.cq-tencentclb.com"
    
    mcp_type_dlc = MCPEngineTypeEnum.DLC.value
    mcp_url_dlc = f"http://{lb}:31234/sse?key=xxxx"
    sql_dlc = "SELECT year(to_timestamp(datetime)) AS year, COUNT(*) AS sighting_count FROM online_demo.ufo GROUP BY year(to_timestamp(datetime)) ORDER BY year"

    mcp_type_tchoused = MCPEngineTypeEnum.TCHouseD.value
    mcp_url_tchoused = f"http://{lb}:31234/sse?key=xxxx"
    sql_tchoused = "SELECT * FROM nl2sql_test.orders limit 3;"
    
    import asyncio
    asyncio.run(
        test_run_stdio(mcp_type_tchoused,mcp_url_tchoused,sql_tchoused))
