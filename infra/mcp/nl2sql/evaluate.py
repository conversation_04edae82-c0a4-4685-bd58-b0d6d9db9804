from infra.mcp.nl2sql.mcp_client.client import dlc_execute_query, DLCExecuteQueryReq
import json


def main():
    with open("/Users/<USER>/Desktop/execute_dlc/execute_bird_fix_output.json", 'r', encoding='utf-8') as f:
        data = json.load(f)
    for d in data:
        try:
            arguments = DLCExecuteQueryReq(
                SparkSQL=d["FIX_SQL"],
                DatabaseName=d["db_id"],
                DatasourceConnectionName="DataLakeCatalog",
                DataEngineName="data-agent-exp-dev"
            )

            gold_rsp = dlc_execute_query(url="http://127.0.0.1:31234/sse?auth_token=SECRET_KEY_2025",
                                         arguments=arguments, tool_name="DLCExecuteQuery")

            arguments = DLCExecuteQueryReq(
                SparkSQL=d["pred_sql"],
                DatabaseName=d["db_id"],
                DatasourceConnectionName="DataLakeCatalog",
                DataEngineName="data-agent-exp-dev"
            )

            pred_rsp = dlc_execute_query(url="http://127.0.0.1:31234/sse?auth_token=SECRET_KEY_2025",
                                         arguments=arguments,
                                         tool_name="DLCExecuteQuery")
            gold_tuples = {tuple(row) for row in gold_rsp.ResultSet}
            pred_tuples = {tuple(row) for row in pred_rsp.ResultSet}
            d["result"] = int(gold_tuples == pred_tuples)
            _save_result(d)
        except Exception as e:
            _handle_error(d, e)


def _save_result(data: dict) -> None:
    """保存处理结果到文件"""
    with open("/Users/<USER>/Desktop/execute_dlc/execute_bird_result_output.json", 'r+', encoding='utf-8') as f:
        try:
            existing_data = json.load(f)
        except json.JSONDecodeError:
            existing_data = []
        existing_data.append(data)
        f.seek(0)
        json.dump(existing_data, f, indent=4, ensure_ascii=False)
        f.truncate()


def _handle_error(data: dict, error: Exception) -> None:
    """统一错误处理"""
    print(f"处理失败[ID:{data.get('question_id')}]: {str(error)}")
    data["result"] = 0
    data["error"] = str(error)
    _save_result(data)


if __name__ == '__main__':
    main()
