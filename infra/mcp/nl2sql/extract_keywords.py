import ast
from typing import List

from infra.mcp.nl2sql.llm.extract_keywords_prompt import EXTRACT_KEYWORDS_PROMPT
from infra.mcp.nl2sql.llm.llm import llm_chat
from infra.mcp.nl2sql.utils.gpt_rsp_util import parse_last_python_code


def extract_keywords(question: str,trace_id:str) -> List[str]:
    """从自然语言问题中提取关键词

    通过大语言模型分析用户问题，提取SQL生成所需的关键词

    返回:
        List[str]: 提取出的关键词列表

    异常:
        Exception: 当关键词解析失败时抛出
    """
    # 构建关键词提取任务描述（保留后续可能的提示扩展）
    task = (
        f"Question: {question}"
        # f"Hint: {hint}"  # 预留的提示信息扩展点
    )
    # 使用预定义模板构造LLM提示
    prompt = EXTRACT_KEYWORDS_PROMPT.format(TASK=task)

    llm_prompts = [
        {"role": "user", "content": prompt},
    ]

    try:
        # 调用大语言模型接口（空字符串表示使用默认模型）
        response = llm_chat(llm_prompts)
        # 解析模型返回的Python代码片段
        keywords = parse_last_python_code(response)
        # 将字符串形式的列表转换为实际列表对象（注意eval的安全风险）
        keywords = ast.literal_eval(keywords)
    except Exception:
        raise ValueError(f"Failed to extract keywords,question:{question},trace_id{trace_id}")
    return keywords
