from enum import Enum


class EngineTypeEnum(Enum):
    """定义支持的查询引擎类型枚举

    该枚举类用于标识不同的大数据查询引擎类型，每个枚举值对应具体的引擎服务名称

    枚举成员:
        DLC: 数据湖计算引擎（Data Lake Compute）
        TC_HOUSE_D: TC_HOUSE_D 数据库
        ES: Elasticsearch（分布式搜索引擎）
        TC_HOUSE_C: TC_HOUSE_C 数据库
        TC_HOUSE_X: TC_HOUSE_C 数据库
    """
    DLC = "DLC"
    TC_HOUSE_D = "TCHouseD"
    ES = "ES"
    TC_HOUSE_C = "TCHouseC"
    TC_HOUSE_X = "TCHouseX"
