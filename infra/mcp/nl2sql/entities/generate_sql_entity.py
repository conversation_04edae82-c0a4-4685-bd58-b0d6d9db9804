from typing import List, Optional, Dict, Set

from pydantic import BaseModel

from common.logger.logger import logger
from common.share import error


class SQLGenerateData(BaseModel):
    """SQL生成结果实体（用于自然语言转SQL场景）

    属性:
        sql (str): 生成的SQL查询语句
        reasoning (str): 生成该SQL的逻辑推理过程
        tables (List[str]): 生成该SQL 使用的表
    """
    sql: Optional[str]
    reasoning: Optional[str]
    tables: Optional[List[str]]
    db_schema: Optional[str]


class Metadata:
    """应用元数据信息类

    属性:
        app_id (str): 应用唯一标识
        user_id (str): 用户唯一标识
        trace_id (str): 请求追踪标识
    """

    app_id: str
    sub_account_uin: str
    trace_id: str
    record_id: str

    def __init__(self, app_id: str, sub_account_uin: str, trace_id: str, record_id: str):
        """初始化元数据对象

        参数:
            app_id: 应用ID（不可为空）
            sub_account_uin: 用户ID（不可为空）
            trace_id: 追踪ID（不可为空）

        异常:
            ValueError: 当任意参数为空时抛出
        """
        # 参数非空校验
        if not app_id or not sub_account_uin or not trace_id or not record_id:
            logger.error(
                '初始化元数据参数为空错误 app_id: %s, user_id: %s, trace_id: %s, record_id: %s',
                app_id, sub_account_uin, trace_id, record_id
            )
            raise ValueError(error.ErrorCode.ParamError.value)

        self.app_id = app_id
        self.sub_account_uin = sub_account_uin
        self.trace_id = trace_id
        self.record_id = record_id


class DatabaseInfo:
    """数据库连接和结构信息存储类

    属性:
        engine_name (str): 数据库引擎类型
        datasource_name (str): 数据库目录/模式
        dbname (str): 数据库名称
        tables (Set[str]): 可用表集合
        isSampling (bool): 是否启用采样模式
    """
    engine_name: str
    datasource_name: str
    dbname: str
    tables: list[str]
    isSampling: bool
    mcp_url: Dict[str, str]
    engine_type: str

    def __init__(self, engine: str, datasource_name: str, dbname: str, tables: Set[str],
                 is_sampling: bool, mcp_url: Dict[str, str], engine_type: str):
        """初始化数据库信息对象

        参数:
            engine_name: 数据库引擎类型（如mysql/postgresql等）
            datasource_name: 数据库目录/模式名称
            dbname: 数据库名称
            tables: 允许访问的表集合
            is_sampling: 是否启用数据采样模式
        """
        # 初始化对象属性
        self.engine_name = engine  # 数据库引擎类型标识
        self.datasource_name = datasource_name  # 数据库目录/模式名称
        self.dbname = dbname  # 具体数据库名称
        self.tables = list(tables)  # 可用表集合（用于SQL生成时的表权限控制）
        self.isSampling = is_sampling  # 下划线参数转为驼峰命名字段（保持命名规范统一）
        self.mcp_url = mcp_url
        self.engine_type = engine_type
