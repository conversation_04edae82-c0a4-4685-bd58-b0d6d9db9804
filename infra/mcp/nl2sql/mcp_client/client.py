from concurrent.futures import ThreadPoolExecutor
import mcp.types as types
import asyncio

from mcp import ClientSession
from mcp.client.sse import sse_client
from common.logger.logger import logger


def __run_async(coro):
    """更优的异步执行封装方案

    改进点：
    1. 使用标准库的线程池替代自定义线程类
    2. 自动处理事件循环生命周期
    3. 更简洁的资源管理
    """
    # 检测当前是否在异步上下文中
    try:
        running_loop = asyncio.get_running_loop()
    except RuntimeError:
        running_loop = None

    if running_loop:
        # 在已有事件循环的上下文中，使用线程池执行
        with ThreadPoolExecutor(max_workers=1) as executor:
            def run_coroutine(c):
                """协程运行包装函数"""
                return asyncio.run(c)

            future = executor.submit(run_coroutine, coro)
            return future.result()
    else:
        # 直接运行并自动管理事件循环
        return asyncio.run(coro)


async def __run_mvp_server_sse(url, tool_name, arguments) -> types.CallToolResult:
    """通过SSE协议执行MCP工具调用并返回结果

    Args:
        url (str): MCP服务器SSE连接地址（格式示例：http://host:port/sse?auth_token=xxx）
        tool_name (str): 要调用的工具名称（需与MCP服务注册的工具名一致）
        arguments (dict): 工具调用参数（需符合目标工具的参数规范，自动转换为JSON格式）

    Returns:
        types.CallToolResult: 工具调用结果对象，包含执行状态和返回内容

    实现流程：
    1. 建立SSE长连接
    2. 初始化客户端会话
    3. 获取可用工具列表（调试阶段）
    4. 执行目标工具调用
    5. 返回结构化结果
    """
    # 建立SSE长连接（Server-Sent Events），使用异步上下文管理器管理连接资源
    async with sse_client(url) as (read, write):
        logger.info(f"Initialized session,url: {url}")
        # 创建客户端会话上下文，用于管理整个调用生命周期
        async with ClientSession(read, write) as session:
            # 初始化连接（完成握手协议），建立与服务器的通信基础
            await session.initialize()

            # 执行核心工具调用逻辑，await等待异步操作完成
            try:
                return await session.call_tool(tool_name, arguments=arguments)
            except Exception as e:
                logger.error(f"调用工具失败，工具名称:{tool_name} URL:{url}", exc_info=e)
                raise

    # return mcp_rsp  # 返回包含状态码和内容的调用结果对象


def call_mcp_tool(arguments: dict, url: str, tool_name: str) -> types.CallToolResult:
    logger.info(f"Start request tool name:{tool_name}, params:{arguments}")
    try:
        # 在同步上下文中运行异步SSE通信（asyncio.run用于管理事件循环）
        raw_result = __run_async(__run_mvp_server_sse(url, tool_name, arguments))
        logger.info(f"Finished request tool name:{tool_name}, params:{arguments},result:{raw_result}")
        # 错误处理：记录详细错误日志并抛出异常
        if raw_result.isError:
            logger.warning(f"MCP调用失败 参数:{arguments} 工具名称:{tool_name} 错误内容:{raw_result.content}")
        return raw_result
    except Exception as e:
        logger.error(f"SSE通信异常 tool:{tool_name} URL:{url}", exc_info=True)
        raise
