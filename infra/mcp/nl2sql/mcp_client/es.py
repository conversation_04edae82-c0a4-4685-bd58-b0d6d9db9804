import json
from typing import Dict, List, Any

from mcp import types
from pydantic import BaseModel

from infra.mcp.nl2sql.mcp_client.client import call_mcp_tool

from common.logger.logger import logger

from infra.mcp.nl2sql.mcp_client.common_entity import Table, Column


class ESGetIndexSchemaRsp(BaseModel):
    Tables: List[Table]

    @classmethod
    def default(cls) -> "ESGetIndexSchemaRsp":
        """返回带有默认值的空响应对象"""
        return cls(
            Tables=[]
        )

    @classmethod
    def parse_raw_result(cls, raw_result: List[types.TextContent]) -> "ESGetIndexSchemaRsp":
        if not raw_result or not raw_result[0]:
            logger.error(f"es get mappings 获取索引信息 mcp text content 返回为空")
            return ESGetIndexSchemaRsp.default()
        content = raw_result[0].model_dump()
        if 'text' not in content:
            return ESGetIndexSchemaRsp.default()
        # 解析第一段文本内容中的JSON响应
        try:
            all_content = json.loads(content["text"]).get("content")
        except json.JSONDecodeError as e:
            logger.error(f"es get mappings 获取索引信息 JSON解析失败,text: {content["text"]},error:{e}")
            return ESGetIndexSchemaRsp.default()

        if len(all_content) < 2:
            logger.error(f"es get mappings 获取索引信息,索引内容返回为空,text: {raw_result}")
            return ESGetIndexSchemaRsp.default()

        mcp_rsp_json = json.loads(all_content[1]["text"])
        if mcp_rsp_json is None:
            logger.error(f"es get mappings 获取索引信息,索引内容返回为空,text: {raw_result}")
            return ESGetIndexSchemaRsp.default()
        tables = []
        for index, value in mcp_rsp_json.items():
            properties = value.get("mappings").get("properties")
            columns = []
            for col, col_v in properties.items():
                if col != "logtime" and col != "logtype":
                    columns.append(Column(Name=col, Type=col_v.get("type"), Comment=""))
            tables.append(Table(Name=index, TableComment="", Columns=columns))
        return cls.model_validate({
            "Tables": tables,
        })


class ESSamplingDataReq(BaseModel):
    index: str
    query_body: Dict[str, Any]


class ESSamplingDataRsp(BaseModel):
    Table: str
    ResultSchema: list[str]  # 字段
    ResultSet: list[list[Any]]  # 数据

    @classmethod
    def default(cls) -> "ESSamplingDataRsp":
        """返回带有默认值的空响应对象"""
        return cls(
            Table="",
            ResultSchema=[],  # 字段
            ResultSet=[]  # 数据
        )

    @classmethod
    def parse_raw_result(cls, raw_result: List[types.TextContent], index: str, columns: List[Column]) ->\
            "ESSamplingDataRsp":
        if not raw_result or not raw_result[0]:
            logger.error(f"es sampling data mcp text content 返回为空")
            return ESSamplingDataRsp.default()

        content = raw_result[0].model_dump()
        if 'text' not in content:
            return ESSamplingDataRsp.default()

        try:
            all_content = json.loads(content["text"]).get("content")
        except json.JSONDecodeError as e:
            logger.error(f"es sampling data sampling data JSON解析失败,text: {content["text"]},error:{e}")
            return ESSamplingDataRsp.default()
        if len(all_content) < 2:
            logger.error(f"es sampling data 内容返回为空,text: {raw_result}")
            return ESSamplingDataRsp.default()
        values = []
        columns_fields = [column.Name for column in columns]
        for content in all_content[1:]:
            lines = content["text"].strip().split('\n')
            value = []
            for line in lines:
                idx = line.find(':')
                if idx == -1:
                    continue  # 跳过无效行
                key = line[:idx].strip()  # 提取键（去除空格）
                v = line[idx + 1:].strip()  # 提取值（保留内部空格和特殊字符）
                if key in columns_fields:
                    value.append(v)
            if len(value) == len(columns_fields):
                values.append(value)
        return cls.model_validate({
            "Table": index,
            "ResultSchema": columns_fields,
            "ResultSet": values,
        })


def es_get_index_schema(url: str, arguments: Dict, tool_name="get_mappings") -> ESGetIndexSchemaRsp:
    logger.info(f"Start request es get_mappings tool, params={arguments}")
    mcp_result = call_mcp_tool(arguments, url, tool_name)
    try:
        # 将原始SSE响应内容反序列化为强类型响应模型
        return ESGetIndexSchemaRsp.parse_raw_result(mcp_result.content)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"调用 es get mappings mcp 工具，响应解析失败，原始内容:{mcp_result.content}", exc_info=True)
        raise e


def es_sampling_data(url: str, arguments: ESSamplingDataReq, tool_name="sql_query") -> ESSamplingDataRsp:
    params = arguments.model_dump()
    logger.info(f"Start request es sql_query tool, params={params}")
    schema = es_get_index_schema(url, {"index": arguments.index})
    columns = schema.Tables[0].Columns
    mcp_result = call_mcp_tool(params, url, tool_name)
    try:
        # 将原始SSE响应内容反序列化为强类型响应模型
        return ESSamplingDataRsp.parse_raw_result(mcp_result.content, arguments.index, columns)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"调用 es sql query mcp 工具，响应解析失败，原始内容:{mcp_result.content}", exc_info=True)
        raise e


if __name__ == '__main__':
    es = es_get_index_schema("http://localhost:8000/sse", arguments={"index": "default-service"})
    print(es)

    rsp = es_sampling_data("http://localhost:8000/sse", arguments=ESSamplingDataReq(index="default-service",
                                                                                    query_body={
                                                                                        "size": 2,
                                                                                        "query": {
                                                                                            "match_all": {}
                                                                                        }
                                                                                    }), tool_name="elasticsearch_query")
    print(rsp)
