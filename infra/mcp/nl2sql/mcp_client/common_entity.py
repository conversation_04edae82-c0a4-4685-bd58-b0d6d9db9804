from typing import List

from pydantic import BaseModel


class TableBaseInfo(BaseModel):
    TableName: str


class Column(BaseModel):
    """结果字段结构定义

    用于描述查询结果集中每个字段的元数据信息

    Attributes:
        Name (str): 字段名称
        Type (str): 数据类型
        Comment (str): 字段注释说明
    """
    Name: str
    Type: str
    Comment: str


class Table(BaseModel):
    Name: str
    TableComment: str
    Columns: List[Column]
