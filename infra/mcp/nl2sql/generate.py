from typing import Dict, Any

from common.logger.logger import logger
from common.share import config
from infra.mcp.nl2sql.core.generate_pipeline import Metadata, DatabaseInfo, SQLGenerator
import traceback

from infra.mcp.nl2sql.utils.verify_utils import validate_common_params

from infra.mcp.nl2sql.preprocessing.es_vector_store import Nl2SQLVectorStore, UpdateNl2sqlRecordFeedbackParam, \
    UpdateNl2sqlRecordExecuteResultParam


def generate_sql(params: Dict[str, Any]) -> Dict[str, Any]:
    """根据自然语言生成SQL语句

    参数:
        params (Dict[str, Any]): 包含以下键的字典参数:
            - app_id: 应用ID
            - sub_account_uin: 用户ID
            - trace_id: 追踪ID
            - data_engine_name->数据库引擎名称 原先的 engine
            - db_info: db 信息->[
                {
                        "CatalogName": "", 原先的 datasource_name
                        "DbName": "customer_db",原先的 dbname
                        "TableList": [
                            "users",
                            "orders",
                            "payments"
                        ] 原先的 tables
                    }
            ]
            - is_sampling: 是否采样（默认False）
            - mcp_url: MCP服务地址
            - type: 引擎类型
            - question: 自然语言问题
            - record_id: 上游定义，每次请求一个都是一个新的 ID，
    返回:
        Dict[str, Any]: 包含以下键的结果字典:
            - sql: 生成的SQL语句
            - reasoning: 推理过程说明
            - tables: 涉及的表列表

    异常:
        当参数缺失或处理失败时抛出异常并记录日志
    """
    # 参数校验和转换
    try:
        logger.info(f"Start generating SQL for {params},trace_id ={params.get('trace_id', '')}")

        # 提取元数据参数（用于系统追踪和日志记录）
        metadata = Metadata(
            app_id=params.get("app_id", ""),
            sub_account_uin=params.get("sub_account_uin", ""),
            trace_id=params.get("trace_id", ""),
            record_id=params.get("record_id", ""),
        )

        # 使用公共函数校验参数并提取信息
        final_db_info, engine_type, engine, datasource_name = validate_common_params(params)

        # generate.py特有校验：TableList
        if "TableList" not in final_db_info or not final_db_info.get('TableList'):
            raise ValueError("TableList is required and must not be empty")

        tables = final_db_info.get('TableList')
        dbname = final_db_info.get('DbName')

        database_info = DatabaseInfo(
            engine=engine,
            datasource_name=datasource_name,
            dbname=dbname,
            tables=set(tables),  # 转换为要求的Set类型以去重
            is_sampling=params.get("is_sampling", False),  # 默认不开启采样
            mcp_url=params.get("mcp_url", {}),
            engine_type=engine_type
        )

        # 初始化SQL生成器（核心处理逻辑封装）
        generator = SQLGenerator(
            question=params.get("question", ""),  # 自然语言问题
            metadata=metadata,  # 系统元数据
            database_info=database_info  # 数据库配置信息
        )

        # 执行生成流程并格式化输出结果
        result = generator.generate()
        logger.info(f"Finish generating SQL for {params}, result: {result}")
        return {
            "sql": result.sql,  # 最终生成的SQL语句
            "reasoning": result.reasoning,  # 生成过程的推理说明
            "tables": result.tables  # 实际使用的表列表
        }
    except Exception as e:
        # 记录完整错误日志（包含输入参数和异常详情）
        logger.error(f"SQL生成失败: params={params}, e={e}, {traceback.format_exc()}")
        raise  # 向上层抛出异常保持调用栈


def update_feedback(params: Dict[str, Any]) -> bool:
    """更新NL2SQL记录的反馈信息

    参数:
        params (Dict[str, Any]): 包含以下键的字典参数:
            - app_id: 需要更新的记录ID
            - record_id: 需要更新的记录ID
            - trace_id: 追踪ID用于日志关联
            - feedback: 用户提供的反馈内容 (1-肯定反馈, 2-否定反馈)

    返回:
        bool: 表示更新操作是否成功的布尔值
    """
    logger.info(f"Start update record feedback for {params}")

    required_keys = {"app_id", "record_id", "trace_id", "feedback"}
    if not required_keys.issubset(params.keys()):
        missing = required_keys - params.keys()
        raise ValueError(f"缺少必要参数: {', '.join(missing)}")
    es_config = config.appConfig.automic.nl2sql.es
    emb_model = config.appConfig.automic.nl2sql.embedding
    vector_store = Nl2SQLVectorStore(es_config, params.get("trace_id"), emb_model.model_dims, params.get("app_id"))

    return vector_store.update_nl2sql_record_feedback(
        UpdateNl2sqlRecordFeedbackParam(record_id=params.get("record_id"), trace_id=params.get("trace_id"),
                                        feedback=params.get("feedback")))



if __name__ == '__main__':
    # flag = update_feedback({"record_id": "xxxxxxxxxx", "feedback": 2, "app_id": "ceshi", "trace_id": "collinsdeng"})
    # print(flag)
    # flag2 = update_sql_execution_result(
    #     {"record_id": "xxxxxxxxxx", "doc_id": "Qn-L-JcBy5cxHh5tr6LT", "app_id": "ceshi", "trace_id": "collinsdeng", "is_success": 2,"error_msg":"xsdwewf"})
    # print(flag2)
    p = {
        # "app_id": "1",
        # "sub_account_uin": "collinsdeng",
        # "trace_id": "collinsdeng",
        # # "engine": "data-agent-exp-dev",
        # # "datasource_name": "DataLakeCatalog",
        # "dbname": "default-service",
        # "tables": ["default-service"],
        # "is_sampling": True,
        # "mcp_url": {"es_sql": "http://localhost:8000/sse"},
        # "engine_type": "es_sql",
        # "question": "昨天每个分钟有多少条 info 类型的日志呢？",
        # "record_id": "5"
        "app_id": "ceshi",
        "sub_account_uin": "collinsdeng",
        "trace_id": "collinsdeng",
        "data_engine_name": "data-agent-exp-dev",
        "db_info": '[{"CatalogName": "DataLakeCatalog","DbName": "nl2sql_test","TableList": ["orders","products","customers"]}]',
        "is_sampling": True,
        "mcp_url": {"DLC": "http://127.0.0.1:31234/sse?auth_token=SECRET_KEY_2025"},
        "type": "DLC",
        "question": "查询products表的前10行数据",
        "record_id": "xxxxxxxxxx"
    }

    rsp = generate_sql(p)
    print(f"sql={rsp["sql"]},reasoning = {rsp["reasoning"]},tables = {rsp["tables"]}")
