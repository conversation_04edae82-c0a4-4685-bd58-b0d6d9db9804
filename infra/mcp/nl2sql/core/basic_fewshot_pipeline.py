import json
from typing import List, Dict

from common.logger.logger import logger
from infra.mcp.nl2sql.core.lsh import LSH
from infra.mcp.nl2sql.entities.generate_sql_entity import SQLGenerateData
from infra.mcp.nl2sql.llm.example_prompt import BASIC_FEWSHOT_PIPELINE_EXAMPLE_PROMPT
from infra.mcp.nl2sql.llm.generate_prompt import get_system_prompt, get_user_prompt
from infra.mcp.nl2sql.llm.llm import llm_chat, embedding_texts
from infra.mcp.nl2sql.preprocessing.basic_entity_retrieval import EntityRetrieval
from infra.mcp.nl2sql.preprocessing.es_vector_store import Nl2SQLVectorStore, Nl2sqlEsQueryParams
from infra.mcp.nl2sql.utils.db_util import Column
from infra.mcp.nl2sql.utils.gpt_rsp_util import extract_first_json_block

class BasicFewshotPipeline:
    lsh: LSH
    question: str
    keywords: List[str]
    db_schema: Dict[str, List[Column]]
    ddl: Dict[str, str]
    trace_id: str
    vector_store: Nl2SQLVectorStore
    app_id: str
    datasource_name: str
    dbname: str
    engine_type: str
    engine_type: str
    business_terms: str

    def __init__(self, lsh: LSH, question: str, keywords: List[str], db_schema: Dict[str, List[Column]],
                 ddl: Dict[str, str], trace_id: str, vector_store: Nl2SQLVectorStore, app_id: str,
                 datasource_name: str, dbname: str, engine_type: str, business_terms: str = ""):
        self.lsh = lsh
        self.question = question
        self.keywords = keywords
        self.db_schema = db_schema
        self.ddl = ddl
        self.trace_id = trace_id
        self.vector_store = vector_store
        self.app_id = app_id
        self.datasource_name = datasource_name
        self.dbname = dbname
        self.engine_type = engine_type
        self.business_terms = business_terms

    def run(self):
        logger.info(f"Start basic pipeline,trace_id ={self.trace_id}")
        similar_entities = {}
        if self.lsh:
            entity_retrieval = EntityRetrieval(self.lsh, self.keywords, self.db_schema, self.question,self.trace_id)
            similar_entities = entity_retrieval.get_similar_entities()
            logger.info(f"basic entity retrieval result ={similar_entities},trace_id={self.trace_id}")

        schema_string = "\n".join([f"{k}: {v}" for k, v in self.ddl.items()])
        examples = self._get_fewshot()
        sql_generate_data = self._candidate_generation(schema_string, similar_entities, examples)
        logger.info(f"Finished basic fewshot pipeline,trace_id ={self.trace_id}")
        return sql_generate_data

    def _candidate_generation(self, schema_string: str, unique_values: Dict, examples: str) -> SQLGenerateData:

        system_prompt = get_system_prompt(engine_type=self.engine_type, database_schema=schema_string,
                                          database_table_data_example=unique_values, task=self.question,
                                          examples=examples, database_name=self.dbname,
                                          business_terms=self.business_terms,data_source_name=self.datasource_name)
        llm_prompts = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": get_user_prompt(self.question)},
        ]
        rsp = llm_chat(llm_prompts)
        logger.info(f"Basic pipeline generation completed for question: {self.question},rsp = {rsp},"
                    f"trace_id={self.trace_id}")
        sql_data = extract_first_json_block(rsp)
        return SQLGenerateData(
            sql=sql_data.get("sql", "").rstrip(';') if isinstance(sql_data.get("sql", ""), str) else sql_data.get("sql",
                                                                                                                  ""),
            reasoning=sql_data.get("reasoning", ""),
            tables=sql_data.get("tables", []),
            db_schema=schema_string
        )

    def _get_fewshot(self):
        embeddings = embedding_texts([self.question])
        if not embeddings:
            logger.warning(f"basic fewshot embedding_texts返回空数组，问题内容：'{self.question}'，trace_id={self.trace_id}")
            return ""
        question_emb = embeddings[0]
        script = {
            "source": "cosineSimilarity(params.query_vector, 'vector_question') + 1.0",
            "params": {
                "query_vector": question_emb
            }
        }
        params = Nl2sqlEsQueryParams(vector_field="vector_question", vector=question_emb, app_id=self.app_id,
                                     datasource_name=self.datasource_name, dbname=self.dbname, top_k=10, min_score=1.7,
                                     script=script, engine_type=self.engine_type,feedback=1)
        examples = self.vector_store.get_nl2sql_records_for_script(params)
        logger.info(f"basic fewshot get examples,trace_id={self.trace_id},examples:{len(examples)}")
        if len(examples) == 0:
            return ""

        example_prompts = []
        for idx, example in enumerate(examples):
            example_prompts.append(BASIC_FEWSHOT_PIPELINE_EXAMPLE_PROMPT.format(DATABASE_SCHEMA=example.db_schema,
                                                                                TASK=example.question,
                                                                                SQL=example.sql,NUMBER=idx+1))
        prompt = "\n\n".join(example_prompts)
        return prompt
