import json

from datasketch import MinHash, <PERSON><PERSON>ashLS<PERSON>
from tqdm import tqdm
from typing import Dict, List, Tuple, Hashable

from common.database.redis_client import RedisClient
from common.logger.logger import logger
from common.redis_key.nl2sql_redis_key import format_redis_key, nl2sql_sampling_data_key
from common.share.config import appConfig
from infra.mcp.nl2sql.utils.db_util import DatabaseContext

BLACK_LIST = {"_id", " id", "url", "email", "web", "time", "phone", "date", "address"}


class LSH:
    """基于MinHash LSH的文本相似性检索类

    属性:
        lsh (MinHashLSH): LSH索引实例
        signature_size (int): MinHash签名长度
        n_gram (int): 文本分片的n-gram长度
        threshold (float): LSH相似度阈值
        verbose (bool): 是否显示进度条
        minhash (Dict): 存储所有MinHash对象的字典，结构为 {key: (MinHash, 表名, 列名, 原始值)}
        unique_values (Dict): 数据库各表唯一值集合，结构为 {表名: {列名: [唯一值列表]}}
        tables (List[str]): 需要处理的表名列表
        db_name (str): 数据库标识符
        db_client (callable): 数据库查询执行函数
    """
    lsh: MinHashLSH
    signature_size: int
    n_gram: int
    threshold: float
    verbose: bool = True
    minhash: Dict[Hashable, Tuple[MinHash, str, str, str]] = {}
    unique_values: Dict[str, Dict[str, List[str]]]
    tables: List[str]
    engine_name: str
    datasource_name: str
    dbname: str
    database_context: DatabaseContext
    trace_id: str
    app_id: str
    sub_account_uin: str
    engine_type: str

    def __init__(self, signature_size: int, n_gram: int, threshold: float, database_context: DatabaseContext,
                 trace_id: str, app_id: str, sub_account_uin: str, engine_type: str, verbose=True, tables=None,
                 db_name=None,
                 engine_name=None, datasource_name=None):
        """初始化LSH索引并构建数据结构

        参数:
            signature_size: MinHash签名长度，决定哈希精度
            n_gram: 文本分片长度，影响特征提取粒度
            threshold: LSH相似度阈值(0-1之间)
            verbose: 是否显示处理进度条
            tables: 需要处理的数据库表名列表
            db_name: 数据库标识符
        """
        if db_name is None:
            db_name = []
        if tables is None:
            tables = []
        # 初始化LSH索引
        self.lsh = MinHashLSH(threshold=threshold, num_perm=signature_size)
        self.signature_size = signature_size
        self.n_gram = n_gram
        self.threshold = threshold
        self.verbose = verbose
        self.tables = tables
        self.db_name = db_name
        self.engine_name = engine_name
        self.datasource_name = datasource_name
        self.database_context = database_context
        self.trace_id = trace_id
        self.app_id = app_id
        self.sub_account_uin = sub_account_uin
        self.engine_type = engine_type

    def _get_unique_values(self) -> Dict[str, Dict[str, List[str]]]:
        """从数据库表中提取唯一值构建结构化数据

        遍历所有表，执行采样查询获取数据，进行多级校验后，
        收集各列唯一值并过滤黑名单列

        返回:
            Dict[str, Dict[str, List[str]]]: 双层字典结构，
                外层键为表名，内层键为列名，值为该列唯一值列表
        """
        unique_values: Dict[str, Dict[str, List[str]]] = {}
        for table_name in self.tables:
            logger.info(f"Starting sampling data,table: {table_name},trace_id: {self.trace_id}")
            # 执行SQL查询并添加结果校验
            try:
                sampled_data = self._fetch_sampled_data_with_cache(table_name)
                columns = sampled_data.ResultSchema
                data_set = sampled_data.ResultSet
                # 获取列信息（从结果模式中提取列名）
                logger.info(f"sampling data,table: {table_name},columns ={columns},data_set ={data_set},"
                            f"trace_id: {self.trace_id}")
                if not columns:
                    logger.warning(f"空表结构，跳过处理，表名: {table_name},trace_id={self.trace_id}")
                    continue
                # 初始化值存储：每列使用集合存储保证唯一性
                values: list[set[str]] = [set() for _ in range(len(columns))]

                # 处理结果集：逐行处理数据
                for row_data in data_set:
                    if len(row_data) != len(columns):  # 列数校验（防御性编程）
                        logger.warning(
                            f"数据列数不匹配，表名: {table_name}，期望列数: {len(columns)}，实际列数: {len(row_data)},"
                            f"trace_id={self.trace_id}")
                        continue
                    for i, value in enumerate(row_data):
                        values[i].add(str(value))  # 强制类型转换保证一致性

                # 构建列值字典，并且排除黑名单
                column_values = {
                    columns[i]: list(values[i])  # 转换集合为列表
                    for i in range(len(columns))
                    if columns[i].strip().lower() not in BLACK_LIST  # 过滤敏感列
                }
                unique_values[table_name] = column_values
                logger.info(f"final sampling data,table={table_name},unique_values={column_values},"
                            f"trace_id={self.trace_id}")
            except Exception as e:
                logger.warning(f"处理表 {table_name} 时发生异常: {str(e)}")
                continue
        logger.info(f"final sampling  all data,unique_values={unique_values},trace_id={self.trace_id}")
        return unique_values

    def _fetch_sampled_data_with_cache(self, table_name: str):
        """带缓存的采样数据获取方法"""
        # 生成唯一Redis键
        redis_key = format_redis_key(
            nl2sql_sampling_data_key,
            app_id=self.app_id,
            sub_account_uin=self.sub_account_uin,
            engine_type=self.engine_type,
            ds_name=self.datasource_name,
            db=self.db_name,
            tbl=table_name
        )
        redis_client = None
        try:
            # 尝试获取缓存
            redis_client = RedisClient.get_instance(appConfig.common.redis)
            cached_data = redis_client.get(redis_key)
            if cached_data:
                data_str = cached_data.decode('utf-8') if isinstance(cached_data, bytes) else cached_data
                data = json.loads(data_str)
                logger.info(f"命中缓存数据 table={table_name}, key={redis_key}, trace_id={self.trace_id}")
                return type('SampledData', (), data)  # 返回动态对象保持接口兼容
        except Exception as e:
            logger.warning(f"获取 Key:{redis_key} 异常: {e}, 从远程采样数据, trace_id={self.trace_id}")

        logger.info(f"未命中缓存数据，从 MCP 获取数据 table={table_name}, key={redis_key}, trace_id={self.trace_id}")
        # 无缓存或异常时查询原始数据
        sampled_data = self.database_context.sampling_data(
            self.db_name, self.datasource_name, self.engine_name, table_name
        )

        try:
            # 缓存结果
            redis_client.setex(
                name=redis_key,
                time=1800,  # 30分钟缓存
                value=json.dumps({
                    'ResultSchema': sampled_data.ResultSchema,
                    'ResultSet': sampled_data.ResultSet
                })
            )
            logger.info(f"更新缓存成功 table={table_name}, key={redis_key}, trace_id={self.trace_id}")
        except Exception as e:
            logger.warning(f"缓存更新失败，key: {redis_key},e: {e}, 数据仍有效, trace_id={self.trace_id}")

        return sampled_data

    def _create_minhash(self, keyword: str) -> MinHash:
        """根据输入字符串生成MinHash签名

        参数:
            keyword (str): 需要生成哈希签名的输入字符串

        返回:
            MinHash: 生成的MinHash对象，包含字符串的n-gram特征签名
        """
        m = MinHash(num_perm=self.signature_size)
        # 生成n-gram滑动窗口：将字符串按指定n_gram长度切分
        # 例如"hello"用3-gram会得到["hel", "ell", "llo"]
        for d in [keyword[i: i + self.n_gram] for i in range(len(keyword) - self.n_gram + 1)]:
            m.update(d.encode("utf8"))  # 将每个n-gram编码后加入MinHash
        return m

    def create_lsh(self):
        logger.info(f"Starting LSH creation,trace_id: {self.trace_id}")
        unique_values = self._get_unique_values()
        self.unique_values = unique_values
        # 计算总唯一值数量用于进度条
        total_unique_values = sum(
            len(column_values)
            for table_values in self.unique_values.values()
            for column_values in table_values.values()
        )

        logger.info(f"Total unique values: {total_unique_values},trace_id={self.trace_id}")
        # 根据verbose标志初始化进度条
        progress_bar = (
            tqdm(total=total_unique_values, desc="Creating LSH") if self.verbose else None
        )

        # 遍历所有表的唯一值生成MinHash
        for table_name, table_values in self.unique_values.items():
            for column_name, column_values in table_values.items():
                # 特殊处理doctype列（调试用）
                if column_name.lower() == "doctype":
                    print("=" * 20)
                    print("Doctype found")
                    print("=" * 20)
                logger.info(
                    f"Processing {table_name} - {column_name} - {len(column_values)}"
                )
                # 为每个唯一值生成MinHash并插入LSH
                for idx, value in enumerate(column_values):
                    minhash = self._create_minhash(value)
                    minhash_key = f"{table_name}_{column_name}_{idx}"  # 生成唯一键名
                    self.minhash[minhash_key] = (minhash, table_name, column_name, value)
                    self.lsh.insert(minhash_key, minhash)

                    if self.verbose:
                        progress_bar.update(1)  # 更新进度条

        if self.verbose:
            progress_bar.close()  # 关闭进度条
        logger.info(f"Finished LSH creation,trace_id: {self.trace_id}")

    def query_lsh(
            self,
            keyword: str,
            top_n: int,
    ) -> List[Tuple[Dict[str, Dict[str, List[str]]], float]]:
        """基于LSH的近似最近邻查询方法

        参数:
            keyword (str): 查询关键词字符串
            top_n (int): 需要返回的相似结果数量

        返回:
            List[Tuple[Dict, float]]: 包含相似值层级字典和相似度的元组列表，
                结构为 [(分层字典, 相似度分数), ...]
                分层字典结构：{表名: {列名: [相似值列表]}}
        """

        # 为查询关键词生成MinHash签名
        query_minhash = self._create_minhash(keyword)
        # 通过LSH进行近似最近邻查询，获取候选结果集
        results = self.lsh.query(query_minhash)

        similarities = []
        final_similarities = []
        # 遍历所有候选结果，计算精确相似度
        for value_id in results:
            try:
                # 从存储中获取目标MinHash（存储结构为元组：MinHash对象,表名,列名,原始值）
                target = self.minhash[value_id]
                # 处理不同存储格式的兼容性（元组或直接存储MinHash对象）
                if isinstance(target, tuple):
                    target_minhash = target[0]
                else:
                    target_minhash = target
                # 类型安全检查（确保获取到有效的MinHash对象）
                if not isinstance(target_minhash, MinHash):
                    raise ValueError(f"ValueID {value_id} is not Minhash: {target},target_minhash={target_minhash}")

                # 计算Jaccard相似度（集合交集大小/集合并集大小）
                sim = target_minhash.jaccard(query_minhash)
                similarities.append((value_id, sim))
                _, table_name, column_name, value = self.minhash[value_id]
                similar_values: Dict[str, Dict[str, List[str]]] = {
                    table_name: {column_name: [value]}
                }
                final_similarities.append(
                    (similar_values, sim)
                )
            except Exception as e:
                raise ValueError(f"Failed query ValueID {value_id}: {e},trace_id={self.trace_id}")

        # 按相似度降序排序并截取Top N结果
        final_similarities = sorted(final_similarities, key=lambda x: x[1], reverse=True)[:top_n]

        return final_similarities
