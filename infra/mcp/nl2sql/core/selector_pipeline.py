import json
from typing import Dict, List

from common.logger.logger import logger
from infra.mcp.nl2sql.core.lsh import LSH
from infra.mcp.nl2sql.llm.llm import llm_chat
from infra.mcp.nl2sql.llm.selector_prompt import SELECT_CANDIDATES_SQL_PROMPT
from infra.mcp.nl2sql.preprocessing.basic_entity_retrieval import EntityRetrieval
from infra.mcp.nl2sql.utils.db_util import Column
from infra.mcp.nl2sql.utils.gpt_rsp_util import extract_first_json_block


class SelectorPipeline:
    lsh: LSH
    ddl: Dict[str, str]
    candidates: [str]
    question: str
    keywords: List[str]
    db_schema: Dict[str, List[Column]]
    trace_id: str

    def __init__(self, lsh: LSH, candidates: [str], question: str, ddl: Dict[str, str], keywords: List[str],
                 db_schema: Dict[str, List[Column]], trace_id: str):
        self.lsh = lsh
        self.candidates = candidates
        self.question = question
        self.ddl = ddl
        self.keywords = keywords
        self.db_schema = db_schema
        self.trace_id = trace_id

    def select_final_sql(self) -> str:
        logger.info(f"Start selecting final sql,trace_id = {self.trace_id}")
        similar_entities = {}
        if self.lsh:
            entity_retrieval = EntityRetrieval(self.lsh, self.keywords, self.db_schema, self.question, self.trace_id)
            similar_entities = entity_retrieval.get_similar_entities()

        schema_string = "\n".join([f"{k}: {v}" for k, v in self.ddl.items()])

        task = (
            f'"input": "{self.question}"\n' f'"output": '
        )
        data_examples = json.dumps(similar_entities)
        prompt = SELECT_CANDIDATES_SQL_PROMPT.format(DATABASE_SCHEMA=schema_string,
                                                     DATABASE_TABLE_DATA_EXAMPLE=data_examples,
                                                     TASK=task, CANDIDATES="\n".join(self.candidates))
        llm_prompts = [
            {"role": "user", "content": prompt},
        ]
        rsp = llm_chat(llm_prompts)
        logger.info(f"Finished selecting final sql,trace_id = {self.trace_id},rsp ={rsp}")
        sql_data = extract_first_json_block(rsp)
        return sql_data["sql"]
