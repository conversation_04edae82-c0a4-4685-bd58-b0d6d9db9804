from typing import Dict, List, Any, Optional
import json
import traceback
import os
import asyncio

"""
业务术语处理模块

用于查询和处理与关键词相关的业务术语，使用同步ES查询，稳定可靠。
"""

from common.logger.logger import logger
from infra.mcp.nl2sql.llm.llm import embedding_texts
from infra.mcp.nl2sql.preprocessing.es_vector_store import Nl2SQLVectorStore, Nl2sqlEsQueryParams
from infra.memory.semantic_operator import SemanticOperator, SemanticSearchRequest, SemanticDataByScore, Scope

class BusinessTerm:
    """业务术语处理类，用于查询和处理与关键词相关的业务术语"""

    def __init__(self, app_id: str, user_id: str, trace_id: str,
                 datasource_name: str, dbname: str, engine_type: str,
                 vector_store: Nl2SQLVectorStore,
                 tables: Optional[List[str]] = None):
        self.app_id = app_id
        self.user_id = user_id
        self.trace_id = trace_id
        self.datasource_name = datasource_name  # level1
        self.dbname = dbname  # level2
        self.tables = tables or []  # level3 - 表名列表
        self.engine_type = engine_type
        self.vector_store = vector_store
        self.business_terms_section = ""

    def _get_semantics_sync(self, semantic_request: SemanticSearchRequest) -> List[SemanticDataByScore]:
        """
        同步版本的语义搜索方法
        直接调用ES同步客户端，避免异步事件循环问题

        改进版本：使用竞争式评分，主术语和同义词公平竞争，取最高分
        """
        try:
            # 获取semantic_operator实例来复用其方法和配置
            semantic_op = SemanticOperator.get_instance(self.app_id)

            # 1. 生成查询向量（复用embedding_texts方法）
            query_vector = embedding_texts([semantic_request.query])[0]

            # 2. 设置向量和文本匹配的权重
            embedding_weight = 0.7
            text_weight = 1 - embedding_weight

            # 3. 构建查询DSL（复用build_permission_dsl方法）
            permission_dsl = semantic_op.build_permission_dsl(
                semantic_request.level1,
                semantic_request.level2,
                semantic_request.level3,
                self.engine_type
            )

            # 4. 改进的查询结构：使用竞争式评分
            query = {
                "min_score": semantic_request.min_score,
                "query": {
                    "bool": {
                        "filter": [
                            {"term": {"app_id": semantic_request.app_id}},
                            permission_dsl
                        ],
                        "must": [
                            {
                                # 使用dis_max查询来取最高分，而不是累加分数
                                "dis_max": {
                                    "queries": [
                                        # 主术语候选项：文本和向量匹配相加（因为只有一个主术语）
                                        {
                                            "bool": {
                                                "should": [
                                                    {
                                                        "match": {
                                                            "term": {
                                                                "query": semantic_request.query,
                                                                "boost": text_weight
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "knn": {
                                                            "field": "term_vectors",
                                                            "query_vector": query_vector,
                                                            "k": semantic_request.top_k,
                                                            "num_candidates": min(100, semantic_request.top_k * 2),
                                                            "boost": embedding_weight
                                                        }
                                                    }
                                                ]
                                            }
                                        },
                                        # 同义词候选项：多个同义词中取最高分
                                        {
                                            "bool": {
                                                "should": [
                                                    {
                                                        "match": {
                                                            "synonyms": {
                                                                "query": semantic_request.query,
                                                                "boost": text_weight
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "nested": {
                                                            "path": "synonyms_vectors",
                                                            "query": {
                                                                "knn": {
                                                                    "field": "synonyms_vectors.embedding",
                                                                    "query_vector": query_vector,
                                                                    "k": semantic_request.top_k,
                                                                    "num_candidates": min(100,
                                                                                          semantic_request.top_k * 2),
                                                                }
                                                            },
                                                            "score_mode": "max",  # 多个同义词取最高分
                                                            "boost": embedding_weight
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    ],
                                    "tie_breaker": 0.0  # 不使用tie_breaker，确保只取最高分
                                }
                            }
                        ]
                    }
                },
                "sort": [{"_score": {"order": "desc"}}, {"create_time": {"order": "asc"}}],
                "size": semantic_request.top_k,
                "track_scores": True
            }

            # 5. 执行同步搜索（直接使用sync_client）
            logger.info(f"执行同步ES查询(竞争式评分), query={semantic_request.query},dsl={query}, trace_id={self.trace_id}")

            response = semantic_op.sync_client.search(
                index=semantic_op.config.index_name,
                body=query
            )

            # 6. 解析结果
            results = [
                SemanticDataByScore(
                    **hit["_source"],
                    score=hit.get("_score", 0.0),
                )
                for hit in response["hits"]["hits"]
            ]

            logger.info(f"ES查询完成，获得 {len(results)} 个结果, trace_id={self.trace_id}")
            return results

        except Exception as e:
            logger.error(f"同步ES查询失败: {e}, trace_id={self.trace_id}")
            return []

    def process_business_terms(self, keywords: List[str]) -> None:
        """处理业务术语，将结果存储在对象属性中

        参数:
            keywords: 关键词列表
        """
        try:
            if not keywords:
                return

            logger.info(f"处理业务术语: keywords={keywords}, trace_id={self.trace_id}")
            matched_terms = {}
            threshold = 0.8  # 相似度阈值80%

            for keyword in keywords:
                try:
                    # 统一使用同步方法处理所有业务术语查询
                    logger.info(f"使用同步方法查询业务术语, keyword={keyword}, trace_id={self.trace_id}")

                    level3_value = self.tables if self.tables else "*"

                    # 构建语义搜索请求
                    search_request = SemanticSearchRequest(
                        app_id=self.app_id,
                        query=keyword,
                        top_k=10,
                        min_score=threshold,
                        level1=self.datasource_name,
                        level2=self.dbname,
                        level3=level3_value
                    )

                    # 使用同步方法查询
                    results = self._get_semantics_sync(search_request)

                    logger.info(f"get semantic result trace_id={self.trace_id},keyword {keyword},results={results}")

                    # 过滤相似度低于80%的结果
                    valid_results = [r for r in results if r.score >= threshold]

                    logger.info(f"filter semantic result trace_id={self.trace_id}, keyword {keyword},"
                                f"valid_results={valid_results}")
                    if not valid_results:
                        continue

                    # 按权限级别分组并选择最佳匹配
                    best_match = self._select_best_match_by_permission(valid_results)

                    logger.info(f"best_match semantic result trace_id={self.trace_id}, keyword {keyword},"
                                f"best_match={best_match}")
                    if best_match:
                        matched_terms[keyword] = best_match

                except Exception as e:
                    logger.error(f"查询语义数据失败: {e}, trace_id={self.trace_id}")
                    continue

            logger.info(f"matched terms semantic result,trace_id={self.trace_id},matched_terms={matched_terms}")
            # 处理最终结果
            if matched_terms:
                self.business_terms_section = self._format_business_terms(matched_terms)
                logger.info(f"找到匹配业务术语: trace_id={self.trace_id}, {self.business_terms_section}")
            else:
                logger.info(f"未找到匹配的业务术语: trace_id={self.trace_id}")

        except Exception as e:
            logger.error(f"业务术语处理失败: {e}, {traceback.format_exc()}, trace_id={self.trace_id}")

    def _select_best_match_by_permission(self, results: List) -> Dict[str, Any]:
        """按权限级别优先级选择最佳匹配并格式化结果

        优先级: 数据表级别 > 数据库级别 > 数据源级别 > 全局级别
        在同一权限级别内，选择score最高的
        """
        if not results:
            return None

        best_result = None
        best_scope = None
        best_priority = 5  # 比最低优先级还低

        for result in results:
            if not result.scope:
                continue

            # 找到最具体的权限级别和对应的scope（一次性计算）
            most_specific_level = 4  # 默认全局级别
            result_best_scope = None

            for scope in result.scope:
                level = self._get_permission_level(scope)
                if level < most_specific_level:
                    most_specific_level = level
                    result_best_scope = scope

            # 比较优先级，如果优先级相同则比较分数
            if (most_specific_level < best_priority or
                    (most_specific_level == best_priority and result.score > (
                            best_result.score if best_result else 0))):
                best_result = result
                best_scope = result_best_scope
                best_priority = most_specific_level

        if not best_result:
            return None

        # 直接格式化结果，避免额外的方法调用
        term_info = {}
        if best_result.definition:
            term_info["definition"] = best_result.definition
        if best_result.synonyms:
            term_info["synonyms"] = best_result.synonyms
        if best_scope:
            scope_info = {}
            if best_scope.level1:
                scope_info["level1"] = best_scope.level1
            if best_scope.level2:
                scope_info["level2"] = best_scope.level2
            if best_scope.level3:
                scope_info["level3"] = best_scope.level3
            if scope_info:
                term_info["scope"] = scope_info

        return {
            'term': best_result.term,
            'info': term_info,
            'similarity': float(best_result.score)
        }

    def _get_permission_level(self, scope) -> int:
        """获取权限级别

        返回值: 1=数据表级别, 2=数据库级别, 3=数据源级别, 4=全局级别
        """
        if scope.level1 != "*" and scope.level2 != "*" and scope.level3 != "*":
            return 1  # 数据表级别
        elif scope.level1 != "*" and scope.level2 != "*" and scope.level3 == "*":
            return 2  # 数据库级别
        elif scope.level1 != "*" and scope.level2 == "*" and scope.level3 == "*":
            return 3  # 数据源级别
        else:
            return 4  # 全局级别

    def get_business_terms_section(self) -> str:
        """获取格式化的业务术语部分，用于替换prompt中的{BUSINESS_TERMS}占位符

        返回:
            str: 格式化的业务术语部分，如果没有匹配则返回空字符串
        """
        logger.info(f"Final business terms section is {self.business_terms_section}, trace_id={self.trace_id}")
        if not self.business_terms_section:
            return ""

        return f"""
#### Business Terms
The following are keywords corresponding to business terms and their explanations. You should use the business term names and their definitions to understand the domain-specific meaning when generating SQL queries:

{self.business_terms_section}

When generating SQL, prioritize using the business term definitions to ensure accurate interpretation of user requirements and improve query relevance.
"""

    def _format_business_terms(self, matched_terms: Dict[str, Any]) -> str:
        """格式化处理业务术语

        参数:
            matched_terms: 匹配的业务术语字典

        返回:
            str: 格式化的业务术语字符串
        """
        result = ""
        for keyword, match in matched_terms.items():
            term_name = match['term']
            term_info = match.get('info', {})
            definition = term_info.get('definition', '无定义')

            # 格式：Keyword: [关键词] - Business Term: [业务术语] - Definition: [定义]
            result += f"- Keyword: {keyword}; Business Term: {term_name}; Definition: {definition}\n"

        return result
