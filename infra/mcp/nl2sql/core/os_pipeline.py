from typing import List, Dict

from common.logger.logger import logger

from infra.mcp.nl2sql.core.lsh import LSH
from infra.mcp.nl2sql.entities.generate_sql_entity import SQLGenerateData
from infra.mcp.nl2sql.llm.generate_prompt import get_user_prompt, get_system_prompt
from infra.mcp.nl2sql.llm.llm import llm_chat
from infra.mcp.nl2sql.llm.synthetic_question_prompt import SYNTHETIC_QUESTION_SQL_PARIS_WITH_SQL_PROMPT, \
    SYNTHETIC_QUESTION_SQL_PARIS_WITH_SCHEMA_PROMPT
from infra.mcp.nl2sql.preprocessing.basic_entity_retrieval import EntityRetrieval
from infra.mcp.nl2sql.preprocessing.es_vector_store import Nl2SQLVectorStore
from infra.mcp.nl2sql.utils.db_util import Column
from infra.mcp.nl2sql.utils.gpt_rsp_util import extract_first_json_block


class OS:
    lsh: LSH
    question: str
    keywords: List[str]
    db_schema: Dict[str, List[Column]]
    ddl: Dict[str, str]
    trace_id: str
    vector_store: Nl2SQLVectorStore
    app_id: str
    datasource_name: str
    dbname: str
    engine_type: str
    business_terms: str

    def __init__(self, lsh: LSH, question: str, keywords: List[str], db_schema: Dict[str, List[Column]],
                 ddl: Dict[str, str], trace_id: str, vector_store: Nl2SQLVectorStore, app_id: str,
                 datasource_name: str, dbname: str, engine_type: str, business_terms: str = ""):
        self.lsh = lsh
        self.question = question
        self.keywords = keywords
        self.db_schema = db_schema
        self.ddl = ddl
        self.trace_id = trace_id
        self.vector_store = vector_store
        self.app_id = app_id
        self.datasource_name = datasource_name
        self.dbname = dbname
        self.engine_type = engine_type
        self.business_terms = business_terms

    def run(self) -> SQLGenerateData:
        logger.info(f"Start os pipeline,trace_id ={self.trace_id}")
        similar_entities = {}
        if self.lsh:
            entity_retrieval = EntityRetrieval(self.lsh, self.keywords, self.db_schema, self.question, self.trace_id)
            similar_entities = entity_retrieval.get_similar_entities()
            logger.info(f"os entity retrieval result ={similar_entities},trace_id={self.trace_id}")

        schema_string = "\n".join([f"{k}: {v}" for k, v in self.ddl.items()])
        examples = _get_fewshot(schema_string)
        sql_generate_data = self._candidate_generation(schema_string, similar_entities, examples)
        logger.info(f"Finished os pipeline,trace_id ={self.trace_id}")
        return sql_generate_data

    def _candidate_generation(self, schema_string: str, unique_values: Dict, examples: str) -> SQLGenerateData:
        system_prompt = get_system_prompt(engine_type=self.engine_type, database_schema=schema_string,
                                          database_table_data_example=unique_values, task=self.question,
                                          examples=examples, database_name=self.dbname,
                                          business_terms=self.business_terms, data_source_name=self.datasource_name)
        llm_prompts = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": get_user_prompt(self.question)},
        ]
        rsp = llm_chat(llm_prompts)
        logger.info(f"OS pipeline generation completed for question: {self.question},rsp = {rsp},"
                    f"trace_id={self.trace_id}")
        sql_data = extract_first_json_block(rsp)
        return SQLGenerateData(
            sql=sql_data.get("sql", "").rstrip(';') if isinstance(sql_data.get("sql", ""), str) else sql_data.get("sql",
                                                                                                                  ""),
            reasoning=sql_data.get("reasoning", ""),
            tables=sql_data.get("tables", []),
            db_schema=schema_string
        )


def _get_fewshot(schema_string) -> str:
    sql_examples = _online_synthetic_with_sql_feature(schema_string)
    schema_sql_examples = _online_synthetic_with_sql_schema(schema_string)
    all_examples = schema_sql_examples + sql_examples
    if len(all_examples) == 0:
        return ""
    examples = []
    for example in all_examples:
        if isinstance(example, dict):
            examples.append("\n\n- {}\n{}".format(example.get("question"), example.get("sql")))
    return "\n\n".join(examples)


def _online_synthetic_with_sql_feature(schema_string: str) -> []:
    prompt = SYNTHETIC_QUESTION_SQL_PARIS_WITH_SQL_PROMPT.format(DATABASE_SCHEMA=schema_string, k=3
                                                                 )
    llm_prompts = [
        {"role": "user", "content": prompt},
    ]
    response = llm_chat(prompt=llm_prompts)
    examples = extract_first_json_block(response)
    if isinstance(examples, dict):
        return examples.get("answer")
    return []


def _online_synthetic_with_sql_schema(schema_string: str) -> []:
    prompt = SYNTHETIC_QUESTION_SQL_PARIS_WITH_SCHEMA_PROMPT.format(DATABASE_SCHEMA=schema_string, k=3
                                                                    )

    llm_prompts = [
        {"role": "user", "content": prompt},
    ]

    response = llm_chat(prompt=llm_prompts)
    examples = extract_first_json_block(response)
    if isinstance(examples, dict):
        return examples.get("answer")
    return []
