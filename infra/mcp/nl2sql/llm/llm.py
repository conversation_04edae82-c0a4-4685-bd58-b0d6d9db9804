from typing import List

from common.logger.logger import logger
from openai import OpenAI

from common.share import config

def llm_chat(prompt: List):
    nl2sqlconfig = config.appConfig.automic.nl2sql.llm
    logger.info(f"llm_chat model: {nl2sqlconfig.model_name}")
    try:
        client = OpenAI(base_url=nl2sqlconfig.base_url, api_key=nl2sqlconfig.api_key)
        response = client.chat.completions.create(
            model=nl2sqlconfig.model_name,
            messages=prompt,
            temperature=nl2sqlconfig.temperature,
            # response_format={"type": "json_object"}
        )
        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"When call llm chat model has error, model:{nl2sqlconfig.model_name}, error: {e} ")
        raise e


def embedding_texts(texts: list[str]):
    nl2sqlconfig = config.appConfig.automic.nl2sql.embedding
    logger.info(f"embedding_texts model: {nl2sqlconfig.model_name}")
    try:
        client = OpenAI(base_url=nl2sqlconfig.base_url, api_key=nl2sqlconfig.api_key)
        embeddings_rsp = client.embeddings.create(model=nl2sqlconfig.model_name, input=texts).data
        rsp = []
        for embedding in embeddings_rsp:
            rsp.append(embedding.embedding)
        return rsp
    except Exception as e:
        logger.warning(f"When call embeddings model has error,model:{nl2sqlconfig.model_name},text:{texts}, error: {e} ")
        return []


if __name__ == '__main__':
    s1 = ["WITH orders_2024 AS (SELECT COUNT(*) AS count_2024 FROM orders WHERE EXTRACT(YEAR FROM order_date) = '2024'), orders_2025 AS (SELECT COUNT(*) AS count_2025 FROM orders WHERE EXTRACT(YEAR FROM order_date) = '2025') SELECT (orders_2025.count_2025 - orders_2024.count_2024) AS growth FROM orders_2024, orders_2025"]
    s2 = ["SELECT SUM(o.quantity) AS total_quantity FROM `DataLakeCatalog`.`nl2sql_test`.orders o JOIN `DataLakeCatalog`.`nl2sql_test`.products p ON o.product_id = p.product_id WHERE p.category = '箱包配饰'"]
    s = embedding_texts(s2)
    print(s)
