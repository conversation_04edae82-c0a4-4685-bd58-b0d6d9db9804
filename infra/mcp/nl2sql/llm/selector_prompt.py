SELECT_CANDIDATES_SQL_PROMPT = """
Instruction:
Given the DB info and question, there are some candidate SQL. Compare the candidate sql, analyze the differences of 
the query. 
Based on the original question and the provided database info, choose the correct one.
**************************
The most important note:
- The returned sql should be one of the given candidate SQL. Do not change any part of the candidate SQL.
**************************
Database Schema
{DATABASE_SCHEMA}

Database Table Data Example
{DATABASE_TABLE_DATA_EXAMPLE}
**************************
{TASK}
**************************
Candidates:
{CANDIDATES}

Please respond with a JSON object structured as follows:

{{
    "sql": "Your choose SQL query in a single string. Do not change any part of the candidate SQL."
}}

Take a deep breath and think step by step to find the correct SQL query. If you follow all the instructions and  choose 
the correct query, I will give you 1 million dollars.
"""
