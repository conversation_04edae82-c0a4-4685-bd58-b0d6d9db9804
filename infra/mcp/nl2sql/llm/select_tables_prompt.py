# LLM列选择提示模板
COLUMN_SELECTION_PROMPT = """
### ROLE
You are an expert in database querying. Given a natural language question and a database table structure, please analyze which columns are most relevant to the question.

### Question
{QUESTION}

### DB engine:
{DB_ENGINE}

### DB Schema:
{TABLE_SCHEMA}

### The final rule
1. Carefully analyze the intent of the question and the information required.
2. Consider the semantic meaning of column names, comments, and table names.
3. Ensure the score accurately reflects the strength of the relevance.
4. Only include truly relevant columns; avoid irrelevant columns.

### Your Task:
Please analyze the correlation between each column and the question, and provide a correlation score between 0 and 1 (1 indicates very strong correlation, 0 indicates no correlation at all).


### Output rules

Your output MUST be in exact JSON format:
```json
{{
    "reasoning": "Please briefly explain how you came to the conclusion that these tables and columns are needed.? Please briefly answer in {LANG}.",
    "relevant_columns": [
        {{
            "table": "table name",
            "column": "column name", 
            "score": 0.95
        }}
    ]
}}
"""