import json
from typing import Dict

from common.share import error
from common.share.utils import detect_language
from infra.mcp.nl2sql.entities.engine_type_enum import EngineTypeEnum
from infra.mcp.nl2sql.llm.generate_click_house_sql_prompt import CLICKHOUSE_SYSTEM_PROMPT
from infra.mcp.nl2sql.llm.generate_doris_sql_prompt import DORIS_SYSTEM_PROMPT
from common.logger.logger import logger
from infra.mcp.nl2sql.llm.generate_es_sql_prompt import GENERATE_ES_SQL_SYSTEM_PROMPT
from infra.mcp.nl2sql.llm.generate_spark_sql_prompt import GENERATE_SPARK_SQL_SYSTEM_PROMPT

GENERATE_SQL_USER_PROMPT = """
### Your task:
{TASK}

Now take a deep breath and think step by step to find the correct SQL query. If you follow all the instructions and generate the correct query, I will give you 1 million dollars.
"""


def get_system_prompt(engine_type: str, database_schema: str, database_table_data_example: Dict,
                      task: str, examples: str,
                      database_name: str, business_terms: str, data_source_name="") -> str:
    data_examples = json.dumps(database_table_data_example)
    lang = detect_language(task)
    if engine_type.lower() == EngineTypeEnum.DLC.value.lower():
        system_prompt = GENERATE_SPARK_SQL_SYSTEM_PROMPT.format(DATABASE_SCHEMA=database_schema,
                                                                DATABASE_TABLE_DATA_EXAMPLE=data_examples,
                                                                TASK=task, EXAMPLES=examples,
                                                                QUESTION_TYPE=lang,
                                                                DATABASE_NAME=database_name,
                                                                BUSINESS_TERMS=business_terms)
    elif engine_type.lower() == EngineTypeEnum.ES.value.lower():
        system_prompt = GENERATE_ES_SQL_SYSTEM_PROMPT.format(DATABASE_SCHEMA=database_schema,
                                                             DATABASE_TABLE_DATA_EXAMPLE=data_examples,
                                                             TASK=task, EXAMPLES=examples,
                                                             QUESTION_TYPE=lang,
                                                             DATABASE_NAME=database_name,
                                                             BUSINESS_TERMS=business_terms)
    elif engine_type.lower() == EngineTypeEnum.TC_HOUSE_D.value.lower():
        system_prompt = DORIS_SYSTEM_PROMPT.format(QUESTION_TYPE=lang,
                                                   DATABASE_NAME=database_name,
                                                   DATABASE_SCHEMA=database_schema,
                                                   DATABASE_TABLE_DATA_EXAMPLE=data_examples,
                                                   EXAMPLES=examples,
                                                   BUSINESS_TERMS=business_terms, CATALOG_NAME=data_source_name)
    elif engine_type.lower() == EngineTypeEnum.TC_HOUSE_C.value.lower():
        system_prompt = CLICKHOUSE_SYSTEM_PROMPT.format(QUESTION_TYPE=lang,
                                                        DATABASE_NAME=database_name,
                                                        TABLE_SCHEMA=database_schema,
                                                        DATABASE_TABLE_DATA_EXAMPLE=data_examples,
                                                        EXAMPLES=examples,
                                                        BUSINESS_TERMS=business_terms)
    else:
        logger.error(f"{engine_type} is not supported")
        raise ValueError(error.ErrorCode.ParamError.value)

    return system_prompt


def get_user_prompt(question: str) -> str:
    return GENERATE_SQL_USER_PROMPT.format(TASK=question)
