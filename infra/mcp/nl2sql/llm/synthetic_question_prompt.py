SYNTHETIC_QUESTION_SQL_PARIS_WITH_SQL_PROMPT = """
You are a SQL expert. Your job is to create {k} examples, where each example consists of a question and a standard SQL query to fetch the data for it. 
I want each example to look like this, question input and SQL output pairs:

"question": "What's the description of the series code SM.POP.TOTL for Aruba? (Hints: Aruba is the name of the country where ShortName = 'Aruba')"
"sql": "SELECT T2.Description FROM Country AS T1 INNER JOIN CountryNotes AS T2 ON T1.CountryCode = T2.Countrycode WHERE T1.ShortName = 'Aruba' AND T2.Seriescode = 'SM.POP.TOTL'"

You should generate examples that examine and showcase different aspects and relationships of the following table schemas, described in "Table creation statements". 
Understand the database tables and their relationships. Understand the columns and their types and meanings to construct intresting examples.
Generate a mixture of SQL examples that include:
* some simple SQL query examples without JOIN
* some SQL query examples with aggregates, like COUNT
* some simple SQL query examples with JOIN
* some complex SQL query examples with nested JOIN
**************************
Table creation statements
{DATABASE_SCHEMA}
**************************
### The most important thing is to remember:
- Generate total of {k} examples. Only outputs the examples (question input and SQL output pairs)
- Please respond with a JSON array structured as follows:

{{
    "answer":[
            {{
                "question": "What's the description of the series code SM.POP.TOTL for Aruba? Please output in Chinese.",
                "sql": "SELECT T2.Description FROM Country AS T1 INNER JOIN CountryNotes AS T2 ON T1.CountryCode = T2.Countrycode WHERE T1.ShortName = 'Aruba' AND T2.Seriescode = 'SM.POP.TOTL'"
            }},
            {{
                "question": "Show me the total population of countries in Asia. Please output in Chinese.",
                "sql": "SELECT SUM(Population) AS TotalPopulation FROM Country WHERE Continent = 'Asia'"
            }}
    ]
}}
"""

SYNTHETIC_QUESTION_SQL_PARIS_WITH_SCHEMA_PROMPT = """
You are a SQL expert. Your job is to create a set of examples, where each example consists of a question and a standard SQL query to fetch the data for it.
You should generate examples that examine and showcase different aspects and relationships of the following table schemas. 
Understand the database tables and their relationships. Understand the columns and their types and meanings to construct intresting examples.
I will also show you multiple examples generated for the other database and its table schemas, so you can see what kind of examples can be generated for a given database.

**************************
###Examples from other database### 
The following is the table schemas and column examples for other database:
The database structure is defined by the following table schemas. 
CREATE TABLE Country
(
        CountryCode TEXT ,
        ShortName TEXT, -- examples: `Aruba`
        TableName TEXT, --
        LongName TEXT, --
        Alpha2Code TEXT, --
        CurrencyUnit TEXT, --
        SpecialNotes TEXT, --
        Region TEXT, --
        IncomeGroup TEXT, --
        Wb2Code TEXT, --
        NationalAccountsBaseYear TEXT, --
        NationalAccountsReferenceYear TEXT, --
        SnaPriceValuation TEXT, --
        LendingCategory TEXT, -- examples: `IDA`
        OtherGroups TEXT, -- examples: `HIPC`
        SystemOfNationalAccounts TEXT, --
        AlternativeConversionFactor TEXT, --
        PppSurveyYear TEXT, --
        BalanceOfPaymentsManualInUse TEXT, --
        ExternalDebtReportingStatus TEXT, --
        SystemOfTrade TEXT, --
        GovernmentAccountingConcept TEXT, --
        ImfDataDisseminationStandard TEXT, --
        LatestPopulationCensus TEXT, --
        LatestHouseholdSurvey TEXT, --
        SourceOfMostRecentIncomeAndExpenditureData TEXT, --
        VitalRegistrationComplete TEXT, --
        LatestAgriculturalCensus TEXT, --
        LatestIndustrialData INTEGER, --
        LatestTradeData INTEGER, --
        LatestWaterWithdrawalData INTEGER, --
);

CREATE TABLE Series
(
        SeriesCode str,
        Topic str, --
        IndicatorName str, --
        ShortDefinition str, --
        LongDefinition str, --
        UnitOfMeasure str, --
        Periodicity str, --
        BasePeriod str, --
        OtherNotes int, --
        AggregationMethod str, --
        LimitationsAndExceptions str, --
        NotesFromOriginalSource str, --
        GeneralComments str, --
        Source str, --
        StatisticalConceptAndMethodology str, --
        DevelopmentRelevance str, --
        RelatedSourceLinks str, --
        OtherWebLinks int, --
        RelatedIndicators int, --
        LicenseType str, --
);

CREATE TABLE CountryNotes
(
        Countrycode str , --
        Seriescode str , --
        Description str, --
);

CREATE TABLE Footnotes
(
        Countrycode str , --
        Seriescode str , -- examples: `AG.LND.FRST.K2`
        Year str, -- examples: `YR1990`
        Description str, --
);

CREATE TABLE Indicators
(
        CountryName str, --
        CountryCode str, --
        IndicatorName str, --
        IndicatorCode str , --
        Year int , --
        Value int, --
);

CREATE TABLE SeriesNotes
(
        Seriescode str , --
        Year str , --
        Description str, --
);

The following are the examples generated for the above database schemas:
"question": "Among the countries in the group of Heavily Indebted Poor Countries, how many of them are under the lending category of the International Development Associations? (Hints: group of Heavily Indebted Poor Countries is OtherGroups = 'HIPC'; International Development Associations refers to lendingcategory = 'IDA')"
"sql": "SELECT COUNT(CountryCode) FROM Country WHERE LendingCategory = 'IDA' AND OtherGroups = 'HIPC'"

"question": "What is the description of the footnote on the series code AG.LND.FRST.K2 in 1990 for Aruba? (Hints: Year = 1990; Aruba is the name of country where ShortName = 'Aruba')"
"sql": "SELECT T2.Description FROM Country AS T1 INNER JOIN FootNotes AS T2 ON T1.CountryCode = T2.Countrycode WHERE T1.ShortName = 'Aruba' AND T2.Seriescode = 'AG.LND.FRST.K2' AND T2.Year = 'YR1990'"

Now similarly, generate examples (question input and SQL output pairs) for the table schemas defined below, in "Table creation statements".
**************************
###Table creation statements###
{DATABASE_SCHEMA}
**************************
### The most important thing is to remember:
- Generate total of {k} examples. Only outputs the examples (question input and SQL output pairs)
- Please respond with a JSON array structured as follows:

{{
    "answer":[
            {{
                "question": "What's the description of the series code SM.POP.TOTL for Aruba? Please output in Chinese.",
                "sql": "SELECT T2.Description FROM Country AS T1 INNER JOIN CountryNotes AS T2 ON T1.CountryCode = T2.Countrycode WHERE T1.ShortName = 'Aruba' AND T2.Seriescode = 'SM.POP.TOTL'"
            }},
            {{
                "question": "Show me the total population of countries in Asia. Please output in Chinese.",
                "sql": "SELECT SUM(Population) AS TotalPopulation FROM Country WHERE Continent = 'Asia'"
            }}
    ]
}}


"""