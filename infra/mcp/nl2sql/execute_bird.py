import json

from infra.mcp.nl2sql.generate import generate_sql

if __name__ == '__main__':
    with open("/Users/<USER>/Desktop/execute_dlc/dev_dlc.json", "r", encoding="utf-8") as f:
        data = json.load(f)

    for case in data:
        if case["question_id"] in [253,652,662,708,851,1012,1062]:
            p = {
                # "app_id": "1",
                # "sub_account_uin": "collinsdeng",
                # "trace_id": "collinsdeng",
                # # "engine": "data-agent-exp-dev",
                # # "datasource_name": "DataLakeCatalog",
                # "dbname": "default-service",
                # "tables": ["default-service"],
                # "is_sampling": True,
                # "mcp_url": {"es_sql": "http://localhost:8000/sse"},
                # "engine_type": "es_sql",
                # "question": "昨天每个分钟有多少条 info 类型的日志呢？",
                # "record_id": "5"
                "app_id": "execute_bird_fix",
                "sub_account_uin": "execute_bird",
                "trace_id": case.get("question_id"),
                "engine": "data-agent-exp-dev",
                "datasource_name": "DataLakeCatalog",
                "dbname": case.get("db_id"),
                "tables": case.get("tables"),
                "is_sampling": True,
                "mcp_url": {"dlc": "http://127.0.0.1:31234/sse?auth_token=SECRET_KEY_2025"},
                "engine_type": "dlc",
                "question": case.get('question', ''),
                "record_id": case.get("question_id")
            }
            try:
                rsp = generate_sql(p)
                case["pred_sql"] = rsp["sql"]
                case["pred_tables"] = rsp["tables"]
                with open("/Users/<USER>/Desktop/execute_dlc/execute_bird_output_fix.json", 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)

                existing_data.append(case)
                with open("/Users/<USER>/Desktop/execute_dlc/execute_bird_output_fix.json", 'w', encoding='utf-8') as f:
                    json.dump(existing_data, f, indent=4, ensure_ascii=False)
            except Exception as e:
                print(f"generate sql failed,question_id:{case.get("question_id")},e:{e}")
