from abc import ABC, abstractmethod
from typing import Dict, List, Protocol, Any, Optional

import sqlglot
import sqlglot.expressions as exp
from mcp import McpError
from pydantic import BaseModel
from common.logger.logger import logger
from infra.mcp.nl2sql.entities.engine_type_enum import EngineTypeEnum

from infra.mcp.nl2sql.mcp_client.dlc import dlc_list_tables, dlc_execute_query, DLCListTablesReq, DLCExecuteQueryReq, \
    dlc_list_table_names, DLCListTableNamesReq
from infra.mcp.nl2sql.mcp_client.es import es_get_index_schema, es_sampling_data, ESSamplingDataReq
from infra.mcp.nl2sql.mcp_client.tc_house_c import tc_house_c_get_tables_schema, GetTCHouseCGetTablesSchemaReq, \
    tc_house_c_sample_data, TCHouseCSampleDataReq, TCHouseCListTablesReq, tc_house_c_list_tables, \
    tc_house_c_list_table_names, TCHouseCListTableNames, tc_house_c_execute_query
from infra.mcp.nl2sql.mcp_client.tc_house_d import tc_house_d_get_tables_schema, tc_house_d_sample_data, \
    tc_house_d_list_tables, GetTCHouseDGetTablesSchemaReq, TCHouseDSampleDataReq, TCHouseDListTablesReq, \
    tc_house_d_list_table_names, TCHouseDListTableNamesReq, tc_house_d_execute_query


class SamplingDataRsp(BaseModel):
    ResultSchema: list[str]  # 字段
    ResultSet: list[list[Any]]  # 数据


class Column(BaseModel):
    Name: str
    Type: Optional[str]
    Comment: Optional[str]


class DatabaseStrategy(Protocol):
    """数据库操作策略接口"""

    @abstractmethod
    def get_tables_ddl(self, table_names: list[str], database_name: str,
                       datasource_name: str, url: str) -> Dict[str, str]:
        """获取表DDL定义"""
        ...

    @abstractmethod
    def sampling_data(self, database_name: str, datasource_name: str,
                      engine_name: str, table: str, url: str) -> SamplingDataRsp:
        """数据采样"""
        ...

    @abstractmethod
    def get_tables_schema(self, params: dict[str, Any] | dict[str, str], url: str) -> Dict[str, List[Column]]:
        ...

    @abstractmethod
    def list_all_db_tables(self, params: dict[str, str], url: str) -> List[str]:
        ...

    @abstractmethod
    def execute_query(self, param: Dict[str, str], url: str) -> Dict[str, Any]:
        ...


# endregion

# region 具体策略实现
def _generate_ddl(table_name: str, columns: List[Column], dialect) -> str:
    """生成DDL语句（内部方法）"""
    table = exp.Table(this=exp.to_identifier(table_name, quoted=True))

    # 2. 构造列表达式
    column_defs = []
    for col in columns:
        # 解析字段类型（自动处理类型参数，如VARCHAR(100)）
        dtype = exp.DataType.build(col.Type)
        constraints = []
        if col.Comment:
            constraints.append(
                exp.CommentColumnConstraint(this=exp.Literal.string(col.Comment))
            )
        # 构造列定义，添加注释约束
        col_def = exp.ColumnDef(
            this=exp.to_identifier(col.Name, quoted=True),
            kind=dtype,
            constraints=constraints
        )
        column_defs.append(col_def)
    schema = exp.Schema(this=table, expressions=column_defs)
    create = exp.Create(
        kind="TABLE",  # 类型为创建表
        this=schema
    )

    # 3. 组合表结构（Schema）和属性
    sql = create.sql(dialect=dialect, identify=False, pretty=True)
    return sql


def _handler_exception(eg: ExceptionGroup) -> str:
    # 优先提取McpError的详细错误信息
    mcp_error_msg = ""
    # 递归提取所有嵌套的McpError错误
    for exc in eg.exceptions:
        if isinstance(exc, McpError):  # 直接捕获的McpError
            mcp_error_msg = str(exc)
            break
        elif isinstance(exc, BaseExceptionGroup):  # 处理嵌套异常组
            for sub_exc in exc.exceptions:
                if isinstance(sub_exc, McpError):
                    mcp_error_msg = str(sub_exc)
                    break

    # 如果没有McpError，使用异常组首行摘要
    if not mcp_error_msg:
        # 提取异常组中的第一条异常摘要
        first_error = str(eg.exceptions[0]).split("\n")[0]
        mcp_error_msg = f"MultiError: {first_error} [and {len(eg.exceptions) - 1} more]"
    return mcp_error_msg


class DLCStrategy(DatabaseStrategy):
    """DLC数据库策略实现"""

    def get_tables_ddl(self, table_names: list[str], database_name: str,
                       datasource_name: str, url: str) -> Dict[str, str]:
        all_ddl = {}
        for i in range(0, len(table_names), 20):
            batch = table_names[i:i + 20] or table_names[i:]
            batch_args = DLCListTablesReq(
                DatabaseName=database_name,
                DatasourceConnectionName=datasource_name,
                TableNames=batch
            )
            batch_tables = dlc_list_tables(url=url, arguments=batch_args, tool_name="DLCListTables")
            if not batch_tables:
                continue

            for table in batch_tables:
                table_name = table.TableBaseInfo.TableName
                if table_name in table_names:
                    all_ddl[table_name] = _generate_ddl(table_name, table.Columns, dialect="spark")
        return all_ddl

    def sampling_data(self, database_name: str, datasource_name: str, engine_name: str, table: str,
                      url: str) -> SamplingDataRsp:
        arguments = DLCExecuteQueryReq(
            SparkSQL=f"select * from {table} limit 100",
            DatabaseName=database_name,
            DatasourceConnectionName=datasource_name,
            DataEngineName=engine_name
        )
        dlc_sample_rsp = dlc_execute_query(url=url, arguments=arguments)
        if len(dlc_sample_rsp.ResultSchema) == 0:
            return SamplingDataRsp()
        columns = [item.Name for item in dlc_sample_rsp.ResultSchema]
        return SamplingDataRsp(ResultSchema=columns, ResultSet=dlc_sample_rsp.ResultSet)

    def get_tables_schema(self, params: Dict[str, str], url: str) -> Dict[str, List[Column]]:
        rsp = {}
        for table, ddl in params.items():
            try:
                parse = sqlglot.parse_one(ddl, read="spark")
            except Exception as e:
                logger.error(f"parse create ddl fail,e:{e},ddl:{ddl}")
                continue
            columns = []
            for column in parse.find(exp.Schema).find_all(exp.ColumnDef):
                comment = ""
                for constraint in column.args.get("constraints", []):
                    if isinstance(constraint, exp.ColumnConstraint) and \
                            isinstance(constraint.args.get("kind"), exp.CommentColumnConstraint):
                        comment = constraint.args.get("kind").name

                col_info = Column(Name=column.find(exp.Identifier).name, Type=column.find(exp.DataType).this.name,
                                  Comment=comment)
                columns.append(col_info)
            rsp[table] = columns
        return rsp

    def list_all_db_tables(self, params: dict[str, str], url: str) -> List[str]:
        table_names = []
        offset = 0
        limit = 100

        while True:
            # 创建分页请求参数
            arguments = DLCListTableNamesReq(
                DatabaseName=params.get('Database'),
                DatasourceConnectionName=params['Catalog'],
                Limit=limit,
                Offset=offset
            )

            # 调用API获取表名
            mcp_rsp = dlc_list_table_names(url=url, arguments=arguments, tool_name="DLCListTableNames")

            # 如果没有返回表名，退出循环
            if not mcp_rsp.TableNames:
                break

            # 将结果添加到列表中
            table_names.extend(mcp_rsp.TableNames)

            # 如果返回结果少于请求数量，说明已获取所有数据
            if len(mcp_rsp.TableNames) < limit:
                break

            # 增加偏移量以获取下一页
            offset += limit

        return table_names

    def execute_query(self, param: Dict[str, str], url: str) -> Dict[str, Any]:
        arguments = DLCExecuteQueryReq(
            SparkSQL=param.get('SQL'),
            DatabaseName=param.get('Database'),
            DatasourceConnectionName=param.get('Catalog'),
            DataEngineName=param.get("Engine")
        )
        try:
            dlc_execute_query(url=url, arguments=arguments,tool_name="DLCExecuteQuery")
            return {
                "is_success": 1
            }
        except ExceptionGroup as e:
            error_msg = _handler_exception(e)
            return {
                "is_success": 2,
                "error_msg": error_msg
            }


class TCHouse_D_Strategy(DatabaseStrategy):
    """TCHouse-D 数据库策略（示例）"""

    def get_tables_ddl(self, table_names: list[str], database_name: str,
                       datasource_name: str, url: str) -> Dict[str, str]:
        all_ddl = {}
        for i in range(0, len(table_names), 20):
            batch = table_names[i:i + 20] or table_names[i:]
            batch_args = GetTCHouseDGetTablesSchemaReq(
                Catalog=datasource_name,
                Database=database_name,
                Tables=batch
            )
            batch_tables = tc_house_d_get_tables_schema(url=url, arguments=batch_args,
                                                        tool_name="TCHouseDGetTableSchema")
            if not batch_tables:
                continue

            for table in batch_tables.Tables:
                table_name = table.Table
                if table_name in table_names:
                    all_ddl[table_name] = table.CreateSql
        return all_ddl

    def sampling_data(self, database_name: str, datasource_name: str, engine_name: str, table: str,
                      url: str) -> SamplingDataRsp:
        arguments = TCHouseDSampleDataReq(
            Catalog=datasource_name,
            Database=database_name,
            Table=table,
            Limit=1000
        )

        tc_house_d_rsp = tc_house_d_sample_data(url=url, arguments=arguments, tool_name="TCHouseDGetTableSample")
        return SamplingDataRsp(ResultSchema=tc_house_d_rsp.ResultSchema, ResultSet=tc_house_d_rsp.ResultSet)

    def get_tables_schema(self, params: Dict[str, Any], url: str) -> Dict[str, List[Column]]:
        arguments = TCHouseDListTablesReq(Catalog=params['Catalog'],
                                          Database=params.get('Database'), Tables=params.get('Tables'), )
        mcp_rsp = tc_house_d_list_tables(url=url, arguments=arguments, tool_name="TCHouseDListTableInfos")
        logger.info(f"tc house d list tables {mcp_rsp}")
        rsp = {}
        for table in mcp_rsp.Tables:
            columns = []
            for col in table.Columns:
                columns.append(Column(Name=col.Name, Type=col.Type, Comment=col.Comment))
            rsp[table.Name] = columns

        logger.info(f"tc house d final list tables {mcp_rsp}")
        return rsp

    def list_all_db_tables(self, params: dict[str, str], url: str) -> List[str]:
        arguments = TCHouseDListTableNamesReq(Catalog=params['Catalog'],
                                              Database=params.get('Database'))
        mcp_rsp = tc_house_d_list_table_names(url=url, arguments=arguments, tool_name="TCHouseDListTableNames")
        logger.info(f"tc house d final list table names {mcp_rsp.TableNames}")
        return mcp_rsp.TableNames

    def execute_query(self, param: Dict[str, str], url: str) -> Dict[str, Any]:
        try:
            tc_house_d_execute_query(url=url, sql=param.get("SQL"), tool_name="TCHouseDExecuteQuery")
            return {
                "is_success": 1
            }
        except ExceptionGroup as e:
            error_msg = _handler_exception(e)
            return {
                "is_success": 2,
                "error_msg": error_msg
            }


class ES_Strategy(DatabaseStrategy):
    """ES_SQL 数据库策略（示例）"""

    def get_tables_ddl(self, table_names: list[str], database_name: str,
                       datasource_name: str, url: str) -> Dict[str, str]:
        all_index = {}
        for table_name in table_names:
            rsp = es_get_index_schema(url, {"index": table_name}, tool_name="get_mappings")
            for index in rsp.Tables:
                columns = []
                for col in index.Columns:
                    columns.append(Column(Name=col.Name, Type=col.Type, Comment=col.Comment))
                # TODO 完善，先按照 mysql 处理
                ddl = _generate_ddl(index.Name, columns, "mysql")
                all_index[index.Name] = ddl
        return all_index

    def sampling_data(self, database_name: str, datasource_name: str, engine_name: str, table: str,
                      url: str) -> SamplingDataRsp:
        es_sample_data = es_sampling_data(url=url, arguments=ESSamplingDataReq(index=table,
                                                                               query_body={
                                                                                   "size": 100,
                                                                                   "query": {
                                                                                       "match_all": {}
                                                                                   }
                                                                               }),
                                          tool_name="elasticsearch_query")
        return SamplingDataRsp(ResultSchema=es_sample_data.ResultSchema, ResultSet=es_sample_data.ResultSet)

    def get_tables_schema(self, params: Dict[str, Any], url: str) -> Dict[str, List[Column]]:
        tables = params.get('Tables')
        all_tables_schema = {}
        for table_name in tables:
            rsp = es_get_index_schema(url, {"index": table_name}, tool_name="get_mappings")
            for index in rsp.Tables:
                columns = []
                for col in index.Columns:
                    columns.append(Column(Name=col.Name, Type=col.Type, Comment=col.Comment))
                all_tables_schema[index.Name] = columns
        return all_tables_schema

    def list_all_db_tables(self, params: dict[str, str], url: str) -> List[str]:
        # TODO ES 后续再说
        return []

    def execute_query(self, param: Dict[str, str], url: str) -> Dict[str, str]:
        # TODO ES 后续再说
        return {}


class TCHouse_C_Strategy(DatabaseStrategy):
    """TCHouse-c 数据库策略（示例）"""

    def get_tables_ddl(self, table_names: list[str], database_name: str,
                       datasource_name: str, url: str) -> Dict[str, str]:
        all_ddl = {}
        for i in range(0, len(table_names), 20):
            batch = table_names[i:i + 20] or table_names[i:]
            batch_args = GetTCHouseCGetTablesSchemaReq(
                Database=database_name,
                Tables=batch
            )
            batch_tables = tc_house_c_get_tables_schema(url=url, arguments=batch_args,
                                                        tool_name="TCHouseCGetTableSchema")
            if not batch_tables:
                continue

            for table in batch_tables.Tables:
                table_name = table.Table
                if table_name in table_names:
                    all_ddl[table_name] = table.CreateSql
        return all_ddl

    def sampling_data(self, database_name: str, datasource_name: str, engine_name: str, table: str,
                      url: str) -> SamplingDataRsp:
        arguments = TCHouseCSampleDataReq(
            Database=database_name,
            Table=table,
            Limit=1000
        )

        tc_house_c_rsp = tc_house_c_sample_data(url=url, arguments=arguments, tool_name="TCHouseCGetTableSample")
        return SamplingDataRsp(ResultSchema=tc_house_c_rsp.ResultSchema, ResultSet=tc_house_c_rsp.ResultSet)

    def get_tables_schema(self, params: Dict[str, Any], url: str) -> Dict[str, List[Column]]:
        arguments = TCHouseCListTablesReq(Database=params.get('Database'), Tables=params.get('Tables'), )
        mcp_rsp = tc_house_c_list_tables(url=url, arguments=arguments, tool_name="TCHouseCListTableInfos")
        logger.info(f"tc house c list tables {mcp_rsp}")
        rsp = {}
        for table in mcp_rsp.Tables:
            columns = []
            for col in table.Columns:
                columns.append(Column(Name=col.Name, Type=col.Type, Comment=col.Comment))
            rsp[table.Name] = columns

        logger.info(f"tc house c final list tables {mcp_rsp}")
        return rsp

    def list_all_db_tables(self, params: dict[str, str], url: str) -> List[str]:
        arguments = TCHouseCListTableNames(Database=params.get('Database'))
        mcp_rsp = tc_house_c_list_table_names(url=url, arguments=arguments, tool_name="TCHouseCListTableNames")
        logger.info(f"tc house c final list table names {mcp_rsp.TableNames}")
        return mcp_rsp.TableNames

    def execute_query(self, param: Dict[str, str], url: str) -> Dict[str, Any]:
        try:
            tc_house_c_execute_query(url=url, sql=param.get("SQL"), tool_name="TCHouseCExecuteQuery")
            return {
                "is_success": 1
            }
        except ExceptionGroup as e:
            error_msg = _handler_exception(e)
            return {
                "is_success": 2,
                "error_msg": error_msg
            }


class DatabaseContext:
    """数据库操作上下文"""

    def __init__(self, strategy: DatabaseStrategy, mcp_url):
        self.strategy = strategy
        self.mcp_url = mcp_url

    def get_tables_ddl(self, table_names: list[str], database_name: str,
                       datasource_name: str) -> Dict[str, str]:
        return self.strategy.get_tables_ddl(table_names, database_name, datasource_name, self.mcp_url)

    def sampling_data(self, database_name: str, datasource_name: str,
                      engine_name: str, table: str) -> SamplingDataRsp:
        return self.strategy.sampling_data(database_name, datasource_name, engine_name, table, self.mcp_url)

    def get_tables_schema(self, params: dict[str, Any] | dict[str, str]) -> Dict[str, List[Column]]:
        return self.strategy.get_tables_schema(params=params, url=self.mcp_url)

    def list_all_db_tables(self, params: dict[str, str]) -> List[str]:
        return self.strategy.list_all_db_tables(params, self.mcp_url)

    def execute_query(self, param: Dict[str, str]) -> Dict[str, Any]:
        return self.strategy.execute_query(param, self.mcp_url)


def create_database_context(engine_type: str, mcp_url: dict) -> DatabaseContext:
    """根据引擎类型创建数据库上下文对象"""
    engine_type_lower = engine_type.lower()
    if engine_type_lower == EngineTypeEnum.DLC.value.lower():
        strategy = DLCStrategy()
    elif engine_type_lower == EngineTypeEnum.TC_HOUSE_D.value.lower():
        strategy = TCHouse_D_Strategy()
    elif engine_type_lower == EngineTypeEnum.ES.value.lower():
        strategy = ES_Strategy()
    elif engine_type_lower == EngineTypeEnum.TC_HOUSE_C.value.lower():
        strategy = TCHouse_C_Strategy()
    else:
        raise ValueError(f"不支持的引擎类型: {engine_type}")

    return DatabaseContext(strategy, mcp_url.get(engine_type))


if __name__ == '__main__':
    dlc_strategy = DLCStrategy()
    context = DatabaseContext(dlc_strategy, "http://127.0.0.1:31234/sse?auth_token=SECRET_KEY_2025")
    desc = context.get_tables_ddl(table_names=["cards"], datasource_name="DataLakeCatalog", database_name="card_games")
    columns = [Column(Name="id", Type="int", Comment="it`s 'name','type'")]
    ddl = _generate_ddl("table", columns, "mysql")
    x = context.get_tables_schema(desc)
    print(desc)
