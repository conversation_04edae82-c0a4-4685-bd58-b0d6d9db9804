import json
import logging
from typing import Any, Dict
from common.share.config import appConfig
from common.tencentcloud.ai_search_retrieve import get_text_embeddings, search_elasticsearch, rerank_documents, \
    search_elasticsearch_by_score, rerank_documents_by_score
from infra.adapter.knowledge_list_adapter import KnowledgeListAdapter
from infra.adapter.user_search_config_adapter import UserSearchConfigAdapter
from common.database.database import get_metadata_db_pool
mysql_pool = get_metadata_db_pool()
index_name = appConfig.automic.aisearch.es.index_name


def retrieve(params: Dict[str, Any]) -> Dict[str, Any]:
    query = params["question"]
    app_id = params["app_id"]
    knowledge_base_ids = params.get("knowledge_base_ids")
    if not knowledge_base_ids:
        return {"answer": []}

    adapter = UserSearchConfigAdapter(mysql_pool)
    user_search_config = adapter.get_by_app_id(app_id)

    try:
        # Step 1: 根据搜索类型决定是否需要向量化
        logging.info(f"Step 1: Processing query - {query}")

        embedding_list = []
        if user_search_config.search_type in [0, 1]:  # 0-混合搜索,1-向量搜索需要embedding
            embedding_list = get_text_embeddings(query)
            if not embedding_list:
                raise ValueError("No embeddings generated for the query")
        # Step 2: 根据向量化的结果查询搜索引擎
        logging.info("Step 2: Query search engine based on embedding list...")

        doc_list = search_elasticsearch(app_id, embedding_list, index_name, query, user_search_config, knowledge_base_ids)
        if not doc_list:
            logging.error("No documents found for Elasticsearch search")
            return {}
        # Step 3: 根据配置决定是否重排序搜索结果
        logging.info("Step 3: Reorder the results found by search engines...")
        if user_search_config.rerank_status == 0:  # 0表示开启重排序
            reranked_results = rerank_documents(query, doc_list)
            if not reranked_results:
                raise ValueError("No documents returned after reranking")
            final_results = reranked_results
        else:
            final_results = doc_list

        return {
            "answer": final_results,
        }
    except Exception as e:
        logging.error(f"Exception occurred while online document query: {e}")
        raise


def replace_file_info_with_actual(final_results):
    # 1. 提取所有唯一的 file_id，同时处理可能缺失 file_id 的情况
    file_ids_to_query = []
    for item in final_results:
        file_id = item.get('file_id') # 使用 .get() 安全地获取 file_id
        if file_id:
            file_ids_to_query.append(file_id)

    # 去除重复的 file_id，以提高查询效率
    unique_file_ids = list(set(file_ids_to_query))

    knowledge_adapter = KnowledgeListAdapter(mysql_pool)
    # 2. 批量查询文件名称、URL和知识库名称
    file_info_map = knowledge_adapter.get_file_info_by_ids(unique_file_ids)

    # 3. 遍历结果，为每个 chunk 添加文件名称、URL和知识库名称
    for item in final_results:
        file_id = item.get('file_id') # 再次安全获取 file_id
        if file_id:
            item['file_name'] = file_info_map.get(file_id, {}).get('file_name', "N/A")
            item['file_url'] = file_info_map.get(file_id, {}).get('file_url', "N/A")
            item['knowledge_base_name'] = file_info_map.get(file_id, {}).get('knowledge_base_name') or "default"
        else:
            item['file_name'] = "N/A"
            item['file_url'] = "N/A"
            item['knowledge_base_name'] = "default"

    return final_results

def retrieve_by_score(params: Dict[str, Any]) -> Dict[str, Any]:
    query = params["question"]
    app_id = params["app_id"]
    knowledge_base_ids = params.get("knowledge_base_ids")
    if not knowledge_base_ids:
        return {"answer": []}
    recall_num = params.get("recall_num")
    adapter = UserSearchConfigAdapter(mysql_pool)
    user_search_config = adapter.get_by_app_id(app_id)

    try:
        # Step 1: 根据搜索类型决定是否需要向量化
        logging.info(f"Step 1: Processing query - {query}")

        embedding_list = []
        if user_search_config.search_type in [0, 1]:  # 0-混合搜索,1-向量搜索需要embedding
            embedding_list = get_text_embeddings(query)
            if not embedding_list:
                raise ValueError("No embeddings generated for the query")
        # Step 2: 根据向量化的结果查询搜索引擎
        logging.info("Step 2: Query search engine based on embedding list...")

        doc_list = search_elasticsearch_by_score(app_id, embedding_list, index_name, query, user_search_config, knowledge_base_ids, recall_num)
        if not doc_list:
            logging.error("No documents found for Elasticsearch search")
            return {"answer": []}
        # Step 3: 根据配置决定是否重排序搜索结果
        logging.info("Step 3: Reorder the results found by search engines...")
        if user_search_config.rerank_status == 0:  # 0表示开启重排序
            reranked_results = rerank_documents_by_score(query, doc_list)
            if not reranked_results:
                logging.error("No documents returned after reranking")
                final_results = doc_list
            else:
                final_results = reranked_results
        else:
            final_results = doc_list
        # Step 4: 由于文件名可修改，需要将结果中的文件名替换为实际文件名
        final_results = replace_file_info_with_actual(final_results)
        return {
            "answer": final_results,
        }
    except Exception as e:
        logging.error(f"Exception occurred while online document query: {e}")
        raise


if __name__ == "__main__":
    # print(json.dumps(retrieve({"question": "如何使用AI搜索", "user_id": "1", "app_id": "test1"}), ensure_ascii=False))

    print(json.dumps(retrieve_by_score({"question": "langchain", "user_id": "1", "app_id": "test1", "knowledge_base_ids": ["default"]}), ensure_ascii=False))
