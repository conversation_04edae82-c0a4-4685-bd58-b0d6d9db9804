from typing import Dict, Optional
from pydantic import BaseModel, field_validator
from fastapi import APIRouter, Response, status
from common.logger.logger import logger
from common.share.error import ErrorCode
from infra.mcp.basic import mcp_call_tool
from common.share.enums import MCPEngineTypeEnum
from infra.server.model import CommonResponse, ErrorInfo

router = APIRouter()
class ConnectionTestConfig(BaseModel):
    """连接测试接口参数"""
    Url: str
    Type: str
    Timeout: float = 5

    @field_validator('Url')
    def validate_mcp_url(cls, v):
        if not v or not v.strip():
            raise ValueError("Url is required")
        return v.strip()

    @field_validator('Type')
    def validate_mcp_type(cls, v):
        valid_types = [engine.value for engine in MCPEngineTypeEnum]
        if v not in valid_types:
            raise ValueError(f"Type must be one of: {', '.join(valid_types)}")
        return v

test_tools = {
    MCPEngineTypeEnum.DLC.value: {
        'tool_name': 'DLCListEngines',
        'arguments': {},
        'check_result': lambda result: not result.isError and 'DataEngines' in result.content[0].text
    },
    MCPEngineTypeEnum.TCHouseD.value: {
        'tool_name': 'TCHouseDListDatabases',
        'arguments': {},
        'check_result': lambda result: not result.isError and 'Databases' in result.content[0].text
    }
}

async def mcp_connection_test(url: str, mcp_type: str, timeout: float=5) -> CommonResponse:
    """
    测试MCP连接
    Args:
        url: MCP服务地址
        mcp_type: MCP类型 (DLC, TCHouseX, ES, TCHouseD)
    
    Returns:
        Dict: 包含连接测试结果的字典
    """
    try:
        logger.info(f"start to test mcp connection - URL: {url}, Type: {mcp_type}, timeout: {timeout}")
        
        # 根据MCP类型选择测试工具和参数
        if mcp_type not in test_tools:
            return CommonResponse(Status=status.HTTP_404_NOT_FOUND, Error=ErrorInfo(Code=ErrorCode.ConnectionTestError.value, Message=f'Unsupported MCP type: {mcp_type}'))
        tool_config = test_tools[mcp_type]
        
        # 调用MCP工具测试连接
        result = await mcp_call_tool(
            arguments=tool_config['arguments'],
            url=url,
            tool_name=tool_config['tool_name'],
            timeout=timeout,
            sse_read_timeout=timeout*2
        )
        logger.info(f"mcp connection test result: {result}")
        
        # Check if the result indicates an error
        try:
            if not tool_config['check_result'](result):
                return CommonResponse(Status=status.HTTP_424_FAILED_DEPENDENCY, Error=ErrorInfo(Code=ErrorCode.ConnectionTestError.value, Message=f'mcp connection test failed: {result.content}'))
        except Exception as e:
            logger.error(f"mcp connection test error - URL: {url}, Type: {mcp_type}, Error: {str(e)}", exc_info=True)
            return CommonResponse(Status=status.HTTP_424_FAILED_DEPENDENCY, Error=ErrorInfo(Code=ErrorCode.ConnectionTestError.value, Message=f'mcp connection test failed: {str(e)}'))
        
        return CommonResponse(Status=status.HTTP_200_OK, Error=None)
            
    except Exception as e:
        logger.error(f"mcp connection test error - URL: {url}, Type: {mcp_type}, Error: {str(e)}", exc_info=True)
        return CommonResponse(Status=status.HTTP_424_FAILED_DEPENDENCY, Error=ErrorInfo(Code=ErrorCode.ConnectionTestError.value, Message=f'mcp connection test failed: {str(e)}'))


@router.post("/connection_test")
async def connection_test(config: ConnectionTestConfig) -> CommonResponse:
    """
    MCP连接测试接口
    
    Args:
        config: 连接测试配置
        response: FastAPI响应对象
    
    Returns:
        ConnectionTestResponse: 连接测试结果
    """
    
    logger.info(f"receive connection test request - URL: {config.Url}, Type: {config.Type}")
    # execute connection test
    response = await mcp_connection_test(config.Url, config.Type, config.Timeout)
    logger.info(f"connection test result: {response}")
    return response