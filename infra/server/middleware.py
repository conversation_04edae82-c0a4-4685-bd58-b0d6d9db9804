from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from common.share.context import ChatContext
import datetime
import uuid
from common.logger.logger import logger
from common.trace.trace import flush_tracer, set_span_status
from contextlib import asynccontextmanager
from fastapi import FastAPI
from common.trace.trace import start_parent_span, span_add_event
from opentelemetry.trace import use_span
from common.metric.enpoints import record_request, record_latency, record_error
from infra.knowledge_base.knowledge_base_ray import reset_orphaned_task


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Data Agent Lifespan started")
    yield
    logger.info("Data Agent Lifespan Started flush tracer")
    flush_tracer()
    # 重置孤立RAG任务
    reset_orphaned_task()
    logger.info("Data Agent Lifespan ended")


class RequestTracingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Pre-request operations
        # get request body as dict, handle non-JSON requests gracefully
        try:
            if request.method in ["GET", "HEAD", "OPTIONS"]:
                body = {}
            else:
                body = await request.json()
        except Exception:
            body = {}
        header = request.headers
        ctx = ChatContext(body, header)
        ctx.set_path(request.url.path)
        ctx.set_start_time(datetime.datetime.now())
        ctx.set_end_time(datetime.datetime.now()+datetime.timedelta(seconds=20*60))
        if ctx.get_apm_trace_id() == 0:
            ctx.set_apm_trace_id(int(uuid.uuid4().hex[:16], 16))
            # TODO 该处设置未生效 Samuelzan
        span = start_parent_span(ctx)
        ctx.set_apm_trace_id(span.get_span_context().trace_id)
        ctx.set_parent_span(span.get_span_context().span_id)
        logger.info(f"request started, CtxInfo: {ctx}")

        with use_span(span, end_on_exit=False):  # Changed to False to handle SSE manually
            # Add request start event with attributes
            span_add_event(span, "request_started", {
                "request.method": request.method,
                "request.url": str(request.url),
                "request.body": str(body)  # Convert body to string to ensure it's serializable
            })
            
            request.state.context = ctx

            response = await call_next(request)
            
            # Post-request operations
            logger.info(f"request completed, CtxInfo: {ctx}, cost_seconds: {ctx.get_cost_seconds(datetime.datetime.now())}")
            # Check if this is an SSE response
            if response.headers.get("content-type","").lower().startswith("text/event-stream"):
                logger.info(f"sse response started, CtxInfo: {ctx}, cost_seconds: {ctx.get_cost_seconds(datetime.datetime.now())}")
                
                # Create a wrapper for the response body
                original_body = response.body_iterator
                
                async def wrapped_body():
                    try:
                        async for chunk in original_body:
                            yield chunk
                    except Exception as e:
                        logger.error(f"sse connection error: {str(e)}")
                        span_add_event(span, "request_error", {
                            "error.message": str(e),
                            "error.type": type(e).__name__,
                            "trace.id": ctx.get_trace_id()
                        })
                        raise
                    finally:
                        logger.info(f"sse response closed, CtxInfo: {ctx}, cost_seconds: {ctx.get_cost_seconds(datetime.datetime.now())}")
                        record_metrics(ctx, response, span)

                response.body_iterator = wrapped_body()
            else:
                # Add response end event for non-SSE responses
                span_add_event(span, "request_ended", {
                    "response.status_code": response.status_code,
                    "response.content_type": response.headers.get("content-type", ""),
                    "response.duration_ms": int(ctx.get_cost_seconds(datetime.datetime.now()) * 1000),  # Convert to int to ensure it's serializable
                    "response.type": "normal"
                })
                record_metrics(ctx, response,span)

            return response

def setup_fastapi_middleware(app):
    """Add tracing middleware to FastAPI application"""
    logger.info(f"setup_fastapi_middleware, app: {app.user_middleware}")
    if len(app.user_middleware) == 0:
        app.add_middleware(RequestTracingMiddleware)

def record_metrics(ctx, response, span):
    cost_seconds = ctx.get_cost_seconds(datetime.datetime.now())
    ctx.set_end_time(datetime.datetime.now())
    record_request(ctx.get_path(), str(response.status_code), ctx.get_sub_account_uin())
    record_latency(ctx.get_path(), cost_seconds, ctx.get_sub_account_uin())
    span_add_event(span, "request_ended", {
        "response.status_code": response.status_code,
        "response.content_type": response.headers.get("content-type", ""),
        "response.duration_ms": int(cost_seconds * 1000),  # Convert to int to ensure it's serializable
        "response.type": "sse" if response.headers.get("content-type", "").lower().startswith("text/event-stream") else "normal"
    })
    set_span_status(span, response.status_code != 200)
    span.end()

