from typing import List, Tuple, Dict, Any
from common.logger.logger import get_logger_with_ctx

class KernelURN:
    def __init__(self, 
                 sub_uin: str = "$SUB_UIN", 
                 kernel_host: str = "$KN_HOST", 
                 kernel_name: str = "$KN_NAME", 
                 kernel_id: str = "$KN_ID",
                 kernel_type: str = "dlc"  # 新增 kernel_type 参数，默认为 "dlc"
        ):
        self.sub_uin = sub_uin if sub_uin else "$SUB_UIN"
        self.kernel_host = kernel_host if kernel_host else "$KN_HOST"
        self.kernel_name = kernel_name if kernel_name else "$KN_NAME"
        self.kernel_id = kernel_id if kernel_id else "$KN_ID"
        self.kernel_type = kernel_type if kernel_type else "dlc"  # 默认为 dlc

    def to_str(self):
        return f"{self.kernel_type}:::{self.sub_uin}:::{self.kernel_host}:::{self.kernel_name}:::{self.kernel_id}"
    
    def str_without_id(self):
        return f"{self.kernel_type}:::{self.sub_uin}:::{self.kernel_host}:::{self.kernel_name}"

    def can_assign_to(self, urn_request: "KernelURN") -> bool:
        '''
        判断是否可以分配给urn请求
        '''
        if not self.is_valid():
            return False
        if self.kernel_type != urn_request.kernel_type or self.sub_uin != urn_request.sub_uin or self.kernel_host != urn_request.kernel_host or self.kernel_name != urn_request.kernel_name:
            return False
        if self.has_kernel_id() and not urn_request.has_kernel_id():
            return True
        if self.has_kernel_id() and urn_request.has_kernel_id() and self.kernel_id != urn_request.kernel_id:
            return False
        return True
    
    def is_valid(self, check_kernel_id: bool = True):
        if not self.has_kernel_type() or not self.has_kernel_name() or not self.has_kernel_host() or not self.has_sub_uin():
            return False
        if check_kernel_id:
            return self.has_kernel_id()
        else:
            return True

    def has_kernel_id(self):
        return self.kernel_id is not None and self.kernel_id != "" and self.kernel_id != "$KN_ID"
    def has_kernel_name(self):
        return self.kernel_name is not None and self.kernel_name != "" and self.kernel_name != "$KN_NAME"
    def has_kernel_host(self):
        return self.kernel_host is not None and self.kernel_host != "" and self.kernel_host != "$KN_HOST"
    def has_sub_uin(self):
        return self.sub_uin is not None and self.sub_uin != "" and self.sub_uin != "$SUB_UIN"
    def has_kernel_type(self):
        return self.kernel_type is not None and self.kernel_type != "" and self.kernel_type != "$KN_TYPE"

    def is_ipython_mode(self):
        """判断是否为ray本地执行（ipython类型）"""
        return self.kernel_type == "ipython"
    
    def __str__(self):
        return self.to_str()

    @staticmethod
    def from_str(kernel_urn: str):
        parts = kernel_urn.split(":::")
        if len(parts) == 5:
            # 新格式，有 type 前缀
            return KernelURN(parts[1], parts[2], parts[3], parts[4], parts[0])
        else:
            raise ValueError(f"Invalid kernel URN format: {kernel_urn}")
    
def get_logger(urn: KernelURN, session_id: str = "none", trace_id: str = "none"):
    return get_logger_with_ctx(urn.sub_uin, session_id, trace_id, "jupyter")

class KernelClient:
    def __init__(self, urn: KernelURN, timeout: int = 600):
        self.urn = urn
        self.timeout = timeout

    def start_kernel(self) -> KernelURN:
        pass

    def exists_kernel(self, kernel_name: str = None, kernel_id: str = None):
        pass

    def kernel_status_ready(self) -> bool:
        pass
    
    def execute(self, code: str, timeout: int = None) -> Tuple[List[Dict[str, Any]], bool]:
        pass
    
    def install_requirements(self, requirements: List[str]) -> Tuple[List[Dict[str, Any]], bool]:
        requirements_str = " ".join(requirements)
        self.execute(f"%pip config set global.index-url https://mirrors.tencentyun.com/pypi/simple/ > /dev/null")
        return self.execute(f"%pip install {requirements_str}")

    def shutdown(self):
        pass