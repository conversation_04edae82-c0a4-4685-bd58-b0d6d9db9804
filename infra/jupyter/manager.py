import threading
import time
import logging
import asyncio
from typing import Callable, Dict, Optional, List
from common.logger.logger import logger
from common.utils.thread import ThreadSafeMap
import queue
from datetime import datetime, timedelta
from common.share import env
import ray
import os
from ray.exceptions import RayTaskError
from infra.jupyter.ipython_client import IpythonKernelClient
from infra.jupyter.basic import KernelURN, KernelClient
from infra.jupyter.eg_client import DLCKernelClient
get_kernel_client = lambda x : DLCKernelClient(x) if not x.is_ipython_mode() else IpythonKernelClient(x)

global_manager = None
def init_global_manager():
    global global_manager
    global_manager = KernelManager(use_ray=env.EXECUTION_MODE_RAY)
    return global_manager

def get_global_manager():
    global global_manager
    if global_manager is None:
       return init_global_manager()
    return global_manager

class KernelUtil:
    @staticmethod
    def reuse_kernel(urn: KernelURN, reuse_checker: Callable[[str], bool] = None):
        if not urn.is_valid(check_kernel_id=False):
            raise ValueError("Invalid kernel urn")
        if urn.is_ipython_mode():
            return urn
        client : KernelClient = get_kernel_client(urn)
        if urn.has_kernel_id() and client.exists_kernel(kernel_id=urn.kernel_id, kernel_name=urn.kernel_name) is not None:
            return urn
        if reuse_checker is not None:
            kernels = client.exists_kernel(kernel_name = urn.kernel_name)
            for kernel in kernels:
                urn_str = f"{urn.kernel_type}:::{urn.sub_uin}:::{urn.kernel_host}:::{urn.kernel_name}:::{kernel['id']}"
                if reuse_checker(urn_str):
                    logger.info(f"Found existing kernel: {urn_str}")
                    return KernelURN.from_str(urn_str)
        return KernelURN(kernel_type=urn.kernel_type, sub_uin=urn.sub_uin, kernel_host=urn.kernel_host, kernel_name=urn.kernel_name)
    
    @staticmethod
    def start_kernel(urn: KernelURN, reuse_checker: Callable[[str], bool] = None, timeout: int = 900,):
        old_urn = KernelUtil.reuse_kernel(urn, reuse_checker)
        if old_urn.has_kernel_id() and not old_urn.is_ipython_mode():
            return old_urn
        else:
            logger.info(f"Starting new kernel: {old_urn.to_str()}")
            new_urn = get_kernel_client(old_urn).start_kernel()
            logger.info(f"Started new kernel: {new_urn.to_str()}")
            return new_urn
    
    @staticmethod
    def execute_cmd(urn: KernelURN, command: str, timeout: int = 30):
        if not urn.is_valid():
            raise ValueError("Invalid kernel urn")
        client : KernelClient = get_kernel_client(urn)
        return client.execute(command, timeout)
    
    @staticmethod
    def stop_kernel(urn: KernelURN):
        if not urn.is_valid():
            raise ValueError("Invalid kernel urn")
        try:
            client = get_kernel_client(urn)
            kernel = client.get_kernel(urn.kernel_id, urn.kernel_name)
            kernel.shutdown()
            urn.kernel_id = "$KN_ID"
            return urn
        except Exception as e:
            logger.error(f"Error stopping kernel {urn.to_str()}: {e}")
            return urn

    @staticmethod
    def check_kernel_status_ready(urn: KernelURN):
        if not urn.is_valid():
            raise ValueError("Invalid kernel urn")
        client = get_kernel_client(urn)
        return client.kernel_status_ready()

    @staticmethod
    def execute_keep_alive(urn: KernelURN):
        """执行保活任务，让内核保持活跃"""
        if not urn.is_valid():
            return False
        try:
            return KernelUtil.execute_cmd(urn, "print('keep_alive')", timeout=30)
        except Exception as e:
            logger.error(f"Error executing keep_alive for kernel {urn.to_str()}: {e}")
            return False
    
    @staticmethod
    def execute_cmd(urn: KernelURN, command: str, timeout: int = 30):
        if not urn.is_valid():
            raise RuntimeError("Invalid kernel urn")
        try:
            client = get_kernel_client(urn)
            rst = client.execute(command, timeout=timeout)
        except TimeoutError as e:
            logger.error(f"Timeout executing cmd for kernel {urn.to_str()}: {e}")
            raise e
        except Exception as e:
            logger.error(f"Error executing cmd for kernel {urn.to_str()}: {e}")
            raise e
        return rst
        
    @staticmethod
    def install_requirements(urn: KernelURN, requirements: List[str]):
        if not urn.is_valid():
            raise RuntimeError("Invalid kernel urn")
        client = get_kernel_client(urn)
        return client.install_requirements(requirements)

class KernelRunner:
    def __init__(self, keep_alive_seconds: int = 30*60, subuin_max_kernel: int = 2, 
                 keep_alive_interval: int = 9*60, cleanup_interval: int = 5, use_existing_kernel: bool = True):
        # 使用线程安全的映射
        self.available_kernels: ThreadSafeMap[str, datetime] = ThreadSafeMap()
        self.busy_kernels: ThreadSafeMap[str, datetime] = ThreadSafeMap()
        self.kernel_errors = ThreadSafeMap()
        self.subuin_max_kernel = subuin_max_kernel
        self.keep_alive_seconds = keep_alive_seconds
        self.keep_alive_interval = keep_alive_interval  # 保活间隔（秒）
        self.cleanup_interval = cleanup_interval  # 清理间隔（秒）
        self.use_existing_kernel = use_existing_kernel

        # kernel 启动相关        
        self.start_kernel_queue = queue.Queue()
        self.reset_kernel_queue = queue.Queue()
        self.starting_uins: ThreadSafeMap[str, int] = ThreadSafeMap()
        # log interval
        self.interval_status_check = 30
        self.intervel_reset_kernel = 2
        self.intervel_start_kernel = 1
        # 线程控制
        self.running = False
        self.kernel_util = KernelUtil()
        
        # 异步事件循环
        self.loop = None
        
        # 启动后台线程
        self.start_background_threads()

    def full_subuin_kernel_count(self, kernel_urn: KernelURN) -> bool:
        """检查用户内核数量是否满足限制"""
        if kernel_urn.is_ipython_mode():
            return self._get_user_kernel_count(kernel_urn.str_without_id()) >= self.subuin_max_kernel * 10
        return self._get_user_kernel_count(kernel_urn.str_without_id()) >= self.subuin_max_kernel
    
    def get_subuin_starting_job_count(self, kernel_urn: KernelURN) -> int:
        """获取用户当前正在启动的内核数量"""
        return self.starting_uins.get_counter(kernel_urn.str_without_id(), 0)
    
    def reset_urn_activity(self, kernel_urn: KernelURN):
        """重置内核活动时间"""
        self.available_kernels[kernel_urn.to_str()] = datetime.now()
        logger.info(f"Reset kernel activity: {kernel_urn.to_str()}")
    
    def start_background_threads(self):
        """启动三个后台线程"""
        self.running = True
        
        # 1. 启动内核线程（异步）
        self.start_kernel_thread = threading.Thread(
            target=self._start_kernel_worker,
            name="KernelStarter",
            daemon=True
        )
        self.start_kernel_thread.start()
        
        # 2. 保活线程
        self.keep_alive_thread = threading.Thread(
            target=self._keep_alive_worker,
            name="KernelKeepAlive",
            daemon=True
        )
        self.keep_alive_thread.start()
        
        # 3. 清理过期内核线程
        self.cleanup_thread = threading.Thread(
            target=self._cleanup_expired_kernels_worker,
            name="KernelCleanup",
            daemon=True
        )
        self.cleanup_thread.start()
        
        # 4. 重置内核线程
        self.reset_thread = threading.Thread(
            target=self._reset_kernel_worker,
            name="KernelReset",
            daemon=True
        )
        self.reset_thread.start()

        # 5. 状态检查线程
        self.status_check_thread = threading.Thread(
            target=self._status_check_worker,
            name="KernelStatusCheck",
            daemon=True
        )
        self.status_check_thread.start()
        
        logger.info("Started five background threads: kernel starter, keep alive, cleanup, reset and status check")

    def stop_background_threads(self):
        """停止所有后台线程"""
        self.running = False
        if self.loop:
            self.loop.call_soon_threadsafe(self.loop.stop)
        logger.info("Stopping background threads...")

    def check_can_reuse(self, urn_str: str) -> bool:
        urn = KernelURN.from_str(urn_str)
        return urn.kernel_id not in [
            KernelURN.from_str(x).kernel_id for x in self.available_kernels.keys() 
        ] and urn.kernel_id not in [
            KernelURN.from_str(x).kernel_id for x in self.busy_kernels.keys()
        ]
    
    def add_kernel_to_available(self, urn: KernelURN):
        self.available_kernels[urn.to_str()] = datetime.now()
        self.starting_uins.decrement_counter(urn.str_without_id())
        if self.starting_uins.get_counter(urn.str_without_id(), 0) <= 0:
            self.starting_uins.pop(urn.str_without_id())
        
    def _start_kernel_worker(self):
        """启动内核的工作线程（运行异步事件循环）"""
        logger.info("Kernel starter thread started")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        async def _async_start_kernel(cls: KernelRunner, urn: KernelURN):
            """异步启动内核"""
            try:
                logger.info(f"Starting kernel: {urn.to_str()}")
                # 在线程池中运行同步的start_kernel方法
                
                updated_urn = await loop.run_in_executor(
                    None, cls.kernel_util.start_kernel, 
                    urn, cls.check_can_reuse if cls.use_existing_kernel else None,
                )
                
                # 更新内核映射
                cls.add_kernel_to_available(updated_urn)
                logger.info(f"Successfully started kernel: {updated_urn.to_str()}")
            except RuntimeError as e:
                cls.kernel_errors[urn.to_str()] = e
                logger.error(f"Failed to start kernel {urn.to_str()}: {e.args[0]}")
                cls.starting_uins.decrement_counter(urn.str_without_id())
            except Exception as e:
                logger.error(f"Failed to start kernel {urn.to_str()}: {e}")
                # 启动失败时也要减少计数
                cls.starting_uins.decrement_counter(urn.str_without_id())
        
        async def _async_kernel_worker(cls: KernelRunner):
            """异步内核启动工作协程"""
            while cls.running:
                try:
                    # 使用队列的get方法，设置超时避免阻塞
                    try:
                        urn = cls.start_kernel_queue.get_nowait()
                        logger.info(f"Starting kernel: {urn.to_str()}")
                        if cls.starting_uins.get_counter(urn.str_without_id(), 0) >= cls.subuin_max_kernel:
                            continue
                        cls.starting_uins.increment_counter(urn.str_without_id())
                        # 异步启动内核
                        loop.create_task(_async_start_kernel(cls, urn))
                        
                    except queue.Empty:
                        # 队列为空，等待一段时间
                        await asyncio.sleep(self.intervel_start_kernel)
                    
                except Exception as e:
                    logger.error(f"Error in async kernel worker: {e}")
                    await asyncio.sleep(5)
        # 创建新的事件循环
        
        try:
            loop.run_until_complete(_async_kernel_worker(self))
        except Exception as e:
            logger.error(f"Error in async kernel worker: {e}")
        finally:
            loop.close()

    def _keep_alive_worker(self):
        """保活工作线程"""
        logger.info("Kernel keep alive thread started")
        while self.running:
            try:
                current_time = datetime.now()
                for urn_str, activity_time in self.available_kernels.items():
                    urn = KernelURN.from_str(urn_str)
                    # 检查是否需要保活
                    if current_time - activity_time + timedelta(seconds=60) > timedelta(seconds=self.keep_alive_interval) and not urn.is_ipython_mode():
                        logger.debug(f"Executing keep alive for kernel: {urn_str}")
                        success = self.kernel_util.execute_keep_alive(urn)
                        if not success:
                            logger.warning(f"Keep alive failed for kernel: {urn_str}")
                
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"Error in keep alive thread: {e}")
                time.sleep(60)

    def _cleanup_expired_kernels_worker(self):
        """清理过期内核的工作线程"""
        logger.info("Kernel cleanup thread started")
        while self.running:
            try:
                current_time = datetime.now()
                expired_kernels = []
                expired_busy_kernels = []
                
                for urn_str, last_activity in self.available_kernels.items():
                    # 检查内核是否过期
                    if current_time - last_activity > timedelta(seconds=self.keep_alive_seconds):
                        logger.info(f"Available Kernel expired: {urn_str}")
                        expired_kernels.append(urn_str)
                
                for urn_str, last_activity in self.busy_kernels.items():
                    if current_time - last_activity > timedelta(seconds=self.keep_alive_seconds):
                        logger.error(f"Busy Kernel expired: {urn_str}")
                        expired_busy_kernels.append(urn_str)
                # 清理过期的内核
                for urn_str in expired_kernels:
                    # 清理映射和分配记录
                    self.available_kernels.pop(urn_str, None)
                for urn_str in expired_busy_kernels:
                    self.busy_kernels.pop(urn_str, None)
                time.sleep(self.cleanup_interval)  # 按配置的间隔清理
            except Exception as e:
                logger.error(f"Error in cleanup thread: {e}")
                time.sleep(self.cleanup_interval)

    def _reset_kernel_worker(self):
        """重置内核的工作线程"""
        logger.info("Kernel reset thread started")
        while self.running:
            try:
                urn : KernelURN = self.reset_kernel_queue.get_nowait()
                self.busy_kernels.pop(urn.to_str())
                # reset_result = self.kernel_util.execute_reset_cmd(urn) 
                reset_result = True
                if reset_result:
                    logger.info(f"Reset kernel: {urn.to_str()} success")
                    self.available_kernels[urn.to_str()] = datetime.now()
                else:
                    logger.error(f"Reset kernel failed: {urn.to_str()}")
            except queue.Empty:
                time.sleep(self.intervel_reset_kernel)
            except Exception as e:
                logger.error(f"Error in reset kernel worker: {e}")
                time.sleep(5)
    
    def _status_check_worker(self):
        """状态检查工作线程"""
        logger.info("Kernel status check thread started")
        while self.running:
            logger.info(f"Kernel status check: {self.get_stats()}")
            time.sleep(self.interval_status_check)
                
    def _get_user_kernel_count(self, sub_uin: str) -> int:
        """获取用户当前的内核数量"""
        count = 0
        for urn_str in self.available_kernels.keys():
            if KernelURN.from_str(urn_str).str_without_id() == sub_uin:
                count += 1
        for urn_str in self.busy_kernels.keys():
            if KernelURN.from_str(urn_str).str_without_id() == sub_uin:
                count += 1
        return count

    def start_kernel(self, kernel_urn: KernelURN) -> Optional[KernelURN]:
        """请求一个内核"""
        if not kernel_urn.is_valid(check_kernel_id=False):
            raise ValueError("Invalid kernel urn")
        if self.kernel_errors.get(kernel_urn.to_str(), None) is not None:
            e = self.kernel_errors.pop(kernel_urn.to_str())
            logger.error(f"Error starting kernel {kernel_urn.to_str()}: {e}")
            raise e
        # 创建新的内核请求
        self.start_kernel_queue.put(kernel_urn)
        logger.info(f"Queued new kernel request: {kernel_urn.to_str()}")

    def reset_kernel(self, kernel_urn: KernelURN):
        """请求一个内核"""
        if not kernel_urn.is_valid():
            raise ValueError("Invalid kernel urn")
        # 创建新的内核请求
        self.reset_kernel_queue.put(kernel_urn)
        logger.info(f"Queued new kernel reset request: {kernel_urn.to_str()}")

    def mark_kernel_busy(self, urn: KernelURN):
        """释放内核"""
        urn_str = urn.to_str()
        logger.info(f"Marking kernel as busy: {urn.to_str()} ")
        if self.available_kernels.pop(urn_str) is not None:
            self.busy_kernels[urn_str] = datetime.now()
            return True
        else:
            logger.warning(f"Kernel not found in available kernels: {urn.to_str()}")
            return False

    def get_kernel(self, kernel_urn: KernelURN):
        if not kernel_urn.is_valid():
            return None
        result = self.available_kernels.get(kernel_urn.to_str())
        return kernel_urn if result else None

    def assign_kernel(self, kernel_urn: KernelURN) -> Optional[KernelURN]:
        """分配内核，如果available_kernels里面有就返回已有的，如果没有的话就list kernels获取可用的"""
        if not kernel_urn.is_valid(check_kernel_id=False):
            return None
            
        # 首先检查available_kernels中是否有可用的内核
        for key in self.available_kernels.keys():
            urn = KernelURN.from_str(key)
            if not urn.can_assign_to(kernel_urn):
                continue
            if KernelUtil.check_kernel_status_ready(urn):
                logger.info(f"Found available kernel in cache: {urn.to_str()}")
                return urn
        if not self.use_existing_kernel:
            return None
        # 如果available_kernels中没有找到，则从gateway获取可用的内核列表
        try:
            client = get_kernel_client(kernel_urn)
            kernels = client.exists_kernel(kernel_name=kernel_urn.kernel_name)
            
            for kernel in kernels:
                # 构造完整的kernel URN
                urn_str = f"{kernel_urn.kernel_type}:::{kernel_urn.sub_uin}:::{kernel_urn.kernel_host}:::{kernel_urn.kernel_name}:::{kernel['id']}"
                urn = KernelURN.from_str(urn_str)
                
                # 检查是否可以分配给请求的内核
                if not urn.can_assign_to(kernel_urn):
                    continue
                    
                # 检查内核状态
                if KernelUtil.check_kernel_status_ready(urn) and self.check_can_reuse(urn.to_str()):
                    # 将找到的内核添加到available_kernels中
                    self.add_kernel_to_available(urn)
                    logger.info(f"Found available kernel from gateway: {urn.to_str()}")
                    return urn
                    
        except Exception:
            logger.error(f"Error listing kernels for {kernel_urn.to_str()}", exc_info=True)
            
        logger.info(f"No available kernel found for {kernel_urn.to_str()}, waiting for start kernel")
        return None

    def get_stats(self) -> Dict:
        """获取统计信息"""
        return {
            'available_kernel_count': len(self.available_kernels),
            'start_kernel_queue_size': self.start_kernel_queue.qsize(),
            'reset_kernel_queue_size': self.reset_kernel_queue.qsize(),
            'available_kernels': [(urn_str, activity_time) for urn_str, activity_time in self.available_kernels.items()],
            'busy_kernels': [(urn_str, activity_time) for urn_str, activity_time in self.busy_kernels.items()],
            'running': self.running,
            'starting_uins': [(sub_uin, count) for sub_uin, count in self.starting_uins.items()]
        }
    
class KernelManagerLocal:
    def __init__(self, keep_alive_seconds=30*60, cleanup_interval=10):
        self.kernel_assignments: ThreadSafeMap[str, str] = ThreadSafeMap()
        self.kernel_runner = KernelRunner(keep_alive_seconds=keep_alive_seconds, cleanup_interval=cleanup_interval)

    async def get_assigned_urn(self, usage_id: str) -> Optional[KernelURN]:
        if self.kernel_assignments.get(usage_id, None) is None:
            return None
        return KernelURN.from_str(self.kernel_assignments.get(usage_id))

    async def session_start(self, sub_uin: str, kernel_host: str, kernel_name: str, usage_id: str, kernel_type: str = "dlc"):
        kernel_urn = KernelURN(sub_uin = sub_uin, kernel_host = kernel_host, kernel_name = kernel_name, kernel_type = kernel_type)
        if await self.assign_kernel(usage_id, kernel_urn) is not None:
            return
        try:
            self.kernel_runner.start_kernel(kernel_urn)
        except Exception as e:
            logger.warning(f"Error starting kernel prepare {kernel_urn.to_str()}: {e}")

    async def assign_kernel(self, usage_id: str, kernel_urn: KernelURN):
        if self.kernel_assignments.get(usage_id, None) is not None:
            return KernelURN.from_str(self.kernel_assignments.get(usage_id))
        urn = self.kernel_runner.assign_kernel(kernel_urn)
        if urn is None:
            return None
        if not urn.can_assign_to(kernel_urn):
            return None
        if urn not in self.kernel_assignments.values() and KernelUtil.check_kernel_status_ready(urn) and self.kernel_runner.mark_kernel_busy(urn):
            self.kernel_assignments[usage_id] = urn.to_str()
            logger.info(f"Assigned kernel: {urn} to usage_id: {usage_id}")
            return urn
        return None

    async def start_kernel_with_timeout(self, usage_id: str, kernel_urn: KernelURN, timeout: int = 300) -> Optional[KernelURN]:
        start_time = datetime.now()
        while True:
            urn = await self.assign_kernel(usage_id, kernel_urn)
            if urn is not None:
                return urn
            if self.kernel_runner.full_subuin_kernel_count(kernel_urn):
                raise RuntimeError(f"Subuin {kernel_urn.sub_uin} has reached the maximum number of kernels")
            if self.kernel_runner.get_subuin_starting_job_count(kernel_urn) == 0:
                self.kernel_runner.start_kernel(kernel_urn)
            if datetime.now() - start_time > timedelta(seconds=timeout):
                raise TimeoutError(f"Start kernel with usage_id {usage_id} timeout")
            await asyncio.sleep(5)

    async def session_stop(self, usage_id: str):
        logger.info(f"KernelManagerLocal session_stop usage_id: {usage_id}")
        urn = self.kernel_assignments.pop(usage_id, None)
        if urn is not None:
            self.kernel_runner.reset_kernel(KernelURN.from_str(urn))

@ray.remote
class KernelManagerRay(KernelManagerLocal):
    def __init__(self, keep_alive_seconds=30*60, cleanup_interval=10):
        super().__init__(keep_alive_seconds=keep_alive_seconds, cleanup_interval=cleanup_interval)
        logger.info(f"KernelManagerRay init success")
        self.last_active = time.time()
        self.timeout = 60 * 60
        try:
            asyncio.get_event_loop().create_task(self._self_cleanup())
        except Exception as e:
            logger.error(f"KernelManagerRay init self_cleanup error: {e}")

    async def _self_cleanup(self):
        logger.info(f"[KernelManagerRay] start !")
        while True:
            try:
                if len(self.kernel_runner.available_kernels) + len(self.kernel_runner.busy_kernels) > 0:
                    self.last_active = time.time()
                    logger.info(f"[KernelManagerRay] 有分配内核，更新last_active")
                kernal_hosts = {}
                for urn_str in self.kernel_runner.available_kernels.keys():
                    urn = KernelURN.from_str(urn_str)
                    kernal_hosts[urn.kernel_host] = urn.kernel_type
                for urn_str in self.kernel_runner.busy_kernels.keys():
                    urn = KernelURN.from_str(urn_str)
                    kernal_hosts[urn.kernel_host] = urn.kernel_type
                for kernel_host in kernal_hosts.keys():
                    client = get_kernel_client(KernelURN(kernel_type=kernal_hosts[kernel_host], kernel_host=kernel_host))
                    kernels = client.exists_kernel()
                    logger.info(f"[KernelManagerRay] 检查kernel_host: {kernel_host}, kernels: {kernels}")
                logger.info(f"[KernelManagerRay] 检查自杀: last_active={self.last_active}, timeout={self.timeout}")
            except Exception as e:
                logger.error(f"[KernelManagerRay] 检查自杀 error: {e}")
            finally:
                await asyncio.sleep(60)
            if time.time() - self.last_active > self.timeout:
                logger.warning(f"[KernelManagerRay] 超时自杀: last_active={self.last_active}, now={time.time()}, timeout={self.timeout}")
                os._exit(0)

class KernelManager:
    def __init__(self, keep_alive_seconds=30*60, cleanup_interval=10, use_ray: bool = False):
        logger.info(f"KernelManager init use_ray: {use_ray}, keep_alive_seconds: {keep_alive_seconds}, cleanup_interval: {cleanup_interval}")
        self.use_ray = use_ray
        self.keep_alive_seconds = keep_alive_seconds
        self.cleanup_interval = cleanup_interval
        self.local_manager = None

    def get_kernel_manager(self):
        kernel_manager = None
        if self.use_ray:
            name = "jupyter_manager_" + env.IMAGE_TAG
            try:
                kernel_manager = ray.get_actor(name = name, namespace="ray")
                logger.info(f"KernelManagerRay use existing success")
            except Exception as e:
                kernel_manager = KernelManagerRay.options(
                    name = name,
                    namespace="ray",
                    lifetime="detached",
                    max_concurrency=100,
                    num_cpus=15,  # 每个Actor分配1个CPU核心
                ).remote(
                    keep_alive_seconds=self.keep_alive_seconds, cleanup_interval=self.cleanup_interval
                )
                logger.info(f"KernelManagerRay init success")
        else:
            if self.local_manager is None:
                kernel_manager = KernelManagerLocal(keep_alive_seconds=self.keep_alive_seconds, cleanup_interval=self.cleanup_interval)
                self.local_manager = kernel_manager
            else:
                kernel_manager = self.local_manager
        return kernel_manager

    async def get_assigned_urn(self, usage_id: str) -> Optional[KernelURN]:
        kernel_manager = self.get_kernel_manager()
        if self.use_ray:
            return await kernel_manager.get_assigned_urn.remote(usage_id)
        else:
            return await kernel_manager.get_assigned_urn(usage_id)

    async def session_start(self, sub_uin: str, kernel_host: str, kernel_name: str, usage_id: str, kernel_type: str = "dlc"):
        kernel_manager = self.get_kernel_manager()
        if self.use_ray:
            await kernel_manager.session_start.remote(sub_uin, kernel_host, kernel_name, usage_id, kernel_type)
        else:
            await kernel_manager.session_start(sub_uin, kernel_host, kernel_name, usage_id, kernel_type)

    async def assign_kernel(self, usage_id: str, kernel_urn: KernelURN):
        kernel_manager = self.get_kernel_manager()
        if self.use_ray:
            return await kernel_manager.assign_kernel.remote(usage_id, kernel_urn)
        else:
            return await kernel_manager.assign_kernel(usage_id, kernel_urn)

    async def start_kernel_with_timeout(self, usage_id: str, kernel_urn: KernelURN, timeout: int = 300) -> Optional[KernelURN]:
        kernel_manager = self.get_kernel_manager()
        if self.use_ray:
            try:
                return await kernel_manager.start_kernel_with_timeout.remote(usage_id, kernel_urn, timeout)
            except RayTaskError as err:
                logger.error(f"failed to start kernel with timeout, error: {err.cause}")
                raise err.cause
        else:
            return await kernel_manager.start_kernel_with_timeout(usage_id, kernel_urn, timeout)
        
    async def session_stop(self, usage_id: str):
        kernel_manager = self.get_kernel_manager()
        if self.use_ray:
            await kernel_manager.session_stop.remote(usage_id)
        else:
            await kernel_manager.session_stop(usage_id)