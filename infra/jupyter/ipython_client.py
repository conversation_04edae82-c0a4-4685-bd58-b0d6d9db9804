
from common.share import env
import ray
import time
import asyncio
import os
import subprocess
import shutil
from typing import List, Tu<PERSON>, Dict, Any
from infra.jupyter.basic import KernelURN, KernelClient, get_logger
from infra.jupyter.executor import IPythonExecutor, VenvManager
@ray.remote
class RayIPythonExecutor(IPythonExecutor, VenvManager):
    def __init__(self, urn: KernelURN):
        # 调用第一个父类 IPythonExecutor 的初始化
        super().__init__()
        # 调用第二个父类 VenvManager 的初始化
        VenvManager.__init__(self)
        self.urn = urn
        self.logger = get_logger(urn, trace_id=urn.kernel_id)
        self.logger.info(f"RayIPythonExecutor init success, urn: {urn}, venv_path: {self.venv_path}, cache_path: {self.cache_path}")
        self.last_active = time.time()
        self.timeout = 15 * 60
        try:
            asyncio.get_event_loop().create_task(self._self_cleanup())
        except Exception:
            pass

    def execute(self, code: str, timeout: int = None) -> Tuple[List[Dict[str, Any]], bool]:
        try:
            return super().execute(code, timeout)
        except Exception as e:
            self.logger.error(f"RayIPythonExecutor execute error: {e}")
            raise e
        finally:
            self.last_active = time.time()
            
    def install_requirements(self, requirements: List[str]) -> Tuple[List[Dict[str, Any]], bool]:
        outputs, has_error = super().install_requirements(requirements)
        if not has_error:
            self.add_pythonpath(self.lib_path)
        return outputs, has_error
    
    async def _self_cleanup(self):
        self.logger.info(f"[RayIPythonExecutor] start !")
        while True:
            if time.time() - self.last_active > self.timeout:
                self.logger.warning(f"[RayIPythonExecutor] 超时自杀: last_active={self.last_active}, now={time.time()}, timeout={self.timeout}")
                os._exit(0)
            else:
                self.logger.info(f"[RayIPythonExecutor] 检查自杀: last_active={self.last_active}, now={time.time()}, timeout={self.timeout}")
            await asyncio.sleep(60)

class IpythonKernelClientRay(KernelClient):
    def __init__(self, urn: KernelURN):
        super().__init__(urn)
        self.logger = get_logger(urn, trace_id=urn.kernel_id)
        self.ipython_kernel = None
        self.ray_actor_name = f"ipython_{self.urn.sub_uin}_{self.urn.kernel_id}"

    def start_kernel(self):
        if self.ipython_kernel:
            return self.urn
        try:
            actor = ray.get_actor(self.ray_actor_name, namespace="ray")
            if ray.get(actor.status_ok.remote()):
                self.ipython_kernel = actor
                self.logger.info(f"IpythonKernelClientRay get existed kernel success: {actor}")
            else:
                self.logger.info(f"IpythonKernelClientRay get existed kernel error: {actor}")
                raise Exception("IpythonKernelClientRay get existed kernel error: {actor}")
            return self.urn
        except Exception as e:
            self.logger.info(f"IpythonKernelClientRay get existed kernel error: {e}, start new kernel")
            self.ipython_kernel = RayIPythonExecutor.options(
                name=self.ray_actor_name, lifetime="detached",
                max_concurrency=100,
                namespace="ray",
                num_cpus=15,
            ).remote(self.urn)
        return self.urn
    
    def kernel_status_ready(self) -> bool:
        try:
            return ray.get_actor(self.ray_actor_name) is not None
        except Exception as e:
            self.logger.error(f"Kernel status not ready: {e}")
            return False

    def exists_kernel(self, kernel_name: str = None, kernel_id: str = None):
        try:
            actor = ray.get_actor(self.ray_actor_name)
            return [{'id': self.urn.kernel_id, 'name': kernel_name or self.urn.kernel_name}]
        except Exception:
            return []

    def execute(self, code: str, timeout: int = None) -> Tuple[List[Dict[str, Any]], bool]:
        self.start_kernel()
        if not self.ipython_kernel:
            self.logger.error(f"IpythonKernelClientRay execute error: ipython_kernel is None")
            raise Exception("IpythonKernelClientRay execute error: ipython_kernel is None")
        result, has_error = ray.get(self.ipython_kernel.execute.remote(code, timeout))
        return result['outputs'], has_error
    
    def install_requirements(self, requirements: List[str]) -> Tuple[List[Dict[str, Any]], bool]:
        self.start_kernel()
        if self.ipython_kernel:
            outputs, has_error = ray.get(self.ipython_kernel.install_requirements.remote(requirements))
            return outputs, has_error
        else:
            self.logger.error(f"IpythonKernelClientRay add_lib_path error: ipython_kernel is None")
            raise RuntimeError("IpythonKernelClientRay add_lib_path error: ipython_kernel is None")
        
    def shutdown(self):
        try:
            ray.kill(self.ipython_kernel)
            self.logger.info(f"Shutdown ipython kernel success")
        except Exception as e:
            self.logger.error(f"Shutdown ipython kernel error: {e}")

class IpythonKernelClientLocal(KernelClient):
    global_ipython_kernel = None
    
    def __init__(self, urn: KernelURN):
        super().__init__(urn)
        self.logger = get_logger(urn, trace_id=urn.kernel_id)
        self.ipython_kernel: IPythonExecutor = IpythonKernelClientLocal.global_ipython_kernel
        self.venv_manager = VenvManager()

    def install_requirements(self, requirements: List[str]) -> Tuple[List[Dict[str, Any]], bool]:
        outputs, has_error = self.venv_manager.install_requirements(requirements)
        if not has_error:
            self.ipython_kernel.add_pythonpath(self.venv_manager.lib_path)
        return outputs, has_error
    
    def start_kernel(self):
        self.logger.info(f"Start ipython kernel")
        IpythonKernelClientLocal.global_ipython_kernel = IPythonExecutor()
        self.ipython_kernel = IpythonKernelClientLocal.global_ipython_kernel
        # 为ipython内核生成一个唯一的kernel_id
        if not self.urn.has_kernel_id():
            import uuid
            self.urn.kernel_id = f"ipython_{uuid.uuid4().hex[:8]}"
        return self.urn
    
    def kernel_status_ready(self) -> bool:
        self.logger.info(f"Check ipython kernel status: {self.ipython_kernel is not None}")
        return self.ipython_kernel is not None

    def exists_kernel(self, kernel_name: str = None, kernel_id: str = None):
        if self.ipython_kernel:
            return [{'id': self.urn.kernel_id, 'name': self.urn.kernel_name}]
        return []

    def execute(self, code: str, timeout: int = None) -> Tuple[List[Dict[str, Any]], bool]:
        result, has_error = self.ipython_kernel.execute(code, timeout)
        return result['outputs'], has_error
    
    def shutdown(self):
        self.ipython_kernel.shutdown()
        self.logger.info(f"Shutdown ipython kernel success")

class IpythonKernelClient(KernelClient):
    def __init__(self, urn: KernelURN):
        super().__init__(urn)
        self.logger = get_logger(urn, trace_id=urn.kernel_id)
        self.client = IpythonKernelClientRay(urn) if env.EXECUTION_MODE_RAY else IpythonKernelClientLocal(urn)
        self.logger.info(f"IpythonKernelClient init success")
    
    def start_kernel(self):
        self.logger.info(f"Start ipython kernel")
        return self.client.start_kernel()
    
    def kernel_status_ready(self) -> bool:
        self.logger.info(f"Check ipython kernel status: {self.client.kernel_status_ready()}")
        return self.client.kernel_status_ready()

    def exists_kernel(self, kernel_name: str = None, kernel_id: str = None):
        self.logger.info(f"Check ipython kernel exists: {kernel_name}, {kernel_id}")
        return self.client.exists_kernel(kernel_name, kernel_id)

    def install_requirements(self, requirements: List[str]) -> Tuple[List[Dict[str, Any]], bool]:
        start_time = time.time()
        self.logger.info(f"Ipython kernel install requirements: {requirements}")
        outputs, has_error = self.client.install_requirements(requirements)
        self.logger.info(f"Ipython kernel install requirements finish: {outputs}, has_error: {has_error}, requirements: {requirements}, cost: {time.time() - start_time}")
        return outputs, has_error

    def execute(self, code: str, timeout: int = None) -> Tuple[List[Dict[str, Any]], bool]:
        self.logger.info(f"Ipython kernel execute: {code}")
        return self.client.execute(code, timeout)
    
    def shutdown(self):
        self.logger.info(f"Shutdown ipython kernel")
        self.client.shutdown()