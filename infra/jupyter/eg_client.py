"""An Enterprise Gateway client."""
import os
import queue
import time
import json
from threading import Thread
import traceback
from uuid import uuid4
from infra.jupyter.basic import KernelClient, KernelURN
from typing import List, Tuple, Dict, Any
from common.logger.logger import logger

import requests
import websocket

REQUEST_TIMEOUT = int(os.getenv("EG_TIMEOUT", 600))

def json_decode(data):
    return json.loads(data)

def json_encode(data):
    return json.dumps(data)

class EGGatewayClient():
    """
    An experimental Gateway Client that is used for Enterprise Gateway
    integration tests and can be leveraged for micro service type of
    connections.
    """

    def __init__(self, host="localhost:8888", timeout=REQUEST_TIMEOUT, use_secure_connection=False):
        """Initialize the client."""
        self.timeout = timeout
        self.http_api_endpoint = (
            f"https://{host}/api/kernels" if use_secure_connection else f"http://{host}/api/kernels"
        )
        self.ws_api_endpoint = (
            f"wss://{host}/api/kernels" if use_secure_connection else f"ws://{host}/api/kernels"
        )
        self.log = logger

    def start_kernel_if_not_exists(self, kernel_id, kernelspec_name, sub_uin="unknown", timeout=REQUEST_TIMEOUT, extra_env=None):
        """Start a kernel if it doesn't exist with the given kernel_id."""
        self.log.info(f"Checking if kernel {kernel_id} exists...")
        
        if len(kernel_id) == 0:
            kernels = self.list_kernels(name=kernelspec_name, debug=False)
            if len(kernels) > 0:
                self.log.info(f"Select kernel {kernels[0]['id']} from list")
                return EGKernelClient(
                    self.http_api_endpoint,
                    self.ws_api_endpoint,
                    kernels[0]["id"],
                    timeout=timeout,
                    logger=self.log,
                )
            else:
                self.log.info(f"No kernel found, starting new kernel...")
                return self.create_kernel_with_client(kernelspec_name, sub_uin, timeout, extra_env)

        # Check if kernel exists
        response = requests.get(f"{self.http_api_endpoint}/{kernel_id}", timeout=timeout)
        if response.status_code == 200:
            self.log.info(f"Kernel {kernel_id} already exists")
            return EGKernelClient(
                self.http_api_endpoint,
                self.ws_api_endpoint,
                kernel_id,
                timeout=timeout,
                logger=self.log,
            )
            
        # Kernel doesn't exist, start a new one
        self.log.info(f"Kernel {kernel_id} not found, starting new kernel...")
        return self.create_kernel_with_client(kernelspec_name, sub_uin, timeout, extra_env)
    
    def get_kernel_status(self, kernelspec_name: str, kernel_id: str):
        """Get the status of a kernel."""
        kernel = self.list_kernels(name=kernelspec_name, kernel_id=kernel_id, debug=False)
        if len(kernel) == 0:
            return None
        url = f"{self.http_api_endpoint}/{kernel_id}"
        response = requests.get(url, timeout=self.timeout)
        if response.status_code == 200:
            json = response.json()
            self.log.debug(f"Kernel {kernel_id} state: {json}")
            return json["execution_state"]
        else:
            msg = "Unexpected response retrieving state for kernel {}: {}".format(
                kernel_id, response.content
            )
            self.log.error(msg)
            return None
        
    def get_kernel(self, kernel_id: str, kernelspec_name: str):
        """Get a kernel by id."""
        kernels = []
        if len(kernels) == 0:
            kernels = self.list_kernels(name=kernelspec_name, debug=False)
        if len(kernels) > 0:
            for kernel in kernels:
                if kernel["id"] == kernel_id:
                    return EGKernelClient(
                        self.http_api_endpoint,
                        self.ws_api_endpoint,
                        kernel["id"],
                        timeout=REQUEST_TIMEOUT,
                        logger=self.log,
                    )
        return None
    
    def create_kernel_with_client(self, kernelspec_name, sub_uin="unknown", timeout=REQUEST_TIMEOUT, extra_env=None):
        kernel_id = self.create_kernel(kernelspec_name, sub_uin, timeout, extra_env)
        return EGKernelClient(
            self.http_api_endpoint,
            self.ws_api_endpoint,
            kernel_id,
            timeout=timeout,
            logger=self.log,
        )
    
    def create_kernel(
        self, kernelspec_name, sub_uin="unknown", timeout=REQUEST_TIMEOUT, extra_env=None
    ):
        """Start a kernel."""
        self.log.info(f"Starting a {kernelspec_name} kernel ....")


        if extra_env is None:
            extra_env = {}

        env = {
            "KERNEL_SUBUIN": sub_uin,
            "KERNEL_USERNAME": sub_uin,
            "KERNEL_LAUNCH_TIMEOUT": str(timeout),
        }
        env.update(extra_env)

        json_data = {
            "name": kernelspec_name,
            "env": env,
        }

        response = requests.post(self.http_api_endpoint, data=json_encode(json_data), timeout=timeout)
        if response.status_code == 201:
            json_data = response.json()
            kernel_id = json_data.get("id")
            self.log.info(f"Started kernel with id {kernel_id}")
        else:
            self.log.error(f"Error starting kernel : {response.content} code {response.status_code}")
            raise RuntimeError(str(response.content, encoding='utf-8'))
        return kernel_id

    def list_kernels(self, name=None, kernel_id=None, debug=True):
        """List all kernels."""
        if debug:
            self.log.info("Listing all kernels...")
        response = requests.get(self.http_api_endpoint, timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            kernels = response.json()
            if name:
                kernels = [kernel for kernel in kernels if kernel["name"] == name]
            if kernel_id:
                kernels = [kernel for kernel in kernels if kernel["id"] == kernel_id]
            if debug:
                self.log.info(f"Found {len(kernels)} kernels")
            return kernels
        else:
            msg = f"Error listing kernels: {response.status_code} response code\n{response.content}"
            logger.error(msg)
            return []

    def shutdown_kernel(self, kernel):
        """Shut down a kernel."""
        self.log.info(f"Shutting down kernel : {kernel.kernel_id} ....")

        if not kernel:
            return False

        kernel.shutdown()



class EGKernelClient:
    """A kernel client class."""

    DEAD_MSG_ID = "deadbeefdeadbeefdeadbeefdeadbeef"
    POST_IDLE_TIMEOUT = 0.5
    DEFAULT_INTERRUPT_WAIT = 1

    def __init__(
        self, http_api_endpoint, ws_api_endpoint, kernel_id, timeout=REQUEST_TIMEOUT, logger=None
    ):
        """Initialize the client."""
        self.shutting_down = False
        self.restarting = False
        self.http_api_endpoint = http_api_endpoint
        self.kernel_http_api_endpoint = f"{http_api_endpoint}/{kernel_id}"
        self.ws_api_endpoint = ws_api_endpoint
        self.kernel_ws_api_endpoint = f"{ws_api_endpoint}/{kernel_id}/channels"
        self.kernel_id = kernel_id
        self.log = logger
        self.timeout = timeout
        self.kernel_socket = None
        self.response_reader = Thread(target=self._read_responses, daemon=True)
        self.response_queues = {}
        self.interrupt_thread = None
        self.log.debug(f"Initializing kernel client ({kernel_id}) to {self.kernel_ws_api_endpoint}")

        try:
            self.kernel_socket = websocket.create_connection(
                f"{ws_api_endpoint}/{kernel_id}/channels", timeout=timeout, enable_multithread=True
            )
        except Exception as e:
            self.log.error(e)
            self.shutdown()
            raise e

        # startup reader thread
        self.response_reader.start()

    def shutdown(self):
        """Shut down the client."""
        # Terminate thread, close socket and clear queues.
        self.shutting_down = True

        if self.kernel_socket:
            self.kernel_socket.close()
            self.kernel_socket = None

        if self.response_queues:
            self.response_queues.clear()
            self.response_queues = None

        if self.response_reader:
            self.response_reader.join(timeout=2.0)
            if self.response_reader.is_alive():
                self.log.warning("Response reader thread is not terminated, continuing...")
            self.response_reader = None

        url = f"{self.http_api_endpoint}/{self.kernel_id}"
        response = requests.delete(url, timeout=self.timeout)
        if response.status_code == 204:
            self.log.info(f"Kernel {self.kernel_id} shutdown")
            return True
        else:
            msg = f"Error shutting down kernel {self.kernel_id}: {response.content}"
            raise RuntimeError(msg)

    def close(self):
        """Close the kernel."""
        if self.kernel_socket:
            self.kernel_socket.close()
            self.kernel_socket = None

    def __del__(self):
        """Destructor."""
        logger.info(f"Destroying kernel client {self.kernel_id}")
        self.close()

    def execute(self, code, timeout=REQUEST_TIMEOUT):
        """
        Executes the code provided and returns the result of that execution.
        """
        response = []
        has_error = False
        try:
            msg_id = self._send_request(code)

            post_idle = False
            while True:
                response_message = self._get_response(msg_id, timeout, post_idle)
                # self.log.info(f"response_message: {response_message}")
                if response_message:
                    response_message_type = response_message["msg_type"]

                    if response_message_type == "error" or (
                        response_message_type == "execute_reply"
                        and response_message["content"]["status"] == "error"
                    ):
                        has_error = True
                        response.extend(self.convert_to_notebook_output(response_message))
                    elif response_message_type == "stream":
                        response.extend(self.convert_to_notebook_output(response_message))
                    elif (
                        response_message_type == "execute_result"
                        or response_message_type == "display_data"
                    ):
                        response.extend(self.convert_to_notebook_output(response_message))
                    elif response_message_type == "status":
                        if response_message["content"]["execution_state"] == "idle":
                            post_idle = True  # indicate we're at the logical end and timeout poll for next message
                            continue
                    else:
                        self.log.debug(
                            "Unhandled response for msg_id: {} of msg_type: {}".format(
                                msg_id, response_message_type
                            )
                        )

                if (
                    response_message is None
                ):  # We timed out.  If post idle, its ok, else make mention of it
                    if not post_idle:
                        self.log.warning(
                            f"Unexpected timeout occurred for msg_id: {msg_id} - no 'idle' status received!"
                        )
                    break
        except TimeoutError as e:
            self.log.error(f"execute timeout: {e}, traceback: {traceback.format_exc()}")
            raise e

        except Exception as e:
            self.log.error(f"execute error: {e}, traceback: {traceback.format_exc()}")

        return response, has_error

    def interrupt(self):
        """Interrupt the kernel."""
        url = "{}/{}".format(self.kernel_http_api_endpoint, "interrupt")
        response = requests.post(url, timeout=self.timeout)
        if response.status_code == 204:
            self.log.debug(f"Kernel {self.kernel_id} interrupted")
            return True
        else:
            msg = f"Unexpected response interrupting kernel {self.kernel_id}: {response.content}"
            raise RuntimeError(msg)

    def restart(self, timeout=REQUEST_TIMEOUT):
        """Restart the kernel."""
        self.restarting = True
        self.kernel_socket.close()
        self.kernel_socket = None
        url = "{}/{}".format(self.kernel_http_api_endpoint, "restart")
        response = requests.post(url, timeout=self.timeout)
        if response.status_code == 200:
            self.log.debug(f"Kernel {self.kernel_id} restarted")
            self.kernel_socket = websocket.create_connection(
                self.kernel_ws_api_endpoint, timeout=timeout, enable_multithread=True
            )
            self.restarting = False
            return True
        else:
            self.restarting = False
            msg = f"Unexpected response restarting kernel {self.kernel_id}: {response.content}"
            self.log.debug(msg)
            raise RuntimeError(msg)

    def get_state(self):
        """Get the state of the client."""
        url = f"{self.kernel_http_api_endpoint}"
        response = requests.get(url, timeout=self.timeout)
        if response.status_code == 200:
            json = response.json()
            self.log.debug(f"Kernel {self.kernel_id} state: {json}")
            return json["execution_state"]
        else:
            msg = "Unexpected response retrieving state for kernel {}: {}".format(
                self.kernel_id, response.content
            )
            raise RuntimeError(msg)

    def start_interrupt_thread(self, wait_time=DEFAULT_INTERRUPT_WAIT):
        """Start the interrupt thread."""
        self.interrupt_thread = Thread(target=self.perform_interrupt, args=(wait_time,), daemon=True)
        self.interrupt_thread.start()

    def perform_interrupt(self, wait_time):
        """Perform an interrupt on the client."""
        time.sleep(wait_time)  # Allow parent to start executing cell to interrupt
        self.interrupt()

    def terminate_interrupt_thread(self):
        """Terminate the interrupt thread."""
        if self.interrupt_thread:
            self.interrupt_thread.join()
            self.interrupt_thread = None

    def _send_request(self, code):
        """
        Builds the request and submits it to the kernel.  Prior to sending the request it
        creates an empty response queue and adds it to the dictionary using msg_id as the
        key.  The msg_id is returned in order to read responses.
        """
        msg_id = uuid4().hex
        message = EGKernelClient.__create_execute_request(msg_id, code)

        # create response-queue and add to map for this msg_id
        self.response_queues[msg_id] = queue.Queue()

        self.kernel_socket.send(message)

        return msg_id

    def _get_response(self, msg_id, timeout, post_idle):
        """
        Pulls the next response message from the queue corresponding to msg_id.  If post_idle is true,
        the timeout parameter is set to a very short value since a majority of time, there won't be a
        message in the queue.  However, in cases where a race condition occurs between the idle status
        and the execute_result payload - where the two are out of order, then this will pickup the result.
        """

        if post_idle and timeout > EGKernelClient.POST_IDLE_TIMEOUT:
            timeout = (
                EGKernelClient.POST_IDLE_TIMEOUT
            )  # overwrite timeout to small value following idle messages.

        msg_queue = self.response_queues.get(msg_id)
        try:
            self.log.debug(f"Getting response for msg_id: {msg_id} with timeout: {timeout}")
            response = msg_queue.get(timeout=timeout)
            self.log.debug(
                "Got response for msg_id: {}, msg_type: {}".format(
                    msg_id, response["msg_type"] if response else "null"
                )
            )
        except queue.Empty:
            response = None

        return response

    def _read_responses(self):
        """
        Reads responses from the websocket.  For each response read, it is added to the response queue based
        on the messages parent_header.msg_id.  It does this for the duration of the class's lifetime until its
        shutdown method is called, at which time the socket is closed (unblocking the reader) and the thread
        terminates.  If shutdown happens to occur while processing a response (unlikely), termination takes
        place via the loop control boolean.
        """
        try:
            while not self.shutting_down:
                try:
                    raw_message = self.kernel_socket.recv()
                    response_message = json_decode(raw_message)

                    msg_id = EGKernelClient._get_msg_id(response_message, self.log)

                    if msg_id not in self.response_queues:
                        # this will happen when the msg_id is generated by the server
                        self.response_queues[msg_id] = queue.Queue()

                    # insert into queue
                    self.log.debug(
                        "Inserting response for msg_id: {}, msg_type: {}".format(
                            msg_id, response_message["msg_type"]
                        )
                    )
                    self.response_queues.get(msg_id).put_nowait(response_message)
                except BaseException as be1:
                    if (
                        self.restarting
                    ):  # If restarting, wait until restart has completed - which includes new socket
                        i = 1
                        while self.restarting:
                            if i >= 10 and i % 2 == 0:
                                self.log.debug(f"Still restarting after {i} secs...")
                            time.sleep(1)
                            i += 1
                        continue
                    raise be1

        except websocket.WebSocketConnectionClosedException:
            pass  # websocket closure most likely due to shutdown

        except BaseException as be2:
            if not self.shutting_down:
                self.log.warning(f"Unexpected exception encountered ({be2})")

        self.log.debug("Response reader thread exiting...")

    @staticmethod
    def _get_msg_id(message, logger):
        msg_id = EGKernelClient.DEAD_MSG_ID
        if message:
            if "msg_id" in message["parent_header"] and message["parent_header"]["msg_id"]:
                msg_id = message["parent_header"]["msg_id"]
            elif "msg_id" in message:
                # msg_id may not be in the parent_header, see if present in response
                # IPython kernel appears to do this after restarts with a 'starting' status
                msg_id = message["msg_id"]
        else:  # Dump the "dead" message...
            logger.debug(f"+++++ Dumping dead message: {message}")
        return msg_id

    @staticmethod
    def _convert_raw_response(raw_response_message):
        """Convert raw response message to a string."""
        result = str(raw_response_message)
        result = result.replace("u'", "")[:-1]

        return result

    @staticmethod
    def convert_to_notebook_output(response_message):
        """
        Convert kernel response messages to notebook format outputs.
        
        Args:
            response_message (list): response messages from kernel execution
            
        Returns:
            list: List of notebook format outputs
        """
        notebook_outputs = []
        msg = response_message
        if isinstance(msg, str):
            # Handle error messages
            if ":" in msg:
                parts = msg.split(":", 2)
                if len(parts) == 3:
                    notebook_outputs.append({
                        "ename": parts[0],
                        "evalue": parts[1],
                        "traceback": parts[2].split("\n"),
                        "output_type": "error"
                    })
            else:
                # Handle plain text output
                notebook_outputs.append({
                    "name": "stdout",
                    "output_type": "stream",
                    "text": [msg]
                })
        elif isinstance(msg, dict):

            if msg["msg_type"] == "execute_reply" and msg["content"]["status"] == "error" :
                notebook_outputs.append({
                    "ename": msg.get("content", {}).get("ename",""),
                    "evalue": msg.get("content", {}).get("evalue",""),
                    "traceback": msg.get("content", {}).get("traceback",""),
                    "output_type": "error"
                })
            # Handle structured messages
            if msg.get("msg_type") == "stream":
                notebook_outputs.append({
                    "name": msg.get("content", {}).get("name", "stdout"),
                    "output_type": "stream",
                    "text": [msg.get("content", {}).get("text", "")]
                })
            elif msg.get("msg_type") in ["execute_result", "display_data"]:
                output = {
                    "output_type": msg.get("msg_type"),
                    "data": msg.get("content", {}).get("data", {}),
                    "metadata": msg.get("content", {}).get("metadata", {})
                }
                if msg.get("msg_type") == "execute_result":
                    output["execution_count"] = msg.get("content", {}).get("execution_count")
                notebook_outputs.append(output)
        if len(notebook_outputs) == 0:
            logger.info(f"output not valid for {response_message}")
        return notebook_outputs

    @staticmethod
    def __create_execute_request(msg_id, code):
        return json_encode(
            {
                "header": {
                    "sub_uin": "",
                    "version": "5.0",
                    "session": "",
                    "msg_id": msg_id,
                    "msg_type": "execute_request",
                },
                "parent_header": {},
                "channel": "shell",
                "content": {
                    "code": "".join(code),
                    "silent": False,
                    "store_history": False,
                    "user_expressions": {},
                    "allow_stdin": False,
                },
                "metadata": {},
                "buffers": {},
            }
        )


class DLCKernelClient(KernelClient):
    def __init__(self, urn: KernelURN, timeout=REQUEST_TIMEOUT):
        super().__init__(urn, timeout)
        self.client = EGGatewayClient(host=urn.kernel_host, timeout=timeout)
        self.timeout = timeout or REQUEST_TIMEOUT
    
    def kernel_status_ready(self) -> bool:
        status = self.client.get_kernel_status(self.urn.kernel_name, self.urn.kernel_id)
        return status in ["busy", "idle", "starting"]
    
    def execute(self, code: str, timeout: int = None) -> Tuple[List[Dict[str, Any]], bool]:
        try:
            client = self.client.get_kernel(self.urn.kernel_id, self.urn.kernel_name)
            return client.execute(code, timeout or self.timeout)
        except Exception as e:
            logger.error(f"Error executing cmd for kernel {self.urn.to_str()}: {e}")
            raise RuntimeError(f"Error executing cmd for kernel {self.urn.to_str()}: {e}")
        finally:
            if client:
                client.close()
    
    def shutdown(self):
        return self.client.shutdown_kernel(self)
    
    def exists_kernel(self, kernel_name: str = None, kernel_id: str = None):
        return self.client.list_kernels(kernel_name, kernel_id, debug=False)
    
    def start_kernel(self):
        urn = self.urn
        urn.kernel_id = self.client.create_kernel(urn.kernel_name, urn.sub_uin, self.timeout)
        return urn

if __name__ == "__main__":
    client = EGGatewayClient(host="lb-2ohhzn5i-egfmcuxsj56dh1op.clb.cq-tencentclb.com:8888")
    kernel = client.get_kernel("20a278f3-3c0d-4c83-bb32-8f97af89f82c","rg-io2c6sp3k4")
    print(kernel.get_state())
    print(kernel.execute("print('1');"))
    # kernel.close()
    # kernel = client.start_kernel_if_not_exists("xxx", kernelspec_name="rg-f84jezrs7i", sub_uin="100041916576", timeout=1)
    # kernel.shutdown()
    print(client.list_kernels())
    # print(kernel.execute("print('1');"))
    # print(client.get_kernel_status("rg-io2c6sp3k4","20a278f3-3c0d-4c83-bb32-8f97af89f82c"))
#     print(client.get_kernel_status("rg-gc48qclfke",""))