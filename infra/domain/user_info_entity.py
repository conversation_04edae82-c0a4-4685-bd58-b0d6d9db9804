from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class UserInfo(BaseModel):
    """用户信息实体类"""
    sub_account_uin: str  # 用户ID
    jupyter_host: str  # Jupyter主机地址
    created_at: Optional[datetime] = None  # 创建时间
    updated_at: Optional[datetime] = None  # 更新时间
    deleted_at: Optional[datetime] = None  # 删除时间

    class Config:
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S") if v else None
        }
