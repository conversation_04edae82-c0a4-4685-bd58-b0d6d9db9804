from datetime import timezone, datetime
from typing import Optional

from pydantic import BaseModel


class UserSearchConfig(BaseModel):
    """用户搜索配置模型"""
    app_id: str
    search_type: int
    recall_num: int
    embedding_weight: float
    rerank_status: int
    create_time: datetime = datetime.now(timezone.utc)
    update_time: Optional[datetime] = None

    class Config:
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }