from pydantic import BaseModel
from datetime import datetime
from typing import Optional


class AgentList(BaseModel):
    """代理列表实体类"""
    agent_id: str  # 代理ID
    agent_name: Optional[str] = None  # 代理名称
    agent_type: Optional[str] = None  # 类型
    agent_version: Optional[str] = None  # 版本号
    description: Optional[str] = None  # 描述信息
    created_at: Optional[datetime] = None  # 创建时间
    updated_at: Optional[datetime] = None  # 更新时间

    class Config:
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S") if v else None
        }
