from pydantic import BaseModel
from datetime import datetime
from typing import Optional


class UserCount(BaseModel):
    """代理列表实体类"""
    app_id: str  # 主用户ID
    sub_account_uin: str  # 子用户ID
    session_id: str      # 会话ID
    record_id: str  # 会话记录ID
    create_time: Optional[datetime] = None  # 创建时间

    class Config:
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S") if v else None
        }
