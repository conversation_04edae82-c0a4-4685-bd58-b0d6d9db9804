from abc import ABC, abstractmethod
from typing import Optional
from infra.domain.user_info_entity import UserInfo
from common.share import context


class IUserInfoPort(ABC):
    """用户信息端口接口"""

    @abstractmethod
    def get_by_sub_account_uin(self, ctx: context.Context, sub_account_uin: str) -> Optional[UserInfo]:
        """根据用户ID获取用户信息"""
        pass

    @abstractmethod
    def create_or_update(self, ctx: context.Context, entity: UserInfo) -> bool:
        """创建或更新用户信息"""
        pass
