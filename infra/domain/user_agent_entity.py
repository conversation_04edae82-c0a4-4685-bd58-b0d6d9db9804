from pydantic import BaseModel
from datetime import datetime
from typing import Optional


class UserAgentVersion(BaseModel):
    """用户代理版本实体类"""
    app_id: str  # 主账号 ID
    sub_account_uin: str  # 用户ID
    agent_id: str  # 代理ID
    agent_name: str  # 用户代理名称
    agent_version: str  # 版本号
    release_date: Optional[datetime] = None  # 发布日期
    description: Optional[str] = None  # 描述信息
    created_at: Optional[datetime] = None  # 创建时间
    updated_at: Optional[datetime] = None  # 更新时间
    deleted_at: Optional[datetime] = None  # 删除时间

    class Config:
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S") if v else None
        }
