from abc import ABC, abstractmethod
from typing import Optional, List
from infra.domain.user_session_entity import UserSession
from common.share import context

class IUserSessionPort(ABC):
    """用户会话端口接口"""

    @abstractmethod
    def create(self, ctx: context.Context, entity: UserSession) -> bool:
        """创建用户会话"""
        pass

    @abstractmethod
    def get_by_id(self, ctx: context.Context) -> Optional[UserSession]:
        """根据用户ID和会话ID获取会话信息"""
        pass

    @abstractmethod
    def get_all_by_user(self, ctx: context.Context) -> List[UserSession]:
        """获取用户所有会话"""
        pass

    @abstractmethod
    def update_session_info(self, ctx: context.Context, db_info: str, run_record: str) -> bool:
        """更新会话信息"""
        pass
