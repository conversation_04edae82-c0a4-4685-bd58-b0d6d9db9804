from abc import ABC, abstractmethod
from typing import Optional, List
from infra.domain.knowledge_base_entity import KnowledgeBase
from common.share import context


class KnowledgeBasePort(ABC):
    """知识库端口接口"""

    @abstractmethod
    def get_by_id(self, ctx: context.Context, knowledge_id: str) -> Optional[KnowledgeBase]:
        """根据ID获取知识库信息"""
        pass

    @abstractmethod
    def get_all(self, ctx: context.Context) -> List[KnowledgeBase]:
        """获取所有知识库列表"""
        pass

    @abstractmethod
    def create_or_update(self, ctx: context.Context, entity: KnowledgeBase) -> bool:
        """创建或更新知识库信息"""
        pass

    @abstractmethod
    def insert(self, ctx: context.Context, entity: KnowledgeBase) -> bool:
        """插入知识库信息
        Args:
            ctx: 上下文
            entity: 知识库信息实体
        Returns:
            bool: 是否成功
        """
        pass

    @abstractmethod
    def delete_by_id(self, ctx: context.Context, knowledge_id: str) -> bool:
        """删除知识库信息
        Args:
            ctx: 上下文
            knowledge_id: 知识库ID
        Returns:
            bool: 是否成功
        """
        pass
