from typing import Optional
from pydantic import BaseModel
from datetime import datetime, timezone


class TaskList(BaseModel):
    task_id: str
    knowledge_base_id: str
    file_id: str
    file_url:str
    app_id: Optional[str] = None
    task_type: int
    status: int
    node_id: Optional[str] = None
    task_params: str
    task_result: Optional[str] = None
    create_time: datetime = datetime.now(timezone.utc)
    update_time: Optional[datetime] = None

    class Config:
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }
