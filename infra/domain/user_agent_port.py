from abc import ABC, abstractmethod
from typing import Optional, List
from infra.domain.user_agent_entity import UserAgentVersion
from common.share import context


class IUserAgentPort(ABC):
    """用户代理版本端口接口"""

    @abstractmethod
    def add_version(self, ctx: context.Context, entity: UserAgentVersion) -> bool:
        """添加用户代理版本"""
        pass

    @abstractmethod
    def get_latest_by_user(self, ctx: context.Context) -> Optional[UserAgentVersion]:
        """获取用户最新代理版本"""
        pass

    @abstractmethod
    def get_all_versions(self, ctx: context.Context) -> List[UserAgentVersion]:
        """获取用户所有代理版本"""
        pass
