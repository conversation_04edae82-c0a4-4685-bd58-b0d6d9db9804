class BaseEntity:
    """base entity"""
    """
    arbitrary number of arguments
    *initial_data  pass as tuple to function; single param is dict
    **kwargs       pass as dict to function
    """

    def __init__(self, *initial_data, **kwargs):
        for dictionary in initial_data:
            for key in dictionary:
                setattr(self, key, dictionary[key])
        for key in kwargs:
            setattr(self, key, kwargs[key])

    def check_constraint(self, constraint: dict, allow_missing=False, must=[]):
        """check if the entity is valid"""
        for key, cons in constraint.items():
            field = self.__dict__.get(key, "")
            if key in must and not field:
                return False

            if allow_missing and not field:
                continue

            if len(field) < cons[0] or len(field) > cons[1]:
                return False

        return True
