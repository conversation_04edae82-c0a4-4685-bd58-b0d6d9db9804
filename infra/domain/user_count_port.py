from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional
from infra.domain.user_count_entity import UserCount


class UserCountPort(ABC):
    """用户计数表端口接口"""

    # ==== 原代码 ====
    @abstractmethod
    def create(self, user_count: UserCount) -> bool:
        """创建用户计数记录
        Args:
            user_count: 用户计数实体
        Returns:
            bool: 是否创建成功
        """
        pass

    @abstractmethod
    def get_by_user(self, app_id: str, sub_account_uin: str) -> List[UserCount]:
        """获取用户的所有计数记录
        Args:
            app_id: 应用ID
            sub_account_uin: 子用户ID
        Returns:
            List[UserCount]: 用户计数记录列表
        """
        pass
    # ... 省略其他代码 ...


    # ==== 修改后代码 ====
    @abstractmethod
    def create(self, user_count: UserCount) -> bool:
        """创建用户计数记录
        Args:
            user_count: 用户计数实体
        Returns:
            bool: 是否创建成功
        """
        pass

    @abstractmethod
    def get_by_user(self, app_id: str, sub_account_uin: str) -> List[UserCount]:
        """获取用户的所有计数记录
        Args:
            app_id: 应用ID
            sub_account_uin: 子用户ID
        Returns:
            List[UserCount]: 用户计数记录列表
        """
        pass

    @abstractmethod
    def get_by_session(self, app_id: str, sub_account_uin: str, session_id: str) -> List[UserCount]:
        """获取指定会话的计数记录
        Args:
            app_id: 应用ID
            sub_account_uin: 子用户ID
            session_id: 会话ID
        Returns:
            List[UserCount]: 用户计数记录列表
        """
        pass
