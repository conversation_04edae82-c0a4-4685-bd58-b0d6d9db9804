from typing import Optional
from pydantic import BaseModel
from datetime import datetime, timezone


class TaskNode(BaseModel):
    node_id: str
    last_heartbeat: datetime = datetime.now()
    is_active: int = 1
    create_time: datetime = datetime.now()
    update_time: Optional[datetime] = datetime.now()

    class Config:
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }