from abc import ABC, abstractmethod
from typing import List, <PERSON><PERSON>

from peewee import Expression

from common.share import context
from infra.domain.knowledge_list_entity import Knowledge


class KnowledgeListPort(ABC):
    """代理列表端口接口"""

    @abstractmethod
    def get_knowledge_list(self, ctx: context.Context, filter_conditions: List[Expression],
                           sort_conditions: List[Expression],
                           offset: int, limit: int) -> Tuple[int, List[Knowledge]]:
        """获取代理列表"""
        pass
