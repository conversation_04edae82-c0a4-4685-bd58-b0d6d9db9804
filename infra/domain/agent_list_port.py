from abc import ABC, abstractmethod
from typing import Optional, List
from infra.domain.agent_list_entity import AgentList
from common.share import context


class AgentListPort(ABC):
    """代理列表端口接口"""

    @abstractmethod
    def get_by_id(self, ctx: context.Context, agent_id: str) -> Optional[AgentList]:
        """根据ID获取代理信息"""
        pass

    @abstractmethod
    def get_all(self, ctx: context.Context) -> List[AgentList]:
        """获取所有代理列表"""
        pass

    @abstractmethod
    def create_or_update(self, ctx: context.Context, entity: AgentList) -> bool:
        """创建或更新代理信息"""
        pass

    @abstractmethod
    def insert(self, ctx: context.Context, entity: AgentList) -> bool:
        """插入代理信息
        Args:
            ctx: 上下文
            entity: 代理信息实体
        Returns:
            bool: 是否成功
        """
        pass

    @abstractmethod
    def delete_by_id(self, ctx: context.Context, agent_id: str) -> bool:
        """删除代理信息
        Args:
            ctx: 上下文
            agent_id: 代理ID
        Returns:
            bool: 是否成功
        """
        pass
