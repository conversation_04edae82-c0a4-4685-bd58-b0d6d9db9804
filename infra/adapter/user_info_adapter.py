from datetime import datetime
from typing import Optional
from common.share import context
from peewee import DoesNotExist
from common.database.database import MysqlPool
from infra.domain.user_info_entity import UserInfo
from common.logger.logger import logger


class UserInfoAdapter:
    """用户信息适配器"""

    def __init__(self, persistence: MysqlPool):
        self.persistence = persistence

    def get_by_sub_account_uin(self, ctx: context.Context, sub_account_uin: str) -> Optional[UserInfo]:
        """根据用户ID获取用户信息"""
        logger.info(f"get_by_sub_account_uin sub_account_uin: {sub_account_uin}")
        model = self.persistence.models['user_info']
        with self.persistence.pool_db:
            try:
                record = model.select().where(
                    (model.sub_account_uin == sub_account_uin) &
                    (model.deleted_at.is_null())
                ).get()

                return UserInfo(
                    sub_account_uin=record.sub_account_uin,
                    jupyter_host=record.jupyter_host,
                    created_at=record.created_at,
                    updated_at=record.updated_at,
                )
            except DoesNotExist:
                return None

    def create_or_update(self, ctx: context.Context, entity: UserInfo) -> bool:
        """创建或更新用户信息"""
        logger.info(f"create_or_update entity: {entity}")
        model = self.persistence.models['user_info']
        with self.persistence.pool_db:
            try:
                now = datetime.now()
                # 如果是新建记录，设置创建时间和更新时间
                if not entity.created_at:
                    entity.created_at = now
                # 总是更新修改时间
                entity.updated_at = now

                existing = model.get_or_none(sub_account_uin=entity.sub_account_uin)
                if existing:
                    # 更新操作
                    query = model.update(
                        jupyter_host=entity.jupyter_host,
                        updated_at=entity.updated_at
                    ).where(model.sub_account_uin == entity.sub_account_uin)
                    query.execute()
                else:
                    # 创建操作
                    model.create(
                        sub_account_uin=entity.sub_account_uin,
                        jupyter_host=entity.jupyter_host,
                        created_at=entity.created_at,
                        updated_at=entity.updated_at,
                        deleted_at=None
                    )
                return True
            except Exception as e:
                logger.error(f"操作用户信息失败: {e}")
                return False
