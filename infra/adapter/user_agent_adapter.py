from typing import Optional, List
from common.share import context
from peewee import DoesNotExist
from common.database.database import MysqlPool
from infra.domain.user_agent_entity import UserAgentVersion
from common.logger.logger import logger
from datetime import datetime


class UserAgentAdapter:
    """用户代理版本适配器"""

    def __init__(self, persistence: MysqlPool):
        self.persistence = persistence

    def add_version(self, ctx: context.Context, entity: UserAgentVersion) -> bool:
        """添加用户代理版本记录"""
        model = self.persistence.models['user_agent_versions']
        with self.persistence.pool_db:
            try:
                model.create(
                    app_id=entity.app_id,
                    sub_account_uin=entity.sub_account_uin,
                    agent_id=entity.agent_id,
                    agent_name=entity.agent_name,
                    agent_version=entity.agent_version,
                    description=entity.description,
                    create_at=datetime.now(),
                    updated_at=datetime.now(),
                    deleted_at=None
                )
                return True
            except Exception as e:
                logger.error(f"添加用户代理版本失败: {e}")
                return False

    def get_latest_by_user(self, ctx: context.Context) -> Optional[UserAgentVersion]:
        """获取用户最新代理版本"""
        model = self.persistence.models['user_agent_versions']
        app_id_condition = get_app_id_condition(model, ctx)
        with self.persistence.pool_db:
            try:
                where_clause = [
                    (model.sub_account_uin == ctx.sub_account_uin),
                    (model.deleted_at.is_null())
                ]
                if app_id_condition is not None:
                    where_clause.append(app_id_condition)
                record = model.select().where(*where_clause).order_by(model.created_at.desc()).limit(1).get()

                return UserAgentVersion(
                    app_id=record.app_id,
                    sub_account_uin=record.sub_account_uin,
                    agent_id=record.agent_id,
                    agent_name=record.agent_name,
                    agent_version=record.agent_version,
                    description=record.description,
                    created_at=record.created_at,
                    updated_at=record.updated_at,
                )
            except DoesNotExist:
                logger.debug(f"用户[{ctx.sub_account_uin}]没有代理版本记录")
                return None
            except Exception as e:
                logger.error(f"查询用户[{ctx.sub_account_uin}]最新代理版本失败: {str(e)}", exc_info=True)
                return None

    def get_all_versions(self, ctx: context.Context) -> List[UserAgentVersion]:
        """获取用户所有代理版本"""
        model = self.persistence.models['user_agent_versions']
        results = []
        app_id_condition = get_app_id_condition(model, ctx)
        with self.persistence.pool_db:
            where_clause = [
                (model.sub_account_uin == ctx.sub_account_uin),
                (model.deleted_at.is_null())
            ]
            if app_id_condition is not None:
                where_clause.append(app_id_condition)
            query = model.select().where(*where_clause).order_by(model.created_at.desc())

            for record in query:
                results.append(UserAgentVersion(
                    app_id=record.app_id,
                    sub_account_uin=record.sub_account_uin,
                    agent_id=record.agent_id,
                    agent_name=record.agent_name,
                    agent_version=record.agent_version,
                    description=record.description,
                    created_at=record.created_at,
                    updated_at=record.updated_at,
                    deleted_at=record.deleted_at
                ))
        return results


def get_app_id_condition(model, ctx):
    app_id = getattr(ctx, 'app_id', None)
    if app_id:
        return (model.app_id == app_id) | (model.app_id == '')
    else:
        return None
