from typing import Optional, List
from common.share.context import Context
from peewee import DoesNotExist
from common.database.database import MysqlPool
from infra.domain.agent_list_entity import Agent<PERSON>ist
from infra.domain.agent_list_port import AgentListPort
from common.logger.logger import logger


class AgentListAdapter(AgentListPort):
    """代理列表适配器"""

    def __init__(self, persistence: MysqlPool):
        self.persistence = persistence

    def get_by_id(self, ctx: Context, agent_id: str) -> Optional[AgentList]:
        """根据ID获取代理信息"""
        model = self.persistence.models['agent_list']
        with self.persistence.pool_db:
            try:
                record = model.select().where(
                    model.agent_id == agent_id
                ).get()

                return AgentList(
                    agent_id=record.agent_id,
                    agent_name=record.agent_name,
                    agent_type=record.agent_type,
                    agent_version=record.agent_version,
                    description=record.description,
                    CreatedAt=record.created_at,
                    UpdatedAt=record.updated_at
                )
            except DoesNotExist:
                return None

    def get_all(self, ctx: Context) -> List[AgentList]:
        """获取所有代理列表"""
        model = self.persistence.models['agent_list']
        results = []
        with self.persistence.pool_db:
            query = model.select().order_by(model.created_at.desc())

            for record in query:
                results.append(AgentList(
                    agent_id=record.agent_id,
                    agent_name=record.agent_name,
                    agent_type=record.agent_type,
                    agent_version=record.agent_version,
                    description=record.description,
                    created_at=record.created_at,
                    updated_at=record.updated_at
                ))
        return results

    def create_or_update(self, ctx: Context, entity: AgentList) -> bool:
        """创建或更新代理信息"""
        model = self.persistence.models['agent_list']
        with self.persistence.pool_db:
            try:
                model.replace(
                    agent_id=entity.agent_id,
                    agent_name=entity.agent_name,
                    agent_type=entity.agent_type,
                    agent_version=entity.agent_version,
                    description=entity.description
                ).execute()
                return True
            except Exception as e:
                print(f"操作代理信息失败: {e}")
                return False

    def insert(self, ctx: Context, entity: AgentList) -> bool:
        """插入代理信息
        Args:
            ctx: 上下文
            entity: 代理信息实体
        Returns:
            bool: 是否成功
        """
        model = self.persistence.models['agent_list']
        with self.persistence.pool_db:
            try:
                # 检查是否已存在
                if self.get_by_id(ctx, entity.agent_id) is not None:
                    print(f"代理ID {entity.agent_id} 已存在")
                    return False
                
                # 插入新记录
                model.insert(
                    agent_id=entity.agent_id,
                    agent_name=entity.agent_name,
                    agent_type=entity.agent_type,
                    agent_version=entity.agent_version,
                    description=entity.description,
                    created_at=entity.created_at,
                    updated_at=entity.updated_at
                ).execute()
                return True
            except Exception as e:
                print(f"插入代理信息失败: {e}")
                return False

    def delete_by_id(self, ctx: Context, agent_id: str) -> bool:
        """删除代理信息
        Args:
            ctx: 上下文
            agent_id: 代理ID
        Returns:
            bool: 是否成功
        """
        model = self.persistence.models['agent_list']
        with self.persistence.pool_db:
            try:
                # 检查是否存在
                if self.get_by_id(ctx, agent_id) is None:
                    print(f"代理ID {agent_id} 不存在")
                    return False
                
                # 删除记录
                model.delete().where(model.agent_id == agent_id).execute()
                return True
            except Exception as e:
                print(f"删除代理信息失败: {e}")
                return False
