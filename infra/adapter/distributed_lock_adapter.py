from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
from peewee import IntegrityError, DoesNotExist

from common.database.database import MysqlPool
from common.logger.logger import logger
from infra.domain.distributed_lock_entity import DistributedLock


class DistributedLockAdapter:
    def __init__(self, persistence: MysqlPool):
        self.persistence = persistence
        self.model = persistence.models['distributed_lock']

    def create(self, entity: DistributedLock) -> bool:
        model = self.persistence.models['distributed_lock']
        with self.persistence.pool_db:
            try:
                model.create(
                    lock_name=entity.lock_name,
                    locked_by=entity.locked_by,
                    locked_time=entity.locked_time,
                    expired_time=entity.expired_time
                )
                return True
            except IntegrityError:
                logger.debug(f"锁 {entity.lock_name} 已存在")
                return False
            except Exception as e:
                logger.error(f"创建锁失败: {e}")
                return False

    def get_by_id(self, lock_name: str) -> Optional[DistributedLock]:
        model = self.persistence.models['distributed_lock']
        with self.persistence.pool_db:
            try:
                record = model.get(model.lock_name == lock_name)
                return DistributedLock(
                    lock_name=record.lock_name,
                    locked_by=record.locked_by,
                    locked_time=record.locked_time,
                    expired_time=record.expired_time
                )
            except DoesNotExist:
                return None
            except Exception as e:
                logger.error(f"获取锁失败: {e}")
                return None

    def update_expired_and_owner(self, expired_time: datetime, lock_name: str, locked_by: str) -> bool:
        model = self.persistence.models['distributed_lock']
        with self.persistence.pool_db:
            try:
                query = model.update(expired_time=expired_time).where(
                    (model.lock_name == lock_name) & (
                            (model.expired_time < datetime.now()) |
                            (model.locked_by == locked_by)
                    )
                )
                return query.execute() > 0
            except Exception as e:
                logger.error(f"更新锁失败: {e}")
                return False

    def delete_by_id(self, lock_name: str) -> bool:
        model = self.persistence.models['distributed_lock']
        with self.persistence.pool_db:
            try:
                if self.get_by_id(lock_name) is None:
                    logger.debug(f"锁 {lock_name} 不存在")
                    return False
                return model.delete().where(model.lock_name == lock_name).execute() > 0
            except Exception as e:
                logger.error(f"删除锁失败: {e}")
                return False

    def acquire(self, lock_name: str, locked_by: str, timeout_seconds: int = 30) -> bool:
        expired_time = datetime.now() + timedelta(seconds=timeout_seconds)
        entity = DistributedLock(
            lock_name=lock_name,
            locked_by=locked_by,
            locked_time=datetime.now(),
            expired_time=expired_time
        )

        if self.create(entity):
            return True

        if self.update_expired_and_owner(expired_time, lock_name, locked_by):
            updated_lock = self.get_by_id(lock_name)
            return updated_lock and updated_lock.locked_by == locked_by
        return False

    def create_lock(self, lock_name: str, locked_by: str, timeout: int):
        expired_time = datetime.now() + timedelta(seconds=timeout)
        try:
            self.model.create(
                lock_name=lock_name,
                locked_by=locked_by,
                locked_time=datetime.now(),
                expired_time=expired_time
            )
            return True
        except IntegrityError:
            logger.debug(f"锁 {lock_name} 已存在")
            return False

    def renew_lock(self, lock_name: str, locked_by: str, timeout: int):
        expired_time = datetime.now() + timedelta(seconds=timeout)
        updated = self.model.update(expired_time=expired_time).where(
            (self.model.lock_name == lock_name) &
            (self.model.locked_by == locked_by)
        ).execute()
        return updated > 0

    def release_lock(self, lock_name: str, locked_by: str):
        with self.persistence.pool_db:
            try:
                lock = self.model.get(self.model.lock_name == lock_name)
                if lock.locked_by != locked_by:
                    logger.warning(f"无权限释放锁 {lock_name}（持有者不匹配）")
                    return False
                lock.delete_instance()
                return True
            except DoesNotExist:
                logger.debug(f"锁 {lock_name} 已不存在")
                return True

    def is_lock_owner(self, lock_name: str, locked_by: str):
        with self.persistence.pool_db:
            try:
                lock = self.model.get(self.model.lock_name == lock_name)
                return lock.locked_by == locked_by and lock.expired_time > datetime.now()
            except DoesNotExist:
                return False

    def acquire_lock(self, lock_name: str, locked_by: str, timeout: int = 30):
        with self.persistence.pool_db:
            if self.create_lock(lock_name, locked_by, timeout):
                logger.debug(f"获取锁 {lock_name} 成功（创建）")
                return True

            lock = self.model.get_or_none(self.model.lock_name == lock_name)
            if not lock or lock.expired_time < datetime.now():
                result = self.model.replace(
                    lock_name=lock_name,
                    locked_by=locked_by,
                    locked_time=datetime.now(),
                    expired_time=datetime.now() + timedelta(seconds=timeout)
                ).execute()
                logger.info(f"获取锁 {lock_name} 成功（抢占过期锁）")
                return result > 0
            elif lock.locked_by == locked_by:
                if self.renew_lock(lock_name, locked_by, timeout):
                    logger.info(f"锁 {lock_name} 续约成功")
                    return True

            logger.debug(f"获取锁 {lock_name} 失败")
            return False
