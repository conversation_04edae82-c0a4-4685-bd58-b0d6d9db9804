from datetime import datetime, timedelta, timezone
from typing import Optional

from peewee import DoesNotExist

from common.database.database import MysqlPool
from common.logger.logger import logger
from infra.domain.task_node_entity import TaskNode  # 假设存在这个实体类


class TaskNodeAdapter:

    def __init__(self, persistence: MysqlPool):
        self.persistence = persistence

    def register_node(self, node_id: str) -> bool:
        """
        注册节点并记录初始心跳

        Args:
            node_id: 节点唯一标识

        Returns:
            是否注册成功
        """
        model = self.persistence.models['task_nodes']
        with self.persistence.pool_db:
            try:
                # 使用get_or_create避免重复插入
                node, created = model.get_or_create(
                    node_id=node_id,
                    defaults={
                        'last_heartbeat': datetime.now(),
                        'is_active': True,
                        'create_time': datetime.now()
                    }
                )

                # 如果节点已存在，更新心跳
                if not created:
                    node.last_heartbeat = datetime.now()
                node.is_active = True
                node.save()

                return True
            except Exception as e:
                logger.error(f"注册节点失败: {e}")
                return False

    def update_node_heartbeat(self, node_id: str) -> bool:
        """
        更新节点的心跳时间，标记节点为活跃状态

        Args:
            node_id: 节点唯一标识

        Returns:
            更新是否成功
        """
        model = self.persistence.models['task_nodes']
        with self.persistence.pool_db:
            try:
                query = (model
                         .update({
                    'last_heartbeat': datetime.now(),
                    'is_active': 1
                })
                         .where(model.node_id == node_id))

                rows_affected = query.execute()
                return rows_affected > 0
            except Exception as e:
                logger.error(f"更新节点心跳失败: {e}")
                return False

    def mark_node_inactive(self, node_id: str) -> bool:
        """
        标记节点为不活跃状态

        Args:
            node_id: 节点唯一标识

        Returns:
            是否标记成功
        """
        model = self.persistence.models['task_nodes']
        with self.persistence.pool_db:
            try:
                query = (model
                         .update({
                    'is_active': 0,
                    'update_time': datetime.now()
                })
                         .where(model.node_id == node_id))

                return query.execute() > 0
            except Exception as e:
                logger.error(f"标记节点不活跃失败: {e}")
                return False

    def get_node_status(self, node_id: str) -> Optional[TaskNode]:
        """
        获取节点状态

        Args:
            node_id: 节点唯一标识

        Returns:
            节点状态实体或None
        """
        model = self.persistence.models['task_nodes']
        with self.persistence.pool_db:
            try:
                node = model.get(model.node_id == node_id)
                return TaskNode(
                    node_id=node.node_id,
                    last_heartbeat=node.last_heartbeat,
                    is_active=node.is_active,
                    create_time=node.create_time,
                    update_time=node.update_time
                )
            except DoesNotExist:
                return None

    def get_inactive_nodes(self, heartbeat_timeout: int) -> list:
        """
        获取所有不活跃的节点

        Args:
            heartbeat_timeout: 心跳超时时间（秒）

        Returns:
            不活跃节点ID列表
        """
        model = self.persistence.models['task_nodes']
        timeout_threshold = datetime.now() - timedelta(seconds=heartbeat_timeout)

        with self.persistence.pool_db:

            try:
                nodes = (model
                .select(model.node_id)
                .where(
                    (model.last_heartbeat < timeout_threshold) |
                    (model.is_active == 0)
                ))

                return [node.node_id for node in nodes]
            except Exception as e:
                logger.error(f"获取不活跃节点失败: {e}")
                return []
