from typing import Optional, List
from common.share.context import Context
from peewee import DoesNotExist
from common.database.database import MysqlPool
from infra.domain.knowledge_base_entity import KnowledgeBase
from infra.domain.knowledge_base_port import KnowledgeBasePort
from common.logger.logger import logger


class KnowledgeBaseAdapter(KnowledgeBasePort):
    """知识库适配器"""

    def __init__(self, persistence: MysqlPool):
        self.persistence = persistence

    def get_by_id(self, ctx: Context, knowledge_id: str) -> Optional[KnowledgeBase]:
        """根据ID获取知识库信息"""
        model = self.persistence.models['knowledge_base']
        with self.persistence.pool_db:
            try:
                record = model.select().where(
                    model.id == knowledge_id
                ).get()

                return KnowledgeBase(
                    id=record.id,
                    name=record.name
                )
            except DoesNotExist:
                return None

    def get_all(self, ctx: Context) -> List[KnowledgeBase]:
        """获取所有知识库列表"""
        model = self.persistence.models['knowledge_base']
        results = []
        with self.persistence.pool_db:
            query = model.select().order_by(model.create_time.desc())

            for record in query:
                results.append(KnowledgeBase(
                    id=record.id,
                    name=record.name
                ))
        return results

    def create_or_update(self, ctx: Context, entity: KnowledgeBase) -> bool:
        """创建或更新知识库信息"""
        model = self.persistence.models['knowledge_base']
        with self.persistence.pool_db:
            try:
                model.replace(
                    id=entity.id,
                    name=entity.name
                ).execute()
                return True
            except Exception as e:
                logger.error(f"操作知识库信息失败: {e}")
                return False

    def insert(self, ctx: Context, entity: KnowledgeBase) -> bool:
        """插入知识库信息
        Args:
            ctx: 上下文
            entity: 知识库信息实体
        Returns:
            bool: 是否成功
        """
        model = self.persistence.models['knowledge_base']
        with self.persistence.pool_db:
            try:
                # 检查是否已存在
                if self.get_by_id(ctx, entity.id) is not None:
                    logger.warning(f"知识库ID {entity.id} 已存在")
                    return False

                # 插入新记录
                model.insert(
                    id=entity.id,
                    name=entity.name
                ).execute()
                return True
            except Exception as e:
                logger.error(f"插入知识库信息失败: {e}")
                return False

    def delete_by_id(self, ctx: Context, knowledge_id: str) -> bool:
        """删除知识库信息
        Args:
            ctx: 上下文
            knowledge_id: 知识库ID
        Returns:
            bool: 是否成功
        """
        model = self.persistence.models['knowledge_base']
        with self.persistence.pool_db:
            try:
                # 删除记录
                model.delete().where(model.id == knowledge_id).execute()
                return True
            except Exception as e:
                logger.error(f"删除知识库信息失败: {e}")
                return False
