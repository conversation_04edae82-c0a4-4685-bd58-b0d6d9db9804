from datetime import datetime
from peewee import DoesNotExist
from common.database.database import MysqlPool
from common.logger.logger import logger
from infra.domain.user_search_config_entity import UserSearchConfig


class UserSearchConfigAdapter:
    """用户搜索配置适配器"""

    def __init__(self, persistence: MysqlPool):
        self.persistence = persistence
        self.default_config = UserSearchConfig(
            app_id="",
            search_type=0,
            recall_num=5,
            embedding_weight=0.5,
            rerank_status=0,
            create_time=datetime.now(),
            update_time=datetime.now()
        )

    def create(self, app_id: str, search_type: int = 0, recall_num: int = 10,
               embedding_weight: float = 0.5, rerank_status: int = 0) -> bool:
        """
        创建用户搜索配置

        Args:
            app_id: 用户ID
            search_type: 搜索类型(0-混合,1-向量,2-全文)
            recall_num: 召回数量
            embedding_weight: 向量权重
            rerank_status: 重排序状态(0-开启,1-关闭)

        Returns:
            bool: 是否创建成功
        """
        model = self.persistence.models['user_search_config']
        with self.persistence.pool_db:
            try:
                model.create(
                    app_id=app_id,
                    search_type=search_type,
                    recall_num=recall_num,
                    embedding_weight=embedding_weight,
                    rerank_status=rerank_status
                )
                return True
            except Exception as e:
                logger.error(f"创建用户搜索配置失败: {e}")
                return False

    def update(self, app_id: str, **kwargs) -> bool:
        """
        更新用户搜索配置

        Args:
            app_id: 用户ID
            **kwargs: 可更新字段(search_type, recall_num, embedding_weight, rerank_status)

        Returns:
            bool: 是否更新成功
        """
        model = self.persistence.models['user_search_config']
        with self.persistence.pool_db:
            try:
                # 检查记录是否存在
                if not model.select().where(model.app_id == app_id).exists():
                    logger.warning(f"用户配置不存在，自动创建: {app_id}")
                    return self.create(
                        app_id=app_id,
                        **kwargs
                    )

                query = model.update(
                    **kwargs,
                    update_time=datetime.now()
                ).where(model.app_id == app_id)
                query.execute()
                return True
            except Exception as e:
                logger.error(f"更新用户搜索配置失败: {e}")
                return False

    def get_by_app_id(self, app_id: str) -> UserSearchConfig:
        """
        获取用户搜索配置

        Args:
            app_id: 用户ID

        Returns:
            UserSearchConfig: 配置信息，不存在返回默认配置
        """
        model = self.persistence.models['user_search_config']
        with self.persistence.pool_db:
            try:
                record = model.select().where(model.app_id == app_id).get()
                return UserSearchConfig(
                    app_id=record.app_id,
                    search_type=record.search_type,
                    recall_num=record.recall_num,
                    embedding_weight=record.embedding_weight,
                    rerank_status=record.rerank_status,
                    create_time=record.create_time,
                    update_time=record.update_time
                )
            except DoesNotExist:
                return self.default_config
            except Exception as e:
                logger.error(f"查询用户搜索配置失败: {e}")
                return self.default_config
