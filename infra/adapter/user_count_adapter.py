from typing import List, Optional
from common.database.database import MysqlPool
from common.share.context import Context
from common.logger.logger import logger
from infra.domain.user_count_entity import UserCount


class UserCountAdapter:
    """用户计数表适配器"""

    def __init__(self, mysql_pool: MysqlPool):
        self.mysql_pool = mysql_pool
        self.model = self.mysql_pool.models['user_count']

    def create(self, ctx: Context, user_count: UserCount) -> bool:
        """创建用户计数记录
        Args:
            ctx: 上下文对象
            user_count: 用户计数实体
        Returns:
            bool: 是否创建成功
        """
        with self.mysql_pool.pool_db:
            try:
                self.model.create(
                    app_id=user_count.app_id,
                    sub_account_uin=user_count.sub_account_uin,
                    session_id=user_count.session_id,
                    record_id=user_count.record_id,
                    create_time=user_count.create_time
                )
                return True
            except Exception as e:
                logger.error(f"创建用户计数记录失败: {e}", exc_info=True)
                return False

    def get_by_user(self, ctx: Context, app_id: str, sub_account_uin: str) -> List[UserCount]:
        """获取用户的所有计数记录
        Args:
            ctx: 上下文对象
            app_id: 应用ID
            sub_account_uin: 子用户ID
        Returns:
            List[UserCount]: 用户计数记录列表
        """
        with self.mysql_pool.pool_db:
            try:
                query = self.model.select().where(
                    (self.model.app_id == app_id) &
                    (self.model.sub_account_uin == sub_account_uin)
                ).order_by(self.model.create_time.desc())

                return [
                    UserCount(
                        app_id=record.app_id,
                        sub_account_uin=record.sub_account_uin,
                        session_id=record.session_id,
                        record_id=record.record_id,
                        create_time=record.create_time
                    ) for record in query
                ]
            except Exception as e:
                logger.error(f"查询用户计数记录失败: {e}", exc_info=True)
                return []

    def get_statistics(self, ctx: Context) -> dict:
        """获取用户的统计信息
        Args:
            ctx: 上下文对象
        Returns:
            dict: 包含统计信息的字典:
                - total_count: 主账号下的总记录数
                - sub_account_count: 当前子账号的记录数
                - session_count: 指定会话的记录数 (当传入session_id时)
                - session_counts: 当前子账号下按会话分组的记录数 (当未传入session_id时)
        """
        with self.mysql_pool.pool_db:
            try:
                # 主账号下的总记录数
                total_count = self.model.select().where(
                    self.model.app_id == ctx.app_id
                ).count()

                # 当前子账号的记录数
                sub_account_count = self.model.select().where(
                    (self.model.app_id == ctx.app_id) &
                    (self.model.sub_account_uin == ctx.sub_account_uin)
                ).count()

                result = {
                    'total_count': total_count,
                    'sub_account_count': sub_account_count
                }

                # 查询指定会话的记录数
                session_count = self.model.select().where(
                    (self.model.app_id == ctx.app_id) &
                    (self.model.sub_account_uin == ctx.sub_account_uin) &
                    (self.model.session_id == ctx.session_id)
                ).count()
                result['session_count'] = session_count

                return result
            except Exception as e:
                logger.error(f"获取用户统计信息失败: {e}", exc_info=True)
                return {
                    'total_count': 0,
                    'sub_account_count': 0,
                    'session_count': 0 if ctx.session_id else {},
                }
