from datetime import datetime, timezone
from typing import Dict
from typing import Optional, List

from pydantic import BaseModel

from common.database.es_operator import ESOperator
from common.logger.logger import logger
from common.share.config import appConfig, ESConfig
from common.trace.trace import traceable

class SessionData(BaseModel):
    sub_account_uin: str
    session_id: str
    agent_id: str
    record_id: str
    create_time: datetime = datetime.now(timezone.utc)
    session_status: Optional[int] = 1

    # 可选字段
    update_time: Optional[datetime] = None
    question: str = ""
    answer: Optional[str] = None
    final_summary: Optional[str] = None
    knowledge_base_ids: Optional[List[str]] = None
    thinking_chain: Optional[str] = None
    task_list: Optional[str] = None
    task_list_dict: Optional[dict] = None
    context: Optional[str] = None
    error: Optional[str] = None
    feedback: Optional[int] = 0
    trace_id: Optional[str] = None
    db_info: Optional[str] = None
    error_context: Optional[str] = None

    class Config:
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }


class ChatESOperator(ESOperator):
    def __init__(self, config: ESConfig):
        super().__init__(config)
        self._ensure_index_exists()

    @classmethod
    def get_instance(cls, app_id: str) -> 'ChatESOperator':
        """
        获取指定app_id的ChatESOperator实例（带缓存）

        Args:
            app_id: 应用/用户ID
        Returns:
            ChatESOperator实例（相同app_id返回缓存实例）
        """
        if not hasattr(cls, "_instance_cache"):
            cls._instance_cache = {}
        app_id = app_id or "none"
        if app_id not in cls._instance_cache:
            modified_config = appConfig.memory.es.model_copy()
            modified_config.index_name = f"{modified_config.index_name}_{app_id}"
            cls._instance_cache[app_id] = ChatESOperator(modified_config)

        return cls._instance_cache[app_id]

    def _ensure_index_exists(self):
        mapping = {
            "settings": {
                "number_of_shards": 5,
                "refresh_interval": "1s"
            },
            "mappings": {
                "dynamic": "true",
                "properties": {
                    "sub_account_uin": {"type": "keyword"},
                    "session_id": {"type": "keyword"},
                    "record_id": {"type": "keyword"},
                    "agent_id": {"type": "keyword"},
                    "create_time": {"type": "date", "format": "strict_date_optional_time||epoch_millis"},
                    "update_time": {"type": "date", "format": "strict_date_optional_time||epoch_millis"},
                    "session_status": {"type": "integer"},
                    "question": {"type": "text"},
                    "answer": {"type": "text"},
                    "thinking_chain": {"type": "text"},
                    "task_list": {"type": "text"},
                    "context": {"type": "text"},
                    "final_summary": {"type": "text"},
                    "knowledge_base_ids": {"type": "text"},
                    "error": {"type": "text"},
                    "trace_id": {"type": "keyword"},
                    "db_info": {"type": "keyword"},
                    "error_context": {"type": "keyword"},
                    "feedback": {"type": "integer"}
                }
            }
        }
        try:
            if not self.sync_client.indices.exists(index=self.config.index_name):
                self.sync_client.indices.create(
                    index=self.config.index_name,
                    body=mapping
                )
                logger.info("索引创建成功")
        except Exception as e:
            logger.error(f"索引操作失败: {e}")
            raise

    @ESOperator._retry_decorator
    def save_chat_record(self, chat_record: Dict) -> str:
        response = self.sync_client.index(
            index=self.config.index_name,
            document=chat_record
        )
        return response["_id"]

    @ESOperator._retry_decorator
    def get_chat_records(self, sub_account_uin: str, session_id: str, agent_id: str) -> List[Dict]:
        query = {
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"sub_account_uin": sub_account_uin}},
                        {"term": {"session_id": session_id}},
                        {"term": {"agent_id": agent_id}}
                    ]
                }
            }
        }
        response = self.sync_client.search(
            index=self.config.index_name,
            body=query
        )
        return [hit["_source"] for hit in response["hits"]["hits"]]

    @ESOperator._retry_decorator
    def get_chat_by_record(self, sub_account_uin: str, record_id: str) -> List[Dict]:
        query = {
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"sub_account_uin": sub_account_uin}},
                        {"term": {"record_id": record_id}}
                    ]
                }
            }
        }
        response = self.sync_client.search(
            index=self.config.index_name,
            body=query
        )
        return [hit["_source"] for hit in response["hits"]["hits"]]

    @ESOperator._retry_decorator
    def save_session_record(self, chat_record: SessionData) -> str:
        response = self.sync_client.index(
            index=self.config.index_name,
            document=chat_record.model_dump(
                exclude_none=True,
            )
        )
        return response["_id"]

    @ESOperator._retry_decorator
    def get_session_records(self, sub_account_uin: str, session_id: str) -> List[SessionData]:
        # def get_session_records(self, sub_account_uin: str, session_id: str, agent_id: str) -> List[SessionData]:
        query = {
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"sub_account_uin": sub_account_uin}},
                        {"term": {"session_id": session_id}},
                        # {"term": {"agent_id": agent_id}},
                        {
                            "range": {
                                "create_time": {
                                    "gte": "now-3M",
                                    "lte": "now"
                                }
                            }
                        }
                    ]
                }
            },
            "sort": [
                {
                    "create_time": {
                        "order": "asc"
                    }
                }
            ]
        }
        response = self.sync_client.search(
            index=self.config.index_name,
            body=query
        )
        return [SessionData(**hit["_source"]) for hit in response["hits"]["hits"]]

    @ESOperator._async_retry_decorator
    @traceable(name="async_save_chat_record")
    async def async_save_chat_record(self, chat_record: SessionData) -> str:
        response = await self.async_client.index(
            index=self.config.index_name,
            document=chat_record.model_dump(
                exclude_none=True,
            )
        )
        return response["_id"]

    @ESOperator._async_retry_decorator
    async def async_get_chat_records(self, sub_account_uin: str, session_id: str, agent_id: str) -> List[SessionData]:
        query = {
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"sub_account_uin": sub_account_uin}},
                        {"term": {"session_id": session_id}},
                        {"term": {"agent_id": agent_id}},
                        {
                            "range": {
                                "create_time": {
                                    "gte": "now-3M",
                                    "lte": "now"
                                }
                            }
                        }
                    ]
                }
            },
            "sort": [
                {
                    "create_time": {
                        "order": "asc"
                    }
                }
            ]
        }
        response = await self.async_client.search(
            index=self.config.index_name,
            body=query
        )
        return [SessionData(**hit["_source"]) for hit in response["hits"]["hits"]]

    @ESOperator._retry_decorator
    def update_feedback_by_record_id(
            self,
            record_id: str,
            feedback: int
    ) -> bool:
        try:
            # 构建更新请求体（保持原有逻辑）
            update_body = {
                "query": {"term": {"record_id": record_id}},

                "script": {
                    "lang": "painless",
                    "params": {"new_feedback": feedback, "update_time": datetime.now(timezone.utc).isoformat()},
                    "source": """
                          ctx._source.feedback = params.new_feedback;
                          ctx._source.update_time = params.update_time;
                        """
                }
            }

            # 执行更新操作
            response = self.sync_client.update_by_query(
                index=self.config.index_name,
                body=update_body,
                refresh=True
            )
            logger.info(f"update feedback success：{response}")
            return True
        except Exception as e:
            logger.error(f"update feedback exception：{str(e)}")
            # 其他未知异常（如网络问题）
            return False

    @ESOperator._retry_decorator
    def delete_chat_record(self, sub_account_uin: str, session_id: str, record_id: str) -> bool:
        """
        根据sub_account_uin, session_id和record_id删除聊天记录

        Args:
            sub_account_uin: 用户ID
            session_id: 会话ID
            record_id: 记录ID

        Returns:
            是否删除成功
        """
        try:
            query = {
                "query": {
                    "bool": {
                        "filter": [
                            {"term": {"sub_account_uin": sub_account_uin}},
                            {"term": {"session_id": session_id}},
                            {"term": {"record_id": record_id}}
                        ]
                    }
                }
            }

            response = self.sync_client.delete_by_query(
                index=self.config.index_name,
                body=query,
                refresh=True
            )

            logger.info(f"删除记录成功: {response}")
            return True

        except Exception as e:
            logger.error(f"删除记录失败: {e}")
            return False

    @ESOperator._async_retry_decorator
    @traceable(name="async_delete_chat_record")
    async def async_delete_chat_record(self, sub_account_uin: str, session_id: str, record_id: str) -> bool:
        """
        异步删除聊天记录

        Args:
            sub_account_uin: 用户ID
            session_id: 会话ID
            record_id: 记录ID

        Returns:
            是否删除成功
        """
        try:
            query = {
                "query": {
                    "bool": {
                        "filter": [
                            {"term": {"sub_account_uin": sub_account_uin}},
                            {"term": {"session_id": session_id}},
                            {"term": {"record_id": record_id}}
                        ]
                    }
                }
            }

            response = await self.async_client.delete_by_query(
                index=self.config.index_name,
                body=query,
                refresh=True
            )

            logger.info(f"异步删除记录成功: {response}")
            return True

        except Exception as e:
            logger.error(f"异步删除记录失败: {e}")
            return False

#chat_es_operator = ChatESOperator(appConfig.memory.es)
if __name__ == "__main__":

    chat_es_operator = ChatESOperator.get_instance("aisearch")

    # # 测试获取聊天记录
    # tid = chat_es_operator.save_session_record(
    #     SessionData(
    #         sub_account_uin="user_test1",
    #         session_id="session456",
    #         record_id="record123",
    #         agent_id="agent789",
    #         question="你好",
    #         answer="你好",
    #         thinking_chain="",
    #         task_list="",
    #         trace_id="trace1234",
    #     )
    # )
    # print(tid)
    mock_records = chat_es_operator.get_session_records("user_test1", "session456")
    print(mock_records)
    for record in mock_records:
        print(record)
    # delete
    # chat_es_operator.delete_chat_record("user_test1", "session456", "record123")
    #chat_es_operator.update_feedback_by_trace_id("trace1234", 1)
