import uuid
from datetime import datetime, timezone
from typing import Optional, List, Union

from elasticsearch.dsl.query import Script
from openai import OpenAI
from pydantic import BaseModel

from common.database.es_operator import ESOperator
from common.logger.logger import logger
from common.share.config import appConfig, ESConfig


class Scope(BaseModel):
    level1: Optional[str] = None
    level2: Optional[str] = None
    level3: Optional[str] = None


class SemanticData(BaseModel):
    app_id: str
    term_id: str
    term: Optional[str] = None
    synonyms: Optional[List[str]] = None
    definition: Optional[str] = None
    scope: Optional[List[Scope]] = None
    update_time: Optional[datetime] = None
    create_time: Optional[datetime] = None

    class Config:
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }


class SemanticDataByScore(SemanticData):
    score: float


class SemanticSearchRequest(BaseModel):
    app_id: str
    query: str  # 原始查询文本
    top_k: int = 10  # 最大返回结果数
    min_score: Optional[float] = 0.5  # 最小匹配分数阈值
    level1: str = "*"  # 权限层级1（库）
    level2: str = "*"  # 权限层级2（表）
    level3: Union[str, List[str]] = "*"  # 权限层级3（列）- 支持单个值或多个值


class SemanticOperator(ESOperator):
    def __init__(self, config: ESConfig):
        super().__init__(config)
        self._ensure_index_exists()

    @classmethod
    def get_instance(cls, app_id: str) -> 'SemanticOperator':
        """
        获取指定app_id的ChatESOperator实例（带缓存）

        Args:
            app_id: 应用/用户ID
        Returns:
            ChatESOperator实例（相同app_id返回缓存实例）
        """
        if not hasattr(cls, "_instance_cache"):
            cls._instance_cache = {}
        app_id = app_id or "none"
        if app_id not in cls._instance_cache:
            modified_config = appConfig.memory.es.model_copy()
            modified_config.index_name = "semantic"
            modified_config.index_name = f"{modified_config.index_name}_{app_id}"
            cls._instance_cache[app_id] = SemanticOperator(modified_config)

        return cls._instance_cache[app_id]

    def _ensure_index_exists(self):
        mapping = {
            "settings": {
                "number_of_shards": 1,
                "refresh_interval": "1s"
            },
            "mappings": {
                "dynamic": "true",
                "properties": {
                    "app_id": {"type": "keyword"},
                    "term_id": {"type": "keyword"},
                    "term": {"type": "text"},
                    "term_vectors": {
                        "type": "dense_vector",
                        "dims": 1024,
                        "index": True,
                        "similarity": "cosine",
                    },
                    "synonyms": {"type": "text"},
                    "synonyms_vectors": {
                        "type": "nested",
                        "properties": {
                            "name": {"type": "keyword"},
                            "embedding": {
                                "type": "dense_vector",
                                "dims": 1024,
                                "index": True,
                                "similarity": "cosine"
                            }
                        }
                    },
                    "definition": {"type": "text"},
                    "definition_vectors": {
                        "type": "dense_vector",
                        "dims": 1024,
                        "index": True,
                        "similarity": "cosine",
                    },
                    "scope": {
                        "type": "nested",
                        "properties": {
                            "level1": {"type": "keyword"},
                            "level2": {"type": "keyword"},
                            "level3": {"type": "keyword"}
                        }
                    },
                    "create_time": {"type": "date", "format": "strict_date_optional_time||epoch_millis"},
                    "update_time": {"type": "date", "format": "strict_date_optional_time||epoch_millis"},
                }
            }
        }
        try:
            if not self.sync_client.indices.exists(index=self.config.index_name):
                self.sync_client.indices.create(
                    index=self.config.index_name,
                    body=mapping
                )
                logger.info("索引创建成功")
        except Exception as e:
            logger.error(f"索引操作失败: {e}")
            raise

    def build_permission_dsl(self, level1: str, level2: str, level3: Union[str, List[str]],engine_type="") -> dict:
        """
        构建支持权限继承的嵌套查询DSL（原生字典实现）
        :param engine_type:
        :param level1: 库名（如"A"或"*"）
        :param level2: 表名（如"B"或"*"）
        :param level3: 列名（如"C"或["C1", "C2"]或"*"）- 支持单个值或多个值
        :return: Elasticsearch DSL字典
        """
        # 处理level3为列表的情况
        level3_list = []
        if isinstance(level3, str):
            level3_list = [level3]
        elif isinstance(level3, list):
            level3_list = level3
        else:
            level3_list = ["*"]

        # 添加全局权限缓存
        if level1 == "*" and level2 == "*" and ("*" in level3_list):
            return {
                "nested": {
                    "path": "scope",
                    "query": {"term": {"scope.level1": "*"}}
                }
            }

        # 为每个level3值构建权限条件
        all_should_clauses = []

        for level3_value in level3_list:
            # 定义权限覆盖链：精确 → 表级 → 库级 → 全局
            if "dlc" == engine_type.lower():
                cases = [
                    {"scope.level2": level2, "scope.level3": level3_value},  # 精确权限 A.B.C
                    {"scope.level2": level2, "scope.level3": "*"},  # 表级权限 A.B.*
                    {"scope.level2": "*", "scope.level3": "*"},  # 库级权限 A.*.*
                    {"scope.level1": "*", "scope.level2": "*", "scope.level3": "*"}  # 全局权限 *.*.*
                ]
            else:
                cases = [
                    {"scope.level1": level1, "scope.level2": level2, "scope.level3": level3_value},  # 精确权限 A.B.C
                    {"scope.level1": level1, "scope.level2": level2, "scope.level3": "*"},  # 表级权限 A.B.*
                    {"scope.level1": level1, "scope.level2": "*", "scope.level3": "*"},  # 库级权限 A.*.*
                    {"scope.level1": "*", "scope.level2": "*", "scope.level3": "*"}  # 全局权限 *.*.*
                ]

            level3_should_clauses = []
            for case in cases:
                # 构建每个权限条件的must子句
                must_clauses = [{"term": {k: v}} for k, v in case.items()]

                # 构建nested查询结构
                nested_query = {
                    "nested": {
                        "path": "scope",
                        "query": {
                            "bool": {"must": must_clauses}
                        }
                    }
                }
                level3_should_clauses.append(nested_query)

            # 将当前level3值的所有权限条件组合
            all_should_clauses.extend(level3_should_clauses)

        return {"bool": {"should": all_should_clauses}}

    @ESOperator._async_retry_decorator
    async def async_get_semantics(self, semantic_request: SemanticSearchRequest) -> List[SemanticDataByScore]:
        # 1. 生成查询向量
        query_vector = embedding_texts([semantic_request.query])[0]

        # 2. 设置向量和文本匹配的权重
        embedding_weight = 0.7  # 可调整，范围 [0, 1]
        text_weight = 1 - embedding_weight

        # 3. 构建查询
        query = {
            "min_score": semantic_request.min_score,
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"app_id": semantic_request.app_id}},
                        self.build_permission_dsl(semantic_request.level1, semantic_request.level2,
                                                  semantic_request.level3)
                    ],
                    "should": [
                        # 文本匹配查询
                        {
                            "match": {
                                "term": {
                                    "query": semantic_request.query,
                                    "boost": text_weight
                                }
                            }
                        },
                        {
                            "match": {
                                "synonyms": {
                                    "query": semantic_request.query,
                                    "boost": text_weight
                                }
                            }
                        },
                        # KNN向量匹配查询 - term_vectors字段
                        {
                            "knn": {
                                "field": "term_vectors",
                                "query_vector": query_vector,
                                "k": semantic_request.top_k,
                                "num_candidates": min(100, semantic_request.top_k * 2),  # 平衡性能和精度
                                "boost": embedding_weight
                            }
                        },
                        # KNN向量匹配查询 - synonyms_vectors.embedding字段
                        {
                            "nested": {
                                "path": "synonyms_vectors",
                                "query": {
                                    "knn": {
                                        "field": "synonyms_vectors.embedding",
                                        "query_vector": query_vector,
                                        "k": semantic_request.top_k,
                                        "num_candidates": min(100, semantic_request.top_k * 2),
                                    }
                                },
                                "score_mode": "max",
                                "boost": embedding_weight
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            },
            "sort": [{"_score": {"order": "desc"}}, {"create_time": {"order": "asc"}}],
            "size": semantic_request.top_k,
            "track_scores": True
        }

        # 4. 执行搜索并解析结果
        response = await self.async_client.search(
            index=self.config.index_name,
            body=query
        )

        return [
            SemanticDataByScore(
                **hit["_source"],
                score=hit.get("_score", 0.0),
            )
            for hit in response["hits"]["hits"]
        ]

    @ESOperator._retry_decorator
    def get_semantics_with_pagination(self, app_id: str, page: int = 1, page_size: int = 10) -> dict:
        """
        同步查询语义配置列表，支持分页，并返回总数和分页结果

        Args:
            app_id: 应用ID
            page: 页码（从1开始）
            page_size: 每页数量

        Returns:
            {
                "total": 总数,
                "data": 分页后的语义配置列表
            }
        """
        query = {
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"app_id": app_id}},
                    ]
                }
            },
            "from": (page - 1) * page_size,
            "size": page_size,
            "sort": [{"create_time": {"order": "desc"}}],
            "track_total_hits": True  # 确保返回总数
        }
        response = self.sync_client.search(
            index=self.config.index_name,
            body=query
        )
        return {
            "total": response["hits"]["total"]["value"],
            "data": [SemanticData(**hit["_source"]) for hit in response["hits"]["hits"]]
        }

    @ESOperator._retry_decorator
    def save_semantic_data(self, semantic_data: SemanticData) -> str:
        """
        保存语义数据（自动生成文档 ID）

        Returns:
            文档的 _id
        """
        try:
            # 转换 Pydantic 模型为字典，并处理日期格式
            doc_body = semantic_data.model_dump(exclude_none=True)

            # 合并所有需要向量化的文本
            texts_to_embed = [semantic_data.term, semantic_data.definition]
            if semantic_data.synonyms:
                texts_to_embed.extend(semantic_data.synonyms)

            # 批量调用嵌入函数
            all_vectors = embedding_texts(texts_to_embed)

            # 分配结果
            doc_body['term_vectors'] = all_vectors[0]
            doc_body['definition_vectors'] = all_vectors[1]

            # 处理同义词向量
            if semantic_data.synonyms:
                synonym_vectors = all_vectors[2:]  # 从第三个元素开始是同义词的向量
                doc_body["synonyms_vectors"] = [
                    {
                        "name": synonym,
                        "embedding": vector
                    }
                    for synonym, vector in zip(semantic_data.synonyms, synonym_vectors)
                ]

            # 执行索引操作（自动生成 ID）
            response = self.sync_client.index(
                index=self.config.index_name,
                document=doc_body,
                refresh=True  # 立即刷新更新后的文档
            )
            logger.info(f"保存语义数据成功，自动生成 ID: {response['_id']}")
            return response["_id"]
        except Exception as e:
            logger.error(f"保存语义数据失败: {e}")
            raise

    @ESOperator._retry_decorator
    @ESOperator._retry_decorator
    def update_semantic_data(self, app_id: str, term_id: str, update_data: SemanticData) -> int:
        """
        根据 term_id 和 app_id 更新文档字段

        Returns:
            更新的文档数量（0 或 1）
        """
        # 构建查询条件
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"app_id": app_id}},
                        {"term": {"term_id": term_id}}
                    ]
                }
            }
        }

        # 准备需要向量化的文本
        texts_to_embed = []
        vector_fields = {}  # 记录每个向量字段对应的文本在列表中的索引

        # 收集需要向量化的文本
        if update_data.term:
            vector_fields["term_vectors"] = len(texts_to_embed)
            texts_to_embed.append(update_data.term)

        if update_data.definition:
            vector_fields["definition_vectors"] = len(texts_to_embed)
            texts_to_embed.append(update_data.definition)

        if update_data.synonyms:
            synonym_start_idx = len(texts_to_embed)
            texts_to_embed.extend(update_data.synonyms)
            vector_fields["synonyms_vectors"] = (synonym_start_idx, len(update_data.synonyms))

        # 批量生成向量（如果有需要向量化的文本）
        if texts_to_embed:
            all_vectors = embedding_texts(texts_to_embed)
        else:
            all_vectors = []

        # 构建更新数据
        doc_body = update_data.model_dump(exclude_none=True)

        # 设置向量字段
        for field, index_info in vector_fields.items():
            if field == "synonyms_vectors":
                # 处理同义词向量
                start_idx, count = index_info
                doc_body[field] = [
                    {"name": synonym, "embedding": vector}
                    for synonym, vector in zip(
                        update_data.synonyms,
                        all_vectors[start_idx: start_idx + count]
                    )
                ]
            else:
                # 处理单个文本向量
                doc_body[field] = all_vectors[index_info]

        # 构建更新脚本
        update_body = {
            "script": {
                "source": "ctx._source.putAll(params)",
                "params": doc_body
            },
            "query": query["query"]
        }

        try:
            # 使用 update_by_query API 更新匹配的文档
            response = self.sync_client.update_by_query(
                index=self.config.index_name,
                body=update_body,
                refresh=True  # 立即刷新更新后的文档
            )
            logger.info(f"更新语义数据成功，匹配 {response['total']} 个文档，更新 {response['updated']} 个")
            return response["updated"]

        except Exception as e:
            logger.error(f"更新语义数据失败: {e}")
            raise

    @ESOperator._retry_decorator
    def delete_semantic_datas(self, app_id: str, term_ids: List[str]) -> int:
        """根据 term_id 和 app_id 批量删除文档

        Args:
            app_id: 应用ID
            term_ids: 术语ID列表

        Returns:
            删除的文档数量
        """
        if not term_ids:
            return 0  # 空列表直接返回

        # 构建查询条件（使用 terms 而非 term）
        query = {
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"app_id": app_id}},
                        {"terms": {"term_id": term_ids}}  # 修正为 terms 查询
                    ]
                }
            }
        }
        try:
            response = self.sync_client.delete_by_query(
                index=self.config.index_name,
                body=query,
                refresh=True  # 立即刷新更新后的文档
            )
            logger.info(f"删除语义数据成功，匹配 {response['total']} 个文档，删除 {response['deleted']} 个")
            return response["deleted"]

        except Exception as e:
            logger.error(f"删除语义数据失败: {e}")
            raise


def embedding_texts(texts: list[str]):
    nl2sqlconfig = appConfig.common.llm_embedding
    logger.info(f"embedding_texts model: {nl2sqlconfig.model_name}")
    try:
        client = OpenAI(base_url=nl2sqlconfig.base_url, api_key=nl2sqlconfig.api_key)
        embeddings_rsp = client.embeddings.create(model=nl2sqlconfig.model_name, input=texts).data
        rsp = []
        for embedding in embeddings_rsp:
            rsp.append(embedding.embedding)
        return rsp
    except Exception as e:
        logger.error(f"When call embeddings model has error,model:{nl2sqlconfig.model_name},text:{texts}, error: {e} ")
        raise e


if __name__ == "__main__":
    # 初始化操作器
    operator = SemanticOperator.get_instance("test_app")

    # 测试数据
    datas = [
        SemanticData(
            app_id =  "test_app",
            term="人工智能",
            term_id=str(uuid.uuid4()),
            synonyms=["AI", "Artificial Intelligence"],
            definition="模拟人类智能的计算机系统",
            scope=[Scope(level1="tech", level2="ai", level3="basic")]
        ),
        SemanticData(
            app_id="test_app",
            term="机器学习",
            term_id=str(uuid.uuid4()),
            synonyms=["ML", "Machine Learning"],
            definition="通过数据训练模型的算法",
            scope=[Scope(level1="tech", level2="ai", level3="advanced")]
        ),
        SemanticData(
            app_id="test_app",
            term="深度学习",
            term_id=str(uuid.uuid4()),
            synonyms=["DL", "Deep Learning"],
            definition="基于神经网络的机器学习方法",
            scope=[Scope(level1="tech", level2="ai", level3="advanced")]
        )
    ]

    # 测试保存数据
    print("=== 测试保存数据 ===")
    for data in datas:
        doc_id = operator.save_semantic_data(data)
        print(f"保存成功，文档ID: {doc_id}")

    # 测试分页查询
    print("\n=== 测试分页查询 ===")
    page_result = operator.get_semantics_with_pagination("test_app", page=1, page_size=2)
    print(f"总数: {page_result['total']}")
    print(f"第一页数据: {[d.term for d in page_result['data']]}")

    # 测试删除数据
    print("\n=== 测试删除数据 ===")
    if page_result['data']:
        term_id = page_result['data'][0].term_id
        deleted = operator.delete_semantic_datas("test_app", [term_id])
        print(f"删除文档 {term_id}，结果: {'成功' if deleted else '失败'}")

    # 测试异步查询
    print("\n=== 测试异步查询 ===")
    import asyncio


    async def async_query():
        request = SemanticSearchRequest(
            app_id="test_app",
            query="人工智能",
            top_k=5,
            min_score=0.1, level1="tech", level2="ai", level3="advanced"
        )
        results = await operator.async_get_semantics(request)
        print(f"异步查询结果: results: {results}")


    asyncio.run(async_query())

    print("\n=== 列表查询 ===")
    result = operator.get_semantics_with_pagination("test_app", page=1, page_size=10)
    print(f"异步查询结果: result: {result}")
