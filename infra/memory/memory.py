import asyncio
from asyncio import Lock
from typing import Dict, List, Optional, Any

from mem0 import AsyncMemory
from mem0.configs.base import MemoryConfig

from common.share.config import appConfig
from common.trace.trace import traceable


class MemoryManager:
    def __init__(self, config: Dict[str, Any]):
        config = AsyncMemory._process_config(config)
        config = MemoryConfig(**config)
        self.memory = AsyncMemory(config)

    @traceable(name="add_memory")
    async def add_memory(
            self,
            messages: List[Dict[str, str]],
            sub_account_uin: str,
            agent_id: str,
            run_id: str,
    ) -> Dict:
        """
        添加记忆（支持自动生成 run_id，带元数据）
        :param messages: 消息列表（格式同 mem0 要求）
        :param sub_account_uin: 用户 ID（必填）
        :param agent_id: 应用 ID（默认 "agent_1"）
        :param run_id: 会话 ID（可选，自动生成时间戳格式）
        :param infer: 是否开启记忆推理（默认 True）
        :param metadata: 自定义元数据（默认空字典）
        :return: mem0.add() 的返回结果
        """
        return await self.memory.add(
            messages=messages,
            user_id=sub_account_uin,
            agent_id=agent_id,
            run_id=run_id
        )

    async def search_memory(
            self,
            query: str,
            user_id: str,
            agent_id: Optional[str] = None,
            run_id: Optional[str] = None,
            limit: int = 5,
    ) -> list:
        """
        搜索记忆（带置信度排序和结果限制）
        :param query: 搜索关键词
        :param user_id: 用户 ID
        :param agent_id: 应用 ID（默认 "agent_1"）
        :param run_id: 会话 ID（可选，缩小搜索范围）
        :param limit: 最大返回结果数（默认 5）
        :return: mem0.search() 的返回结果（含 score 和 memory 内容）
        """
        return await self.memory.search(
            query=query,
            user_id=user_id,
            agent_id=agent_id,
            run_id=run_id,
            limit=limit,
        )

    async def get_all_memories(
            self,
            user_id: str,
    ) -> List[Dict]:
        """
        获取用户所有记忆（调试或全量分析场景）
        :param user_id: 用户 ID
        :return: 该用户的所有记忆列表（含元数据和时间戳）
        """
        return await self.memory.get_all(user_id=user_id)


llm_config = appConfig.memory.llm
embedding_config = appConfig.memory.embedding
vector_store_config = appConfig.memory.vector_store

init_config = {
    "llm": {
        "provider": "openai",
        "config": {
            "api_key": llm_config.api_key,
            "model": llm_config.model_name,
            "openai_base_url": llm_config.base_url,
        }
    },
    "embedder": {
        "provider": "openai",
        "config": {
            "api_key": embedding_config.api_key,
            "model": embedding_config.model_name,
            "openai_base_url": embedding_config.base_url,
        },
    },
    "vector_store": {
        "provider": "elasticsearch",
        "config": {
            "collection_name": "memory_collection",
            "host": "http://" + vector_store_config.host,
            "port": vector_store_config.port,
            "user": vector_store_config.user,
            "password": vector_store_config.password,
            "embedding_model_dims": embedding_config.model_dims,
        },
    },
}


class MemoryFactory:
    def __init__(self, base_config: Dict[str, Any]):
        self.base_config = base_config  # 基础配置（不包含动态参数）
        self.user_instances = {}  # 初始化为空字典
        self.lock = Lock()  # 异步锁，确保线程安全

    def _get_dynamic_config(self, app_id: str) -> Dict[str, Any]:
        """
        生成包含动态collection_name的配置
        """
        config = self.base_config.copy()
        app_id = app_id or "none"
        collection_name = "memory_collection" if not app_id else f"memory_collection_{app_id}"
        config["vector_store"]["config"]["collection_name"] = collection_name
        return config

    async def get_memory_manager(self, app_id: str) -> MemoryManager:
        async with self.lock:
            if app_id not in self.user_instances:
                dynamic_config = self._get_dynamic_config(app_id)
                instance = MemoryManager(dynamic_config)
                self.user_instances[app_id] = instance
            return self.user_instances[app_id]

factory = MemoryFactory(init_config)

# ==================== 使用示例 ====================

async def main():
    # 1. 初始化配置（保留原始 config 结构）
    mm = await factory.get_memory_manager("alice")
    # 3. 添加记忆（带用户对话历史）
    sample_messages = [
        {"role": "user", "content": "who are you ?"},
        {"role": "assistant", "content": "Name is John"},
    ]
    add_result = await mm.add_memory(
        messages=sample_messages,
        user_id="user3",
        agent_id="",
        run_id="",
    )
    print("记忆添加结果:", add_result)

    search_result = await mm.search_memory(
        query="user3",
        user_id="alice",
        limit=2  # 只返回前 2 条相关记忆
    )
    print("搜索结果:",search_result)

if __name__ == "__main__":
    asyncio.run(main())
