import asyncio
from datetime import datetime, timezone
from typing import Optional, List

from pydantic import BaseModel

from common.database.es_operator import <PERSON><PERSON>perator
from common.logger.logger import logger
from common.share.config import appConfig, ESConfig
from common.trace.trace import traceable


class JupyterData(BaseModel):
    sub_account_uin: str
    session_id: str
    record_id: str
    cell_id: str
    jupyter: Optional[dict] = None
    create_time: datetime = datetime.now(timezone.utc)

    class Config:
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }


class JupyterOperator(ESOperator):
    def __init__(self, config: ESConfig):
        super().__init__(config)
        self._ensure_index_exists()

    def _ensure_index_exists(self):
        mapping = {
            "settings": {
                "number_of_shards": 5,
                "refresh_interval": "1s"
            },
            "mappings": {
                "dynamic": "true",
                "properties": {
                    "sub_account_uin": {"type": "keyword"},
                    "session_id": {"type": "keyword"},
                    "record_id": {"type": "keyword"},
                    "cell_id": {"type": "keyword"},
                    "jupyter": {"type": "object", "enabled": "false"},
                    "create_time": {"type": "date", "format": "strict_date_optional_time||epoch_millis"}
                }
            }
        }
        try:
            if not self.sync_client.indices.exists(index=self.config.index_name):
                self.sync_client.indices.create(
                    index=self.config.index_name,
                    body=mapping
                )
                logger.info("索引创建成功")
        except Exception as e:
            logger.error(f"索引操作失败: {e}")
            raise

    @classmethod
    def get_instance(cls, app_id: str) -> 'JupyterOperator':
        """
        获取指定app_id的JupyterOperator实例（带缓存）

        Args:
            app_id: 应用/用户ID
        Returns:
            JupyterOperator实例（相同app_id返回缓存实例）
        """
        if not hasattr(cls, "_instance_cache"):
            cls._instance_cache = {}
        app_id = app_id or "none"
        if app_id not in cls._instance_cache:
            modified_config = ESConfig(
                host=appConfig.memory.es.host,
                port=appConfig.memory.es.port,
                user=appConfig.memory.es.user,
                password=appConfig.memory.es.password,
                index_name="jupyter_records")
            modified_config.index_name = f"{modified_config.index_name}_{app_id}"
            cls._instance_cache[app_id] = JupyterOperator(modified_config)

        return cls._instance_cache[app_id]

    @ESOperator._async_retry_decorator
    @traceable(name="async_save_jupyter_record")
    async def async_save_jupyter_record(self, jupyter_record: JupyterData) -> str:
        response = await self.async_client.index(
            index=self.config.index_name,
            document=jupyter_record.model_dump(
                exclude_none=True,
            )
        )
        return response["_id"]

    @ESOperator._async_retry_decorator
    @traceable(name="async_save_jupyter_records")
    async def async_save_jupyter_records(self, jupyter_records: List[JupyterData]) -> List[str]:
        """批量保存Jupyter记录"""
        # 正确构建 Bulk API 请求格式
        actions = []
        for record in jupyter_records:
            # 操作元数据行
            action = {
                "index": {  # 直接使用操作类型作为根字段
                    "_index": self.config.index_name
                }
            }
            actions.append(action)

            # 文档源数据行
            source = record.model_dump(exclude_none=True)
            actions.append(source)

        response = await self.async_client.bulk(
            operations=actions,  # 注意：这里传入的是 actions 列表
            refresh=True
        )

        if response["errors"]:
            logger.error(f"批量保存Jupyter记录失败: {response}")
            raise Exception("部分记录保存失败")

        return [item["index"]["_id"] for item in response["items"]]

    @ESOperator._async_retry_decorator
    async def async_get_jupyter_records(self, sub_account_uin: str, session_id: str, record_id: str, cell_id: str) -> List[
        JupyterData]:
        query = {
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"sub_account_uin": sub_account_uin}},
                        {"term": {"session_id": session_id}},
                        {"term": {"record_id": record_id}},
                        {"term": {"cell_id": cell_id}},
                    ]
                }
            }
        }
        response = await self.async_client.search(
            index=self.config.index_name,
            body=query
        )
        return [JupyterData(**hit["_source"]) for hit in response["hits"]["hits"]]

    @ESOperator._retry_decorator
    def get_jupyter_records(self, sub_account_uin: str, session_id: str, record_id: str, cell_id: str) -> List[JupyterData]:
        filters = [
            {"term": {"sub_account_uin": sub_account_uin}},
            {"term": {"session_id": session_id}},
            {"term": {"record_id": record_id}},
        ]

        # 只有当 cell_id 非空（非空字符串）时才添加条件
        if cell_id:
            filters.append({"term": {"cell_id": cell_id}})

        query = {
            "query": {
                "bool": {
                    "filter": filters
                }
            }
        }

        response = self.sync_client.search(
            index=self.config.index_name,
            body=query
        )

        return [JupyterData(**hit["_source"]) for hit in response["hits"]["hits"]]

    @ESOperator._retry_decorator
    def delete_jupyter_record(self, sub_account_uin: str, session_id: str, record_id: str,
                              cell_id: Optional[str] = None) -> bool:
        """
        根据sub_account_uin, session_id, record_id和可选的cell_id删除Jupyter记录

        Args:
            sub_account_uin: 用户ID
            session_id: 会话ID
            record_id: 记录ID
            cell_id: 单元格ID（可选）

        Returns:
            是否删除成功
        """
        try:
            # 构建查询条件
            query_filter = [
                {"term": {"sub_account_uin": sub_account_uin}},
                {"term": {"session_id": session_id}},
                {"term": {"record_id": record_id}}
            ]
            if cell_id is not None:
                query_filter.append({"term": {"cell_id": cell_id}})

            query = {
                "query": {
                    "bool": {
                        "filter": query_filter
                    }
                }
            }

            response = self.sync_client.delete_by_query(
                index=self.config.index_name,
                body=query,
                refresh=True
            )

            logger.info(f"删除Jupyter记录成功: {response}")
            return True

        except Exception as e:
            logger.error(f"删除Jupyter记录失败: {e}")
            return False

    @ESOperator._async_retry_decorator
    @traceable(name="async_delete_jupyter_record")
    async def async_delete_jupyter_record(self, sub_account_uin: str, session_id: str, record_id: str,
                                          cell_id: Optional[str] = None) -> bool:
        """
        异步删除Jupyter记录

        Args:
            sub_account_uin: 用户ID
            session_id: 会话ID
            record_id: 记录ID
            cell_id: 单元格ID（可选）

        Returns:
            是否删除成功
        """
        try:
            # 构建查询条件
            query_filter = [
                {"term": {"sub_account_uin": sub_account_uin}},
                {"term": {"session_id": session_id}},
                {"term": {"record_id": record_id}}
            ]
            if cell_id is not None:
                query_filter.append({"term": {"cell_id": cell_id}})

            query = {
                "query": {
                    "bool": {
                        "filter": query_filter
                    }
                }
            }

            response = await self.async_client.delete_by_query(
                index=self.config.index_name,
                body=query,
                refresh=True
            )

            logger.info(f"异步删除Jupyter记录成功: {response}")
            return True

        except Exception as e:
            logger.error(f"异步删除Jupyter记录失败: {e}")
            return False

if __name__ == "__main__":
    # # 测试获取聊天记录
    # tid = asyncio.run(jupyter_operator.async_save_jupyter_record(
    #     JupyterData(
    #         sub_account_uin="user_test1",
    #         session_id="session456",
    #         record_id="record123",
    #         cell_id="cell123",
    #         jupyter={
    #             "input": "print('hello world')",
    #         }
    #     )
    # ))
    # print(tid)

    # records = [
    #     JupyterData(sub_account_uin="user1", session_id="sess1", record_id="rec1", cell_id="cell1",jupyter={"input": "print('hello world')",
    #         }),
    #     JupyterData(sub_account_uin="user1", session_id="sess1", record_id="rec1", cell_id="cell1",jupyter={"input": "print('hello world')",})
    # ]
    # record_ids = asyncio.run(jupyter_operator.async_save_jupyter_records(records))

    jupyter_operator = JupyterOperator.get_instance("aisearch")
    mock_records = jupyter_operator.get_jupyter_records("user1", "sess1", "rec1", "cell1")
    for record in mock_records:
        print(record)
    # jupyter_operator.delete_jupyter_record("user1", "sess1", "rec1", "cell1")
