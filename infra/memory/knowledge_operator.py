from datetime import datetime, timezone
from typing import List, Any, Dict, Optional

from pydantic import BaseModel

from common.database.es_operator import ESOperator
from common.logger.logger import logger
from common.metric.prom_metric import prom_metric
from common.share.config import appConfig, ESConfig
from common.trace.trace import traceable


class KnowledgeData(BaseModel):
    file_id: str
    knowledge_base_id: str
    chunk_id: str
    content: str
    create_time: datetime = datetime.now(timezone.utc)

    class Config:
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }


def _get_min_sort_value(slices: List[Dict[str, float]]) -> float:
    """获取最小sort_value（处理边界情况）"""
    return min(slice_["sort_value"] for slice_ in slices)


def _get_max_sort_value(slices: List[Dict[str, float]]) -> float:
    """获取最大sort_value（处理边界情况）"""
    return max(slice_["sort_value"] for slice_ in slices)


class KnowledgeOperator(ESOperator):
    def __init__(self, config: ESConfig):
        super().__init__(config)
        self._ensure_index_exists()

    def _ensure_index_exists(self):
        mapping = {
            "settings": {
                "number_of_shards": 5,
                "refresh_interval": "1s"
            },
            "mappings": {
                "dynamic": "true",
                "properties": {
                    "file_id": {"type": "keyword"},
                    "knowledge_base_id": {"type": "keyword"},
                    "chunk_id": {"type": "keyword"},
                    "sort_value": {"type": "double"},
                    "content": {"type": "text"},
                    "content_embedding": {
                        "type": "dense_vector",
                        "dims": 1024,
                        "index": True,
                        "similarity": "cosine",
                        "index_options": {
                            "type": "int8_hnsw",
                            "m": 16,
                            "ef_construction": 100
                        },
                    },
                    "create_time": {"type": "date", "format": "strict_date_optional_time||epoch_millis"}
                }
            }
        }
        try:
            if not self.sync_client.indices.exists(index=self.config.index_name):
                self.sync_client.indices.create(
                    index=self.config.index_name,
                    body=mapping
                )
                logger.info("索引创建成功")
        except Exception as e:
            logger.error(f"索引操作失败: {e}")
            raise

    @classmethod
    def get_instance(cls, app_id: str) -> 'KnowledgeOperator':
        """
        获取指定app_id的KnowledgeOperator实例（带缓存）

        Args:
            app_id: 应用/用户ID（默认为"default"）
        Returns:
            KnowledgeOperator实例（相同app_id返回缓存实例）
        """
        if not hasattr(cls, "_instance_cache"):
            cls._instance_cache = {}

        app_id = app_id or "none"

        if app_id not in cls._instance_cache:
            modified_config = appConfig.automic.aisearch.es.model_copy()
            modified_config.index_name = f"{modified_config.index_name}_{app_id}"
            cls._instance_cache[app_id] = KnowledgeOperator(modified_config)

        return cls._instance_cache[app_id]

    @ESOperator._async_retry_decorator
    @traceable(name="save_knowledge_records")
    @prom_metric("rag_write_to_es")
    async def save_knowledge_records(self, knowledge_records: List[Dict[str, Any]]) -> List[str]:
        """批量保存Jupyter记录"""
        # 正确构建 Bulk API 请求格式
        actions = []
        for record in knowledge_records:
            # 操作元数据行
            action = {
                "index": {  # 直接使用操作类型作为根字段
                    "_index": self.config.index_name
                }
            }
            actions.append(action)
            actions.append(record)

        response = await self.async_client.bulk(
            operations=actions
        )

        if response["errors"]:
            logger.error(f"批量保存knowledge记录失败: {response}")
            raise Exception("部分记录保存失败")

        return [item["index"]["_id"] for item in response["items"]]

    @ESOperator._retry_decorator
    def search_knowledge_records(
            self,
            file_id: str,
            content: Optional[str] = None,
            page: int = 1,
            page_size: int = 10,
    ) -> Dict[str, Any]:
        sort = [{"sort_value": "asc"}]
        from_ = (page - 1) * page_size

        # 初始化 bool 查询，确保 must 子句存在且为列表
        query = {
            "query": {
                "bool": {
                    "filter": [{"term": {"file_id": file_id}}],
                    "must": []  # 提前初始化 must 为空列表
                }
            },
            "sort": sort,
            "from": from_,
            "size": page_size,
            "_source": ["file_id", "knowledge_base_id", "chunk_id", "content", "create_time"],
            "track_total_hits": True
        }

        if content:
            query["query"]["bool"]["must"].append({
                "match": {
                    "content": {
                        "query": content,
                        "operator": "and"  # 按词项逻辑与匹配
                    }
                }
            })

        response = self.sync_client.search(
            index=self.config.index_name,
            body=query
        )

        hits = response["hits"]["hits"]
        total = response["hits"]["total"]["value"]
        data = [KnowledgeData(**hit["_source"]) for hit in hits]
        has_next_page = from_ + page_size < total

        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "data": data,
            "has_next_page": has_next_page
        }

    @ESOperator._retry_decorator
    def update_knowledge_content(
            self,
            file_id: str,
            chunk_id: str,
            new_content: str,
            new_content_embedding: List[float],
            refresh: bool = True
    ) -> bool:
        # 构建查询条件
        query = {
            "bool": {
                "filter": [
                    {"term": {"file_id": file_id}},
                    {"term": {"chunk_id": chunk_id}}
                ]
            }
        }

        # 构建更新脚本
        script = {
            "source": "ctx._source.content = params.new_content; ctx._source.content_embedding = params.new_content_embedding",
            "params": {
                "new_content": new_content,
                "new_content_embedding": new_content_embedding
            }
        }

        response = self.sync_client.update_by_query(
            index=self.config.index_name,
            query=query,
            script=script,
        )

        # 检查更新是否成功（匹配并更新了至少一条记录）
        return response["updated"] > 0

    @ESOperator._retry_decorator
    def delete_knowledge_by_file_and_chunk(
            self,
            file_id: str,
            chunk_id: str,
            refresh: bool = True
    ) -> bool:
        """
        按 file_id 和 chunk_id 删除记录

        Args:
            file_id: 文件ID
            chunk_id: 块ID
            refresh: 是否刷新索引（默认True）

        Returns:
            删除是否成功
        """
        query = {
            "bool": {
                "filter": [
                    {"term": {"file_id": file_id}},
                    {"term": {"chunk_id": chunk_id}}
                ]
            }
        }

        response = self.sync_client.delete_by_query(
            index=self.config.index_name,
            query=query
        )

        # 检查是否删除了记录
        return response["deleted"] > 0

    @ESOperator._retry_decorator
    def delete_knowledge_by_files(
            self,
            file_ids: List[str],  # 改为支持多个 file_id
            refresh: bool = True
    ) -> bool:
        """
        按多个 file_id 批量删除记录

        Args:
            file_ids: 文件ID列表
            refresh: 是否刷新索引（默认True）

        Returns:
            删除是否成功
        """
        if not file_ids:
            return False  # 空列表直接返回

        query = {
            "bool": {
                "filter": [
                    {"terms": {"file_id": file_ids}}  # 使用 terms 查询匹配多个 file_id
                ]
            }
        }

        response = self.sync_client.delete_by_query(
            index=self.config.index_name,
            query=query,
            refresh=refresh
        )

        # 检查是否删除了记录
        return response["deleted"] > 0

    @ESOperator._async_retry_decorator
    async def delete_knowledge_by_file_id(
            self,
            knowledge_base_id: str,
            file_id: str,
    ) -> bool:
        """
        根据 file_id 删除所有匹配的知识记录

        Args:
            file_id: 文件ID
            refresh: 是否刷新索引（默认True）

        Returns:
            删除是否成功（至少删除一条记录）
        """
        if not file_id:
            return False  # 空 file_id 直接返回

        query = {
            "bool": {
                "filter": [
                    {"term": {"file_id": file_id}},
                    {"term": {"knowledge_base_id": knowledge_base_id}},
                ]
            }
        }

        try:
            response = await self.async_client.delete_by_query(
                index=self.config.index_name,
                query=query
            )
            return response["deleted"] > 0
        except Exception as e:
            logger.error(f"删除知识记录失败: file_id={file_id}, error={e}")
            return False

    @ESOperator._retry_decorator
    @traceable(name="add_knowledge_by_file_id")
    def add_knowledge_by_file_id(
            self,
            knowledge_data: List[Dict[str, Any]] or Dict[str, Any],
            refresh: bool = True
    ) -> List[str]:
        """
        按 file_id 新增知识记录（支持单条或批量）

        Args:
            knowledge_data: 单条或多条记录数据
            refresh: 是否刷新索引（默认True）

        Returns:
            新增记录的 ID 列表
        """
        # 处理单条记录的情况
        if not isinstance(knowledge_data, list):
            knowledge_data = [knowledge_data]
        # 构建 Bulk API 请求
        actions = []
        for record in knowledge_data:
            action = {
                "index": {
                    "_index": self.config.index_name
                }
            }
            actions.append(action)
            actions.append(record)

        response = self.sync_client.bulk(
            operations=actions,
            refresh=refresh
        )

        if response["errors"]:
            logger.error(f"新增记录失败: {response}")
            raise Exception("部分记录新增失败")

        # 提取新增记录的 ID
        return [item["index"]["_id"] for item in response["items"]]

    @ESOperator._retry_decorator
    def insert_knowledge_at_position(
            self,
            file_id: str,  # 非默认参数（必传）
            knowledge_data: Dict[str, Any],  # 非默认参数（必传）
            before_chunk_id: Optional[str] = None,  # 默认参数（可选）
            after_chunk_id: Optional[str] = None,  # 默认参数（可选）
            insert_position: Optional[int] = None,  # 默认参数（可选）
            knowledge_base_id: Optional[str] = "default",  # 默认参数（可选）
    ) -> str:
        # 获取当前排序数据
        sorted_slices = self._get_sorted_slices(file_id)

        # 确定插入位置
        if before_chunk_id is None and after_chunk_id is None:
            insert_pos = insert_position  # 优先使用显式指定的位置
        else:
            insert_pos = self._get_insert_position(sorted_slices, before_chunk_id, after_chunk_id)

        # 计算排序值
        sort_value = self._calculate_sort_value(sorted_slices, insert_pos, file_id)

        # 组装数据
        knowledge_data["sort_value"] = sort_value
        knowledge_data["knowledge_base_id"] = knowledge_base_id
        knowledge_data["create_time"] = datetime.now(timezone.utc).isoformat()

        # 执行插入
        return self._do_insert(knowledge_data)

    def _get_sorted_slices(self, file_id: str) -> List[Dict[str, float]]:

        """获取文件下所有切片的sort_value（按升序排列）"""
        query = {
            "query": {"term": {"file_id": file_id}},
            "sort": [{"sort_value": "asc"}],
            "_source": ["chunk_id", "sort_value"],
            "size": 65535  # 设置一个足够大的值，获取所有结果
        }
        response = self.sync_client.search(index=self.config.index_name, body=query)
        return [{"chunk_id": hit["_source"]["chunk_id"], "sort_value": hit["_source"]["sort_value"]}
                for hit in response["hits"]["hits"]]

    from typing import List, Optional, Dict

    def _get_insert_position(
            self,
            sorted_slices: List[Dict],
            before_chunk_id: Optional[str] = None,
            after_chunk_id: Optional[str] = None
    ) -> int:
        """获取新切片的插入位置（优化版）

        Args:
            sorted_slices: 已排序的切片列表，需包含"chunk_id"字段
            before_chunk_id: 插入到该chunkId之后（不包含该位置）
            after_chunk_id: 插入到该chunkId之前（包含该位置+1）
            注意：两个参数不能同时指定

        Returns:
            合法的插入位置索引

        Raises:
            ValueError: 当参数不合法时抛出
        """
        # 参数合法性校验
        if before_chunk_id and after_chunk_id:
            raise ValueError("before_chunk_id和after_chunk_id不能同时指定")

        # 处理空列表特殊情况
        if not sorted_slices:
            return 0 if not before_chunk_id else -1  # 空列表时before参数无效

        # 构建chunk_id到索引的映射表，提高查询效率
        chunk_id_index = {chunk["chunk_id"]: i for i, chunk in enumerate(sorted_slices)}

        # 优先处理before_chunk_id
        if before_chunk_id:
            if before_chunk_id not in chunk_id_index:
                raise ValueError(f"before_chunk_id: {before_chunk_id} 不存在于切片列表中")
            return chunk_id_index[before_chunk_id]

        # 处理after_chunk_id
        if after_chunk_id:
            if after_chunk_id not in chunk_id_index:
                raise ValueError(f"after_chunk_id: {after_chunk_id} 不存在于切片列表中")
            return chunk_id_index[after_chunk_id] + 1

        # 无位置参数时插入队尾
        return len(sorted_slices)

    def _calculate_sort_value(
            self,
            sorted_slices: List[Dict[str, float]],
            insert_pos: int,
            file_id: str,
    ) -> float:
        """计算插入位置的sort_value（间隔为1的稀疏排序）"""
        n = len(sorted_slices)
        if n == 0:
            return 1.0  # 空文件从1开始

        if insert_pos == 0:
            return _get_min_sort_value(sorted_slices) - 1.0  # 插入到最前

        if insert_pos == n:
            return _get_max_sort_value(sorted_slices) + 1.0  # 插入到最后

        # 插入到中间：取前后sort_value的中间值
        prev_val = sorted_slices[insert_pos - 1]["sort_value"]
        next_val = sorted_slices[insert_pos]["sort_value"]

        # 检查是否需要重新平衡（当两个值过于接近时）
        if next_val - prev_val < 1e-6:  # 设置一个阈值，例如1e-6
            logger.warning(f"Sort values too close: {prev_val} and {next_val}, triggering local rebalance")
            self._rebalance_local_sort_values(file_id, sorted_slices, insert_pos)
            # 重新获取排序后的数据
            sorted_slices = self._get_sorted_slices(file_id)  # 需要传入file_id
            insert_pos = min(insert_pos, len(sorted_slices))  # 确保插入位置合法

            # 重新计算插入位置
            if insert_pos == 0:
                return _get_min_sort_value(sorted_slices) - 1.0
            if insert_pos == len(sorted_slices):
                return _get_max_sort_value(sorted_slices) + 1.0

            prev_val = sorted_slices[insert_pos - 1]["sort_value"]
            next_val = sorted_slices[insert_pos]["sort_value"]

        return (prev_val + next_val) / 2.0

    def _rebalance_local_sort_values(self, file_id, sorted_slices: List[Dict[str, float]], insert_pos: int) -> None:
        """
        局部重新平衡排序值，将插入点附近的元素排序值重新平均分配
        例如：原有值为[2, 5]，插入后生成10个均匀分布的值
        """
        # 确定重新平衡的范围（插入点前后各5个元素，共11个元素）
        start_idx = max(0, insert_pos - 5)
        end_idx = min(len(sorted_slices), insert_pos + 5)
        if start_idx == end_idx:
            return  # 范围不足2个元素，无需调整

        # 提取范围内的原始排序值
        range_slices = sorted_slices[start_idx:end_idx + 1]
        original_values = [slice_["sort_value"] for slice_ in range_slices]

        # 计算新的均匀分布排序值
        num_elements = len(range_slices)
        if num_elements < 2:
            return

        # 计算总间隔和每个间隔的步长
        start_val = original_values[0]
        end_val = original_values[-1]
        total_gap = end_val - start_val
        step = total_gap / (num_elements - 1)  # 等间隔划分

        new_sort_values = [start_val + i * step for i in range(num_elements)]
        updated_sorted_slices = sorted_slices.copy()
        for i, slice_ in enumerate(range_slices):
            # 复制原切片，仅更新sort_value
            updated_slice = {**slice_, "sort_value": new_sort_values[i]}
            updated_sorted_slices[start_idx + i] = updated_slice
        total = self._bulk_update_sort_values(file_id, updated_sorted_slices)
        logger.info(f"局部排序值重新平衡：更新{len(total)}条记录，范围[{start_idx},{end_idx}]，新步长{step:.2f}")

    def _bulk_update_sort_values(self, file_id: str, updates: List[Dict[str, Any]], refresh: bool = True) -> int:
        """
        批量更新知识切片的 sort_value

        Args:
            updates: 包含 file_id, chunk_id 和新 sort_value 的列表
            refresh: 是否刷新索引使更改立即生效

        Returns:
            成功更新的记录数量
        """
        # 校验输入参数
        if not updates:
            return 0

        # 构建批量更新请求
        bulk_request = []

        for update in updates:
            chunk_id = update.get("chunk_id")
            sort_value = update.get("sort_value")

            if not all([file_id, chunk_id, sort_value]):
                logger.warning(f"跳过不完整的更新数据: {update}")
                continue

            # 构建更新条件
            query = {
                "bool": {
                    "filter": [
                        {"term": {"file_id": file_id}},
                        {"term": {"chunk_id": chunk_id}}
                    ]
                }
            }

            # 构建更新脚本
            script = {
                "source": "ctx._source.sort_value = params.sort_value",
                "params": {"sort_value": sort_value}
            }

            # 添加到批量请求
            bulk_request.append({
                "update_by_query": {
                    "index": self.config.index_name,
                    "body": {
                        "query": query,
                        "script": script
                    }
                }
            })

        # 执行批量更新
        if bulk_request:
            response = self.sync_client.msearch(body=bulk_request)

            # 统计成功更新的记录数
            total_updated = 0
            for item in response["responses"]:
                if "updated" in item:
                    total_updated += item["updated"]
                else:
                    error = item.get("error", "未知错误")
                    logger.error(f"批量更新失败: {error}")

            return total_updated

        return 0

    def _do_insert(self, knowledge_data: Dict[str, Any]) -> str:
        """执行单条插入（带重试和错误处理）"""
        try:
            response = self.sync_client.index(
                index=self.config.index_name,
                body=knowledge_data,
                refresh=True  # 立即刷新索引，确保数据立即可见
            )

            # 检查响应状态
            if response["result"] not in ["created", "updated"]:
                raise RuntimeError(f"插入失败: {response.get('result', '未知错误')}")

            return knowledge_data["chunk_id"]

        except Exception as e:
            # 处理异常
            raise RuntimeError(f"插入失败: {str(e)}") from e

    @ESOperator._retry_decorator
    def get_knowledge_by_chunk_id(self, file_id: str, chunk_id: Optional[str] = None) -> Optional[KnowledgeData]:
        """
        根据file_id和可选的chunk_id查询知识记录

        Args:
            file_id: 文件ID
            chunk_id: 要查询的chunk_id（可选）

        Returns:
            匹配的KnowledgeData对象，如果不存在则返回None
        """
        # 构建查询条件
        query_filter = [{"term": {"file_id": file_id}}]
        if chunk_id is not None:
            query_filter.append({"term": {"chunk_id": chunk_id}})

        query = {
            "query": {
                "bool": {
                    "filter": query_filter
                }
            },
            "_source": ["file_id", "knowledge_base_id", "chunk_id", "content", "create_time"],
            "size": 1
        }

        try:
            response = self.sync_client.search(
                index=self.config.index_name,
                body=query
            )

            hits = response["hits"]["hits"]
            if not hits:
                return None

            return KnowledgeData(**hits[0]["_source"])

        except Exception as e:
            logger.error(f"查询知识记录失败: {e}")
            raise

    @ESOperator._retry_decorator
    def batch_delete_knowledge_chunks(self, file_id: str, chunk_ids: List[str]) -> bool:
        """批量删除切片记录
k
        Args:
            file_id: 文件ID
            chunk_ids: 切片ID列表

        Returns:
            删除是否成功
        """
        if not chunk_ids:
            return False  # 空列表直接返回

        query = {
            "bool": {
                "filter": [
                    {"term": {"file_id": file_id}},
                    {"terms": {"chunk_id": chunk_ids}}  # 使用 terms 查询匹配多个 chunk_id
                ]
            }
        }

        response = self.sync_client.delete_by_query(
            index=self.config.index_name,
            query=query,
            refresh=True
        )

        # 检查是否删除了记录
        return response["deleted"] > 0


# knowledge_operator = KnowledgeOperator(appConfig.automic.aisearch.es)

if __name__ == "__main__":
    # # 测试获取聊天记录
    # tid = asyncio.run(jupyter_operator.async_save_jupyter_record(
    #     JupyterData(
    #         user_id="user_test1",
    #         session_id="session456",
    #         record_id="record123",
    #         cell_id="cell123",
    #         jupyter={
    #             "input": "print('hello world')",
    #         }
    #     )
    # ))
    # print(tid)

    # records = [
    #     JupyterData(user_id="user1", session_id="sess1", record_id="rec1", cell_id="cell1",jupyter={"input": "print('hello world')",
    #         }),
    #     JupyterData(user_id="user1", session_id="sess1", record_id="rec1", cell_id="cell1",jupyter={"input": "print('hello world')",})
    # ]
    # record_ids = asyncio.run(jupyter_operator.async_save_jupyter_records(records))

    # mock_records = knowledge_operator.search_knowledge_records("123", "Hello1")
    # print(mock_records)
    pass
