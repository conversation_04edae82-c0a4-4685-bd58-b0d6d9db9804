from common.elements.agent_event import FinishEvent
import tests.basic.init
import os

import requests

from common.metric.server import start_metrics_server
from common.logger.logger import logger
from tests.server.server_basic import (
    client, get_test_headers, check_stream_response, check_success_response, with_test_chat_config, chat_config
)
# 设置测试环境变量
metric_server = start_metrics_server(port=18080)

def test_chat_endpoint(chat_config):
    """Test the chat endpoint"""
    
    # check metrics
    resp = requests.get("http://127.0.0.1:18080/metrics")
    logger.info(f"metrics: {resp.text}")
    assert resp.status_code == 200
    assert "intellix_data_agent_enpoints_request_total" in resp.text
    assert 'path="/chat",status="200",userid="test_user_gw_metric"' not in resp.text
    
    
    response = client.post(
        "/chat",
        json=chat_config,
        headers=get_test_headers(sub_account_uin="test_user_gw_metric")
    )

    assert response.status_code == 200
    content = response.text
    assert FinishEvent().to_sse_format() in content
    
    
    resp = requests.get("http://127.0.0.1:18080/metrics")
    logger.info(f"metrics: {resp.text}")
    assert resp.status_code == 200
    assert "intellix_data_agent_enpoints_request_total" in resp.text
    assert 'path="/chat",status="200",userid="test_user_gw_metric"' in resp.text
    assert 'intellix_data_agent_enpoints_request_total{path="/chat",status="200",userid="test_user_gw_metric"} 1.0' in resp.text
    # Check request counter increased by 1
    
    # Check latency is under 10s - 修复：使用正确的指标名称 chat_seconds 而不是 path_seconds
    latency_lines = [line for line in resp.text.split('\n') if 'intellix_data_agent_enpoints_chat_seconds_sum' in line and 'path="/chat"' in line]
    assert len(latency_lines) > 0, "No chat latency metric found"
    latency_line = latency_lines[0]
    latency = float(latency_line.split(' ')[1])
    assert latency < 120.0
    # check metrics
