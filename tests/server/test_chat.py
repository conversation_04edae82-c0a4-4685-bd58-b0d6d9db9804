import pytest
import uuid
import json
from common.elements.agent_event import CloseEvent, FinishEvent
from tests.server.server_basic import (
    client, get_test_headers, check_stream_response, check_success_response, with_test_chat_config, chat_config
)

def test_chat_endpoint(chat_config):
    """Test the chat endpoint"""
    response = client.post(
        "/chat",
        json=chat_config
    )
    # 检查响应状态码
    assert response.status_code == 200
    # 检查响应内容是否为SSE格式
    hasFinish = False
    content = response.text
    assert "event: message" in content
    assert "event: think" in content
    assert "event: text" in content
    assert "event: record_meta" in content
    assert "event: title" in content
    assert FinishEvent().to_sse_format() in content
    assert CloseEvent().to_sse_format() in content

class TestChatEndpoints:
    """聊天接口测试类"""

    def test_chat_malformed_json(self):
        """测试格式错误的JSON请求"""
        headers = get_test_headers()
        
        # 发送格式错误的JSON
        try:
            response = client.post("/chat", data="invalid json", headers=headers)
            # 如果服务器处理了错误，应该返回400或422
            assert response.status_code in [400, 422, 500]
        except Exception:
            # 如果客户端抛出异常，这也是可以接受的
            pass
