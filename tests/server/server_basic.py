import os
import uuid
import functools
import pytest
import tests.basic.init
from fastapi.testclient import TestClient
from infra.server.gw import app
from infra.server import gw
from common.share.config import appConfig
from common.database.database import get_metadata_db_pool

# 设置测试环境变量
os.environ["EXECUTION_MODE"] = "local"
os.environ["LOG_LEVEL"] = "DEBUG"

# 初始化数据库连接
db_config = appConfig.common.metadata_db
gw.mysql_pool = get_metadata_db_pool(db_config)

# 创建测试客户端
client = TestClient(app)

# 通用的测试 headers
def get_test_headers(app_id: str = "test_app", sub_account_uin: str = "test_user", uin: str = "test_user"):
    """获取通用的测试请求头"""
    return {
        "Content-Type": "application/json",
        "AppId": app_id,
        "SubAccountUin": sub_account_uin,
        "Uin": uin,
        "TraceId": str(uuid.uuid4()),
        "SessionId": str(uuid.uuid4())
    }

def get_test_context(app_id: str = "test_app", sub_account_uin: str = "test_user"):
    """获取测试上下文"""
    return {
        "app_id": app_id,
        "sub_account_uin": sub_account_uin,
        "trace_id": str(uuid.uuid4()),
        "session_id": str(uuid.uuid4())
    }

# 通用的响应检查函数
def check_success_response(response, expected_status: int = 200):
    """检查成功响应"""
    assert response.status_code == expected_status
    data = response.json()
    assert "Status" in data
    assert data["Status"] == 200
    return data

def check_error_response(response, expected_status: int = 200, error_code: str = None):
    """检查错误响应"""
    assert response.status_code == expected_status
    data = response.json()
    assert "Status" in data
    assert data["Status"] != 200
    if error_code:
        assert "Error" in data
        assert data["Error"]["Code"] == error_code
    return data

def check_stream_response(response, expected_status: int = 200):
    """检查流式响应"""
    assert response.status_code == expected_status
    content = response.text
    assert "event:" in content
    return content

# 通用的测试数据生成函数
def generate_test_document(file_name: str = "test_file.txt", file_type: str = "TXT"):
    """生成测试文档数据"""
    return {
        "FileName": file_name,
        "FileId": str(uuid.uuid4()),
        "FileType": file_type,
        "FileUrl": f"https://data-agent-dev-1353879163.cos.ap-chongqing.myqcloud.com/test.txt",
        "FileSize": 1024.0,
        "Source": 0
    }

def generate_test_chunk_config(chunk_type: int = 0, max_chunk_size: int = 1000):
    """生成测试分块配置"""
    return {
        "ChunkType": chunk_type,
        "MaxChunkSize": max_chunk_size,
        "Delimiters": ["\n", "\r\n"],
        "ChunkOverlap": 100
    }

def generate_test_chat_config(question: str = "Test question", session_id: str = None):
    """生成测试聊天配置"""
    if session_id is None:
        session_id = str(uuid.uuid4())
    return {
        "SessionId": session_id,
        "Question": question,
        "Model": appConfig.memory.llm.model_name,
        "DeepThinking": True,
        "DataSourceId": [],
        "AgentType": "",
        "Context": "",
        "OldRecordId": ""
    }

def generate_test_session_config(session_id: str = None):
    """生成测试会话配置"""
    if session_id is None:
        session_id = str(uuid.uuid4())
    return {
        "SessionId": session_id
    }

def generate_test_user_info_config(jupyter_host: str = "http://jupyter.test.com", operate: str = "create"):
    """生成测试用户信息配置"""
    return {
        "JupyterHost": jupyter_host,
        "Operate": operate
    }

# 通用的清理函数
def cleanup_test_data():
    """清理测试数据的通用函数"""
    # 这里可以添加通用的清理逻辑
    pass

# 新增：with_test_chat_config 装饰器
def with_test_chat_config(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        chat_config = generate_test_chat_config()
        return func(*args, chat_config=chat_config, **kwargs)
    return wrapper

# 新增：pytest fixture for chat_config
@pytest.fixture
def chat_config():
    """提供测试聊天配置的pytest fixture"""
    return generate_test_chat_config() 