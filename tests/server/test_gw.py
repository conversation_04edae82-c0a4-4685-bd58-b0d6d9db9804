import uuid

import pytest
import os

from tests.server.server_basic import (
    client, get_test_headers, check_stream_response, check_success_response, with_test_chat_config, chat_config
)



def test_query_user_session_list():
    """Test the query_user_session_list endpoint"""
    response = client.post(
        "/query_user_session_list",
        json={
            "UserId": "test_user",
            "TraceId": "test_trace"
        }
    )
    # 检查响应状态码
    assert response.status_code == 200
    data = response.json()
    assert "SessionInfoList" in data



def test_query_knowledge_list():
    """Test the query_knowledge_list endpoint"""
    response = client.post(
        "/query_knowledge_list",
        json={
            "SubAccountUin": "test_user",
        }
    )
    # 检查响应状态码
    assert response.status_code == 200
    data = response.json()
    assert "KnowledgeInfoList" in data


def test_create_knowledge_task():
    """Test the create_knowledge_task endpoint"""
    response = client.post(
        "/create_knowledge_task",
        json={
            "Documents": [
                {
                    "FileName": "testFile",
                    "FileId": str(uuid.uuid4()),
                    "FileType": "TXT",
                    "FileUrl": "https://data-agent-dev-**********.cos.ap-chongqing.myqcloud.com/test.txt",
                    "FileSize": 9.0
                }
            ],
            "Config": {
                "ChunkType": 0,
                "MaxChunkSize": 1000
            }
        },
        headers=get_test_headers()
    )
    # 检查响应状态码
    assert response.status_code == 200


def test_query_chunk_list():
    response = client.post(
        "/query_chunk_list",
        json={
            "FileId": "test_file_id",
        },
        headers=get_test_headers()
    )
    # 检查响应状态码
    assert response.status_code == 200
    data = response.json()
    assert "Chunks" in data
    assert "Total" in data

def test_query_knowledge_config():
    response = client.post(
        "/query_knowledge_config",
        json={
            "SubAccountUin": "test_user",
        },
        headers=get_test_headers()
    )
    # 检查响应状态码
    assert response.status_code == 200
    data = response.json()
    assert "SearchConfig" in data

def test_modify_knowledge_config():
    response = client.post(
        "/modify_knowledge_config",
        json={
            "SearchConfig": {
                "Type": 0,
                "Num": 1000,
                "EmbeddingWeight": 0.5,
                "Rerank": True,
            }
        },
        headers=get_test_headers()
    )
    # 检查响应状态码
    assert response.status_code == 200

