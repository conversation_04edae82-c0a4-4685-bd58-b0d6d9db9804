from infra.server.model import UpdateKnowledgeInfo, KnowledgeBase
from tests.server.server_basic import client


class TestUpdateKnowledgeInfo:
    def test_query_knowledge_info_success(self):
        """测试创建知识库信息成功"""
        update_info = UpdateKnowledgeInfo(
            KnowledgeBase=KnowledgeBase(Id="test_id", Name="test_name"),
            Operate="create"
        )
        response = client.post("/update_knowledge_info", json=update_info.model_dump())
        assert response.status_code == 200

        """测试查询知识库信息成功"""
        update_info = UpdateKnowledgeInfo(
            KnowledgeBase=KnowledgeBase(Id="test_id", Name="test_name"),
            Operate="query"
        )
        response = client.post("/update_knowledge_info", json=update_info.model_dump())
        assert response.status_code == 200
        data = response.json()
        assert "Status" in data
        assert "KnowledgeBase" in data

        """测试删除不存在的知识库信息"""
        update_info = UpdateKnowledgeInfo(
            KnowledgeBase=KnowledgeBase(Id="test_id", Name="test_id"),
            Operate="delete"
        )
        response = client.post("/update_knowledge_info", json=update_info.model_dump())
        assert response.status_code == 200

    def test_query_knowledge_info_not_found(self):
        """测试查询不存在的知识库信息"""
        update_info = UpdateKnowledgeInfo(
            KnowledgeBase=KnowledgeBase(Id="invalid_id", Name="invalid_name"),
            Operate="query"
        )
        response = client.post("/update_knowledge_info", json=update_info.model_dump())
        assert response.status_code == 404

    def test_modify_knowledge_info_success(self):
        """测试创建知识库信息成功"""
        update_info = UpdateKnowledgeInfo(
            KnowledgeBase=KnowledgeBase(Id="existing_id", Name="updated_name"),
            Operate="create"
        )
        response = client.post("/update_knowledge_info", json=update_info.model_dump())
        assert response.status_code == 200

        """测试修改知识库信息成功"""
        update_info = UpdateKnowledgeInfo(
            KnowledgeBase=KnowledgeBase(Id="existing_id", Name="updated_name"),
            Operate="modify"
        )
        response = client.post("/update_knowledge_info", json=update_info.model_dump())
        assert response.status_code == 200

        update_info = UpdateKnowledgeInfo(
            KnowledgeBase=KnowledgeBase(Id="existing_id", Name="updated_name"),
            Operate="delete"
        )
        response = client.post("/update_knowledge_info", json=update_info.model_dump())
        assert response.status_code == 200

    def test_modify_knowledge_info_not_found(self):
        """测试修改不存在的知识库信息"""
        update_info = UpdateKnowledgeInfo(
            KnowledgeBase=KnowledgeBase(Id="invalid_id", Name="invalid_name"),
            Operate="modify"
        )
        response = client.post("/update_knowledge_info", json=update_info.model_dump())
        assert response.status_code == 404

    def test_delete_knowledge_info_not_found(self):
        """测试删除不存在的知识库信息"""
        update_info = UpdateKnowledgeInfo(
            KnowledgeBase=KnowledgeBase(Id="invalid_id", Name="invalid_name"),
            Operate="delete"
        )
        response = client.post("/update_knowledge_info", json=update_info.model_dump())
        assert response.status_code == 404