import pytest
import async<PERSON>
import json
from unittest.mock import patch, MagicMock
from fastapi import status

from tests.server.server_basic import client, get_test_headers
from infra.server.tools import mcp_connection_test, ConnectionTestConfig
from common.share.enums import MCPEngineTypeEnum
from common.share.error import ErrorCode


# Common test constants
TEST_URL = "http://127.0.0.1:31234/sse?key=lCPbVK4Q"
TEST_TIMEOUT = 5.0
TEST_TIMEOUT_CUSTOM = 10.0


def create_async_mock():
    """Create an async-compatible mock for Python 3.12"""
    mock = MagicMock()
    # Create a mock that returns a coroutine
    async def async_return(*args, **kwargs):
        return mock.return_value
    mock.side_effect = async_return
    return mock


def parse_response_data(response):
    """Helper function to parse response data, handling both dict and string formats"""
    data = response.json()
    # If data is a string, parse it as JSON
    if isinstance(data, str):
        data = json.loads(data)
    return data


def make_connection_test_request(url=TEST_URL, mcp_type="DLC", timeout=TEST_TIMEOUT):
    """Helper function to make connection test request"""
    return client.post(
        "/connection_test",
        json={
            "Url": url,
            "Type": mcp_type,
            "Timeout": timeout
        },
        headers=get_test_headers()
    )


def create_mock_result(is_error=False, content_text="DataEngines"):
    """Helper function to create a mock result with the expected structure"""
    mock_result = MagicMock()
    mock_result.isError = is_error
    
    # Create a mock content with the expected structure
    mock_content_item = MagicMock()
    mock_content_item.text = content_text
    mock_result.content = [mock_content_item]
    
    return mock_result


# Test data for parameterized tests
SUCCESS_TEST_CASES = [
    ("DLC", "DataEngines", "DLCListEngines"),
    ("TCHouseD", "Databases", "TCHouseDListDatabases"),
]

ERROR_TEST_CASES = [
    ("", "DLC", 400, "ParamError"),  # Empty URL
    (TEST_URL, "", 400, "ParamError"),  # Empty MCP type
    (TEST_URL, "UNSUPPORTED_TYPE", 400, "ParamError"),  # Invalid MCP type
]

VALIDATION_TEST_CASES = [
    ("", "DLC", "Url is required"),  # Empty URL
    ("   ", "DLC", "Url is required"),  # Whitespace URL
    (TEST_URL, "INVALID_TYPE", "Type must be one of"),  # Invalid MCP type
]


class TestConnectionTest:
    """Test cases for connection_test endpoint"""

    @pytest.mark.parametrize("mcp_type,content_text,tool_name", SUCCESS_TEST_CASES)
    def test_connection_test_success(self, mcp_type, content_text, tool_name):
        """Test successful connection test for different MCP types"""
        with patch('infra.server.tools.mcp_call_tool', new_callable=create_async_mock) as mock_mcp_call:
            # Mock successful MCP call
            mock_mcp_call.return_value = create_mock_result(is_error=False, content_text=content_text)
            
            response = make_connection_test_request(mcp_type=mcp_type)
            
            assert response.status_code == 200
            data = parse_response_data(response)
            
            assert data["Status"] == 200
            assert data["Error"] is None
            
            # Verify MCP call was made with correct parameters
            mock_mcp_call.assert_called_once_with(
                arguments={},
                url=TEST_URL,
                tool_name=tool_name,
                timeout=TEST_TIMEOUT,
                sse_read_timeout=TEST_TIMEOUT*2
            )

    def test_connection_test_mcp_error(self):
        """Test connection test when MCP call returns error"""
        with patch('infra.server.tools.mcp_call_tool', new_callable=create_async_mock) as mock_mcp_call:
            # Mock MCP call that returns error
            mock_mcp_call.return_value = create_mock_result(is_error=True, content_text="Connection failed")
            
            response = make_connection_test_request()
            
            assert response.status_code == 200
            data = parse_response_data(response)
            assert data["Status"] == 424  # Failed Dependency
            assert data["Error"] is not None
            assert data["Error"]["Code"] == ErrorCode.ConnectionTestError.value
            assert "mcp connection test failed" in data["Error"]["Message"]

    def test_connection_test_exception(self):
        """Test connection test when MCP call raises exception"""
        with patch('infra.server.tools.mcp_call_tool', new_callable=create_async_mock) as mock_mcp_call:
            # Mock MCP call that raises exception
            mock_mcp_call.side_effect = Exception("Network timeout")
            
            response = make_connection_test_request()
            
            assert response.status_code == 200
            data = parse_response_data(response)
            assert data["Status"] == 424  # Failed Dependency
            assert data["Error"] is not None
            assert data["Error"]["Code"] == ErrorCode.ConnectionTestError.value
            assert "mcp connection test failed" in data["Error"]["Message"]

    @pytest.mark.parametrize("url,mcp_type,expected_status,error_code", ERROR_TEST_CASES)
    def test_connection_test_validation_errors(self, url, mcp_type, expected_status, error_code):
        """Test connection test with various validation errors"""
        response = make_connection_test_request(url=url, mcp_type=mcp_type)
        
        assert response.status_code == 200
        data = parse_response_data(response)
        
        assert data["Status"] == expected_status
        assert data["Error"] is not None
        assert data["Error"]["Code"] == getattr(ErrorCode, error_code).value

    def test_connection_test_missing_required_fields(self):
        """Test connection test with missing required fields"""
        response = client.post(
            "/connection_test",
            json={
                "Timeout": TEST_TIMEOUT
                # Missing mcp_url and mcp_type
            },
            headers=get_test_headers()
        )
        
        # Should return validation error with status 200 (as per the error handler)
        assert response.status_code == 200
        data = parse_response_data(response)
        
        assert data["Status"] == 400  # ParamError
        assert data["Error"] is not None

    def test_connection_test_all_valid_types(self):
        """Test connection test with all valid MCP types"""
        valid_types = [engine.value for engine in MCPEngineTypeEnum]
        
        for mcp_type in valid_types:
            # Skip unsupported types (only DLC and TCHouseD are supported in test_tools)
            if mcp_type not in ["DLC", "TCHouseD"]:
                continue
                
            with patch('infra.server.tools.mcp_call_tool', new_callable=create_async_mock) as mock_mcp_call:
                # Mock successful MCP call
                content_text = "DataEngines" if mcp_type == "DLC" else "Databases"
                mock_mcp_call.return_value = create_mock_result(is_error=False, content_text=content_text)
                
                response = make_connection_test_request(mcp_type=mcp_type)
                
                # Should either succeed (for supported types) or return 404 (for unsupported types)
                assert response.status_code in [200, 200]  # All should return 200, but some with error status
                data = parse_response_data(response)
                
                if mcp_type in ["DLC", "TCHouseD"]:
                    assert data["Status"] == 200
                    assert data["Error"] is None
                else:
                    assert data["Status"] == 404
                    assert data["Error"] is not None

    def test_connection_test_timeout_parameter(self):
        """Test connection test with different timeout values"""
        with patch('infra.server.tools.mcp_call_tool', new_callable=create_async_mock) as mock_mcp_call:
            # Mock successful MCP call
            mock_mcp_call.return_value = create_mock_result(is_error=False, content_text="DataEngines")
            
            # Test with default timeout
            response1 = client.post(
                "/connection_test",
                json={
                    "Url": TEST_URL,
                    "Type": "DLC"
                    # timeout not specified, should use default
                },
                headers=get_test_headers()
            )
            
            # Test with custom timeout
            response2 = make_connection_test_request(timeout=TEST_TIMEOUT_CUSTOM)
            
            # Both should succeed
            assert response1.status_code == 200
            assert response2.status_code == 200
            
            data1 = parse_response_data(response1)
            data2 = parse_response_data(response2)
            
            assert data1["Status"] == 200
            assert data2["Status"] == 200
            assert data1["Error"] is None
            assert data2["Error"] is None


class TestConnectionTestConfig:
    """Test cases for ConnectionTestConfig model validation"""

    def test_valid_config(self):
        """Test valid configuration"""
        config = ConnectionTestConfig(
            Url=TEST_URL,
            Type="DLC",
            Timeout=TEST_TIMEOUT
        )
        assert config.Url == TEST_URL
        assert config.Type == "DLC"
        assert config.Timeout == TEST_TIMEOUT

    @pytest.mark.parametrize("url,mcp_type,expected_error", VALIDATION_TEST_CASES)
    def test_validation_errors(self, url, mcp_type, expected_error):
        """Test various validation errors"""
        with pytest.raises(ValueError, match=expected_error):
            ConnectionTestConfig(
                Url=url,
                Type=mcp_type
            )

    def test_url_validation_strip(self):
        """Test URL validation with leading/trailing whitespace"""
        config = ConnectionTestConfig(
            Url=f"  {TEST_URL}  ",
            Type="DLC"
        )
        assert config.Url == TEST_URL

    def test_mcp_type_validation_all_valid(self):
        """Test MCP type validation with all valid types"""
        valid_types = [engine.value for engine in MCPEngineTypeEnum]
        
        for mcp_type in valid_types:
            config = ConnectionTestConfig(
                Url=TEST_URL,
                Type=mcp_type
            )
            assert config.Type == mcp_type

    def test_timeout_default(self):
        """Test timeout default value"""
        config = ConnectionTestConfig(
            Url=TEST_URL,
            Type="DLC"
        )
        assert config.Timeout == 5.0  # Default value


class TestTestMCPConnection:
    """Test cases for mcp_connection_test function"""

    @pytest.mark.asyncio
    async def test_mcp_connection_test_success(self):
        """Test successful MCP connection test"""
        with patch('infra.server.tools.mcp_call_tool', new_callable=create_async_mock) as mock_mcp_call:
            # Mock successful MCP call
            mock_mcp_call.return_value = create_mock_result(is_error=False, content_text="DataEngines")
            
            result = await mcp_connection_test(
                url=TEST_URL,
                mcp_type="DLC",
                timeout=TEST_TIMEOUT
            )
            
            assert result.Status == 200
            assert result.Error is None
            
            # Verify MCP call was made with correct parameters
            mock_mcp_call.assert_called_once_with(
                arguments={},
                url=TEST_URL,
                tool_name="DLCListEngines",
                timeout=TEST_TIMEOUT,
                sse_read_timeout=TEST_TIMEOUT*2
            )

    @pytest.mark.asyncio
    async def test_mcp_connection_test_unsupported_type(self):
        """Test MCP connection test with unsupported type"""
        result = await mcp_connection_test(
            url=TEST_URL,
            mcp_type="UNSUPPORTED_TYPE",
            timeout=TEST_TIMEOUT
        )
        
        assert result.Status == 404
        assert result.Error is not None
        assert result.Error.Code == ErrorCode.ConnectionTestError.value
        assert "Unsupported MCP type" in result.Error.Message

    @pytest.mark.asyncio
    async def test_mcp_connection_test_exception(self):
        """Test MCP connection test when exception occurs"""
        with patch('infra.server.tools.mcp_call_tool', new_callable=create_async_mock) as mock_mcp_call:
            # Mock MCP call that raises exception
            mock_mcp_call.side_effect = Exception("Network timeout")
            
            result = await mcp_connection_test(
                url=TEST_URL,
                mcp_type="DLC",
                timeout=TEST_TIMEOUT
            )
            
            assert result.Status == 424
            assert result.Error is not None
            assert result.Error.Code == ErrorCode.ConnectionTestError.value
            assert "mcp connection test failed" in result.Error.Message 