import unittest
import json
import sys
import os
import re
from pathlib import Path
from infra.jupyter.executor import IPythonExecutor, JupyterExecutor

class TestIPythonExecutor(unittest.TestCase):
    """IPythonExecutor 单元测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.executor = IPythonExecutor()
        self.test_cases = self.load_test_cases_from_notebook()
    
    def load_test_cases_from_notebook(self):
        """从ipython_case.ipynb文件加载测试用例"""
        notebook_path = Path(__file__).parent / "ipython_case.ipynb"
        
        with open(notebook_path, 'r', encoding='utf-8') as f:
            notebook_data = json.load(f)
        
        test_cases = []
        for i, cell in enumerate(notebook_data['cells']):
            if cell['cell_type'] == 'code':
                code = ''.join(cell['source'])
                
                outputs = cell.get('outputs', [])
                expected_success = True
                expected_error_type = None
                for output in outputs:
                    if output['output_type'] == 'error':
                        expected_success = False
                        expected_error_type = output['ename']
                test_case = {
                    "name": f"cell_{i+1}",
                    "code": code,
                    "expected_success": expected_success,
                    "expected_outputs": outputs,
                    "execution_count": cell.get('execution_count', i+1),
                    "skip_output_validation": '%pip install' in code  # 标记需要跳过输出校验的单元格
                }
                if expected_error_type:
                    test_case["expected_error_type"] = expected_error_type
                test_cases.append(test_case)
        return test_cases

    def outputs_equivalent(self, expected_outputs, actual_outputs):
        """比较outputs内容，对traceback和display_data进行特殊处理"""
        def strip_ansi(s):
            return re.sub(r'\x1b\[[0-9;]*m', '', s)

        if len(expected_outputs) != len(actual_outputs):
            return False, f"outputs数量不一致: 期望{len(expected_outputs)}，实际{len(actual_outputs)}"
        
        for i, (exp, act) in enumerate(zip(expected_outputs, actual_outputs)):
            # 对于error类型，特殊处理traceback
            if exp['output_type'] == 'error' and act.get('output_type') == 'error':
                # 创建副本进行修改
                exp_copy = exp.copy()
                act_copy = act.copy()
                
                # 移除traceback进行比较
                exp_copy.pop('traceback', None)
                act_copy.pop('traceback', None)
                
                # 比较除traceback外的其他字段
                if exp_copy != act_copy:
                    return False, f"第{i+1}个error字段不一致: 期望{exp_copy}，实际{act_copy}"
                
                # 单独比较traceback的每一行
                exp_tb = exp.get('traceback', [])
                act_tb = act.get('traceback', [])
                exp_tb = [strip_ansi(line).rstrip('\n') for line in exp_tb]
                act_tb = [strip_ansi(line).rstrip('\n') for line in act_tb]
                if len(exp_tb) != len(act_tb):
                    return False, f"第{i+1}个error traceback长度不一致: 期望{len(exp_tb)}，实际{len(act_tb)}"
                for idx, (exp_line, act_line) in enumerate(zip(exp_tb, act_tb)):
                    if exp_line != act_line:
                        return False, f"第{i+1}个error traceback第{idx+1}行不一致: 期望{exp_line!r}，实际{act_line!r}"
            
            # 对于display_data类型，特殊处理图片数据
            elif exp['output_type'] == 'display_data' or act.get('output_type') == 'execute_result':
                # 创建副本进行修改
                exp_copy = exp.copy()
                act_copy = act.copy()
                
                # 检查是否包含图片数据
                exp_data = exp_copy.get('data', {})
                act_data = act_copy.get('data', {})
                
                for key, value in exp_data.items():
                    
                    if key in ['image/png']:
                        assert key in act_data
                    else:
                        assert key in act_data, f"第{i+1}个display_data缺少key: {key}"
                        items = zip(value, act_data[key])
                        for item in items:
                            # print(item)
                            if item[0] != item[1]:
                                return False, f"第{i+1}个display_data第{key}个item不一致: 期望{item[0]}，实际{item[1]}"
            
            elif exp['output_type'] == 'stream':
                assert act['output_type'] == 'stream'
                assert exp['name'] == act['name']
                if isinstance(exp['text'], str):
                    assert isinstance(act['text'], str)
                    if exp['text'] != act['text']:
                        return False, f"第{i+1}个stream不一致: 期望{exp['text']}，实际{act['text']}"
                elif isinstance(exp['text'], list):
                    assert isinstance(act['text'], list)
                    for exp_line, act_line in zip(exp['text'], act['text']):
                        if 'memory usage' in exp_line:
                            continue
                        if exp_line != act_line:
                            return False, f"第{i+1}个stream第{exp_line}行不一致: 期望{exp_line}，实际{act_line}"
                else:
                    return exp == act, f"第{i+1}个stream不一致: 期望{exp}，实际{act}"
            else:
                # 其他类型直接比较
                if exp != act:
                    return False, f"第{i+1}个output不一致: 期望{exp}，实际{act}"
        
        return True, ""

    def test_executor_initialization(self):
        """测试执行器初始化"""
        executor = IPythonExecutor()
        self.assertIsNotNone(executor.ipython)
        self.assertEqual(executor.execution_count, 1)
    
    def test_execute_single_code_execution_result(self):
        """测试执行单个代码块 - notebook格式"""
        result, has_error = self.executor.execute("x = 5")
        
        self.assertIsInstance(result, dict)
        self.assertIn("execution_count", result)
        self.assertIn("outputs", result)
        self.assertEqual(result["execution_count"], 2)
    
    def test_execute_notebook_format(self):
        """测试Notebook格式输出"""
        result, has_error = self.executor.execute('print("Hello, World!")')
        
        self.assertIsInstance(result, dict)
        self.assertIn("execution_count", result)
        self.assertIn("outputs", result)
        
        # 验证输出结构
        outputs = result["outputs"]
        self.assertGreater(len(outputs), 0)
        
        # 验证stdout输出
        stdout_output = next((o for o in outputs if o.get("output_type") == "stream" and o.get("name") == "stdout"), None)
        self.assertIsNotNone(stdout_output)
        self.assertEqual(stdout_output["text"], ["Hello, World!\n"])
    
    def test_execute_json_format(self):
        """测试notebook格式输出（新版本不再支持JSON格式）"""
        result, has_error = self.executor.execute('print("Hello, World!")')
        
        self.assertIsInstance(result, dict)
        self.assertIn("execution_count", result)
        self.assertIn("outputs", result)
        
        # 验证输出内容
        outputs = result["outputs"]
        stdout_output = next((o for o in outputs if o.get("output_type") == "stream" and o.get("name") == "stdout"), None)
        self.assertIsNotNone(stdout_output)
        self.assertEqual(stdout_output["text"], ["Hello, World!\n"])
    
    def test_execute_multiple_execution_result(self):
        """测试执行多个代码块 - notebook格式"""
        codes = [
            "x = 1",
            "y = 2", 
            "print(x + y)"
        ]
        
        results, has_error = self.executor.execute_multiple(codes)
        
        self.assertIsInstance(results, list)
        self.assertEqual(len(results), 3)
        
        for result in results:
            self.assertIsInstance(result, dict)
            self.assertIn("execution_count", result)
            self.assertIn("outputs", result)
    
    def test_execute_multiple_notebook_format(self):
        """测试执行多个代码块 - Notebook格式"""
        codes = [
            "x = 1",
            "y = 2",
            "print(x + y)"
        ]
        
        results, has_error = self.executor.execute_multiple(codes)
        
        self.assertIsInstance(results, list)
        self.assertEqual(len(results), 3)
        
        for result in results:
            self.assertIsInstance(result, dict)
            self.assertIn("execution_count", result)
            self.assertIn("outputs", result)
    
    def test_execute_multiple_json_format(self):
        """测试执行多个代码块 - notebook格式（新版本不再支持JSON格式）"""
        codes = [
            "x = 1",
            "y = 2",
            "print(x + y)"
        ]
        
        results, has_error = self.executor.execute_multiple(codes)
        
        self.assertIsInstance(results, list)
        self.assertEqual(len(results), 3)
        
        for result in results:
            self.assertIsInstance(result, dict)
            self.assertIn("execution_count", result)
            self.assertIn("outputs", result)
    
    def test_execution_count_increment(self):
        """测试执行计数递增"""
        initial_count = self.executor.execution_count
        
        self.executor.execute("x = 1")
        self.assertEqual(self.executor.execution_count, initial_count + 1)
        
        self.executor.execute("y = 2")
        self.assertEqual(self.executor.execution_count, initial_count + 2)
    
    def test_convert_to_notebook_output_success(self):
        """测试成功执行的notebook输出转换（新版本直接返回notebook格式）"""
        result, has_error = self.executor.execute("x = 5\nprint(x)")
        
        self.assertIsInstance(result, dict)
        self.assertIn("execution_count", result)
        self.assertIn("outputs", result)
        
        # 验证stdout输出
        outputs = result["outputs"]
        stdout_output = next((o for o in outputs if o.get("output_type") == "stream" and o.get("name") == "stdout"), None)
        self.assertIsNotNone(stdout_output)
        self.assertIn("5\n", stdout_output["text"])

    def test_convert_to_notebook_output_error(self):
        """测试错误执行的notebook输出转换（新版本直接返回notebook格式）"""
        result, has_error = self.executor.execute("print(undefined_variable)")
        
        self.assertIsInstance(result, dict)
        self.assertIn("execution_count", result)
        self.assertIn("outputs", result)
        
        # 验证错误输出
        outputs = result["outputs"]
        error_output = next((o for o in outputs if o.get("output_type") == "error"), None)
        self.assertIsNotNone(error_output)
        self.assertIn("ename", error_output)
        self.assertIn("evalue", error_output)
        self.assertIn("traceback", error_output)
    
    def test_get_cell_outputs(self):
        """测试get_cell_outputs方法"""
        codes = [
            "x = 1",
            "y = 2",
            "print(x + y)"
        ]
        
        results, has_error = self.executor.execute_multiple(codes)
        
        self.assertIsInstance(results, list)
        self.assertEqual(len(results), 3)
        
        for result in results:
            self.assertIsInstance(result, dict)
            self.assertIn("execution_count", result)
            self.assertIn("outputs", result)
    
    def test_invalid_output_format(self):
        """测试无效输出格式（新版本不再支持output_format参数）"""
        # 新版本直接返回notebook格式，不需要output_format参数
        result, has_error = self.executor.execute("x = 1")
        self.assertIsInstance(result, dict)
        self.assertIn("execution_count", result)
        self.assertIn("outputs", result)
    
    def test_all_test_cases_from_notebook(self):
        """测试从notebook文件提取的所有测试用例，结构化比对outputs（同一个executor顺序执行，自动递增编号）"""
        print(f"\n从notebook文件加载了 {len(self.test_cases)} 个测试用例")
        executor = IPythonExecutor()
        for i, test_case in enumerate(self.test_cases):
            with self.subTest(test_case=test_case["name"]):
                print(f"\n测试用例 {i+1}: {test_case['name']}")
                print(f"代码: {repr(test_case['code'][:100])}...")
                actual_nb, has_error = executor.execute(test_case["code"])
                self.assertIsInstance(actual_nb, dict)
                
                # 检查是否需要跳过输出校验
                if test_case.get("skip_output_validation", False):
                    print(f"跳过输出校验: {test_case['name']} (包含 %pip install)")
                    print(f"✓ 测试用例 {i+1} 通过 (仅执行，跳过校验)")
                else:
                    print(f"期望outputs: {json.dumps(test_case['expected_outputs'], indent=2)}")
                    print(f"实际outputs: {json.dumps(actual_nb.get('outputs', []), indent=2)}")
                    eq, msg = self.outputs_equivalent(test_case["expected_outputs"], actual_nb.get("outputs", []))
                    self.assertTrue(eq, msg)
                    print(f"✓ 测试用例 {i+1} 通过")

    def test_init_pythonpath(self):
        """测试初始化pythonpath"""
        try:
            import tempfile
            temp_dir = tempfile.mkdtemp()
            print(f"temp_dir: {temp_dir}")
            rst, has_error = self.executor.execute('import sys; print(sys.path)', store_history=False)
            assert temp_dir not in str(rst)
            self.executor = IPythonExecutor(pythonpath=temp_dir)
            # 测试查找标准库模块
            rst, has_error = self.executor.execute('import sys; print(sys.path)', store_history=False)
            assert temp_dir in str(rst)

        finally:
            self.executor.shutdown()
    
    def test_add_pythonpath(self):
        """测试添加pythonpath"""
        try:
            import tempfile
            temp_dir = tempfile.mkdtemp()

            rst, has_error = self.executor.execute('from test_module import test_func; print(test_func())', store_history=False)
            assert 'ModuleNotFoundError' in str(rst)
            self.executor.add_pythonpath(temp_dir)
            print(f"self.executor: {rst}")
            # 测试查找标准库模块
            # INSERT_YOUR_CODE
            # 在临时目录下写入 test_module.py
            test_module_path = os.path.join(temp_dir, "test_module.py")
            with open(test_module_path, "w") as f:
                f.write("def test_func(): return 'test_func_ok'")

            rst, has_error = self.executor.execute('from test_module import test_func; print(test_func())', store_history=False)
            assert 'test_func_ok' in str(rst) and 'ModuleNotFoundError' not in str(rst)
            print(f"rst: {rst}")

        finally:
            self.executor.shutdown()

if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
