{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5893307f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://mirrors.tencentyun.com/pypi/simple/, http://mirrors.tencent.com/pypi/simple/\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.9/site-packages (3.9.4)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.9/site-packages (2.0.2)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.9/site-packages (2.3.1)\n", "Requirement already satisfied: importlib-resources>=3.2.0 in /usr/local/lib/python3.9/site-packages (from matplotlib) (6.5.2)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.9/site-packages (from matplotlib) (11.3.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.9/site-packages (from matplotlib) (1.4.7)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.9/site-packages (from matplotlib) (4.58.5)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.9/site-packages (from matplotlib) (24.1)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.9/site-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.9/site-packages (from matplotlib) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in /root/.local/lib/python3.9/site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.9/site-packages (from matplotlib) (1.3.0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.9/site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.9/site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: zipp>=3.1.0 in /root/.local/lib/python3.9/site-packages (from importlib-resources>=3.2.0->matplotlib) (3.21.0)\n", "Requirement already satisfied: six>=1.5 in /root/.local/lib/python3.9/site-packages (from python-dateutil>=2.7->matplotlib) (1.17.0)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install matplotlib numpy pandas"]}, {"cell_type": "code", "execution_count": 2, "id": "df33a39a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello, <PERSON>!\n"]}], "source": ["print(\"Hello, <PERSON>!\")"]}, {"cell_type": "code", "execution_count": 3, "id": "858e19b2", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["a=1\n", "b=2\n", "c=a+b\n", "c"]}, {"cell_type": "code", "execution_count": 4, "id": "36bc9040", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6\n"]}], "source": ["c=c+a+b\n", "c\n", "print(c)"]}, {"cell_type": "code", "execution_count": 5, "id": "c3a42ef9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello World\n"]}, {"data": {"text/markdown": ["\n", "## 数据概览\n", "\n", "### 基本信息\n", "- **数据形状**: 2 行 × 2 列\n", "- **内存使用**: 0.00 MB\n", "\n", "### 数据类型\n"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["\n", "## 数据概览\n", "\n", "### 基本信息\n", "- **数据形状**: 2 行 × 2 列\n", "- **内存使用**: 0.00 MB\n", "\n", "### 数据类型\n", "xx"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(\"Hello World\")\n", "from IPython.display import display, Markdown\n", "\n", "info = \"\"\"\n", "## 数据概览\n", "\n", "### 基本信息\n", "- **数据形状**: 2 行 × 2 列\n", "- **内存使用**: 0.00 MB\n", "\n", "### 数据类型\n", "\"\"\"\n", "\n", "display(Markdown(info))\n", "display(Markdown(info+\"xx\"))"]}, {"cell_type": "code", "execution_count": 6, "id": "3b63b78e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Circle Image\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 100x80 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Forecast for next 2 months:\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "# 方法2：强制设置显示大小\n", "fig = plt.figure(figsize=(1, 0.8), dpi=100)\n", "theta = np.linspace(0, 2 * np.pi, 100)\n", "x = np.cos(theta)\n", "y = np.sin(theta)\n", "plt.plot(x, y, linewidth=0.5)\n", "plt.title(\"Circle\", fontsize=6)\n", "plt.tight_layout(pad=0.05)\n", "print(\"Circle Image\")\n", "plt.show()\n", "print(\"\\nForecast for next 2 months:\")"]}, {"cell_type": "code", "execution_count": 7, "id": "c79bff95", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   a  b\n", "0  1  3\n", "1  2  4"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "df = pd.DataFrame({'a': [1, 2], 'b': [3, 4]})\n", "df"]}, {"cell_type": "code", "execution_count": 8, "id": "3893ce27", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 2 entries, 0 to 1\n", "Data columns (total 2 columns):\n", " #   Column  Non-Null Count  Dtype\n", "---  ------  --------------  -----\n", " 0   a       2 non-null      int64\n", " 1   b       2 non-null      int64\n", "dtypes: int64(2)\n", "memory usage: 160.0 bytes\n", "   a  b\n", "0  1  3\n", "1  2  4\n"]}], "source": ["import pandas as pd\n", "df = pd.DataFrame({'a': [1, 2], 'b': [3, 4]})\n", "df.info()\n", "print(df)"]}, {"cell_type": "code", "execution_count": 9, "id": "dd436f07", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据读取到了 DataFrame 变量 df\n", "数据形状: (2, 2)\n", "列类型: {'a': dtype('int64'), 'b': dtype('int64')}\n", "列名: ['a', 'b']\n", "数据实例:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   a  b\n", "0  1  3\n", "1  2  4"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["print(f\"数据读取到了 DataFrame 变量 df\")\n", "print(f\"数据形状: {df.shape}\")\n", "print(f\"列类型: {df.dtypes.to_dict()}\")\n", "print(f\"列名: {df.columns.tolist()}\")\n", "print(f\"数据实例:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "6eb0a98a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9\n"]}, {"ename": "NameError", "evalue": "name 'z' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[10], line 4\u001b[0m\n\u001b[1;32m      2\u001b[0m c\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28mprint\u001b[39m(c)\n\u001b[0;32m----> 4\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[43mz\u001b[49m)\n", "\u001b[0;31mNameError\u001b[0m: name 'z' is not defined"]}], "source": ["c=c+a+b\n", "c\n", "print(c)\n", "print(z)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 5}