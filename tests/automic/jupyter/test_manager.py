
import asyncio
import time
import pytest
import sys
import os
from infra.jupyter import manager as manager_module
from infra.jupyter.manager import <PERSON>elManager, KernelURN, KernelUtil, <PERSON>el<PERSON><PERSON><PERSON>
from tests.automic.jupyter.client_mock import MockDLCKernelClient
from common.logger.logger import logger

eg_url = "lb-2ohhzn5i-egfmcuxsj56dh1op.clb.cq-tencentclb.com:8888"
kernel_name1 = "rg-io2c6sp3k4"
kernel_name2 = "rg-gc48qclfke"
sub_uins = ["100041916576", "100041916577"]
urns = [
    KernelURN(kernel_type="dlc", sub_uin=sub_uin, kernel_host=eg_url, kernel_name=kernel_name1) for sub_uin in sub_uins
] + [
    KernelURN(kernel_type="dlc", sub_uin=sub_uin, kernel_host=eg_url, kernel_name=kernel_name2) for sub_uin in sub_uins
]

running_urn = urns[0]

def tools_reset_is_work(running_urn):
    """Test that kernel reset functionality works correctly"""
    # Create a kernel manager instance
    if not running_urn:
        pytest.skip("No running kernel")
    def check_can_reuse(urn_str: str) -> bool:
        return True
    client, urn = KernelUtil.start_kernel(running_urn,check_can_reuse=check_can_reuse)

    assert client is not None
    assert urn.is_valid()
    logger.info(KernelUtil.execute_cmd(urn, "print('keep_alive')", timeout=30))
    logger.info(KernelUtil.execute_cmd(urn, "%reset -f", timeout=30))
    rst = KernelUtil.execute_cmd(urn, "try:\n a=a+1\nexcept Exception as e:\n a=1\nprint(a)", timeout=30)
    rst = KernelUtil.execute_cmd(urn, "try:\n a=a+1\nexcept Exception as e:\n a=1\nprint(a)", timeout=30)
    assert '2\n' in str(rst[0][0]['text'][0])
    KernelUtil.execute_cmd(urn, "%reset -f", timeout=30)
    rst = KernelUtil.execute_cmd(urn, "try:\n a=a+1\nexcept Exception as e:\n a=1\nprint(a)", timeout=30)
    assert '1\n' in str(rst[0][0]['text'][0])
   
def setup_mock_client():
    manager_module.get_kernel_client = lambda x : MockDLCKernelClient(x)

async def kernel_assignments_is_ok():
    manager = KernelManager(keep_alive_seconds=5, cleanup_interval=1)
    kernel_manager = manager.get_kernel_manager()
    runner = kernel_manager.kernel_runner
    runner.interval_status_check = 0.5
    runner.intervel_reset_kernel = 0.1
    runner.intervel_start_kernel = 0.1
    await manager.session_start(sub_uin=sub_uins[0], kernel_host=eg_url, kernel_name=kernel_name1, usage_id="test_manager")
    await manager.session_start(sub_uin=sub_uins[1], kernel_host=eg_url, kernel_name=kernel_name2, usage_id="test_manager2")
    await asyncio.sleep(2)
    assert len(runner.available_kernels.keys()) == 2
    await manager.start_kernel_with_timeout("test_manager", urns[0])
    await manager.start_kernel_with_timeout("test_manager2", urns[3])
    assert len(runner.available_kernels.keys()) == 0
    assert len(runner.busy_kernels.keys()) == 2
    urn = await manager.get_assigned_urn("test_manager")
    assert urn is not None
    assert urn.can_assign_to(urns[0])
    urn = await manager.get_assigned_urn("test_manager2")
    assert urn is not None
    assert urn.can_assign_to(urns[3])

    await manager.session_stop("test_manager")
    await manager.session_stop("test_manager2")
    await asyncio.sleep(2)
    assert kernel_manager.kernel_assignments.get("test_manager") is None
    assert kernel_manager.kernel_assignments.get("test_manager2") is None
    assert len(runner.available_kernels.keys()) == 2
    await asyncio.sleep(5)
    assert len(runner.available_kernels.keys()) == 0
    runner.stop_background_threads()


async def kernel_reach_max_kernel():
    manager = KernelManager()
    kernel_manager = manager.get_kernel_manager()
    runner = kernel_manager.kernel_runner
    runner.interval_status_check = 3
    runner.intervel_reset_kernel = 0.1
    runner.intervel_start_kernel = 0.1
    await manager.start_kernel_with_timeout("test_manager", KernelURN.from_str(urns[0].to_str()))
    await manager.start_kernel_with_timeout("test_manager1", KernelURN.from_str(urns[0].to_str()))

    assert len(runner.available_kernels.keys()) == 0
    assert len(runner.busy_kernels.keys()) == 2
    await manager.start_kernel_with_timeout("test_manager", KernelURN.from_str(urns[0].to_str()))
    assert len(runner.available_kernels.keys()) == 0
    assert len(runner.busy_kernels.keys()) == 2
    try:
        await manager.start_kernel_with_timeout("test_manager3", KernelURN.from_str(urns[0].to_str()))
    except RuntimeError as e:
        assert "has reached the maximum number of kernels" in str(e)
    await manager.session_stop("test_manager")
    await asyncio.sleep(2)
    assert len(runner.available_kernels.keys()) == 1
    assert len(runner.busy_kernels.keys()) == 1
    await manager.start_kernel_with_timeout("test_manager3", KernelURN.from_str(urns[0].to_str()))
    assert len(runner.available_kernels.keys()) == 0
    assert len(runner.busy_kernels.keys()) == 2
    runner.stop_background_threads()

def test_manager():
    setup_mock_client()
    asyncio.run(kernel_assignments_is_ok())
    # asyncio.run(kernel_reach_max_kernel())

if __name__ == "__main__":
    tools_reset_is_work(running_urn)