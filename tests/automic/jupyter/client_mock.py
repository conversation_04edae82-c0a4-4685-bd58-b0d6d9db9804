from infra.jupyter.eg_client import DLCKernelClient, REQUEST_TIMEOUT, EGGatewayClient, EGKernelClient
from infra.jupyter.basic import KernelURN
from typing import List, Dict, Any, Tuple
import logging
from common.utils.thread import ThreadSafeInt, ThreadSafeMap
g_kernel_counter = ThreadSafeInt()
g_mock_kernels = ThreadSafeMap()

class MockGatewayClient(EGGatewayClient):
    """Mock Gateway Client for testing purposes."""
    
    def __init__(self, host="localhost:8888", timeout=REQUEST_TIMEOUT, use_secure_connection=False):
        """Initialize the mock client."""
        self.timeout = timeout
        self.http_api_endpoint = host
        self.ws_api_endpoint = host
        self.log = logging.getLogger(__name__)

    def start_kernel_if_not_exists(self, kernel_id, kernelspec_name, sub_uin="unknown", timeout=REQUEST_TIMEOUT, extra_env=None):
        """Mock start kernel if not exists."""
        if kernel_id and g_mock_kernels.get(kernel_id) is not None:
            return MockKernelClient(
                self.http_api_endpoint,
                self.ws_api_endpoint,
                kernel_id,
                timeout=timeout,
                logger=self.log,
            )
        # Create new mock kernel
        g_kernel_counter.increment()
        new_kernel_id = f"mock_kernel_{g_kernel_counter.get()}"
        g_mock_kernels[new_kernel_id] = {
            "id": new_kernel_id,
            "name": kernelspec_name,
            "sub_uin": sub_uin,
            "state": "idle"
        }
        self.log.info(f"Mock kernel {new_kernel_id} created")
        return MockKernelClient(
            self.http_api_endpoint,
            self.ws_api_endpoint,
            new_kernel_id,
            timeout=timeout,
            logger=self.log,
        )
    
    def get_kernel_status(self, kernelspec_name: str, kernel_id: str):
        """Mock get kernel status."""
        if g_mock_kernels.get(kernel_id) is not None:
            return g_mock_kernels.get(kernel_id)["state"]
        return "unknown"
    
    def get_kernel(self, kernel_id: str, kernelspec_name: str):
        """Mock get kernel by id."""
        if g_mock_kernels.get(kernel_id) is not None:
            return MockKernelClient(
                self.http_api_endpoint,
                self.ws_api_endpoint,
                kernel_id,
                timeout=REQUEST_TIMEOUT,
                logger=self.log,
            )
        return None
    
    def create_kernel(self, kernelspec_name, sub_uin="unknown", timeout=REQUEST_TIMEOUT, extra_env=None):
        """Mock start kernel."""
        g_kernel_counter.increment()
        kernel_id = f"mock_kernel_{g_kernel_counter.get()}"
        print(f"Mock kernel {kernel_id} created")
        g_mock_kernels[kernel_id] = {
            "id": kernel_id,
            "name": kernelspec_name,
            "sub_uin": sub_uin,
            "state": "idle"
        }
        return kernel_id
        
    def create_kernel_with_client(self, kernelspec_name, sub_uin="unknown", timeout=REQUEST_TIMEOUT, extra_env=None):
        kernel_id = self.create_kernel(kernelspec_name, sub_uin, timeout, extra_env)
        return MockKernelClient(
            self.http_api_endpoint,
            self.ws_api_endpoint,
            kernel_id,
            timeout=timeout,
            logger=self.log,
        )
    
    def list_kernels(self, name=None, kernel_id=None, debug=True):
        """Mock list kernels."""
        kernels = list(g_mock_kernels.values())
        if name:
            kernels = [kernel for kernel in kernels if kernel["name"] == name]
        if kernel_id:
            kernels = [kernel for kernel in kernels if kernel["id"] == kernel_id]
        return kernels

    def shutdown_kernel(self, kernel):
        """Mock shutdown kernel."""
        if kernel and g_mock_kernels.get(kernel.kernel_id) is not None:
            g_mock_kernels.pop(kernel.kernel_id)
            self.log.info(f"Mock kernel {kernel.kernel_id} shutdown")
            return True
        return False


class MockKernelClient(EGKernelClient):
    """Mock Kernel Client for testing purposes."""
    
    DEAD_MSG_ID = "deadbeefdeadbeefdeadbeefdeadbeef"
    POST_IDLE_TIMEOUT = 0.5
    DEFAULT_INTERRUPT_WAIT = 1

    def __init__(self, http_api_endpoint, ws_api_endpoint, kernel_id, timeout=REQUEST_TIMEOUT, logger=None):
        """Initialize the mock client."""
        self.shutting_down = False
        self.restarting = False
        self.http_api_endpoint = http_api_endpoint
        self.kernel_http_api_endpoint = f"{http_api_endpoint}/{kernel_id}"
        self.ws_api_endpoint = ws_api_endpoint
        self.kernel_ws_api_endpoint = f"{ws_api_endpoint}/{kernel_id}/channels"
        self.kernel_id = kernel_id
        self.log = logger
        self.timeout = timeout
        self.execution_count = 0
        self.variables = {}  # Store variables for mock execution

    def shutdown(self):
        """Mock shutdown."""
        self.shutting_down = True
        return True

    def execute(self, code, timeout=REQUEST_TIMEOUT):
        """Mock execute code."""
        self.execution_count += 1
        response = []
        has_error = False
        try:
            self.log.info(f"Mock kernel {self.kernel_id} executing code: {code}")
            response.append({
                "msg_type": "stream",
                "content": {
                    "text": code,
                    "name": "stdout",
                    "text": "Mock output\n"
                }
            })  
        except Exception as e:
            has_error = True
            self.log.error(f"Mock kernel {self.kernel_id} executing code: {code} failed: {e}")
            response.append({
                "msg_type": "error",
                "content": {
                    "ename": "MockError",
                    "evalue": str(e),
                    "traceback": []
                }
            })
        
        return response, has_error

    def interrupt(self):
        """Mock interrupt."""
        return True


    def get_state(self):
        """Mock get state."""
        return "idle" if not self.shutting_down else "dead"

    def start_interrupt_thread(self, wait_time=DEFAULT_INTERRUPT_WAIT):
        """Mock start interrupt thread."""
        pass



class MockDLCKernelClient(DLCKernelClient):
    """Mock DLCKernelClient for testing purposes."""

    def __init__(self, urn: KernelURN, timeout=REQUEST_TIMEOUT):
        super().__init__(urn, timeout)
        self.client : MockGatewayClient = MockGatewayClient(host=urn.kernel_host, timeout=timeout)
        self.timeout = timeout