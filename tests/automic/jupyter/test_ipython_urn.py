import unittest
import asyncio
from unittest.mock import patch
from infra.jupyter.basic import KernelURN
from infra.jupyter.executor import VenvManager
from infra.jupyter.manager import KernelManager, KernelUtil
from infra.jupyter.ipython_client import IpythonKernelClient

class TestIPythonKernelClient(unittest.TestCase):
    """测试IPython内核客户端"""
    
    def setUp(self):
        """测试前准备"""
        self.urn = KernelURN(
            sub_uin="test_user",
            kernel_host="_",
            kernel_name="_",
            kernel_id="test_kernel",
            kernel_type="ipython"
        )
    
    @patch('infra.jupyter.ipython_client.env.EXECUTION_MODE_RAY', False)
    def test_ipython_kernel_client_local_exists_kernel(self):
        """测试本地IPython内核存在性检查"""
        client = IpythonKernelClient(self.urn)
        # 启动前状态
        self.assertFalse(client.kernel_status_ready())
        
        # 启动前检查
        kernels = client.exists_kernel()
        self.assertEqual(len(kernels), 0)
        self.assertIsNotNone(client)
        self.assertEqual(client.urn, self.urn)
        
        # 启动后检查
        client.start_kernel()

        kernels = client.exists_kernel()
        self.assertEqual(len(kernels), 1)
        self.assertEqual(kernels[0]['id'], self.urn.kernel_id)
        self.assertEqual(kernels[0]['name'], self.urn.kernel_name)
        self.assertTrue(client.kernel_status_ready())
        # 测试简单代码执行
        outputs, has_error = client.execute("print('Hello, World!')")
        print(outputs, has_error)
        self.assertFalse(has_error)
        self.assertIsInstance(outputs, list)
        
        # 验证输出包含stdout
        stdout_outputs = [o for o in outputs if o.get('output_type') == 'stream' and o.get('name') == 'stdout']
        self.assertGreater(len(stdout_outputs), 0)
        self.assertIn('Hello, World!', str(stdout_outputs[0]))

        # 测试多步骤代码执行
        outputs, has_error = client.execute("""a = 1""")
        self.assertFalse(has_error)
        outputs, has_error = client.execute("a+=1")
        self.assertFalse(has_error)
        outputs, has_error = client.execute("print(a)")
        self.assertFalse(has_error)
        self.assertIsInstance(outputs, list)
        self.assertIn('2', str(outputs[0]))


    @patch('infra.jupyter.ipython_client.env.EXECUTION_MODE_RAY', False)
    def test_ipython_kernel_client_local_install_requirements(self):
        """测试本地IPython内核 install_requirements"""
        import tempfile
        import shutil
        import os

        # 创建一个临时目录模拟 lib_path
        temp_dir = tempfile.mkdtemp()
        try:
            client = IpythonKernelClient(self.urn)
            # 覆盖 lib_path 到临时目录，防止污染真实环境
            print(f"venv_path: {client.client.venv_manager.venv_path}, cache_path: {client.client.venv_manager.cache_path}")
            client.client.venv_manager = VenvManager(temp_dir + "/venv", temp_dir + "/cache")
            client.start_kernel()
            pkg_name = "six21"
            # client.execute_in_subprocess(f"source {client.venv_path}/bin/activate && pip uninstall -y {pkg_name} -y")

            self.assertTrue(os.path.exists(temp_dir))
            self.assertFalse(os.path.exists(os.path.join(temp_dir, pkg_name)))

            outputs, has_error = client.execute(f"import {pkg_name}")
            print(outputs, has_error)
            self.assertTrue(has_error, f"{pkg_name} should not be installed in lib_path")

            # 使用一个已安装且体积小的包，防止网络依赖和安装失败
            requirements = [pkg_name]
            outputs, has_error = client.install_requirements(requirements)
            self.assertIsInstance(outputs, list)
            self.assertEqual(len(outputs), 1)
            self.assertIn("returncode", outputs[0])
            self.assertIn("stdout", outputs[0])
            self.assertIn("stderr", outputs[0])
            self.assertFalse(has_error, f"install_requirements failed: {outputs[0].get('stderr', '')}")

            # 检查 pkg_name 是否被安装到 lib_path
            installed = any(
                d.startswith(pkg_name)
                for d in os.listdir(client.client.venv_manager.lib_path.split(':')[0]) + os.listdir(client.client.venv_manager.lib_path.split(':')[1])
            )
            self.assertTrue(installed, f"{pkg_name} should be installed in lib_path")

            outputs, has_error = client.execute(f"import {pkg_name}")
            print(outputs, has_error)
            self.assertFalse(has_error, f"{pkg_name} should be installed in lib_path")

            # 判断非法包
            requirements = [f"{pkg_name}123"]
            outputs, has_error = client.install_requirements(requirements)
            self.assertIsInstance(outputs, list)
            self.assertEqual(len(outputs), 1)
            # self.assertIn("requirement", outputs[0])
            self.assertIn("returncode", outputs[0])
            self.assertIn("stderr", outputs[0]) 
            self.assertIn(f"{pkg_name}123", outputs[0].get("stderr", ""))
            self.assertTrue(has_error, f"install_requirements should failed: {outputs[0].get('stderr', '')}")

            # 判断目录不存在
            installed = any(
                d.startswith(f"{pkg_name}123")
                for d in os.listdir(client.client.venv_manager.lib_path.split(':')[0]) + os.listdir(client.client.venv_manager.lib_path.split(':')[1])
            )
            self.assertFalse(installed, f"{pkg_name}123 should not be installed in lib_path")
        finally:
            shutil.rmtree(temp_dir)


class TestIPythonKernelManager(unittest.TestCase):
    """测试IPython内核管理器"""
    
    def setUp(self):
        """测试前准备"""
        self.test_sub_uin = "test_user_123"
        self.test_kernel_host = "localhost"
        self.test_kernel_name = "python3"
        self.test_usage_id = "test_usage_789"
        self.test_kernel_id = "test_kernel_123"
        
    @patch('infra.jupyter.manager.env.EXECUTION_MODE_RAY', False)
    def test_kernel_manager_ipython_session_start(self):
        """测试IPython内核会话启动"""
        async def test_session_start():
            manager = KernelManager(use_ray=False)
            await manager.session_start(
                sub_uin=self.test_sub_uin,
                kernel_host=self.test_kernel_host,
                kernel_name=self.test_kernel_name,
                usage_id=self.test_usage_id,
                kernel_type="ipython"
            )
            
        asyncio.run(test_session_start())
        
    @patch('infra.jupyter.manager.env.EXECUTION_MODE_RAY', False)
    def test_kernel_manager_ipython_start_with_timeout(self):
        """测试IPython内核超时启动"""
        async def test_start_with_timeout():
            manager = KernelManager(use_ray=False)
            kernel_urn = KernelURN(
                sub_uin=self.test_sub_uin,
                kernel_host=self.test_kernel_host,
                kernel_name=self.test_kernel_name,
                kernel_type="ipython"
            )
            
            result_urn = await manager.start_kernel_with_timeout(
                usage_id=self.test_usage_id,
                kernel_urn=kernel_urn,
                timeout=30
            )
            
            self.assertIsNotNone(result_urn)
            self.assertEqual(result_urn.kernel_type, "ipython")
            self.assertTrue(result_urn.has_kernel_id())
            
        asyncio.run(test_start_with_timeout())
        
    @patch('infra.jupyter.manager.env.EXECUTION_MODE_RAY', False)
    def test_kernel_manager_ipython_code_execution(self):
        """测试IPython内核代码执行"""
        async def test_code_execution():
            manager = KernelManager(use_ray=False)
            kernel_urn = KernelURN(
                sub_uin=self.test_sub_uin,
                kernel_host=self.test_kernel_host,
                kernel_name=self.test_kernel_name,
                kernel_id=self.test_kernel_id,
                kernel_type="ipython"
            )
            
            # 启动内核
            result_urn = await manager.start_kernel_with_timeout(
                usage_id=self.test_usage_id,
                kernel_urn=kernel_urn,
                timeout=30
            )
            
            # 执行代码
            outputs, has_error = KernelUtil.execute_cmd(result_urn, "print('Hello from IPython!')")
            self.assertFalse(has_error)
            self.assertIsInstance(outputs, list)
            
            # 验证输出
            stdout_outputs = [o for o in outputs if o.get('output_type') == 'stream' and o.get('name') == 'stdout']
            self.assertGreater(len(stdout_outputs), 0)
            self.assertIn('Hello from IPython!', str(stdout_outputs[0]))
            
        asyncio.run(test_code_execution())
        
    @patch('infra.jupyter.manager.env.EXECUTION_MODE_RAY', False)
    def test_kernel_manager_ipython_session_stop(self):
        """测试IPython内核会话停止"""
        async def test_session_stop():
            manager = KernelManager(use_ray=False)
            
            # 先启动会话
            await manager.session_start(
                sub_uin=self.test_sub_uin,
                kernel_host=self.test_kernel_host,
                kernel_name=self.test_kernel_name,
                usage_id=self.test_usage_id,
                kernel_type="ipython"
            )
            
            # 停止会话
            await manager.session_stop(self.test_usage_id)
            
        asyncio.run(test_session_stop())


class TestIPythonKernelUtil(unittest.TestCase):
    """测试IPython内核工具类"""
    
    def setUp(self):
        """测试前准备"""
        self.urn = KernelURN(
            sub_uin="test_user",
            kernel_host="localhost",
            kernel_name="python3",
            kernel_id="test_kernel",
            kernel_type="ipython"
        )
        
    def test_kernel_util_reuse_ipython_kernel(self):
        """测试IPython内核重用逻辑"""
        # IPython模式应该直接返回原URN
        result_urn = KernelUtil.reuse_kernel(self.urn)
        self.assertEqual(result_urn, self.urn)
        
    def test_kernel_util_start_ipython_kernel(self):
        """测试IPython内核启动"""
        # 测试启动新的IPython内核
        urn_without_id = KernelURN(
            sub_uin="test_user",
            kernel_host="localhost",
            kernel_name="python3",
            kernel_type="ipython"
        )
        
        result_urn = KernelUtil.start_kernel(urn_without_id)
        self.assertEqual(result_urn.kernel_type, "ipython")
        self.assertTrue(result_urn.has_kernel_id())
        
    def test_kernel_util_execute_ipython_cmd(self):
        """测试IPython内核命令执行"""
        # 先启动内核
        result_urn = KernelUtil.start_kernel(self.urn)
        
        # 执行命令
        outputs, has_error = KernelUtil.execute_cmd(result_urn, "print('Test execution')")
        self.assertFalse(has_error)
        self.assertIsInstance(outputs, list)
        
    def test_kernel_util_check_ipython_status(self):
        """测试IPython内核状态检查"""
        # 先启动内核
        result_urn = KernelUtil.start_kernel(self.urn)
        
        # 检查状态
        status = KernelUtil.check_kernel_status_ready(result_urn)
        self.assertTrue(status)
        
    def test_kernel_util_keep_alive_ipython(self):
        """测试IPython内核保活"""
        # 先启动内核
        result_urn = KernelUtil.start_kernel(self.urn)
        
        # 执行保活
        success = KernelUtil.execute_keep_alive(result_urn)
        self.assertTrue(success)

class TestIPythonURN(unittest.TestCase):
    """测试IPython类型内核URN的功能"""
    
    def setUp(self):
        """测试前准备"""
        self.test_sub_uin = "test_user_123"
        self.test_kernel_host = "localhost"
        self.test_kernel_name = "python3"
        self.test_kernel_id = "test_kernel_456"
        self.test_usage_id = "test_usage_789"
        
    def test_ipython_urn_creation(self):
        """测试创建ipython类型的KernelURN"""
        # 测试创建ipython类型的URN
        urn = KernelURN(
            sub_uin=self.test_sub_uin,
            kernel_host=self.test_kernel_host,
            kernel_name=self.test_kernel_name,
            kernel_id=self.test_kernel_id,
            kernel_type="ipython"
        )
        
        self.assertEqual(urn.kernel_type, "ipython")
        self.assertEqual(urn.sub_uin, self.test_sub_uin)
        self.assertEqual(urn.kernel_host, self.test_kernel_host)
        self.assertEqual(urn.kernel_name, self.test_kernel_name)
        self.assertEqual(urn.kernel_id, self.test_kernel_id)
        self.assertTrue(urn.is_ipython_mode())
        
    def test_ipython_urn_string_format(self):
        """测试ipython类型URN的字符串格式"""
        urn = KernelURN(
            sub_uin=self.test_sub_uin,
            kernel_host=self.test_kernel_host,
            kernel_name=self.test_kernel_name,
            kernel_id=self.test_kernel_id,
            kernel_type="ipython"
        )
        
        expected_str = f"ipython:::{self.test_sub_uin}:::{self.test_kernel_host}:::{self.test_kernel_name}:::{self.test_kernel_id}"
        self.assertEqual(urn.to_str(), expected_str)
        
        expected_str_without_id = f"ipython:::{self.test_sub_uin}:::{self.test_kernel_host}:::{self.test_kernel_name}"
        self.assertEqual(urn.str_without_id(), expected_str_without_id)
        
    def test_ipython_urn_from_string(self):
        """测试从字符串创建ipython类型URN"""
        urn_str = f"ipython:::{self.test_sub_uin}:::{self.test_kernel_host}:::{self.test_kernel_name}:::{self.test_kernel_id}"
        urn = KernelURN.from_str(urn_str)
        
        self.assertEqual(urn.kernel_type, "ipython")
        self.assertEqual(urn.sub_uin, self.test_sub_uin)
        self.assertEqual(urn.kernel_host, self.test_kernel_host)
        self.assertEqual(urn.kernel_name, self.test_kernel_name)
        self.assertEqual(urn.kernel_id, self.test_kernel_id)
        self.assertTrue(urn.is_ipython_mode())
        
    def test_ipython_urn_validation(self):
        """测试ipython类型URN的验证"""
        # 有效的ipython URN
        valid_urn = KernelURN(
            sub_uin=self.test_sub_uin,
            kernel_host=self.test_kernel_host,
            kernel_name=self.test_kernel_name,
            kernel_id=self.test_kernel_id,
            kernel_type="ipython"
        )
        self.assertTrue(valid_urn.is_valid())
        self.assertTrue(valid_urn.is_valid(check_kernel_id=False))
        
        # 无效的ipython URN（缺少kernel_id）
        invalid_urn = KernelURN(
            sub_uin=self.test_sub_uin,
            kernel_host=self.test_kernel_host,
            kernel_name=self.test_kernel_name,
            kernel_type="ipython"
        )
        self.assertFalse(invalid_urn.is_valid())
        self.assertTrue(invalid_urn.is_valid(check_kernel_id=False))
        
    def test_ipython_urn_assignment(self):
        """测试ipython类型URN的分配逻辑"""
        urn1 = KernelURN(
            sub_uin=self.test_sub_uin,
            kernel_host=self.test_kernel_host,
            kernel_name=self.test_kernel_name,
            kernel_id=self.test_kernel_id,
            kernel_type="ipython"
        )
        
        urn2 = KernelURN(
            sub_uin=self.test_sub_uin,
            kernel_host=self.test_kernel_host,
            kernel_name=self.test_kernel_name,
            kernel_type="ipython"
        )
        
        # urn1有kernel_id，urn2没有，应该可以分配
        self.assertTrue(urn1.can_assign_to(urn2))
        
        # 相同的内核应该可以分配给自己
        self.assertTrue(urn1.can_assign_to(urn1))
        
        # 不同的kernel_type不能分配
        urn3 = KernelURN(
            sub_uin=self.test_sub_uin,
            kernel_host=self.test_kernel_host,
            kernel_name=self.test_kernel_name,
            kernel_type="dlc"
        )
        self.assertFalse(urn1.can_assign_to(urn3))


if __name__ == '__main__':
    unittest.main()
