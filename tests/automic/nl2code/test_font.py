import unittest
import sys
import os

# Add the project root to the path to import the function
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../../'))

from infra.mcp.codegen.nl2code.core import filter_plt_font_code


class TestFilterPltFontCode(unittest.TestCase):
    """Test cases for filter_plt_font_code function"""

    def test_filter_plt_rcparams_font(self):
        """Test filtering plt.rcParams font settings"""
        code = """
import matplotlib.pyplot as plt
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.size'] = 12
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.plot([1, 2, 3], [1, 4, 2])
"""
        result = filter_plt_font_code(code)
        
        # Check that font family lines are filtered and commented
        self.assertIn("# Filtered font setting: plt.rcParams['font.family'] = 'sans-serif'", result)
        # Check that font size lines are preserved (not filtered)
        self.assertIn("plt.rcParams['font.size'] = 12", result)
        # Check that font sans-serif lines are preserved (not filtered)
        self.assertIn("# Filtered font setting: plt.rcParams['font.sans-serif'] = ['SimHei']", result)
        # Check that non-font lines are preserved
        self.assertIn("import matplotlib.pyplot as plt", result)
        self.assertIn("plt.plot([1, 2, 3], [1, 4, 2])", result)

    def test_filter_matplotlib_rcparams_font(self):
        """Test filtering matplotlib.rcParams font settings"""
        code = """
import matplotlib
matplotlib.rcParams['font.family'] = 'serif'
matplotlib.rcParams['font.weight'] = 'bold'
plt.plot([1, 2, 3], [1, 4, 2])
"""
        result = filter_plt_font_code(code)
        
        self.assertIn("# Filtered font setting: matplotlib.rcParams['font.family'] = 'serif'", result)
        # Font weight should be preserved (not filtered)
        self.assertIn("matplotlib.rcParams['font.weight'] = 'bold'", result)
        self.assertIn("import matplotlib", result)
        self.assertIn("plt.plot([1, 2, 3], [1, 4, 2])", result)

    def test_filter_rcparams_font(self):
        """Test filtering rcParams font settings"""
        code = """
from matplotlib import rcParams
rcParams['font.family'] = 'monospace'
rcParams['font.size'] = 14
"""
        result = filter_plt_font_code(code)
        
        self.assertIn("# Filtered font setting: rcParams['font.family'] = 'monospace'", result)
        # Font size should be preserved (not filtered)
        self.assertIn("rcParams['font.size'] = 14", result)
        self.assertIn("from matplotlib import rcParams", result)

    def test_filter_set_font_methods(self):
        """Test filtering set_font* methods"""
        code = """
fig, ax = plt.subplots()
ax.set_fontfamily('Arial')
ax.set_fontsize(16)
ax.set_fontweight('normal')
ax.set_fontstyle('italic')
ax.set_fontname('Times New Roman')
ax.plot([1, 2, 3], [1, 4, 2])
"""
        result = filter_plt_font_code(code)
        
        self.assertIn("# Filtered font setting: ax.set_fontfamily('Arial')", result)
        # Font size, weight, and style should be preserved (not filtered)
        self.assertIn("ax.set_fontsize(16)", result)
        self.assertIn("ax.set_fontweight('normal')", result)
        self.assertIn("ax.set_fontstyle('italic')", result)
        self.assertIn("# Filtered font setting: ax.set_fontname('Times New Roman')", result)
        self.assertIn("fig, ax = plt.subplots()", result)
        self.assertIn("ax.plot([1, 2, 3], [1, 4, 2])", result)

    def test_filter_font_parameters(self):
        """Test filtering font parameters in function calls"""
        code = """
plt.title('My Plot', fontfamily='Arial', fontsize=14)
plt.xlabel('X Axis', font_family='Times', font_size=12)
plt.ylabel('Y Axis', fontname='Helvetica', font_name='Verdana')
plt.legend(fontweight='bold', font_weight='normal')
plt.text(0.5, 0.5, 'Text', fontstyle='italic', font_style='normal')
"""
        result = filter_plt_font_code(code)
        
        # Check that lines with font family/name parameters are filtered
        self.assertIn("# Filtered font setting: plt.title('My Plot', fontfamily='Arial', fontsize=14)", result)
        self.assertIn("# Filtered font setting: plt.xlabel('X Axis', font_family='Times', font_size=12)", result)
        self.assertIn("# Filtered font setting: plt.ylabel('Y Axis', fontname='Helvetica', font_name='Verdana')", result)
        # Font weight and style should be preserved (not filtered)
        self.assertIn("plt.legend(fontweight='bold', font_weight='normal')", result)
        self.assertIn("plt.text(0.5, 0.5, 'Text', fontstyle='italic', font_style='normal')", result)

    def test_filter_plt_rc_font(self):
        """Test filtering plt.rc font settings"""
        code = """
plt.rc('font', family='sans-serif')
plt.rc('font', size=12)
matplotlib.rc('font', weight='bold')
rc('font', style='italic')
"""
        result = filter_plt_font_code(code)
        
        self.assertIn("# Filtered font setting: plt.rc('font', family='sans-serif')", result)
        # Font size, weight, and style should be preserved (not filtered)
        self.assertIn("plt.rc('font', size=12)", result)
        self.assertIn("matplotlib.rc('font', weight='bold')", result)
        self.assertIn("rc('font', style='italic')", result)

    def test_case_insensitive_filtering(self):
        """Test that filtering is case insensitive"""
        code = """
plt.RCPARAMS['font.family'] = 'sans-serif'
PLT.RCPARAMS['font.size'] = 12
ax.SET_FONTFAMILY('Arial')
"""
        result = filter_plt_font_code(code)
        print(result)
        # Should still filter despite case differences
        self.assertIn("# Filtered font setting: plt.RCPARAMS['font.family'] = 'sans-serif'", result)
        self.assertIn("PLT.RCPARAMS['font.size'] = 12", result)
        self.assertIn("# Filtered font setting: ax.SET_FONTFAMILY('Arial')", result)

    def test_preserve_non_font_code(self):
        """Test that non-font related code is preserved"""
        code = """
import matplotlib.pyplot as plt
import numpy as np

# Create data
x = np.linspace(0, 10, 100)
y = np.sin(x)

# Create plot
plt.figure(figsize=(10, 6))
plt.plot(x, y, color='blue', linewidth=2)
plt.title('Sine Wave')
plt.xlabel('X')
plt.ylabel('Y')
plt.grid(True)
plt.show()
"""
        result = filter_plt_font_code(code)
        
        # All lines should be preserved as they don't contain font settings
        expected_lines = [
            "import matplotlib.pyplot as plt",
            "import numpy as np",
            "# Create data",
            "x = np.linspace(0, 10, 100)",
            "y = np.sin(x)",
            "# Create plot",
            "plt.figure(figsize=(10, 6))",
            "plt.plot(x, y, color='blue', linewidth=2)",
            "plt.title('Sine Wave')",
            "plt.xlabel('X')",
            "plt.ylabel('Y')",
            "plt.grid(True)",
            "plt.show()"
        ]
        
        for line in expected_lines:
            self.assertIn(line, result)

    def test_mixed_code_with_font_settings(self):
        """Test code with both font and non-font settings"""
        code = """
import matplotlib.pyplot as plt
import numpy as np

# Set font
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.size'] = 12

# Create data
x = np.linspace(0, 10, 100)
y = np.sin(x)

# Create plot
plt.figure(figsize=(10, 6))
plt.plot(x, y, color='blue', linewidth=2)
plt.title('Sine Wave')
plt.xlabel('X')
plt.ylabel('Y')
plt.grid(True)
plt.show()
"""
        result = filter_plt_font_code(code)
        
        # Font family lines should be filtered
        self.assertIn("# Filtered font setting: plt.rcParams['font.family'] = 'sans-serif'", result)
        # Font size should be preserved (not filtered)
        self.assertIn("plt.rcParams['font.size'] = 12", result)
        
        # Non-font lines should be preserved
        self.assertIn("import matplotlib.pyplot as plt", result)
        self.assertIn("import numpy as np", result)
        self.assertIn("# Create data", result)
        self.assertIn("x = np.linspace(0, 10, 100)", result)
        self.assertIn("y = np.sin(x)", result)
        self.assertIn("# Create plot", result)
        self.assertIn("plt.figure(figsize=(10, 6))", result)
        self.assertIn("plt.plot(x, y, color='blue', linewidth=2)", result)
        self.assertIn("plt.title('Sine Wave')", result)
        self.assertIn("plt.xlabel('X')", result)
        self.assertIn("plt.ylabel('Y')", result)
        self.assertIn("plt.grid(True)", result)
        self.assertIn("plt.show()", result)

    def test_empty_code(self):
        """Test with empty code string"""
        code = ""
        result = filter_plt_font_code(code)
        self.assertEqual(result, "")

    def test_single_line_code(self):
        """Test with single line code"""
        code = "plt.rcParams['font.family'] = 'sans-serif'"
        result = filter_plt_font_code(code)
        self.assertIn("# Filtered font setting: plt.rcParams['font.family'] = 'sans-serif'", result)

    def test_code_with_whitespace(self):
        """Test code with various whitespace patterns"""
        code = """
import matplotlib.pyplot as plt

plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.size'] = 12

plt.plot([1, 2, 3], [1, 4, 2])
"""
        result = filter_plt_font_code(code)
        
        # Should preserve empty lines and whitespace
        lines = result.split('\n')
        self.assertIn("", lines)  # Empty line should be preserved
        self.assertIn("# Filtered font setting: plt.rcParams['font.family'] = 'sans-serif'", lines)
        self.assertIn("plt.rcParams['font.size'] = 12", lines)
        self.assertIn("plt.plot([1, 2, 3], [1, 4, 2])", lines)

    def test_error_handling(self):
        """Test error handling when code processing fails"""
        # This test would require mocking the function to raise an exception
        # For now, we'll test that the function handles normal cases gracefully
        code = "plt.rcParams['font.family'] = 'sans-serif'"
        result = filter_plt_font_code(code)
        self.assertIsInstance(result, str)
        self.assertIn("# Filtered font setting:", result)

    def test_complex_font_settings(self):
        """Test complex font settings with multiple parameters"""
        code = """
import matplotlib.pyplot as plt

# Complex font configuration
plt.rcParams.update({
    'font.family': 'sans-serif',
    'font.size': 12,
    'font.weight': 'normal',
    'font.style': 'normal'
})

# Individual font settings
ax = plt.gca()
ax.set_fontfamily('Arial')
ax.set_fontsize(14)
ax.set_fontweight('bold')
ax.set_fontstyle('italic')
ax.set_fontname('Times New Roman')

# Function calls with font parameters
plt.title('Title', fontfamily='Arial', fontsize=16, fontweight='bold')
plt.xlabel('X Label', font_family='Times', font_size=12, font_weight='normal')
"""
        result = filter_plt_font_code(code)
        
        # Check that font family/name related lines are filtered
        font_patterns_filtered = [
            "'font.family': 'sans-serif'",
            "ax.set_fontfamily('Arial')",
            "ax.set_fontname('Times New Roman')",
            "plt.title('Title', fontfamily='Arial', fontsize=16, fontweight='bold')",
            "plt.xlabel('X Label', font_family='Times', font_size=12, font_weight='normal')"
        ]
        
        # Check that font size, weight, style lines are preserved (not filtered)
        font_patterns_preserved = [
            "'font.size': 12",
            "'font.weight': 'normal'",
            "'font.style': 'normal'",
            "ax.set_fontsize(14)",
            "ax.set_fontweight('bold')",
            "ax.set_fontstyle('italic')"
        ]
        
        print(result)
        for pattern in font_patterns_filtered:
            self.assertIn(f"# Filtered font setting: {pattern}", result)
        
        for pattern in font_patterns_preserved:
            self.assertIn(pattern, result)
        
        # Check that non-font lines are preserved
        self.assertIn("import matplotlib.pyplot as plt", result)
        self.assertIn("# Complex font configuration", result)
        self.assertIn("# Individual font settings", result)
        self.assertIn("ax = plt.gca()", result)
        self.assertIn("# Function calls with font parameters", result)

    def test_preserve_leading_whitespace(self):
        """Test that leading whitespace is preserved in both filtered and non-filtered lines"""
        code = """
import matplotlib.pyplot as plt
import numpy as np

def create_plot():
    # Set font with indentation
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['font.size'] = 12
    
    # Create data with indentation
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    
    # Create plot with indentation
    plt.figure(figsize=(10, 6))
    plt.plot(x, y, color='blue', linewidth=2)
    plt.title('Sine Wave')
    plt.xlabel('X')
    plt.ylabel('Y')
    plt.grid(True)
    plt.show()

class PlotManager:
    def __init__(self):
        # Font settings with class indentation
        plt.rcParams['font.weight'] = 'bold'
        plt.rcParams['font.style'] = 'normal'
    
    def setup_plot(self):
        # More font settings with method indentation
        ax = plt.gca()
        ax.set_fontfamily('Arial')
        ax.set_fontname("Times New Roman")
        ax.set_fontsize(14)
        
        # Regular plotting code with indentation
        ax.plot([1, 2, 3], [1, 4, 2])
        return ax
"""
        result = filter_plt_font_code(code)
        
        # Split result into lines to check indentation
        lines = result.split('\n')
        
        # Check that indentation is preserved for non-font lines
        self.assertIn("import matplotlib.pyplot as plt", lines)
        self.assertIn("import numpy as np", lines)
        self.assertIn("def create_plot():", lines)
        self.assertIn("    # Create data with indentation", lines)
        self.assertIn("    x = np.linspace(0, 10, 100)", lines)
        self.assertIn("    y = np.sin(x)", lines)
        self.assertIn("    # Create plot with indentation", lines)
        self.assertIn("    plt.figure(figsize=(10, 6))", lines)
        self.assertIn("    plt.plot(x, y, color='blue', linewidth=2)", lines)
        self.assertIn("    plt.title('Sine Wave')", lines)
        self.assertIn("    plt.xlabel('X')", lines)
        self.assertIn("    plt.ylabel('Y')", lines)
        self.assertIn("    plt.grid(True)", lines)
        self.assertIn("    plt.show()", lines)
        self.assertIn("class PlotManager:", lines)
        self.assertIn("    def __init__(self):", lines)
        self.assertIn("    def setup_plot(self):", lines)
        self.assertIn("        # Regular plotting code with indentation", lines)
        self.assertIn("        ax.plot([1, 2, 3], [1, 4, 2])", lines)
        self.assertIn("        return ax", lines)
        
        # Check that font lines are filtered with correct indentation preserved
        self.assertIn("# Filtered font setting: plt.rcParams['font.family'] = 'sans-serif'", lines)
        # Font size should be preserved (not filtered)
        self.assertIn("    plt.rcParams['font.size'] = 12", lines)
        # Font weight should be preserved (not filtered)
        self.assertIn("        plt.rcParams['font.weight'] = 'bold'", lines)
        # Font style should be preserved (not filtered)
        self.assertIn("# Filtered font setting: ax.set_fontfamily('Arial')", lines)
        self.assertIn("# Filtered font setting: ax.set_fontname(\"Times New Roman\")", lines)

    def test_preserve_mixed_indentation_levels(self):
        """Test that different indentation levels are preserved correctly"""
        code = """
import matplotlib.pyplot as plt

# No indentation
plt.rcParams['font.family'] = 'sans-serif'

def level1():
    # Level 1 indentation (4 spaces)
    plt.rcParams['font.size'] = 12
    
    def level2():
        # Level 2 indentation (8 spaces)
        plt.rcParams['font.weight'] = 'bold'
        
        if True:
            # Level 3 indentation (12 spaces)
            plt.rcParams['font.style'] = 'italic'
            
            for i in range(3):
                # Level 4 indentation (16 spaces)
                ax = plt.gca()
                ax.set_fontfamily('Arial')
"""
        result = filter_plt_font_code(code)
        
        # Split result into lines
        lines = result.split('\n')
        
        # Check that non-font lines preserve their indentation
        self.assertIn("import matplotlib.pyplot as plt", lines)
        self.assertIn("def level1():", lines)
        self.assertIn("    # Level 1 indentation (4 spaces)", lines)
        self.assertIn("    def level2():", lines)
        self.assertIn("        # Level 2 indentation (8 spaces)", lines)
        self.assertIn("        if True:", lines)
        self.assertIn("            # Level 3 indentation (12 spaces)", lines)
        self.assertIn("            for i in range(3):", lines)
        self.assertIn("                # Level 4 indentation (16 spaces)", lines)
        self.assertIn("                ax = plt.gca()", lines)
        
        # Check that font lines are filtered with correct indentation preserved
        self.assertIn("# Filtered font setting: plt.rcParams['font.family'] = 'sans-serif'", lines)
        # Font size should be preserved (not filtered)
        self.assertIn("    plt.rcParams['font.size'] = 12", lines)
        # Font weight should be preserved (not filtered)
        self.assertIn("        plt.rcParams['font.weight'] = 'bold'", lines)
        # Font style should be preserved (not filtered)
        self.assertIn("            plt.rcParams['font.style'] = 'italic'", lines)
        self.assertIn("# Filtered font setting: ax.set_fontfamily('Arial')", lines)


if __name__ == '__main__':
    unittest.main()
