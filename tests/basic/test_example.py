import pytest

def test_example():
    """Test basic functionality"""
    assert 1 + 1 == 2
    assert True is True
    assert False is False

def test_string_operations():
    """Test string operations"""
    assert "hello" + " world" == "hello world"
    assert "hello".upper() == "HELLO"

def test_list_operations():
    """Test list operations"""
    sample_list = [1, 2, 3]
    assert len(sample_list) == 3
    assert sum(sample_list) == 6

