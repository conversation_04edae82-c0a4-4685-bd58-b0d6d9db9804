import asyncio
import threading
import time
from concurrent.futures import ThreadPoolExecutor
import queue
from common.elements.queue import StreamQueueManager, StreamQueue

# 协程1：等待事件并获取数据
async def waiter(queue: StreamQueue, thread_name):
    print(f"[{thread_name}] 等待事件...")
    while True:
        try:
            data = queue.get_sync()
            print(f"[{thread_name}] 事件触发了，接收到的数据：{data}")
        except Exception as e:
            print(f"[{thread_name}] 等待事件失败: {e}")
            break

# 协程2：触发事件并传递数据
async def setter(thread_name):
    print(f"[{thread_name}] 事件即将触发...")
    for i in range(3):
        await asyncio.sleep(0.3)  # 模拟一些工作
        print(f"[{thread_name}] 事件已触发")
        yield f"Hello from {thread_name}"

# 协程3：另一个等待者
async def waiter2(queue, thread_name):
    print(f"[{thread_name}] 等待事件...")
    data = await queue.get()
    print(f"[{thread_name}] 事件触发了，接收到的数据：{data}")
    return f"[{thread_name}] 处理完成: {data}"



async def main_with_threads():
    """使用多线程运行协程的主函数"""
    print("=== 多线程协程示例 ===")
    
    # 创建自定义事件对象
    stream_queue_manager = StreamQueueManager()
    
    # 创建线程池
    stream_thread1 = stream_queue_manager.start_producer(setter, "线程1-设置者")
    stream_thread2 = stream_queue_manager.start_consumer(waiter, stream_queue_manager.stream_queue, "线程2-等待者")
    stream_thread1.join()
    stream_thread2.join()
    stream_queue_manager.close()
    print("=== 所有线程执行完成 ===")

if __name__ == "__main__":
    # 运行多线程协程示例
    print("=== 开始运行 ===")
    asyncio.run(main_with_threads())
    print("=== 结束运行 ===")
  