import tests.basic.init
import pytest
import time
import asyncio
from unittest.mock import patch, MagicMock
from prometheus_client import REGISTRY, CollectorRegistry, generate_latest
from common.metric.enpoints import (
    record_request, record_latency, record_error,
    record_memory_usage, record_operation,
    COUNTER_REQUEST_TOTAL, COUNTER_ERROR_TOTAL,
    GAUGE_MEMORY_USAGE, SUMMARY_OPERATION_DURATION, HISTOGRAM_LATENCY
)
from common.metric.prom_metric import prom_metric
from common.metric.server import MetricsServer, start_metrics_server, stop_metrics_server
import requests

def test_record_request():
    """Test recording request metrics"""
    record_request("test_path", "200", "user123")
    record_request("test_path", "500", "user123")
    
    # Check counter values
    assert COUNTER_REQUEST_TOTAL.labels(path="test_path", status="200", userid="user123")._value.get() == 1
    assert COUNTER_REQUEST_TOTAL.labels(path="test_path", status="500", userid="user123")._value.get() == 1

def test_record_latency():
    """Test recording latency metrics"""
    record_latency("test_path", 0.5, "user123")
    record_latency("test_path", 1.5, "user123")
    # 验证 _sum
    assert HISTOGRAM_LATENCY.labels(path="test_path", userid="user123")._sum.get() == 2.0
    # 通过 collect() 获取 count 样本
    samples = list(HISTOGRAM_LATENCY.collect())[0].samples
    count_sample = [s for s in samples if s.name.endswith("_count") and s.labels == {"path": "test_path", "userid": "user123"}]
    assert count_sample and count_sample[0].value == 2

def test_record_error():
    """Test recording error metrics"""
    record_error("test_path", "ValueError", "user123")
    record_error("test_path", "TypeError", "user123")
    
    # Check error counter values
    assert COUNTER_ERROR_TOTAL.labels(path="test_path", error_type="ValueError", userid="user123")._value.get() == 1
    assert COUNTER_ERROR_TOTAL.labels(path="test_path", error_type="TypeError", userid="user123")._value.get() == 1

def test_record_memory_usage():
    """Test recording memory usage metrics"""
    record_memory_usage("test_component", 1024, "user123")
    record_memory_usage("test_component", 2048, "user123")
    
    # Check gauge value (should be the last value set)
    assert GAUGE_MEMORY_USAGE.labels(component="test_component", userid="user123")._value.get() == 2048

def test_record_operation():
    """Test recording operation metrics"""
    record_operation("test_op", 0.5, "success", "user123")
    record_operation("test_op", 1.0, "error", "user123")
    
    generate_latest()  # 强制收集
    # Check summary values
    success_count = REGISTRY.get_sample_value(
        f'{SUMMARY_OPERATION_DURATION._name}_count',
        {'operation': 'test_op', 'status': 'success', 'userid': 'user123'}
    )
    error_count = REGISTRY.get_sample_value(
        f'{SUMMARY_OPERATION_DURATION._name}_count',
        {'operation': 'test_op', 'status': 'error', 'userid': 'user123'}
    )
    assert success_count == 1
    assert error_count == 1

@pytest.mark.asyncio
async def test_prom_metric_decorator_async():
    """Test prom_metric decorator with async function"""
    
    @prom_metric(name="test_async_op")
    async def test_async_func():
        await asyncio.sleep(0.1)
        return "200"
    
    @prom_metric(name="test_async_error")
    async def test_async_error():
        await asyncio.sleep(0.1)
        raise ValueError("test error")
    
    # Test successful operation
    await test_async_func()
    assert COUNTER_REQUEST_TOTAL.labels(path="test_async_op", status="200", userid="unknown")._value.get() == 1
    
    # Test error operation
    with pytest.raises(ValueError):
        await test_async_error()
    assert COUNTER_REQUEST_TOTAL.labels(path="test_async_error", status="error", userid="unknown")._value.get() == 1
    assert COUNTER_ERROR_TOTAL.labels(path="test_async_error", error_type="ValueError", userid="unknown")._value.get() == 1

def test_prom_metric_decorator_sync():
    """Test prom_metric decorator with sync function"""
    
    @prom_metric(name="test_sync_op")
    def test_sync_func():
        time.sleep(0.1)
        return "200"
    
    @prom_metric(name="test_sync_error")
    def test_sync_error():
        time.sleep(0.1)
        raise ValueError("test error")
    
    # Test successful operation
    test_sync_func()
    assert COUNTER_REQUEST_TOTAL.labels(path="test_sync_op", status="200", userid="unknown")._value.get() == 1
    
    # Test error operation
    with pytest.raises(ValueError):
        test_sync_error()
    assert COUNTER_REQUEST_TOTAL.labels(path="test_sync_error", status="error", userid="unknown")._value.get() == 1
    assert COUNTER_ERROR_TOTAL.labels(path="test_sync_error", error_type="ValueError", userid="unknown")._value.get() == 1

def test_metrics_server():
    """Test metrics server functionality"""
    # Test server creation and start
    server = MetricsServer(port=0)  # Use port 0 for testing
    server.start()
    assert server._running is True
    
    # Test server stop
    server.stop()
    assert server._running is False

def test_start_stop_metrics_server():
    """Test global metrics server functions"""
    # Test start
    server = start_metrics_server(port=0)  # Use port 0 for testing
    assert server._running is True
    
    # Test stop
    stop_metrics_server()
    assert server._running is False

def test_metrics_server_exposes_metrics():
    # 启动 metrics server（用随机端口避免冲突）
    server = start_metrics_server(port=18080)
    time.sleep(0.2)  # 等待 server 启动

    # 更新指标
    record_request("test_http", "200", "userhttp")

    # 访问 /metrics
    resp = requests.get("http://127.0.0.1:18080/metrics")
    assert resp.status_code == 200
    # 检查自定义指标内容
    assert "intellix_data_agent_enpoints_request_total" in resp.text
    assert 'path="test_http",status="200",userid="userhttp"' in resp.text

    # 关闭 server
    server.stop()
