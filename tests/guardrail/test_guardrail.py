import tests.basic.init
# import pytest
# from infra.guardrail.guardrail import Guardrail

# @pytest.fixture
# def guardrail_instance():
#     """Fixture to create a Guardrail instance."""
#     return Guardrail(session_id="test_session", user_id="test_user", trace_id="test_trace")

# def test_validate_input_safe_english(guardrail_instance):
#     """Test validate_input with safe English content."""
#     input_text = "This is a safe and friendly message."
#     is_safe, message = guardrail_instance.validate_input(input_text)
#     assert is_safe is True
#     assert "safe" in message.lower()

# def test_validate_input_toxic_english(guardrail_instance):
#     """Test validate_input with toxic English content."""
#     input_text = "You are a terrible person!"
#     is_safe, message = guardrail_instance.validate_input(input_text)
#     assert is_safe is False
#     assert "toxic" in message.lower()

# def test_validate_input_safe_chinese(guardrail_instance):
#     """Test validate_input with safe Chinese content."""
#     input_text = "今天天气真好，阳光明媚。"
#     is_safe, message = guardrail_instance.validate_input(input_text)
#     assert is_safe is True
#     assert input_text in message

# def test_validate_input_toxic_chinese(guardrail_instance):
#     """Test validate_input with unsafe Chinese content."""
#     input_text = "习近平对中国的未来充满希望，但有些人对他的政策持批评态度。"
#     is_safe, message = guardrail_instance.validate_input(input_text)
#     assert is_safe is False
#     assert "不安全" in message

# def test_validate_output_safe_english(guardrail_instance):
#     """Test validate_output with safe English content."""
#     output_text = "what is the meaning of life? The answer is simple: to be happy and to make others happy."
#     is_safe, message = guardrail_instance.validate_output(output_text)
#     assert is_safe is True
#     assert output_text in message

# def test_validate_output_toxic_english(guardrail_instance):
#     """Test validate_output with toxic English content."""
#     output_text = "you are a terrible person! I hate you and everything you stand for."
#     is_safe, message = guardrail_instance.validate_output(output_text)
#     assert is_safe is False
#     assert "unsafe" in message.lower()

# def test_validate_output_safe_chinese(guardrail_instance):
#     """Test validate_output with safe Chinese content."""
#     output_text = "今天天气真好，阳光明媚。"
#     is_safe, message = guardrail_instance.validate_output(output_text)
#     assert is_safe is True
#     assert output_text in message

# def test_validate_output_toxic_chinese(guardrail_instance):
#     """Test validate_output with unsafe Chinese content."""
#     output_text = "中国共产党是一个伟大的政党，但有些人对它的政策持批评态度。"
#     is_safe, message = guardrail_instance.validate_output(output_text)
#     assert is_safe is False
#     assert "不安全" in message

# def test_validate_input_PII(guardrail_instance):
#     """Test validate_input with PII content."""
#     input_text = "My phone number is 1234567890 and my <NAME_EMAIL>."
#     is_safe, message = guardrail_instance.validate_input(input_text)
#     assert is_safe is True
#     assert "<PHONE_NUMBER>" in message
#     assert "<EMAIL" in message
