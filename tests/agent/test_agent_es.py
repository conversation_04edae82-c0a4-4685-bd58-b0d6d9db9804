# test_agent_interface_mock.py
import asyncio
import logging
import json
import pytest
import time # Import time for more detailed logging if needed, though not used in this version
from typing import Optional, List, Dict, Any
from infra.datascience_agent.agent_service import AgentService # Ensure this path and class name are correct
from common.elements.agent_event import AgentEvent
    # Assuming AgentEvent classes are defined within agent_service.py or imported there.

# Add necessary imports for MCPManager
from infra.mcp.manager.mcp_manager import MCPManager
from common.share.stream_param import StreamGenerationParams
from common.share.context import ChatContext
from common.share.config import appConfig
from tests.agent.mock_manager import MockMCPManager

root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG) # Set root to DEBUG

# Or, set specific loggers to DEBUG if you know their names:
logging.getLogger("infra.datascience_agent.graph_nodes").setLevel(logging.DEBUG)
logging.getLogger("infra.datascience_agent.agent_service").setLevel(logging.DEBUG)
logging.getLogger("infra.datascience_agent.agents.executor.task_processor").setLevel(logging.DEBUG)
# Add other module loggers as needed

# Ensure a handler is configured if not already by basicConfig
if not root_logger.hasHandlers():
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    root_logger.addHandler(handler)

logger = logging.getLogger(__name__) # For this test script's own logs

logger = logging.getLogger(__name__)


@pytest.mark.asyncio
async def test_streaming_chat():
    print("\n--- Testing Streaming Chat (agent.stream_chat()) ---")

    mcp_config = appConfig.automic.mcp.example.get("data", {})
    if len(mcp_config) == 0 or mcp_config.get('MCP', None) is None:
        pytest.skip("No MCP config found")
    
    session_id_stream = "test_session_stream_chat_v2_fixed" # Use a clear session ID for the test

    db_table_for_context: List[Dict[str, Any]] = mcp_config.get('DB_TABLE', [])
    # Example: [{'DbName': 'nl2sql_test', 'TableList': ['orders', 'products', 'customers']}]
    
    mcp_instance_for_context: Dict[str, Any] = mcp_config.get('MCP', {})
    # Example: {'Instance': 'dlc', 'DataEngineName': 'data-agent-exp-dev',
    #           'DatasourceConnectionName': 'DataLakeCatalog', 'Type': 'MCP',
    #           'Url': 'http://lb-2ohhzn5i-egfmcuxsj56dh1op.clb.cq-tencentclb.com:31234/sse?auth_token=SECRET_KEY_20251'}

    eg_instance_for_context: Dict[str, Any] = mcp_config.get('EG', {})

    base_chat_context = ChatContext(
        {
            'UserId': 'test_user',
            'SessionId': session_id_stream,
            # TraceId will be set per scenario if needed, or a general one here
        },
        {}
    )
    
    # Set the necessary parameters on the ChatContext so AgentService can find them
    base_chat_context.set("sg_params_db_table", db_table_for_context)
    base_chat_context.set("sg_params_mcp_instance", mcp_instance_for_context)
    mcp_init_sg_params = StreamGenerationParams(
        ctx=base_chat_context, # Pass the same enriched context
        question="Initial MCP Manager setup", # Generic question
        mcp_instance=mcp_instance_for_context,
        eg_instance=eg_instance_for_context,
        record_id="mcp_manager_init_record", # Generic record_id
    )

    #mcp_manager = MCPManager(mcp_init_sg_params)
    mcp_manager = MockMCPManager(mcp_init_sg_params)
    agent_interface = AgentService(mcp_manager)

    test_scenarios = [
        {
            "query": "Please help count the number of error logs for each day over the past week. Use default params, do not confirm again.",
            "dataset_id": "nl2sql_test",
            "model_name": "deepseek-v3",
            "record_id": "scenario_1_record",  # Scenario-specific record_id
            "trace_id": "scenario_1_trace"
        },
        {
            "query": "Please categorize these error level logs based on the service field.Use default params, do not confirm again",
            "dataset_id": "nl2sql_test",
            "model_name": "deepseek-v3",
            "record_id": "scenario_1_record",  # Scenario-specific record_id
            "trace_id": "scenario_1_trace"
        },
        {
            "query": "Please directly read the error level logs from 2025-06-12, limited to 10 lines.",
            "dataset_id": "nl2sql_test",
            "model_name": "deepseek-v3",
            "record_id": "scenario_1_record",  # Scenario-specific record_id
            "trace_id": "scenario_1_trace"
        },
        {
            "query": """
             Please help analyze the reasons for the following error log and provide suggestions for resolution
             ```
                @timestamp: 2025-06-12T07:00:00.611Z
                appid: 1253240642
                clusterid: 190056044
                content: [3d8eac5f-6dae-4b65-93da-f134ef77b8e4 HiveServer2-Handler-Pool: Thread-303|303] ql.Driver: FAILED: HiveAccessControlException Permission denied: user [easechen] does not have [SELECT] privilege on [easechen_dw/test1/*]
                filename: hadoop-hiveserver2
                hostip: *********
                level: ERROR
                namespace: emr-b856cti5
                region: cq
                role: HiveServer2
                service: Hive
                source: /data/emr/hive/logs/hadoop-hiveserver2
            ```
            """,
            "dataset_id": "nl2sql_test",
            "model_name": "deepseek-v3",
            "record_id": "scenario_1_record",  # Scenario-specific record_id
            "trace_id": "scenario_1_trace"
        },
    ]

    for i, scenario in enumerate(test_scenarios):
        print("-" * 40)
        print(f"Scenario {i+1}: You: {scenario['query']}")

        # Update trace_id in context if scenario-specific
        base_chat_context.set("trace_id", scenario.get("trace_id", f"test_trace_scenario_{i+1}"))

        event_count = 0
        async for agent_event in agent_interface.stream_chat(
            ctx=base_chat_context, # Pass the ChatContext that has the db info
            query=scenario['query'],
            session_id=session_id_stream, 
            dataset_id=scenario['dataset_id'],
            #model_name=scenario['model_name'],
            record_id=scenario['record_id'],
            mcp_manager=mcp_manager 
        ):
            event_count += 1
            # Ensure agent_event is not None and has event_type before logging
            if not hasattr(agent_event, 'event_type'):
                logger.warning(f"Test interface: Received a None or malformed agent_event: {agent_event}")
                continue

            logger.debug(f"Received event type: {agent_event.event_type}")
            # Safely convert to dict, handling potential None
            event_dict = agent_event.to_dict() if hasattr(agent_event, 'to_dict') else vars(agent_event)
            logger.debug(f"Test interface: Full event dict: {json.dumps(event_dict, indent=2, default=str)}")

            if agent_event.event_type == "think":
                logger.debug(f"Test interface: Processing think event: {agent_event.content}")
                print(f"\n[THINKING]: {agent_event.content}", flush=True)
                # if isinstance(agent_event.content, str): # Collect only string content for final message
                #      full_streamed_message_for_user_parts.append(f"[THINK]: {agent_event.content}\n")
            elif agent_event.event_type == "message": # This is intended for direct user-facing messages
                logger.debug(f"Test interface: Processing message event: {agent_event.content}")
                print(f"\n[MESSAGE]: {agent_event.content}", flush=True)
                # if isinstance(agent_event.content, str):
                #     full_streamed_message_for_user_parts.append(agent_event.content)
            elif agent_event.event_type == "text": # Also for user-facing text, perhaps from LLM directly
                logger.debug(f"Test interface: Processing text event: {agent_event.content}")
                print(f"\n[TEXT]: {agent_event.content}", flush=True)

            # Handle other event types for logging/display as before
            elif agent_event.event_type == "status":
                logger.debug(f"Test interface: Processing status event: {getattr(agent_event, 'status', 'N/A')} - Stage: {getattr(agent_event, 'stage', 'N/A')}")
                print(f"\n[STATUS: {getattr(agent_event, 'status', 'N/A')} - Stage: {getattr(agent_event, 'stage', 'N/A')}]", flush=True)
            elif agent_event.event_type == "error":
                logger.debug(f"Test interface: Processing error event: {getattr(agent_event, 'error_type', 'N/A')} - {getattr(agent_event, 'error_msg', 'N/A')}")
                print(f"\n[ERROR ({getattr(agent_event, 'error_type', 'N/A')}): {getattr(agent_event, 'error_msg', 'N/A')}]", flush=True)
            elif agent_event.event_type == "final_summary":
                logger.debug(f"Test interface: Processing final_summary event: {agent_event.content}")
                print(f"\n[FINAL SUMMARY]: {agent_event.content}, cell_id={getattr(agent_event, 'cell_id', 'N/A')}", flush=True)
                # if isinstance(agent_event.content, str):
                #      full_streamed_message_for_user_parts.append(f"\n[SUMMARY]: {agent_event.content}\n")
            elif agent_event.event_type == "task_list":
                logger.debug(f"Test interface: Processing task_list event: {agent_event.content}")
                print(f"\n[TASK LIST]: {json.dumps(agent_event.content, ensure_ascii=False, indent=2)}", flush=True) # Pretty print JSON
            elif agent_event.event_type == "studio_jupyter": # Note: your AgentEvent has 'studio_jupyter', previous code used 'jupyter_event'
                # Ensure attribute access is safe
                cell_type = getattr(agent_event, 'cell_type', 'N/A')
                source = getattr(agent_event, 'source', [])
                outputs = getattr(agent_event, 'outputs', [])
                # 从 metadata 中获取状态，如果没有则显示 'N/A'
                status = getattr(agent_event, 'metadata', {}).get('status', 'N/A')
                cell_id = getattr(agent_event, 'id', 'N/A')
                logger.debug(f"Test interface: Processing studio_jupyter event: cell_type={cell_type}, source={source}, outputs={outputs}, status={status}, cell_id={cell_id}")
                print(f"\n[JUPYTER EVENT]: cell_type={cell_type}, source={source}, outputs={outputs}, status={status}, cell_id={cell_id}", flush=True)

        # full_streamed_message_for_user = "".join(full_streamed_message_for_user_parts)
        # print("\n\n" + "=" * 20 + "Aggregated Streamed Text to User" + "=" * 20) # Changed title for clarity
        # print(full_streamed_message_for_user if full_streamed_message_for_user else "(No text content directly streamed to user)")

        print("\n" + "=" * 20 + "Stream Summary" + "=" * 20)
        if event_count == 0:
            print("  (No events yielded from stream_chat for this input)")
        else:
            print(f"  Total events yielded: {event_count}")

        print("-" * 40)
        await asyncio.sleep(0.1) 


if __name__ == "__main__":
    asyncio.run(test_streaming_chat())



    # test_scenarios = [
    #     # {"query": "你好,", 
    #     #  "dataset_id": "nl2sql_test",
    #     #  "model_name": "deepseek-v3",
    #     #  "record_id": "5",
    #     # },
    #     # {"query": "我想对'销售额'列生成一个直方图,", 
    #     #  "dataset_id": "nl2sql_test",
    #     #  "model_name": "deepseek-v3",
    #     #  "record_id": "5",
    #     # },
    #     # {"query": "默认设置就可以", 
    #     #  "dataset_id": "nl2sql_test",
    #     #  "model_name": "deepseek-v3",
    #     #  "record_id": "5",
    #     # },
    #     {"query": "我想对'销售额'列生成一个直方图, 默认设置就可以, 不用反问我了", 
    #      "dataset_id": "data_agent",
    #      "model_name": "deepseek-v3",
    #      "record_id": "5",
    #     },
    #     # {"query": "找出2023年哪个地区销售额最高。不要反问我了 不用其他操作", 
    #     #  "dataset_id": "nl2sql_test",
    #     #  "model_name": "deepseek-v3",
    #     #  "record_id": "5",
    #     # },
    # ]
