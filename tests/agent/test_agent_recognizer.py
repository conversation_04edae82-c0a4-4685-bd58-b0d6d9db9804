from infra.datascience_agent.agents.intent_recognizer.intent_recognizer import Inten<PERSON><PERSON><PERSON>ognizer
from common.share.config import appConfig
from infra.datascience_agent.utils.openai_client import OpenAIClient
from common.elements.agent_event import ThinkEvent, TextEvent, MessageEvent
import asyncio

async def run_test_case(recognizer, test_case, test_case_num):
    """执行单个测试用例并返回错误信息"""
    errors = []
    conversation_history = []

    state = None
    if "datasets" in test_case:
        state = {"database_schema": test_case["datasets"]}

    for turn_idx, turn in enumerate(test_case["turns"], 1):
        query = turn["query"]
        print(f"\n[测试用例 #{test_case_num} Turn {turn_idx}] 用户输入: {query}")

        if "datasets" in turn:
            state = {"database_schema":turn["datasets"]}

        think_buffer = []
        message_buffer = []

        async for event in recognizer.process_query(query, conversation_history=conversation_history, state=state):
            if isinstance(event, ThinkEvent):
                think_buffer.append(event.content)
            elif isinstance(event, MessageEvent):
                message_buffer.append(event.content)

        full_message_to_user = ""
        # 打印AI响应
        if think_buffer:
            print("\n[AI 思考过程]")
            print("".join(think_buffer).strip())
        if message_buffer:
            print("\n[AI 回复]")
            full_message_to_user = "".join(message_buffer).strip()
            print(full_message_to_user)

        if full_message_to_user:
            conversation_history.append({"role": "assistant", "content": full_message_to_user})

        # 获取当前状态
        current_slot = recognizer.get_current_slot_state()
        actual_layer = recognizer.get_current_layer()
        actual_category = recognizer.get_task_category()
        essential_info = recognizer.get_essential_info()
        supplementary_info = recognizer.get_supplementary_info()
        
        print("\n[当前状态]")
        print(f"层次 (layer): {actual_layer}")
        print(f"任务类别 (task category): {actual_category}")
        print(f"必要信息 (essential_info): {json.dumps(essential_info, ensure_ascii=False)}")
        print(f"补充信息 (supplementary_info): {json.dumps(supplementary_info, ensure_ascii=False)}")
        print("完整槽位状态:")
        print(json.dumps(current_slot, indent=2, ensure_ascii=False))
        
        # 检查层次
        if "expect_layer" in turn:
            print(f"\n[层次检查] 期望: {turn['expect_layer']}, 实际: {actual_layer}")
            if actual_layer != turn["expect_layer"]:
                error_msg = f"测试用例 #{test_case_num} Turn {turn_idx}: 层次不匹配 - 期望 {turn['expect_layer']}, 实际 {actual_layer}"
                print(f"⚠️ {error_msg}")
                errors.append(error_msg)
        
        # 检查任务类别
        if "expect_task_category" in turn:
            print(f"\n[任务类别检查] 期望: {turn['expect_task_category']}, 实际: {actual_category}")
            if actual_category != turn["expect_task_category"]:
                error_msg = f"测试用例 #{test_case_num} Turn {turn_idx}: 任务类别不匹配 - 期望 {turn['expect_task_category']}, 实际 {actual_category}"
                print(f"⚠️ {error_msg}")
                errors.append(error_msg)
        
        # 检查必要信息
        # if "expect_essential_info" in turn:
        #     expected_essentials = turn["expect_essential_info"]
        #     print(f"\n[必要信息检查] 期望: {expected_essentials}")
        #
        #     # 检查每个期望的键值对是否存在
        #     for key, expected_value in expected_essentials.items():
        #         actual_value = essential_info.get(key)
        #         print(f"  - 检查键: {key}, 期望值: {expected_value}, 实际值: {actual_value}")
        #
        #         # 通用逻辑：如果期望值是列表，检查实际值是否在列表中
        #         if isinstance(expected_value, list):
        #             # 检查实际值是否在期望值列表中
        #             if actual_value not in expected_value:
        #                 error_msg = f"测试用例 #{test_case_num} Turn {turn_idx}: 键 '{key}' 的值不在期望范围内 - 实际 '{actual_value}', 期望范围 {expected_value}"
        #                 print(f"⚠️ {error_msg}")
        #                 errors.append(error_msg)
        #         else:
        #             # 标准检查逻辑
        #             if key not in essential_info:
        #                 error_msg = f"测试用例 #{test_case_num} Turn {turn_idx}: 必要信息中缺少键: {key}"
        #                 print(f"⚠️ {error_msg}")
        #                 errors.append(error_msg)
        #             elif str(actual_value) != str(expected_value):
        #                 error_msg = f"测试用例 #{test_case_num} Turn {turn_idx}: 键 '{key}' 的值不匹配 - 期望 '{expected_value}', 实际 '{actual_value}'"
        #                 print(f"⚠️ {error_msg}")
        #                 errors.append(error_msg)
        #
        # # 检查补充信息
        # if "expect_supplementary_info" in turn:
        #     expected_supplementaries = turn["expect_supplementary_info"]
        #     print(f"\n[补充信息检查] 期望: {expected_supplementaries}")
        #
        #     # 检查每个期望的键值对是否存在
        #     for key, expected_value in expected_supplementaries.items():
        #         actual_value = supplementary_info.get(key)
        #         print(f"  - 检查键: {key}, 期望值: {expected_value}, 实际值: {actual_value}")
        #
        #         if key not in supplementary_info:
        #             error_msg = f"测试用例 #{test_case_num} Turn {turn_idx}: 补充信息中缺少键: {key}"
        #             print(f"⚠️ {error_msg}")
        #             errors.append(error_msg)
        #         elif str(supplementary_info[key]) != str(expected_value):
        #             error_msg = f"测试用例 #{test_case_num} Turn {turn_idx}: 键 '{key}' 的值不匹配 - 期望 '{expected_value}', 实际 '{actual_value}'"
        #             print(f"⚠️ {error_msg}")
        #             errors.append(error_msg)
        
        # 检查缺失信息
        if "expect_missing_info" in turn:
            expected_missing = turn["expect_missing_info"]
            print(f"\n[缺失信息检查] 期望缺失的信息: {expected_missing}")
            
            # 获取AI回复内容
            ai_response = "".join(message_buffer).strip()
            print(f"AI回复内容: {ai_response}")
            
            # 检查AI是否询问了所有缺失的信息
            for info in expected_missing:
                print(f"  - 检查是否询问: {info}")
                if info not in ai_response:
                    error_msg = f"测试用例 #{test_case_num} Turn {turn_idx}: AI未询问缺失信息: {info}"
                    print(f"⚠️ {error_msg}")
                    errors.append(error_msg)

        # 检查ai回复
        if "expect_response" in turn:
            expected_response = turn["expect_response"]
            print(f"\n期望AI回复: {expected_response}")
            
            # 获取AI回复内容
            ai_response = "".join(message_buffer).strip()
            print(f"AI回复内容: {ai_response}")
            
            # 检查AI回复是否和期望一致
            if str(expected_response) not in str(ai_response):
                error_msg = f"测试用例 #{test_case_num} Turn {turn_idx}: AI回复不符合期望: {ai_response}"
                print(f"⚠️ {error_msg}")
                errors.append(error_msg)
        
        # 检查冲突信息
        if "expect_conflict" in turn and turn["expect_conflict"]:
            print("\n[冲突信息检查] 期望检测到冲突")
            
            # 获取AI回复内容
            ai_response = "".join(message_buffer).strip()
            print(f"AI回复内容: {ai_response}")
            
            # 检查AI是否检测到冲突并询问
            conflict_keywords = ["冲突", "不一致", "矛盾", "conflict", "inconsistency"]
            conflict_detected = any(keyword in ai_response for keyword in conflict_keywords)
            
            if not conflict_detected:
                error_msg = f"测试用例 #{test_case_num} Turn {turn_idx}: AI未检测到预期冲突"
                print(f"⚠️ {error_msg}")
                errors.append(error_msg)
        
        # 检查语言
        if "expect_language" in turn:
            expected_lang = turn["expect_language"]
            actual_lang = recognizer.get_detected_language()
            print(f"\n[语言检查] 期望: {expected_lang}, 实际: {actual_lang}")
            
            if actual_lang != expected_lang:
                error_msg = f"测试用例 #{test_case_num} Turn {turn_idx}: 语言检测不匹配 - 期望 {expected_lang}, 实际 {actual_lang}"
                print(f"⚠️ {error_msg}")
                errors.append(error_msg)
    
    return errors


async def main():
    # 初始化LLM客户端

    llm_client = OpenAIClient(
        api_key=appConfig.common.llm.api_key,
        base_url=appConfig.common.llm.base_url,
        model=appConfig.common.llm.model_name
    )
    print(appConfig.common.llm)
    recognizer = IntentRecognizer(llm_client=llm_client)

    # 定义测试用例
    test_cases = [
        # # ========== 单轮对话测试 ==========
        #  Case0: 闲聊（身份信息）
        # 提示用户："由于没有上传数据信息，当前无法进行数据科学任务和数据库查询任务，请尝试其他任务。"
        {
            "turns": [{
                "query": "你是谁？",
                "expect_task_category": "small_talk",
                "expect_layer": 1,
            }]
        },
        # ================== DATA SCIENCE 测试用例 ==================
        #  Case1: 数据科学任务(数据集信息为空)
        # 提示用户："由于没有上传数据信息，当前无法进行数据科学任务和数据库查询任务，请尝试其他任务。"
        {
            "turns": [{
                "query": "预测下个月销售额，目标变量是 revenue",
                "expect_task_category": "small_talk",
                "expect_layer": 1,
            }]
        },

        # Case2: 数据科学任务(数据集信息为空，但提供了非常详细的输入)
        {
            "description": "长文本输入压力测试",
            "turns": [
                {
                    "query": "我需要分析过去三年每个季度的销售数据，包括产品类别、地区分布和客户细分。"
                    "请使用随机森林模型预测下个季度的销售额，目标变量是total_sales，"
                    "数据集在sales_db的quarterly_sales表，时间范围从2020-Q1到2023-Q4，"
                    "评估指标用MAE和R-squared，不需要特征工程，使用默认验证策略。",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1,
                }
            ]
        },

        # Case3: 数据科学任务(需补充信息)
        # 进入第二层，一次性收集补充参数（模型偏好、评估指标、特征选择、验证策略）
        {
            "description": "数据科学任务，预测下个月销售额",
            "datasets": [
                {
                    "name": "sales_data",
                    "description": "销售数据表，包含产品、日期、销售额等字段",
                    "metadata": {
                        "columns": ["product_id", "date", "revenue", "quantity"],
                        "row_count": 10000,
                        "data_type": "structured"
                    }
                }
            ],
            "turns": [
                {
                    "query": "用 sales_data 表预测下个月销售额，目标变量是 revenue",
                    "expect_task_category": "data_science",
                    "expect_layer": 2,
                }
            ]
        },

        # Case4: 不明确的数据科学任务(需补充信息)
        # 进入第二层，一次性收集补充参数（模型偏好、评估指标、特征选择、验证策略）
        {
            "description": "数据科学任务，预测下个月销售额",
            "datasets": [
                {
                    "name": "sales_data",
                    "description": "销售数据表，包含产品、日期、销售额等字段",
                    "metadata": {
                        "columns": ["product_id", "date", "revenue", "quantity"],
                        "row_count": 10000,
                        "data_type": "structured"
                    }
                }
            ],
            "turns": [
                {
                    "query": "我想进行预测",
                    "expect_task_category": "data_science",
                    "expect_layer": 2
                }
            ]
        },

        # Case5:数据科学任务(用户明确要求跳过反问)
        # 直接进入第三层
        {
            "description": "数据科学任务，预测下个月销售额",
            "datasets": [
                {
                    "name": "sales_data",
                    "description": "销售数据表，包含产品、日期、销售额等字段",
                    "metadata": {
                        "columns": ["product_id", "date", "revenue", "quantity"],
                        "row_count": 10000,
                        "data_type": "structured"
                    }
                }
            ],
            "turns": [
                {
                    "query": "用 sales_data 表预测下个月销售额，目标变量是 revenue，不要反问直接执行",
                    "expect_task_category": "data_science",
                    "expect_layer": 3,
                }
            ]
        },

        # ================== NL_DATABASE_QUERY 测试用例 ==================
        # Case1: 数据库查询请求（数据集信息为空）
        # 提示用户："由于没有上传数据信息，当前无法进行数据库查询任务，请尝试其他任务。"
        {
            "turns":[{
                "query": "帮我查询 test 数据库的 ts 表前十行的数据",
                "expect_task_category": "small_talk",
                "expect_layer": 1,
            }]
        },

        # Case2: 明确的数据库查询请求
        # 直接进入第三层
        {
            "description": "数据库查询任务，查询 test 数据库的 ts 表前十行的数据",

            "datasets": [
                {
                    "name": "test",
                    "description": "测试数据库，包含多个表",
                    "metadata": {
                        "tables": ["ts", "user_logs", "sales_data"],
                        "row_count": 5000,
                        "data_type": "structured"
                    }
                }
            ],

            "turns": [
                {
                    "query": "帮我查询 test 数据库的 ts 表前十行的数据",
                    "expect_task_category": "nl_database_query",
                    "expect_layer": 3,
                    "expect_essential_info": {}
                }
            ]
        },

        # Case3: 不明确的数据库查询请求
        # 继续第一层，收集必要信息
        {
            "datasets": [
                {
                    "name": "test",
                    "description": "测试数据库，包含多个表",
                    "metadata": {
                        "tables": ["ts", "user_logs", "sales_data"],
                        "row_count": 5000,
                        "data_type": "structured"
                    }
                }
            ],
            "turns": [
                {
                    "query": "我想查询数据库",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1,
                    "expect_essential_info": {}
                }
            ]
        },

        # ================== DOCUMENT_QUERY 测试用例 ==================
        # Case1: 明确的技术文档查询请求
        {
            "description": "DOCUMENT_QUERY: 技术文档查询",
            "turns": [
                {
                    "query": "如何在项目中集成API认证机制？",
                    "expect_task_category": "document_query",
                    "expect_layer": 3,
                }
            ]
        },

        # Case2: 技术文档查询请求（无文档）
        # 直接进入第三轮
        {
            "description": "DOCUMENT_QUERY: 技术文档查询",
            "turns": [
                {
                    "query": "如何在项目中集成API认证机制？",
                    "expect_task_category": "document_query",
                    "expect_layer": 3,
                }
            ]
        },

        # ================== SMALL_TALK 测试用例 ==================
        # Case1: 保持在第一层
        {
            "description": "SMALL_TALK: 问候",
            "turns": [
                {
                    "query": "你好！今天过得怎么样？",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                }
            ]
        },
        # Case2:
        {
            "description": "SMALL_TALK: 讲笑话",
            "turns": [
                {
                    "query": "给我讲一个笑话",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                }
            ]
        },

        # Case3: 中英文混合输入——优先中文
        {
            "turns": [
                {
                    "query": "Tell me a interesting 故事",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                }
            ]
        },

        # ===================== 多轮对话测试 =========================
        # ================== DATA SCIENCE 测试用例 ==================
        # Case1: 数据科学任务（预测股票价格）————多轮对话仍旧无法收集补充参数的情况
        {
            "datasets": [
                {
                    "name": "AAPL历史数据",
                    "description": "AAPL历史数据",
                    "metadata": {
                        "tables": ["share_price", "user_logs", "sales_data"],
                        "columns": ["date", "open", "high", "low", "close", "volume"],
                        "row_count": 1000,
                        "data_type": "structured"
                    }
                }
            ],
            "turns":
            [
                {
                    "query": "我想预测股票价格",
                    "expect_task_category": "data_science",
                    "expect_layer": 2,
                },
                {
                    "query": "用 AAPL 的历史数据，预测 close 价格",
                    "expect_task_category": "data_science",
                    "expect_layer": 3,
                },
            ]
        },

        # Case2: 数据科学任务（预测股票价格），多轮（超过3轮）对话补充参数
        {
            "datasets": [
                {
                    "name": "AAPL历史数据",
                    "description": "AAPL历史数据",
                    "metadata": {
                        "columns": ["date", "open", "high", "low", "close", "volume"],
                        "row_count": 1000,
                        "data_type": "structured"
                    }
                }
            ],
            "turns":
            [
                {
                    "query": "用 AAPL 的历史数据，预测 close 价格",
                    "expect_task_category": "data_science",
                    "expect_layer": 2,
                    "expect_essential_info": {
                        "task_type": "预测",
                        "dataset": "AAPL历史数据",
                        "target_variable": "close"
                    }
                },
                {
                    "query": "使用随机森林模型",
                    "expect_task_category": "data_science",
                    "expect_layer": 3,  # 严格只有一轮对话
                },
                {
                    "query": "评估指标用MAE和R-squared",
                    "expect_task_category": "data_science",
                    "expect_layer": 3,  # 严格只有一轮对话
                },
                {
                    "query": "采用三折交叉验证",
                    "expect_task_category": "data_science",
                    "expect_layer": 3,  # 严格只有一轮对话
                }
            ]
        },

        # ================== NL_DATABASE_QUERY 测试用例 ==================
        # 数据库查询（缺少数据集）
        {
            "turns":[
                {
                    "query": "帮我查一些数据",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                },
                {
                    "query": "从 production 库查 error_logs 表的错误数量",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1,
                }
            ]
        },
        # Case2: 数据库查询，未指定数据集->指定数据集
        {
            "turns":[
                {
                    "query": "帮我查一些数据",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                },
                {
                    "query": "从 production 库查 error_logs 表的错误数量",
                    "expect_task_category": "nl_database_query",
                    "expect_layer": 3,
                    "expect_essential_info": {
                        "query_target": "error_logs",
                        "aggregation": "count"
                    },
                    "datasets": [
                        {
                            "name": "production",
                            "description": "生产环境数据库，包含多个表",
                            "metadata": {
                                "tables": ["error_logs", "user_activity", "sales_data"],
                                "row_count": 10000,
                                "data_type": "structured"
                            }
                        }
                    ]

                }
            ]
        },
        {
            "turns": [
                {
                    "query": "2024-1-1到2024-6-30的数码产品的销售额进行按日聚合",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                },
                {
                    "query": "2024-1-1到2024-6-30的数码产品的销售额进行按日聚合",
                    "expect_task_category": "nl_database_query",
                    "expect_layer": 3,
                    # "expect_essential_info": {
                    #     "query_target": "orders",
                    #     "aggregation": "count"
                    # },
                    "datasets": [
                        {
                            "name": "nl2sql_test",
                            "description": "生产环境数据库，包含多个表",
                            "metadata": {
                                "tables": ["products", "orders", "customers"],
                                "row_count": 10000,
                                "data_type": "structured"
                            }
                        }
                    ]

                }
            ]
        },

        # Case3: 数据库查询（在layer3后，用户修改请求）
        # layer3确认后无法修改
        {
            "datasets": [
                {
                    "name": "production",
                    "description": "生产环境数据库，包含多个表",
                    "metadata": {
                        "tables": ["error_logs", "user_activity", "sales_data"],
                        "row_count": 10000,
                        "data_type": "structured"
                    }
                }
            ],
            "turns":[
                {
                    "query": "帮我查一些数据",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                },
                {
                    "query": "从 production 库查 error_logs 表的错误数量",
                    "expect_task_category": "nl_database_query",
                    "expect_layer": 3,
                    "expect_essential_info": {
                        "query_target": "error_logs",
                        "aggregation": "count"
                    }
                },
                {
                    "query": "从 production 库查 user_activity 表的前十条数据",
                    "expect_task_category": "nl_database_query",
                    "expect_layer": 3,
                    "expect_essential_info": {
                        "query_target": "user_activity"
                    }
                }
            ]
        },

        # ================== DOCUMENT_QUERY 测试用例 ==================
        # Case1: 明确的技术文档查询请求(在layer3确认后用户修改查询意图)
        {
            "description": "DOCUMENT_QUERY: 技术文档查询",
            "turns": [
                {
                    "query": "如何在项目中集成API认证机制？",
                    "expect_task_category": "document_query",
                    "expect_layer": 3,
                    "expect_essential_info": {
                        "document_scope": "API使用指南"
                    }
                },{
                    "query": "系统的架构设计是怎么样的？",
                    "expect_task_category": "document_query",
                    "expect_layer": 3,
                    "expect_essential_info": {
                        "document_scope": "API使用指南"
                    }
                }
            ]
        },
        # # Case1: 明确的技术文档查询请求(在layer3确认后用户修改查询意图)
        {
            "turns": [
                {
                    "query": "知识库搜索三个类似案件：“旅行纠纷，骗取用户保证金” 展示案件标题、案件内容总结、案件类型和案件号",
                    "expect_task_category": "document_query",
                    "expect_layer": 3,
                },
                {
                    "query": "展示第二个案件的详细信息",
                    "expect_task_category": "document_query",
                    "expect_layer": 3,
                }
            ]
        },

        # ================== SMALL_TALK 测试用例 ==================
        {
            "description": "多轮 SMALL_TALK，保持在第一层",
            "turns": [
                {
                    "query": "你好！今天过得怎么样？",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                },{
                    "query": "给我讲一个笑话。",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                },{
                    "query": "今天天气怎么样？",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                }
            ]
        },

        {
            "description": "SMALL_TALK 切换到 DOCUMENT_QUERY 任务",
            "turns": [
                {
                    "query": "你好！今天过得怎么样？",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                },
                {
                    "query": "系统的架构设计是怎样的呢？",
                    "expect_task_category": "document_query",
                    "expect_layer": 3
                }
            ]
        },

        {
            "description": "SMALL_TALK 切换到 DATA_SCIENCE 任务",
            "datasets": [
                {
                    "name": "AAPL历史数据",
                    "description": "AAPL历史数据",
                    "metadata": {
                            "columns": ["date", "open", "high", "low", "close", "volume"],
                            "row_count": 1000,
                            "data_type": "structured"
                    }
                }
            ],
            "turns": [
                {
                    "query": "你好！",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                },{
                    "query": "我想预测AAPL股票的收盘价",
                    "expect_task_category": "data_science",
                    "expect_layer": 2
                },{
                    "query": "使用随机森林模型",
                    "expect_task_category": "data_science",
                    "expect_layer": 3
                },{
                    "query": "预测未来一个月",
                    "expect_task_category": "data_science",
                    "expect_layer": 2
                }
            ]
        },

        # ================== 异常测试 ==================
        {
            "description": "空输入",
            "turns": [
                {
                    "query": "",
                    "expect_layer": 1
                }
            ]
        },

        {
            "description": "冲突的数据集信息（未提供数据）",
            "turns": [
                {
                    "query": "查询 experiment 库的 ts_gluon 表，但我要的是 test 库的数据",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1,
                }
            ]
        },

        {
            "description": "冲突的数据集信息（提供数据，不包含冲突部分数据）",
            "datasets":[
                {
                    "name": "experiment",
                    "description": "实验数据库",
                    "metadata": {
                        "tables": ["ts_gluon", "model_results"],
                        "row_count": 2000,
                        "data_type": "structured"
                    }
                }
            ],
            "turns": [
                {
                    "query": "查询 test 库的 ts_gluon 表的数据",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1,
                }
            ]
        },

        {
            "description": "冲突的数据集信息（提供数据，包含冲突部分数据）",

            "datasets":[
                {
                    "name": "experiment",
                    "description": "实验数据库",
                    "metadata": {
                        "tables": ["ts_gluon", "model_results"],
                        "row_count": 2000,
                        "data_type": "structured"
                    }
                },{
                    "name": "test",
                    "description": "测试数据库",
                    "metadata": {
                        "tables": ["model_results"],
                        "row_count": 2000,
                        "data_type": "structured"
                    }
                }
            ],
            "turns": [
                {
                    "query": "查询 experiment 库的 model_results 表的数据",
                    "expect_task_category": "nl_database_query",
                    "expect_layer": 3,
                }
            ]
        },

        # ========== 特殊场景测试 ==========
        {
            "description": "用户多次中途改变意图",
            "turns": [
                {
                    "query": "帮我预测销售额",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                },
                {
                    "datasets": [
                        {
                            "name": "experiment",
                            "description": "实验数据库",
                            "metadata": {
                                "tables": ["orders", "model_results"],
                                "row_count": 2000,
                                "data_type": "structured"
                            }
                        }
                    ],
                    "query": "帮我基于订单表预测销售额",
                    "expect_task_category": "data_science",
                    "expect_layer": 2
                },
                {
                    "datasets":[
                        {
                            "name": "experiment",
                            "description": "实验数据库",
                            "metadata": {
                                "tables": ["orders", "model_results"],
                                "row_count": 2000,
                                "data_type": "structured"
                            }
                        }
                    ],
                    "query": "算了，还是帮我查询最近一个月的销售订单数吧",
                    "expect_task_category": "nl_database_query",
                    "expect_layer": 3,
                },
                {
                    "query": "给我讲个笑话",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1,
                },
                {
                    "query": "如何在项目中集成API认证机制？",
                    "expect_task_category": "document_query",
                    "expect_layer": 3,
                }
            ],
        },

        {
            "description": "中英文切换",
            "datasets": [
                {
                    "name": "experiment",
                    "description": "实验数据库",
                    "metadata": {
                        "tables": ["orders", "model_results"],
                        "row_count": 2000,
                        "data_type": "structured"
                    }
                }
            ],
            "turns": [
                {
                    "query": "查询最近一个月的销售订单数吧",
                    "expect_task_category": "nl_database_query",
                    "expect_layer": 3,
                    "expect_language": "zh"
                },
                {
                    "query": "use last three months data, forecast revenue in next month",
                    "expect_task_category": "data_science",
                    "expect_layer": 2,
                    "expect_language": "en"
                }
            ]
        },
        # #  Case: 举报或投诉
        {
            "turns": [{
                "query": "各区食品投诉、举报数量按区由高到低排序(标出具体数字)",
                "expect_task_category": "complaint_or_report",
                "expect_layer": 3,
            }]
        },
        {
            "turns": [{
                "query": "各区按街道投诉、举报由高到低排序（按年度）",
                "expect_task_category": "complaint_or_report",
                "expect_layer": 3,
            }]
        },
        {
            "description": "用户多次中途改变意图",
            "turns": [
                {
                    "query": "帮我预测销售额",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1
                },
                {
                    "datasets": [
                        {
                            "name": "experiment",
                            "description": "实验数据库",
                            "metadata": {
                                "tables": ["orders", "model_results"],
                                "row_count": 2000,
                                "data_type": "structured"
                            }
                        }
                    ],
                    "query": "帮我基于订单表预测销售额",
                    "expect_task_category": "data_science",
                    "expect_layer": 2
                },
                {
                    "datasets": [
                        {
                            "name": "experiment",
                            "description": "实验数据库",
                            "metadata": {
                                "tables": ["orders", "model_results"],
                                "row_count": 2000,
                                "data_type": "structured"
                            }
                        }
                    ],
                    "query": "算了，还是帮我查询最近一个月的销售订单数吧",
                    "expect_task_category": "nl_database_query",
                    "expect_layer": 3,
                },
                {
                    "query": "给我讲个笑话",
                    "expect_task_category": "small_talk",
                    "expect_layer": 1,
                },
                {
                    "query": "如何在项目中集成API认证机制？",
                    "expect_task_category": "document_query",
                    "expect_layer": 3,
                },
                {
                    "query": "摊贩投诉、举报情况(排名)",
                    "expect_task_category": "complaint_or_report",
                    "expect_layer": 3,
                },
                {
                    "query": "被举报最多的是哪类摊贩？",
                    "expect_task_category": "complaint_or_report",
                    "expect_layer": 3,
                },

            ],
        },
    ]

    # 运行测试并收集所有错误
    print("=== 开始测试 ===")
    all_errors = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n执行测试用例 #{i}")
        errors = await run_test_case(recognizer, test_case, i)
        all_errors.extend(errors)
        recognizer.reset_intent()  # 重置识别器状态
        recognizer.intent_slot.dataset = None  # 重置数据集信息
    
    print("\n=== 所有测试执行完成 ===")
    
    # 如果有错误，统一报告
    if all_errors:
        print("\n=== 测试失败汇总 ===")
        for error in all_errors:
            print(f"❌ {error}")
        raise AssertionError(f"\n共发现 {len(all_errors)} 个测试失败项")
    else:
        print("\n✅ 所有测试用例通过")

    # 交互模式
    # print("\n进入交互模式，输入 'exit' 或 '退出' 结束")
    # while True:
    #     user_input = input("> ").strip()
    #     if user_input.lower() in ["exit", "quit", "退出"]:
    #         break
    #
    #     think_buffer = []
    #     message_buffer = []
    #
    #     async for event in recognizer.process_query(user_input):
    #         if isinstance(event, ThinkEvent):
    #             think_buffer.append(event.content)
    #         elif isinstance(event, MessageEvent):
    #             message_buffer.append(event.content)
    #
    #     if think_buffer:
    #         print("\n[AI 思考过程]")
    #         print("".join(think_buffer).strip())
    #     if message_buffer:
    #         print("\n[AI 回复]")
    #         print("".join(message_buffer).strip())

if __name__ == "__main__":
    import json
    asyncio.run(main())
