from typing import Dict, Any
from mcp.server.fastmcp import FastMCP
from infra.mcp.codegen.nl2code import core

mcp = FastMCP("nl2code Server")


@mcp.tool()
async def nl2code(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate executable Python code based on user instruction and programming context.
    
    This function orchestrates the code generation process by:
    1. Validating input parameters
    2. Building execution context from dependencies, variables, and history
    3. Generating Python code using an LLM
    4. Extracting executable code and required packages

    Args:
        params: Dictionary containing these required keys:
            user_instruction (str): Natural language instruction describing desired functionality
            env_dependencies (list): Installed packages/libraries (e.g., ['pandas', 'numpy'])
            global_vars (dict): Existing global variables (e.g., {'df': "pd.DataFrame()"})
            function_headers (list): Available function signatures (e.g., ['def process_data(data):...'])
            previous_actions (list): Tuples of past code attempts and results:
                Format: [(code_str, exec_status_str, output_str), ...]
            data_type (str): Type of primary data object (e.g., 'DataFrame', 'Array')
            data_schema (str): Schema description of primary data (e.g., 'columns: [id, name]')
            model_name (str): LLM model for code generation (e.g., 'gpt-4')

    Returns:
        Dictionary with these possible structures:
        
        Success case:
            {
                "python_code": str,  # Executable Python code
                "required_packages": list[str]  # Packages needed for execution
            }
        
        Error cases:
            {
                "error": "Parameter validation failed",
                "details": list[dict]  # Pydantic validation errors
            }
            OR
            {
                "error": "Code generation failed.",
                "details": str  # Exception message
            }

    The generated code:
        - Uses existing global variables and functions
        - Follows Python data processing conventions
        - Avoids non-executable elements
        - Includes error recovery patterns
        - Maintains Jupyter Notebook compatibility
    """
    result = {
        "python_code": """
# 确保数据格式符合Prophet的要求
df_prophet = df.rename(columns={'order_date': 'ds', 'daily_sales': 'y'})

# 初始化Prophet模型
from prophet import Prophet
model = Prophet()

# 拟合模型
model.fit(df_prophet)

# 创建未来30天的数据框
future = model.make_future_dataframe(periods=30)

# 进行预测
forecast = model.predict(future)

# 可视化预测结果
model.plot(forecast)
""",
        "required_packages": ["pandas", "prophet"],
    }

    return result

if __name__ == "__main__":
    mcp.run(transport="stdio")
