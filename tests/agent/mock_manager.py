from typing import Dict, Any, List, Optional
import asyncio
import os
import sys

import pytest

from common.share.stream_param import StreamGenerationParams
from infra.mcp.manager.mcp_manager import MCPManager
from common.share.context import ChatContext
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client
import json

class MockMCPManager(MCPManager):
    """Mock version of MCPManager for testing, inherits from MCPManager"""
    
    def __init__(self, params: StreamGenerationParams):
        super().__init__(params)  # 调用父类初始化
        self.register_mock_servers(params)

    def register_mock_servers(self, params: StreamGenerationParams):
        """注册mock服务器"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 设置环境变量
        db_name = ""
        table_list = []
        if params.db_table is not None:
            for db in params.db_table:
                db_name = db["DbName"]
                table_list = db["TableList"]

        ctx = params.ctx
        mock_servers = [
            {
                "name": "generate_sql",
                "command": sys.executable,
                "args": [f"{current_dir}/mock_generate_sql.py"],
                "link": "stdio",
                "env": {
                    "PYTHONPATH": f"{os.getenv('PYTHONPATH', '.')}:{os.getcwd()}",
                    "ENGINE": params.mcp_instance.get("Engine", ""),
                    "DATASOURCE_NAME": params.mcp_instance["DatasourceName"],
                    "DBNAME": db_name,  # "dbname": "nl2sql_test",
                    "TABLES": json.dumps(table_list), # "tables": ["orders", "products", "customers"],
                    "IS_SAMPLING": "True",
                    "MCP_URL": json.dumps(params.mcp_instance["Url"]),
                    "ENGINE_TYPE": params.mcp_instance["Type"],
                    "RECORD_ID": "",
                },
                "url": None,
            },
            {
                "name": "nl2code",
                "command": sys.executable,
                "args": [f"{current_dir}/mock_nl2code.py"],
                "link": "stdio",
                "env": {
                    "PYTHONPATH": f"{os.getenv('PYTHONPATH', '.')}:{os.getcwd()}"
                }
            },
            {
                "name": "jupyter",
                "command": sys.executable,
                "args": [f"{current_dir}/mock_jupyter.py"],
                "link": "stdio",
                "env": {
                    "PYTHONPATH": f"{os.getenv('PYTHONPATH', '.')}:{os.getcwd()}",
                    "EG_SUBUIN": params.eg_instance.get('SubUin', "unknown"),
                    "EG_GATEWAY_HOST": params.eg_instance.get('Url', "unknown"),
                    "KERNEL_ID": params.eg_instance.get('KernelId', ""),
                    "KERNEL_NAME": params.eg_instance.get('KernelName', "unknown")
                }
            }
        ]

        # 处理MCP实例，添加SSE服务器
        mcp_instance = params.mcp_instance
        if isinstance(mcp_instance, dict):
            mcp_instance = [mcp_instance]
        
        if mcp_instance:
            for instance in mcp_instance:
                url = instance.get('Url')
                name = instance.get('Instance')
                if url and name:
                    mock_servers.append({
                        "name": name,
                        "command": sys.executable,
                        "url": url,
                        "link": "sse",
                        "env": None,
                        "args": None,
                    })

        for server in mock_servers:
            self.register_server(
                server_name=server["name"],
                command=server["command"],
                args=server["args"],
                env=server.get("env"),
                link=server["link"],
                url=server.get("url")
            )

    async def call_tool(self, tool_name: str, *args, **kwargs) -> Dict[str, Any]:
        """调用mock工具，支持stdio和sse两种方式"""
        if '__' not in tool_name:
            raise ValueError(f"工具名必须包含'__'分隔符: {tool_name}")

        server_name, after_tool_name = tool_name.split('__', 1)
        if server_name not in self.mcp_servers:
            raise ValueError(f"服务 {server_name} 未注册")

        server_info = self.mcp_servers[server_name]
        link = server_info.get('link')

        if link == "stdio":
            params = server_info.get('params')
            if not params:
                raise ValueError(f"服务 {server_name} 参数未配置")
            
            async with stdio_client(params) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    return await session.call_tool(after_tool_name, *args, **kwargs)
        
        elif link == "sse":
            url = server_info.get('url')
            if not url:
                raise ValueError(f"服务 {server_name} URL未配置")
            
            async with sse_client(url) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    return await session.call_tool(after_tool_name, *args, **kwargs)
        
        else:
            raise ValueError(f"不支持的链接类型: {link}")

    async def list_tools(self):
        """获取所有工具 返回格式: {"server__tool": tool_info}"""
        tools = {}
        for server_name, server_info in self.mcp_servers.items():
            if server_info['tools'] is None:
                server_info['tools'] = await self._fetch_tools(server_name)

            tool_list = server_info['tools'].tools if hasattr(server_info['tools'], 'tools') else []
            tools.update({
                f"{server_name}__{tool.name}": tool
                for tool in tool_list
            })
        return tools

    async def _fetch_tools(self, server_name: str):
        """获取单个服务器的工具列表"""
        server_info = self.mcp_servers[server_name]
        link = server_info.get('link')

        try:
            if link == "stdio":
                params = server_info.get('params')
                async with stdio_client(params) as (read, write):
                    async with ClientSession(read, write) as session:
                        await session.initialize()
                        return await session.list_tools()
            elif link == "sse":
                url = server_info.get('url')
                async with sse_client(url) as (read, write):
                    async with ClientSession(read, write) as session:
                        await session.initialize()
                        return await session.list_tools()
            else:
                raise ValueError(f"不支持的链接类型: {link}")
        except Exception as e:
            print(f"获取工具失败 {server_name}: {str(e)}")
            return {}

# Example usage in tests
@pytest.mark.asyncio
async def test_mock_mcp():
    # Create test params
    params = StreamGenerationParams(
        ctx=ChatContext(
            {
                "session_id": "test_session",
                "trace_id": "test_trace",
                "sub_account_uin": "test_user",
                "app_id": "test_app"
            }, {}),
        mcp_instance=[
            {
                "Type": "dlc",
                "Url": "http://test-dlc-url/sse",
                "Instance": "dlc",
                "DatasourceName": "test-source"
            },
            {
                "Type": "es_sql",
                "Url": "http://test-es-url/sse",
                "Instance": "es_sql",
                "DatasourceName": "test-source"
            }
        ],
        eg_instance={
            "SubUin": "test-user",
            "Url": "http://test-eg-url",
            "KernelId": "test-kernel",
            "KernelName": "python3"
        },
        db_table=[{
            "DbName": "test_db",
            "TableList": ["table1", "table2"]
        }],
        record_id="test_record"
    )

    # Initialize mock manager
    mock_mcp = MockMCPManager(params)

    try:
        # List all available tools
        tools = await mock_mcp.list_tools()
        print("Available tools:", tools)

        # Test nl2code (stdio)
        nl2code_result = await mock_mcp.call_tool(
            "nl2code__nl2code",
            arguments={"params": {"user_instruction": "Create a function to add two numbers"}}
        )
        print("NL2Code result:", nl2code_result)

        # Test jupyter execute_code (stdio)
        jupyter_result = await mock_mcp.call_tool(
            "jupyter__execute_code",
            arguments={
                "code": "print('Hello, World!')",
                "required_packages": ["pandas", "numpy"]
            }
        )
        print("Jupyter execution result:", jupyter_result)

        # Test DLC query (sse)
        dlc_result = await mock_mcp.call_tool(
            "dlc__DLCExecuteQuery",
            arguments={
                "SparkSQL": "SELECT * FROM test_table LIMIT 10",
                "DatabaseName": "test_db",
                "DatasourceConnectionName": "test-source",
                "DataEngineName": "test-engine"
            }
        )
        print("DLC query result:", dlc_result)

    except Exception as e:
        print(f"Error calling tools: {e}")

if __name__ == "__main__":
    asyncio.run(test_mock_mcp())
