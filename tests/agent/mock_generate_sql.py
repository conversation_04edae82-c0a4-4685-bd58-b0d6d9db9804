import os
from typing import Dict, Any
from mcp.server.fastmcp import FastMCP

from infra.mcp.nl2sql import generate
import json
mcp = FastMCP("Generate Sql Server")


@mcp.tool()
async def generate_sql(engine_type: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """根据自然语言生成SQL语句

    参数:
        engine_type (str): 默认值为 dlc；当分析日志时，使用 es_sql
        params (Dict[str, Any]): 包含以下键的字典参数:
            - question: 自然语言问题

    返回:
        Dict[str, Any]: 包含以下键的结果字典:
            - sql: 生成的SQL语句
            - reasoning: 推理过程说明
            - tables: 涉及的表列表
    """
    result = {
        "sql": "SELECT T2.order_date, SUM(T2.quantity * T1.price) AS daily_sales FROM nl2sql_test.products AS T1 INNER JOIN nl2sql_test.orders AS T2 ON T1.product_id = T2.product_id WHERE T1.category = '数码产品' AND T2.order_date BETWEEN '2024-01-01' AND '2024-06-30' GROUP BY T2.order_date ORDER BY T2.order_date",  # 最终生成的SQL语句
        "reasoning": "sql generation success",  # 生成过程的推理说明
        "tables": [],  # 实际使用的表列表
    }
    return result

if __name__ == "__main__":
    mcp.run(transport="stdio")


