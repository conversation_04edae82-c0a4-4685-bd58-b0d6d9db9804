# test_agent_interface_mock.py
import argparse
import asyncio
import logging
import json
import os
import pytest
import time # Import time for more detailed logging if needed, though not used in this version
from typing import Optional, List, Dict, Any
from infra.datascience_agent.agent_service import AgentService # Ensure this path and class name are correct
from common.elements.agent_event import AgentEvent
    # Assuming AgentEvent classes are defined within agent_service.py or imported there.

# Add necessary imports for MCPManager
from infra.mcp.manager.mcp_manager import MCPManager
from common.share.stream_param import StreamGenerationParams
from common.share.context import ChatContext
from common.share.config import appConfig
from tests.agent.mock_manager import MockMCPManager

root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG) # Set root to DEBUG

# Or, set specific loggers to DEBUG if you know their names:
logging.getLogger("infra.datascience_agent.graph_nodes").setLevel(logging.DEBUG)
logging.getLogger("infra.datascience_agent.agent_service").setLevel(logging.DEBUG)
logging.getLogger("infra.datascience_agent.agents.executor.task_processor").setLevel(logging.DEBUG)
# Add other module loggers as needed

# Ensure a handler is configured if not already by basicConfig
if not root_logger.hasHandlers():
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    root_logger.addHandler(handler)

logger = logging.getLogger(__name__) # For this test script's own logs

logger = logging.getLogger(__name__)

def get_mcp_manager_class():
    """根据参数决定使用哪个MCP管理器类"""
    # 方法1: 通过环境变量控制
    use_mock = os.getenv('USE_MOCK_MCP', 'false').lower() == 'false'
    
    # 方法2: 通过命令行参数控制
    parser = argparse.ArgumentParser(description='Test agent interface')
    parser.add_argument('--use-mock', action='store_true', 
                       help='Use MockMCPManager instead of real MCPManager')
    parser.add_argument('--use-real', action='store_true',
                       help='Use real MCPManager (default)')
    
    # 解析命令行参数
    args, _ = parser.parse_known_args()
    
    if args.use_mock:
        use_mock = True
    elif args.use_real:
        use_mock = False
    
    if use_mock:
        logger.info("Using MockMCPManager for testing")
        return MockMCPManager
    else:
        logger.info("Using real MCPManager")
        return MCPManager

@pytest.mark.asyncio
async def test_streaming_chat():
    print("\n--- Testing Streaming Chat (agent.stream_chat()) ---")
    logger.debug("THIS IS A TEST DEBUG MESSAGE FOR STREAMING CHAT")
    
    mcp_config = appConfig.automic.mcp.example.get("data", {})
    if len(mcp_config) == 0 or mcp_config.get('MCP', None) is None:
        pytest.skip("No MCP config found")
    session_id_stream = "test_session_stream_chat_v2_fixed" # Use a clear session ID for the test

    db_table: List[Dict[str, Any]] = mcp_config.get('DB_TABLE', [])
    # Example: [{'DbName': 'nl2sql_test', 'TableList': ['orders', 'products', 'customers']}]

    # use dlc default
    mcp_instances: Dict[str, Any] = mcp_config.get('MCP', [])
    if len(mcp_instances) == 0:
        raise Exception("No MCP instance found")

    dlc_instance = mcp_instances[0]
    # Example: {'Instance': 'dlc', 'DataEngineName': 'data-agent-exp-dev', 
    #           'DatasourceConnectionName': 'DataLakeCatalog', 'Type': 'MCP', 
    #           'Url': 'http://lb-2ohhzn5i-egfmcuxsj56dh1op.clb.cq-tencentclb.com:31234/sse?auth_token=SECRET_KEY_20251'}
    
    eg_instance: Dict[str, Any] = mcp_config.get('EG', {})

    base_chat_context = ChatContext(
        {
            'UserId': 'test_user',
            'SessionId': session_id_stream,
            # TraceId will be set per scenario if needed, or a general one here
        },
        {
            "AppId": "1353879163",
        }
    )
    
    # Set the necessary parameters on the ChatContext so AgentService can find them
    base_chat_context.set("sg_params_db_table", db_table)
    base_chat_context.set("sg_params_mcp_instance", dlc_instance)
    
    # 在 mcp_init_sg_params 创建之前添加 req_context 配置
    
    # 测试不同白名单场景的 req_context 配置
    req_context_with_permission = json.dumps({
        "WhiteList": ["AdvancedFeature", "AdGraphGen"],
        "MCP": {
            "Instance": dlc_instance.get('Instance', 'mcp-default'),
            "DataEngineName": dlc_instance.get('DataEngineName', 'data-agent-exp-dev'),
            "TCCatalogName": dlc_instance.get('DatasourceConnectionName', '11111111111'),
            "IsSampling": "True",
            "Type": "DLC",
            "Url": dlc_instance.get('Url', '')
        },
        "DbTable": db_table,
        "EG": eg_instance
    })
    
    req_context_without_permission = json.dumps({
        "WhiteList": ["BasicFeature"],  # 没有 AdvancedFeature
        "MCP": {
            "Instance": dlc_instance.get('Instance', 'mcp-default'),
            "DataEngineName": dlc_instance.get('DataEngineName', 'data-agent-exp-dev'),
            "TCCatalogName": dlc_instance.get('DatasourceConnectionName', '11111111111'),
            "IsSampling": "True",
            "Type": "DLC",
            "Url": dlc_instance.get('Url', '')
        },
        "DbTable": db_table,
        "EG": eg_instance
    })
    
    req_context_no_whitelist = json.dumps({
        # 没有 WhiteList 字段
        "MCP": {
            "Instance": dlc_instance.get('Instance', 'mcp-default'),
            "DataEngineName": dlc_instance.get('DataEngineName', 'data-agent-exp-dev'),
            "TCCatalogName": dlc_instance.get('DatasourceConnectionName', '11111111111'),
            "IsSampling": "True",
            "Type": "DLC",
            "Url": dlc_instance.get('Url', '')
        },
        "DbTable": db_table,
        "EG": eg_instance
    })

    mcp_init_sg_params = StreamGenerationParams(
        ctx=base_chat_context, # Pass the same enriched context
        question="Initial MCP Manager setup", # Generic question
        mcp_instance=dlc_instance,
        eg_instance=eg_instance,
        db_table=db_table, 
        record_id="mcp_manager_init_record", # Generic record_id,
        knowledge_base_ids=["klbase-K7cWPFjg03"],
        req_context=req_context_with_permission  # 默认使用有权限的配置
    )

    #mcp_manager = MCPManager(mcp_init_sg_params)
    MCPClass = get_mcp_manager_class()
    mcp_manager = MCPClass(mcp_init_sg_params)
    # for server_name, server_info in mcp_manager.mcp_servers.items():
    #     print(f"{server_name}: {server_info}")
    tools_list = await mcp_manager.list_tools()
    for k,v in tools_list.items():
        print(k,v)
    agent_interface = AgentService(mcp_manager)

    test_scenarios = [
        # 测试有权限的投诉举报场景
        # {
        #     "query": "投诉、举报按类型分类（无证超范围、违规操作、异物、有害生物、环境卫生、欺诈、价格、其他）",
        #     "dataset_id": "nl2sql_test",
        #     "record_id": "scenario_1_record",
        #     "trace_id": "scenario_1_trace",
        #     "req_context": req_context_without_permission,
        #     "test_name": "投诉举报-有权限"
        # },
        # {
        #     "query": "投诉、举报按类型分类（无证超范围、违规操作、异物、有害生物、环境卫生、欺诈、价格、其他）",
        #     "dataset_id": "nl2sql_test",
        #     "record_id": "scenario_2_record",
        #     "trace_id": "scenario_2_trace", 
        #     "req_context": req_context_no_whitelist,
        #     "test_name": "投诉举报-无权限"
        # },
        # {
        #     "query": "投诉、举报按类型分类（无证超范围、违规操作、异物、有害生物、环境卫生、欺诈、价格、其他）",
        #     "dataset_id": "nl2sql_test",
        #     "record_id": "scenario_3_record",
        #     "trace_id": "scenario_3_trace",
        #     "req_context": req_context_with_permission,
        #     "test_name": "投诉举报-无白名单"
        # },
        # # 测试完全没有 req_context 的场景
        # {
        #     "query": "投诉、举报按类型分类（无证超范围、违规操作、异物、有害生物、环境卫生、欺诈、价格、其他）",
        #     "dataset_id": "nl2sql_test",
        #     "record_id": "scenario_4_record",
        #     "trace_id": "scenario_4_trace",
        #     "req_context": None,
        #     "test_name": "投诉举报-无req_context"
        # },
        # {
        #     "query": "全市被重复投诉、举报的企业(单位)排序。(三次以上)",
        #     "dataset_id": "nl2sql_test",
        #     "record_id": "scenario_5_record",
        #     "trace_id": "scenario_5_trace",
        #     "req_context": req_context_with_permission,
        #     "test_name": "重复投诉企业排序-有权限"
        # },
        # {
        #     "query": "各区食品投诉、举报数量按区由高到低排序(标出具体数字)",
        #     "dataset_id": "nl2sql_test",
        #     "record_id": "scenario_6_record",
        #     "trace_id": "scenario_6_trace",
        #     "req_context": req_context_with_permission,
        #     "test_name": "食品投诉排序-有权限"
        # },
        # {
        #     "query": "各区按街道投诉、举报由高到低排序（按年度）",
        #     "dataset_id": "nl2sql_test",
        #     "record_id": "scenario_7_record",
        #     "trace_id": "scenario_7_trace",
        #     "req_context": req_context_with_permission,
        #     "test_name": "街道投诉排序-有权限"
        # },
        # {
        #     "query": "各区投诉、举报数量变化(线状图)（按月度、按年度分别统计）",
        #     "dataset_id": "nl2sql_test",
        #     "record_id": "scenario_8_record",
        #     "trace_id": "scenario_8_trace",
        #     "req_context": req_context_with_permission,
        #     "test_name": "投诉数量变化图-有权限"
        # },
        # {
        #     "query": "学校食品投诉、举报、(排名)",
        #     "dataset_id": "nl2sql_test",
        #     "record_id": "scenario_9_record",
        #     "trace_id": "scenario_9_trace",
        #     "req_context": req_context_with_permission,
        #     "test_name": "学校食品投诉-有权限"
        # },
        # {
        #     "query": "摊贩投诉、举报情况(排名)",
        #     "dataset_id": "nl2sql_test",
        #     "record_id": "scenario_10_record",
        #     "trace_id": "scenario_10_trace",
        #     "req_context": req_context_with_permission,
        #     "test_name": "摊贩投诉排名-有权限"
        # },
        # # 测试非投诉举报相关的查询（应该不受白名单影响）
        # {
        #     "query": "什么是逻辑学",
        #     "dataset_id": "nl2sql_test",
        #     # "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        # {
        #     "query": "给我从知识库查找所有关于声称正当防卫，但被判决防卫过当的案件",
        #     "dataset_id": "nl2sql_test",
        #     # "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        # {
        #     "query": "请给我第二个案件的具体细节",
        #     "dataset_id": "nl2sql_test",
        #     # "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        # {
        #     "query": "请给我彭某某案的具体细节",
        #     "dataset_id": "nl2sql_test",
        #     # "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        # {
        #     "query": "请给我欧某案的具体细节",
        #     "dataset_id": "nl2sql_test",
        #     # "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        # {
        #     "query": "给我一个关于声称正当防卫，但被判决防卫过当的案件",
        #     "dataset_id": "nl2sql_test",
        #     # "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        # {
        #     "query": "帮我使用 sklearn 加载 iris数据集，需划分为训练集和测试集，这里我们选取120个为训练集，30个为测试集。由于Iris数据集给出的三种花是按照顺，序前50个是第0类，51-100是第1类，101~150是第2类为实现随机性，选取三个部分最后十组数据作为测试集元素。使用SVM算法分类，通过训练集训练出的分类模型，使用测试集作验证输出计算他们的准确率、召回率和F值，同时把测试分类样本进行可视化展示，参数使用默认设置，不要再问我了",
        #     "dataset_id": "nl2sql_test",
        #     # "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        # {
        #     "query": "默认设置就行",
        #     "dataset_id": "nl2sql_test",
        #     # "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        # {
        #     "query": "nl2sql_test数据库中2024-1-1到2024-6-30的数码产品每日的销售额是多少，默认设置就可以，不用反问我",
        #     "dataset_id": "nl2sql_test",
        #     # "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        # {
        #     "query": "获取当前指定的所有数据表的定义",
        #     "dataset_id": "nl2sql_test",
        #     # "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        # {
        #     "query": "nl2sql_test数据库中2024-1-1到2024-6-30的数码产品每日的销售额是多少，默认设置就可以，不用反问我",
        #     "dataset_id": "nl2sql_test",
        #     # "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        {
            "query": "根据近三年服装鞋帽产品每天的销售额情况，使用 Prophet 算法预测接下来一年该类产品的销售额，不用反问我了",
            "dataset_id": "nl2sql_test",
            # "model_name": "deepseek-v3",
            "req_context": req_context_with_permission,
            "record_id": "scenario_1_record",  # Scenario-specific record_id
            "trace_id": "scenario_1_trace"
        },
        # {
        #     "query": "查询product表前十行的数据",
        #     "dataset_id": "nl2sql_test",
        #     # "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        # {
        #     "query": "获取数据表products的表定义",
        #     "dataset_id": "nl2sql_test",
        #     "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        # {
        #     "query": "Please list the details of the INFO logs from last day",
        #     "dataset_id": "nl2sql_test",
        #     "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
        # {
        #     "query": "Please help analyze the reasons for this INFO log and provide suggestions for resolution.",
        #     "dataset_id": "nl2sql_test",
        #     "model_name": "deepseek-v3",
        #     "record_id": "scenario_1_record",  # Scenario-specific record_id
        #     "trace_id": "scenario_1_trace"
        # },
    ]

    for i, scenario in enumerate(test_scenarios):
        print("-" * 40)
        test_name = scenario.get('test_name', f'Scenario {i+1}')
        print(f"{test_name}: You: {scenario['query']} (Dataset: {scenario['dataset_id']})")
        print("=" * 20 + "Agent Interaction Stream" + "=" * 20)

        # Update trace_id in context if scenario-specific
        base_chat_context.set("trace_id", scenario.get("trace_id", f"test_trace_scenario_{i+1}"))
        
        # 为每个场景创建特定的 StreamGenerationParams，包含对应的 req_context
        scenario_sg_params = StreamGenerationParams(
            ctx=base_chat_context,
            question=scenario['query'],
            mcp_instance=dlc_instance,
            eg_instance=eg_instance,
            db_table=db_table,
            record_id=scenario['record_id'],
            knowledge_base_ids=["klbase-K7cWPFjg03"],
            req_context=scenario.get('req_context')  # 使用场景特定的 req_context
        )

        event_count = 0
        # full_streamed_message_for_user_parts = []

        # The `base_chat_context` is passed as `ctx` argument.
        # `AgentService.stream_chat` will then use `ctx.get("sg_params_db_table")` etc.
        async for agent_event in agent_interface.stream_chat(
            ctx=base_chat_context, # Pass the ChatContext that has the db info
            query=scenario['query'],
            session_id=session_id_stream, 
            dataset_id=scenario['dataset_id'],
            #model_name=scenario['model_name'],
            record_id=scenario['record_id'],
            chat_params=scenario_sg_params,  # 使用场景特定的参数
            mcp_manager=mcp_manager,
        ):
            event_count += 1
            # Ensure agent_event is not None and has event_type before logging
            if agent_event and hasattr(agent_event, 'event_type'):
                logger.debug(f"Test interface: Received event type: {agent_event.event_type}")
                # Safely convert to dict, handling potential None
                event_dict = agent_event.to_dict() if hasattr(agent_event, 'to_dict') else vars(agent_event)
                logger.debug(f"Test interface: Full event dict: {json.dumps(event_dict, indent=2, default=str)}")


                if agent_event.event_type == "think":
                    logger.debug(f"Test interface: Processing think event: {agent_event.content}")
                    print(f"\n[THINKING]: {agent_event.content}", flush=True)
                    # if isinstance(agent_event.content, str): # Collect only string content for final message
                    #      full_streamed_message_for_user_parts.append(f"[THINK]: {agent_event.content}\n")
                elif agent_event.event_type == "message": # This is intended for direct user-facing messages
                    logger.debug(f"Test interface: Processing message event: {agent_event.content}")
                    print(f"\n[MESSAGE]: {agent_event.content}", flush=True)
                    # if isinstance(agent_event.content, str):
                    #     full_streamed_message_for_user_parts.append(agent_event.content)
                elif agent_event.event_type == "text": # Also for user-facing text, perhaps from LLM directly
                    logger.debug(f"Test interface: Processing text event: {agent_event.content}")
                    print(f"\n[TEXT]: {agent_event.content}", flush=True)

                # Handle other event types for logging/display as before
                elif agent_event.event_type == "status":
                    logger.debug(f"Test interface: Processing status event: {getattr(agent_event, 'status', 'N/A')} - Stage: {getattr(agent_event, 'stage', 'N/A')}")
                    print(f"\n[STATUS: {getattr(agent_event, 'status', 'N/A')} - Stage: {getattr(agent_event, 'stage', 'N/A')}]", flush=True)
                elif agent_event.event_type == "error":
                    logger.debug(f"Test interface: Processing error event: {getattr(agent_event, 'error_type', 'N/A')} - {getattr(agent_event, 'error_msg', 'N/A')}")
                    print(f"\n[ERROR ({getattr(agent_event, 'error_type', 'N/A')}): {getattr(agent_event, 'error_msg', 'N/A')}]", flush=True)
                elif agent_event.event_type == "final_summary":
                    logger.debug(f"Test interface: Processing final_summary event: {agent_event.content}")
                    print(f"\n[FINAL SUMMARY]: {agent_event.content}, cell_id={getattr(agent_event, 'cell_id', 'N/A')}", flush=True)
                    # if isinstance(agent_event.content, str):
                    #      full_streamed_message_for_user_parts.append(f"\n[SUMMARY]: {agent_event.content}\n")
                elif agent_event.event_type == "task_list":
                    logger.debug(f"Test interface: Processing task_list event: {agent_event.content}")
                    print(f"\n[TASK LIST]: {json.dumps(agent_event.content, ensure_ascii=False, indent=2)}", flush=True) # Pretty print JSON
                elif agent_event.event_type == "studio_jupyter": # Note: your AgentEvent has 'studio_jupyter', previous code used 'jupyter_event'
                    # Ensure attribute access is safe
                    print("[JUPYTER EVENT]: ")
                    event_str = json.dumps(agent_event.to_dict(), ensure_ascii=False, indent=2)
                    event_str = event_str.replace("\\n", "\n")
                    event_str = event_str.replace('\\"', '\"')
                    print(event_str)
                    # print(json.dumps(agent_event.to_dict(), ensure_ascii=False, indent=2), flush=True)
                    # cell_type = getattr(agent_event, 'cell_type', 'N/A')
                    # source = getattr(agent_event, 'source', [])
                    # outputs = getattr(agent_event, 'outputs', [])
                    # status = getattr(agent_event, 'status', 'N/A') # Directly from event if flat
                    # # If status is nested in metadata: status = getattr(agent_event, 'metadata', {}).get('status', 'N/A')
                    # cell_id = getattr(agent_event, 'id', 'N/A')
                    # logger.debug(f"Test interface: Processing studio_jupyter event: cell_type={cell_type}, source={source}, outputs={outputs}, status={status}, cell_id={cell_id}")
                    # print(f"\n[JUPYTER EVENT]: cell_type={cell_type}, source={source}, outputs={outputs}, status={status}, cell_id={cell_id}", flush=True)
            else:
                logger.warning(f"Test interface: Received a None or malformed agent_event.")


        # full_streamed_message_for_user = "".join(full_streamed_message_for_user_parts)
        # print("\n\n" + "=" * 20 + "Aggregated Streamed Text to User" + "=" * 20) # Changed title for clarity
        # print(full_streamed_message_for_user if full_streamed_message_for_user else "(No text content directly streamed to user)")

        print("\n" + "=" * 20 + "Stream Summary" + "=" * 20)
        if event_count == 0:
            print("  (No events yielded from stream_chat for this input)")
        else:
            print(f"  Total events yielded: {event_count}")

        print("-" * 40)
        await asyncio.sleep(0.1) 


if __name__ == "__main__":
    asyncio.run(test_streaming_chat())


    # test_scenarios = [
    #     # {"query": "你好,", 
    #     #  "dataset_id": "nl2sql_test",
    #     #  "model_name": "deepseek-v3",
    #     #  "record_id": "5",
    #     # },
    #     # {"query": "我想对'销售额'列生成一个直方图,", 
    #     #  "dataset_id": "nl2sql_test",
    #     #  "model_name": "deepseek-v3",
    #     #  "record_id": "5",
    #     # },
    #     # {"query": "默认设置就可以", 
    #     #  "dataset_id": "nl2sql_test",
    #     #  "model_name": "deepseek-v3",
    #     #  "record_id": "5",
    #     # },
    #     {"query": "我想对'销售额'列生成一个直方图, 默认设置就可以, 不用反问我了", 
    #      "dataset_id": "data_agent",
    #      "model_name": "deepseek-v3",
    #      "record_id": "5",
    #     },
    #     # {"query": "找出2023年哪个地区销售额最高。不要反问我了 不用其他操作", 
    #     #  "dataset_id": "nl2sql_test",
    #     #  "model_name": "deepseek-v3",
    #     #  "record_id": "5",
    #     # },
    # ]

