import json
import traceback
from mcp.server import Server
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
import os
import mcp.types as types
from common.logger.logger import logger
from mcp.types import Resource, Tool, TextContent


# Pass lifespan to server
server = Server("intellix-ds-agent")


def tool_execute_code(name: str = "execute_code") -> tuple[str, Tool, callable]:
    tool = Tool(
        name=name,
        description='''
        Execute code in a remote Jupyter kernel and return the output and error information.

        Args:
        code: The code string to execute in the Jupyter kernel.

        Returns:
        {
            "outputs": <list of output results>,
            "error": <is exec error, return true or false>
        }

        Example:
        execute_code(code="print('hello world')")
        execute_code(code="print('hello world')", required_packages=["prophet"])
        ''',
        inputSchema={
            "properties": {
                "code": {
                    "type": "string",
                    "description": "The code to execute in the Jupyter kernel"
                },
                "required_packages": {
                    "type": "array",
                    "description": "The required packages to install in the Jupyter kernel"
                }
            },
            "required": ["code"],
        }
    )

    def execute_code(eg_host, eg_sub_uin, kernel_id, kernel_name, timeout, code: str, required_packages: list = None):
        logger.info(
            f"Executing code with parameters: eg_host={eg_host}, eg_sub_uin={eg_sub_uin}, kernel_id={kernel_id}, kernel_name={kernel_name}, timeout={timeout}, code = {code}, required_packages= {required_packages}")
        return [TextContent(type="text", text=json.dumps("execute code success", ensure_ascii=False))]

    return name, tool, execute_code


tool_list = [tool_execute_code()]
tool_map = {name: tool for name, tool, func in tool_list}
tool_funcs = {name: func for name, tool, func in tool_list}


@server.list_tools()
async def list_tools() -> list:
    return tool_map.values()


@server.call_tool()
async def call_tool(name: str, arguments: dict) -> dict:
    """Call a tool by name."""
    if name not in tool_map:
        raise ValueError(f"Tool {name} not found")
    ctx = server.request_context.lifespan_context
    logger.info(f"Executing tool {name} : with params {arguments} in ctx {ctx}")

    eg_sub_uin = ctx.get("eg_sub_uin", "unknown")
    timeout = int(ctx.get("timeout", "600"))
    eg_host = ctx.get("eg_host", "localhost:8888")
    kernel_id = ctx.get("kernel_id", "unknown")
    kernel_name = ctx.get("kernel_name", "unknown")
    func = tool_funcs[name]
    arguments.update({
        "eg_host": eg_host,
        "eg_sub_uin": eg_sub_uin,
        "kernel_id": kernel_id,
        "kernel_name": kernel_name,
        "timeout": timeout})
    return func(**arguments)


@server.list_resources()
async def list_resources() -> list:
    return []


@server.read_resource()
async def read_resource(url: str) -> str:
    return ""


@server.list_prompts()
async def list_prompts() -> list:
    return [
        types.Prompt(
            name="execute code",
            description="please help me exec code",
            arguments=[],
        )
    ]


@server.get_prompt()
async def handle_get_prompt(
        name: str, arguments: dict[str, str] | None
) -> types.GetPromptResult:
    return types.GetPromptResult(
        description="help me exec code",
        messages=[
            types.PromptMessage(
                role="user",
                content=types.TextContent(type="text", text="please help me exec code"),
            )
        ]
    )


async def run_stdio():
    from mcp.server.stdio import stdio_server
    logger.info("Starting stdio server")
    async with stdio_server() as (read_stream, write_stream):
        try:
            await server.run(
                read_stream,
                write_stream,
                server.create_initialization_options()
            )
        except Exception as e:
            logger.error(f"Server error: {str(e)}", exc_info=True)
            raise


def run_sse(port: int = 8900):
    from mcp.server.sse import SseServerTransport
    from starlette.applications import Starlette
    from starlette.routing import Mount, Route
    import uvicorn
    sse = SseServerTransport("/messages/")  # 创建SSE服务器传输实例，路径为"/messages/"

    async def handle_sse(request):
        async with sse.connect_sse(
                request.scope, request.receive, request._send
        ) as streams:
            # 建立SSE连接，获取输入输出流
            try:
                await server.run(
                    streams[0], streams[1], server.create_initialization_options()
                )
            except Exception as e:
                logger.error(f"Server error: {str(e)}", exc_info=True)
                raise

    starlette_app = Starlette(
        debug=True,  # 启用调试模式
        routes=[
            Route("/sse", endpoint=handle_sse),  # 设置/sse路由，处理函数为handle_sse
            Mount("/messages/", app=sse.handle_post_message),  # 挂载/messages/路径，处理POST消息
        ],
    )  # 创建Starlette应用实例，配置路由
    uvicorn.run(starlette_app, host="0.0.0.0", port=port)


if __name__ == "__main__":
    # sync
    import asyncio

    asyncio.run(run_stdio())

    # async
    # run_sse()
