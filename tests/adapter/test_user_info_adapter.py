import pytest
from infra.adapter.user_info_adapter import UserInfoAdapter
from infra.domain.user_info_entity import UserInfo
from tests.adapter.adapter_basic import get_adapter
from common.share.context import Context
from datetime import datetime
from functools import wraps

@pytest.fixture
def adapter():
    return get_adapter(UserInfoAdapter)

@pytest.fixture
def ctx():
    return Context()

def generate_test_user_info(sub_account_uin: str) -> UserInfo:
    return UserInfo(
        sub_account_uin=sub_account_uin,
        jupyter_host="http://jupyter.test.com",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )

def with_test_data(*test_users, extra_cleanup_ids=None):
    def decorator(func):
        @wraps(func)
        def wrapper(adapter, ctx, *args, **kwargs):
            try:
                for user in test_users:
                    adapter.create_or_update(ctx, user)
                return func(adapter, ctx, *args, **kwargs)
            finally:
                for user in test_users:
                    adapter.persistence.models['user_info'].delete().where(
                        adapter.persistence.models['user_info'].sub_account_uin == user.sub_account_uin
                    ).execute()
                if extra_cleanup_ids:
                    for uid in extra_cleanup_ids:
                        adapter.persistence.models['user_info'].delete().where(
                            adapter.persistence.models['user_info'].sub_account_uin == uid
                        ).execute()
        return wrapper
    return decorator

@with_test_data(generate_test_user_info("user-test-1"))
def test_get_by_sub_account_uin_found(adapter, ctx):
    result = adapter.get_by_sub_account_uin(ctx, "user-test-1")
    assert isinstance(result, UserInfo)
    assert result.sub_account_uin == "user-test-1"
    assert result.jupyter_host == "http://jupyter.test.com"

@with_test_data()
def test_get_by_sub_account_uin_not_found(adapter, ctx):
    result = adapter.get_by_sub_account_uin(ctx, "not-exist")
    assert result is None

# 修正：不使用 with_test_data 装饰器，测试体内插入并清理
def test_create_or_update_create_success(adapter, ctx):
    test_user = generate_test_user_info("user-test-2")
    try:
        assert adapter.create_or_update(ctx, test_user) is True
        # 再查一遍确认
        result = adapter.get_by_sub_account_uin(ctx, "user-test-2")
        assert result is not None
        assert result.sub_account_uin == "user-test-2"
        assert result.jupyter_host == "http://jupyter.test.com"
    finally:
        adapter.persistence.models['user_info'].delete().where(
            adapter.persistence.models['user_info'].sub_account_uin == "user-test-2"
        ).execute()

@with_test_data(generate_test_user_info("user-test-3"))
def test_create_or_update_update_success(adapter, ctx):
    """测试更新已存在的用户信息"""
    # 先验证用户存在
    user = adapter.get_by_sub_account_uin(ctx, "user-test-3")
    assert user is not None
    original_host = user.jupyter_host
    
    # 更新用户信息
    updated_user = UserInfo(
        sub_account_uin="user-test-3",
        jupyter_host="http://jupyter.updated.com",
        created_at=user.created_at,
        updated_at=datetime.now()
    )
    
    assert adapter.create_or_update(ctx, updated_user) is True
    
    # 验证更新结果
    result = adapter.get_by_sub_account_uin(ctx, "user-test-3")
    assert result is not None
    assert result.jupyter_host == "http://jupyter.updated.com"
    assert result.jupyter_host != original_host

def test_create_or_update_without_created_at(adapter, ctx):
    """测试创建用户信息时不提供created_at"""
    test_user = UserInfo(
        sub_account_uin="user-test-4",
        jupyter_host="http://jupyter.test.com",
        created_at=None,
        updated_at=None
    )
    try:
        assert adapter.create_or_update(ctx, test_user) is True
        # 验证用户创建成功，且created_at和updated_at都被设置
        result = adapter.get_by_sub_account_uin(ctx, "user-test-4")
        assert result is not None
        assert result.created_at is not None
        assert result.updated_at is not None
    finally:
        adapter.persistence.models['user_info'].delete().where(
            adapter.persistence.models['user_info'].sub_account_uin == "user-test-4"
        ).execute()

def test_create_or_update_without_updated_at(adapter, ctx):
    """测试创建用户信息时不提供updated_at"""
    test_user = UserInfo(
        sub_account_uin="user-test-5",
        jupyter_host="http://jupyter.test.com",
        created_at=datetime.now(),
        updated_at=None
    )
    try:
        assert adapter.create_or_update(ctx, test_user) is True
        # 验证用户创建成功，且updated_at被设置
        result = adapter.get_by_sub_account_uin(ctx, "user-test-5")
        assert result is not None
        assert result.updated_at is not None
    finally:
        adapter.persistence.models['user_info'].delete().where(
            adapter.persistence.models['user_info'].sub_account_uin == "user-test-5"
        ).execute()

@with_test_data(generate_test_user_info("user-test-6"))
def test_create_or_update_multiple_times(adapter, ctx):
    """测试多次更新同一用户信息"""
    # 第一次更新
    user1 = UserInfo(
        sub_account_uin="user-test-6",
        jupyter_host="http://jupyter.first.com",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    assert adapter.create_or_update(ctx, user1) is True
    
    # 第二次更新
    user2 = UserInfo(
        sub_account_uin="user-test-6",
        jupyter_host="http://jupyter.second.com",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    assert adapter.create_or_update(ctx, user2) is True
    
    # 验证最终结果
    result = adapter.get_by_sub_account_uin(ctx, "user-test-6")
    assert result is not None
    assert result.jupyter_host == "http://jupyter.second.com"

def test_create_or_update_different_users(adapter, ctx):
    """测试创建不同用户的信息"""
    user1 = generate_test_user_info("user-test-7")
    user2 = generate_test_user_info("user-test-8")
    
    try:
        # 创建两个不同的用户
        assert adapter.create_or_update(ctx, user1) is True
        assert adapter.create_or_update(ctx, user2) is True
        
        # 验证两个用户都能正确查询
        result1 = adapter.get_by_sub_account_uin(ctx, "user-test-7")
        result2 = adapter.get_by_sub_account_uin(ctx, "user-test-8")
        
        assert result1 is not None
        assert result2 is not None
        assert result1.sub_account_uin == "user-test-7"
        assert result2.sub_account_uin == "user-test-8"
        assert result1.sub_account_uin != result2.sub_account_uin
    finally:
        # 清理测试数据
        adapter.persistence.models['user_info'].delete().where(
            adapter.persistence.models['user_info'].sub_account_uin.in_(["user-test-7", "user-test-8"])
        ).execute()

def test_get_by_sub_account_uin_deleted_user(adapter, ctx):
    """测试查询已删除的用户信息"""
    # 先创建一个用户
    test_user = generate_test_user_info("user-test-9")
    try:
        assert adapter.create_or_update(ctx, test_user) is True
        
        # 验证用户存在
        result = adapter.get_by_sub_account_uin(ctx, "user-test-9")
        assert result is not None
        
        # 手动删除用户（设置deleted_at）
        adapter.persistence.models['user_info'].update(
            deleted_at=datetime.now()
        ).where(
            adapter.persistence.models['user_info'].sub_account_uin == "user-test-9"
        ).execute()
        
        # 验证查询不到已删除的用户
        deleted_result = adapter.get_by_sub_account_uin(ctx, "user-test-9")
        assert deleted_result is None
    finally:
        adapter.persistence.models['user_info'].delete().where(
            adapter.persistence.models['user_info'].sub_account_uin == "user-test-9"
        ).execute()

def test_create_or_update_with_empty_jupyter_host(adapter, ctx):
    """测试创建用户信息时jupyter_host为空"""
    test_user = UserInfo(
        sub_account_uin="user-test-10",
        jupyter_host="",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    try:
        assert adapter.create_or_update(ctx, test_user) is True
        # 验证用户创建成功
        result = adapter.get_by_sub_account_uin(ctx, "user-test-10")
        assert result is not None
        assert result.jupyter_host == ""
    finally:
        adapter.persistence.models['user_info'].delete().where(
            adapter.persistence.models['user_info'].sub_account_uin == "user-test-10"
        ).execute()

 