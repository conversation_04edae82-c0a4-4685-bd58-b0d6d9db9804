import pytest
from infra.adapter.knowledge_list_adapter import KnowledgeListAdapter
from infra.domain.knowledge_list_entity import Knowledge
from tests.adapter.adapter_basic import get_adapter
from common.share.context import Context
from datetime import datetime
from functools import wraps
from peewee import Expression

@pytest.fixture
def adapter():
    return get_adapter(KnowledgeListAdapter)

@pytest.fixture
def ctx():
    ctx = Context()
    ctx.app_id = "app1"
    return ctx

def generate_test_knowledge(file_id: str) -> Knowledge:
    return Knowledge(
        file_id=file_id,
        file_name="test_file.pdf",
        file_size=1024,
        file_url="http://test.com/file.pdf",
        status=0,
        knowledge_base_id="kb1",
        type=1,
        chunk_config="{}",
        create_user="user1",
        create_time=datetime.now(),
        update_time=None,
        source=0
    )

def with_test_data(*test_knowledge, extra_cleanup_ids=None):
    def decorator(func):
        @wraps(func)
        def wrapper(adapter, ctx, *args, **kwargs):
            try:
                for knowledge in test_knowledge:
                    adapter.create_knowledge_task(ctx, knowledge)
                return func(adapter, ctx, *args, **kwargs)
            finally:
                for knowledge in test_knowledge:
                    adapter.persistence.models['knowledge_list'].delete().where(
                        adapter.persistence.models['knowledge_list'].file_id == knowledge.file_id
                    ).execute()
                if extra_cleanup_ids:
                    for fid in extra_cleanup_ids:
                        adapter.persistence.models['knowledge_list'].delete().where(
                            adapter.persistence.models['knowledge_list'].file_id == fid
                        ).execute()
        return wrapper
    return decorator

@with_test_data(generate_test_knowledge("file-test-1"))
def test_get_knowledge_by_file_id_found(adapter, ctx):
    result = adapter.get_knowledge_by_file_id("file-test-1")
    assert isinstance(result, Knowledge)
    assert result.file_id == "file-test-1"
    assert result.file_name == "test_file.pdf"

@with_test_data()
def test_get_knowledge_by_file_id_not_found(adapter, ctx):
    result = adapter.get_knowledge_by_file_id("not-exist")
    assert result is None

# 修正：不使用 with_test_data 装饰器，测试体内插入并清理
def test_create_knowledge_task_success(adapter, ctx):
    test_knowledge = generate_test_knowledge("file-test-2")
    try:
        assert adapter.create_knowledge_task(ctx, test_knowledge) is True
        # 再查一遍确认
        result = adapter.get_knowledge_by_file_id("file-test-2")
        assert result is not None
        assert result.file_id == "file-test-2"
    finally:
        adapter.persistence.models['knowledge_list'].delete().where(
            adapter.persistence.models['knowledge_list'].file_id == "file-test-2"
        ).execute()

def test_batch_create_knowledge_tasks_success(adapter, ctx):
    """测试批量创建知识任务"""
    test_knowledge_list = [
        generate_test_knowledge("batch-test-1"),
        generate_test_knowledge("batch-test-2"),
        generate_test_knowledge("batch-test-3")
    ]
    try:
        assert adapter.batch_create_knowledge_tasks(ctx, test_knowledge_list) is True
        # 验证所有任务都创建成功
        for knowledge in test_knowledge_list:
            result = adapter.get_knowledge_by_file_id(knowledge.file_id)
            assert result is not None
            assert result.file_id == knowledge.file_id
    finally:
        # 清理测试数据
        for knowledge in test_knowledge_list:
            adapter.persistence.models['knowledge_list'].delete().where(
                adapter.persistence.models['knowledge_list'].file_id == knowledge.file_id
            ).execute()

@with_test_data(generate_test_knowledge("status-test-1"))
def test_update_status(adapter, ctx):
    """测试更新任务状态"""
    # 先验证初始状态
    knowledge = adapter.get_knowledge_by_file_id("status-test-1")
    assert knowledge.status == 0
    
    # 更新状态
    assert adapter.update_status("status-test-1", 1) is True
    
    # 验证状态已更新
    updated = adapter.get_knowledge_by_file_id("status-test-1")
    assert updated.status == 1

@with_test_data()
def test_update_status_not_found(adapter, ctx):
    """测试更新不存在的任务状态"""
    result = adapter.update_status("not-exist", 1)
    assert result is False

@with_test_data(
    generate_test_knowledge("delete-test-1"),
    generate_test_knowledge("delete-test-2"),
    generate_test_knowledge("delete-test-3")
)
def test_delete_knowledge_by_file_ids(adapter, ctx):
    """测试批量删除知识任务"""
    file_ids = ["delete-test-1", "delete-test-2", "delete-test-3"]
    
    # 验证所有任务都存在
    for file_id in file_ids:
        assert adapter.get_knowledge_by_file_id(file_id) is not None
    
    # 执行删除
    assert adapter.delete_knowledge_by_file_ids(file_ids) is True
    
    # 验证所有任务都被删除
    for file_id in file_ids:
        assert adapter.get_knowledge_by_file_id(file_id) is None

def test_delete_knowledge_by_file_ids_empty_list(adapter, ctx):
    """测试删除空列表"""
    result = adapter.delete_knowledge_by_file_ids([])
    assert result is False

@with_test_data(generate_test_knowledge("config-test-1"))
def test_update_config(adapter, ctx):
    """测试更新任务配置"""
    # 先验证初始状态
    knowledge = adapter.get_knowledge_by_file_id("config-test-1")
    assert knowledge.status == 0
    assert knowledge.chunk_config == "{}"
    
    # 更新配置
    new_config = '{"chunk_size": 1000, "overlap": 200}'
    assert adapter.update_config("config-test-1", 2, new_config) is True
    
    # 验证配置已更新
    updated = adapter.get_knowledge_by_file_id("config-test-1")
    assert updated.status == 2
    assert updated.chunk_config == new_config

@with_test_data()
def test_update_config_not_found(adapter, ctx):
    """测试更新不存在的任务配置"""
    result = adapter.update_config("not-exist", 1, "{}")
    assert result is False

@with_test_data(
    generate_test_knowledge("list-test-1"),
    generate_test_knowledge("list-test-2"),
    generate_test_knowledge("list-test-3")
)
def test_get_knowledge_list(adapter, ctx):
    """测试获取知识列表"""
    total, data = adapter.get_knowledge_list(ctx, [], [], 0, 10)
    assert isinstance(total, int)
    assert isinstance(data, list)
    assert total >= 3
    assert len(data) >= 3
    
    # 验证返回的数据都是属于该app的
    for item in data:
        assert item.file_id in ["list-test-1", "list-test-2", "list-test-3"]

@with_test_data()
def test_get_knowledge_list_empty(adapter, ctx):
    """测试获取空的知识列表"""
    total, data = adapter.get_knowledge_list(ctx, [], [], 0, 10)
    assert total >= 0
    assert isinstance(data, list)

@with_test_data(
    generate_test_knowledge("filter-test-1"),
    generate_test_knowledge("filter-test-2")
)
def test_get_knowledge_list_with_filter(adapter, ctx):
    """测试带过滤条件的知识列表"""
    # 创建过滤条件
    model = adapter.persistence.models['knowledge_list']
    filter_conditions = [model.status == 0]
    
    total, data = adapter.get_knowledge_list(ctx, filter_conditions, [], 0, 10)
    assert total >= 2
    assert len(data) >= 2
    
    # 验证所有返回的记录都满足过滤条件
    for item in data:
        assert item.status == 0

@with_test_data(
    generate_test_knowledge("sort-test-1"),
    generate_test_knowledge("sort-test-2")
)
def test_get_knowledge_list_with_sort(adapter, ctx):
    """测试带排序条件的知识列表"""
    # 创建排序条件
    model = adapter.persistence.models['knowledge_list']
    sort_conditions = [model.create_time.asc()]
    
    total, data = adapter.get_knowledge_list(ctx, [], sort_conditions, 0, 10)
    assert total >= 2
    assert len(data) >= 2
    
    # 验证按创建时间升序排列
    create_times = [item.create_time for item in data if item.create_time]
    if len(create_times) > 1:
        assert create_times == sorted(create_times)

@with_test_data(
    generate_test_knowledge("page-test-1"),
    generate_test_knowledge("page-test-2"),
    generate_test_knowledge("page-test-3"),
    generate_test_knowledge("page-test-4")
)
def test_get_knowledge_list_pagination(adapter, ctx):
    """测试知识列表分页"""
    # 第一页，每页2条
    total, data = adapter.get_knowledge_list(ctx, [], [], 0, 2)
    assert total >= 4
    assert len(data) == 2
    
    # 第二页，每页2条
    total2, data2 = adapter.get_knowledge_list(ctx, [], [], 2, 2)
    assert total2 >= 4
    assert len(data2) == 2
    
    # 验证两页数据不重复
    page1_ids = {item.file_id for item in data}
    page2_ids = {item.file_id for item in data2}
    assert page1_ids.isdisjoint(page2_ids)

def test_create_duplicate_knowledge(adapter, ctx):
    """测试创建重复的知识任务"""
    test_knowledge = generate_test_knowledge("dup-test-1")
    try:
        # 第一次创建应该成功
        assert adapter.create_knowledge_task(ctx, test_knowledge) is True
        # 第二次创建相同任务应该失败（主键冲突）
        assert adapter.create_knowledge_task(ctx, test_knowledge) is False
    finally:
        adapter.persistence.models['knowledge_list'].delete().where(
            adapter.persistence.models['knowledge_list'].file_id == "dup-test-1"
        ).execute()

def test_batch_delete_large_list(adapter, ctx):
    """测试批量删除大量记录"""
    # 创建超过1000条记录来测试分批删除
    test_knowledge_list = [
        generate_test_knowledge(f"large-batch-{i}")
        for i in range(1500)
    ]
    try:
        # 批量创建
        assert adapter.batch_create_knowledge_tasks(ctx, test_knowledge_list) is True
        
        # 批量删除
        file_ids = [knowledge.file_id for knowledge in test_knowledge_list]
        assert adapter.delete_knowledge_by_file_ids(file_ids) is True
        
        # 验证所有记录都被删除
        for file_id in file_ids[:10]:  # 只检查前10个
            assert adapter.get_knowledge_by_file_id(file_id) is None
    finally:
        # 清理测试数据
        for knowledge in test_knowledge_list:
            adapter.persistence.models['knowledge_list'].delete().where(
                adapter.persistence.models['knowledge_list'].file_id == knowledge.file_id
            ).execute() 