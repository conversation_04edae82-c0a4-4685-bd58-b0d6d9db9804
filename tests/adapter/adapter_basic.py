from common.database.database import global_metadata_db_pool
from infra.adapter.agent_list_adapter import AgentListAdapter
from typing import Any
import pytest
from common.share import env
from common.share.config import appConfig

try:
    db_config = appConfig.common.metadata_db
    if not db_config.host:
        pytest.skip("DB config not found or incomplete, skipping adapter tests", allow_module_level=True)
except Exception as e:
    pytest.skip(f"DB config not found or failed to load: {str(e)}, skipping adapter tests", allow_module_level=True)


def get_db_pool():
    db_pool = global_metadata_db_pool()
    # Read init.sql
    init_sql_path = env.INIT_SQL_PATH
    with open(init_sql_path, 'r', encoding='utf-8') as f:
        init_sql = f.read()
    
    # Split SQL statements
    sql_statements = [s.strip() for s in init_sql.split(';') if s.strip()]
    
    # Execute init.sql
    with db_pool.pool_db as conn:
        with conn.cursor() as cursor:
            # 先执行CREATE DATABASE
            cursor.execute(sql_statements[0] + ';')
            # 切换到目标数据库
            cursor.execute('USE data_agent;')
            # 执行后续表结构SQL
            for statement in sql_statements[1:]:
                cursor.execute(statement + ';')
            conn.commit()
    return db_pool


def get_adapter(cls: Any = AgentListAdapter) -> Any:
    return cls(get_db_pool())