import pytest
from infra.adapter.task_list_adapter import Task<PERSON>istAdapter
from infra.domain.task_list_entity import TaskList
from tests.adapter.adapter_basic import get_adapter
from common.share.context import ChatContext
from datetime import datetime, timezone
from functools import wraps

@pytest.fixture
def adapter():
    return get_adapter(TaskListAdapter)

@pytest.fixture
def ctx():
    return ChatContext()

def generate_test_task(task_id: str) -> TaskList:
    return TaskList(
        task_id=task_id,
        knowledge_base_id="kb1",
        file_id="file1",
        file_url="http://test.com/file1",
        app_id="app1",
        task_type=1,
        status=0,
        node_id=None,
        task_params="{}",
        task_result=None,
        create_time=datetime.now(timezone.utc),
        update_time=None
    )

def with_test_data(*test_tasks, extra_cleanup_ids=None):
    def decorator(func):
        @wraps(func)
        def wrapper(adapter, ctx, *args, **kwargs):
            try:
                for task in test_tasks:
                    adapter.create(task)
                return func(adapter, ctx, *args, **kwargs)
            finally:
                for task in test_tasks:
                    adapter.persistence.models['task_list'].delete().where(
                        adapter.persistence.models['task_list'].task_id == task.task_id
                    ).execute()
                if extra_cleanup_ids:
                    for task_id in extra_cleanup_ids:
                        adapter.persistence.models['task_list'].delete().where(
                            adapter.persistence.models['task_list'].task_id == task_id
                        ).execute()
        return wrapper
    return decorator

@with_test_data(generate_test_task("task-test-1"))
def test_get_by_id_found(adapter, ctx):
    result = adapter.get_by_id("task-test-1")
    assert isinstance(result, TaskList)
    assert result.task_id == "task-test-1"
    assert result.knowledge_base_id == "kb1"

@with_test_data()
def test_get_by_id_not_found(adapter, ctx):
    result = adapter.get_by_id("not-exist")
    assert result is None

def test_create_success(adapter, ctx):
    test_task = generate_test_task("task-test-2")
    try:
        assert adapter.create(test_task) is True
        # 再查一遍确认
        result = adapter.get_by_id("task-test-2")
        assert result is not None
        assert result.task_id == "task-test-2"
    finally:
        adapter.persistence.models['task_list'].delete().where(
            adapter.persistence.models['task_list'].task_id == "task-test-2"
        ).execute()

@with_test_data(generate_test_task("task-test-3"))
def test_update_response(adapter, ctx):
    assert adapter.update_response("task-test-3", "result-ok") is True
    updated = adapter.get_by_id("task-test-3")
    assert updated.task_result == "result-ok"

def test_batch_create_success(adapter, ctx):
    """测试批量创建任务"""
    test_tasks = [
        generate_test_task("batch-test-1"),
        generate_test_task("batch-test-2"),
        generate_test_task("batch-test-3")
    ]
    try:
        assert adapter.batch_create(test_tasks) is True
        # 验证所有任务都创建成功
        for task in test_tasks:
            result = adapter.get_by_id(task.task_id)
            assert result is not None
            assert result.task_id == task.task_id
    finally:
        # 清理测试数据
        for task in test_tasks:
            adapter.persistence.models['task_list'].delete().where(
                adapter.persistence.models['task_list'].task_id == task.task_id
            ).execute()

@with_test_data(
    generate_test_task("list-test-1"),
    generate_test_task("list-test-2"),
    generate_test_task("list-test-3")
)
def test_get_task_list(adapter, ctx):
    """测试获取任务列表"""
    result = adapter.get_task_list(limit=10)
    assert isinstance(result, list)
    assert len(result) >= 3
    # 验证返回的任务都是状态为0的
    for task in result:
        assert task.status == 0

@with_test_data(
    generate_test_task("running-test-1"),
    generate_test_task("running-test-2")
)
def test_mark_task_running(adapter, ctx):
    """测试标记任务为运行中"""
    result = adapter.mark_task_running(limit=5,actor_name="test_actor")
    assert isinstance(result, list)
    assert len(result) >= 2
    # 验证返回的任务状态都更新为1
    for task in result:
        assert task.status == 1
        # 验证数据库中的状态也更新了
        db_task = adapter.get_by_id(task.task_id)
        assert db_task.status == 1

@with_test_data(generate_test_task("status-test-1"))
def test_mark_task_status(adapter, ctx):
    """测试标记任务状态"""
    # 测试不带node_id
    assert adapter.mark_task_status("status-test-1", status=2) is True
    updated = adapter.get_by_id("status-test-1")
    assert updated.status == 2

    # 测试带node_id
    assert adapter.mark_task_status("status-test-1", status=3, node_id="node1") is True
    updated = adapter.get_by_id("status-test-1")
    assert updated.status == 3
    assert updated.node_id == "node1"

@with_test_data(generate_test_task("error-test-1"))
def test_mark_task_error(adapter, ctx):
    """测试标记任务错误"""
    assert adapter.mark_task_error("error-test-1", status=3, error_msg="test error") is True
    updated = adapter.get_by_id("error-test-1")
    assert updated.status == 3
    # 注意：error_msg字段在TaskList实体中可能没有，这里主要测试状态更新

def test_reset_orphaned_tasks(adapter, ctx):
    """测试重置孤儿任务"""
    # 这个测试比较复杂，需要先创建一些任务和节点数据
    # 这里只测试基本调用，不验证具体逻辑
    result = adapter.reset_orphaned_tasks(heartbeat_timeout=300)
    assert isinstance(result, int)
    assert result >= 0 