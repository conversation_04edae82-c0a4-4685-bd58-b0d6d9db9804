import pytest
from infra.adapter.user_count_adapter import User<PERSON>ountAdapter
from infra.domain.user_count_entity import UserCount
from tests.adapter.adapter_basic import get_adapter
from common.share.context import Chat<PERSON>ontext
from datetime import datetime
from functools import wraps

@pytest.fixture
def adapter():
    return get_adapter(UserCountAdapter)

@pytest.fixture
def ctx():
    return ChatContext(
        header={"SubAccountUin": "utt_adapter", "AppId": "app1"}
    )

def generate_test_user_count(record_id: str) -> UserCount:
    return UserCount(
        app_id="app1",
        sub_account_uin="utt_adapter",
        session_id="sess1",
        record_id=record_id,
        create_time=datetime.now()
    )

def with_test_data(*test_records, extra_cleanup_ids=None):
    def decorator(func):
        @wraps(func)
        def wrapper(adapter, ctx, *args, **kwargs):
            try:
                for rec in test_records:
                    adapter.create(ctx, rec)
                return func(adapter, ctx, *args, **kwargs)
            finally:
                for rec in test_records:
                    adapter.mysql_pool.models['user_count'].delete().where(
                        (adapter.mysql_pool.models['user_count'].app_id == rec.app_id) &
                        (adapter.mysql_pool.models['user_count'].sub_account_uin == rec.sub_account_uin) &
                        (adapter.mysql_pool.models['user_count'].record_id == rec.record_id)
                    ).execute()
                if extra_cleanup_ids:
                    for rid in extra_cleanup_ids:
                        adapter.mysql_pool.models['user_count'].delete().where(
                            (adapter.mysql_pool.models['user_count'].app_id == rec.app_id) &
                            (adapter.mysql_pool.models['user_count'].sub_account_uin == rec.sub_account_uin) &
                            (adapter.mysql_pool.models['user_count'].record_id == rid)
                        ).execute()
        return wrapper
    return decorator

@with_test_data(generate_test_user_count("rec-test-1"))
def test_get_by_user_found(adapter, ctx):
    result = adapter.get_by_user(ctx, "app1", "utt_adapter")
    assert isinstance(result, list)
    assert any(r.record_id == "rec-test-1" for r in result)

@with_test_data()
def test_get_by_user_not_found(adapter, ctx):
    result = adapter.get_by_user(ctx, "app1", "not-exist")
    assert result == []

def test_create_success(adapter, ctx):
    test_rec = generate_test_user_count("rec-test-2")
    try:
        assert adapter.create(ctx, test_rec) is True
        # 再查一遍确认
        result = adapter.get_by_user(ctx, "app1", "utt_adapter")
        assert any(r.record_id == "rec-test-2" for r in result)
    finally:
        adapter.mysql_pool.models['user_count'].delete().where(
            (adapter.mysql_pool.models['user_count'].app_id == "app1") &
            (adapter.mysql_pool.models['user_count'].sub_account_uin == "utt_adapter") &
            (adapter.mysql_pool.models['user_count'].record_id == "rec-test-2")
        ).execute()

@with_test_data(generate_test_user_count("rec-test-3"))
def test_get_statistics(adapter: UserCountAdapter, ctx: ChatContext):
    stats = adapter.get_statistics(ctx)
    assert stats["total_count"] >= 1
    assert stats["sub_account_count"] >= 1
    assert "session_count" in stats

@with_test_data(
    generate_test_user_count("stats-test-1"),
    generate_test_user_count("stats-test-2"),
    generate_test_user_count("stats-test-3")
)
def test_get_statistics_multiple_records(adapter, ctx):
    """测试多个记录的统计信息"""
    stats = adapter.get_statistics(ctx)
    assert stats["total_count"] >= 3
    assert stats["sub_account_count"] >= 3
    assert "session_count" in stats

def test_get_statistics_no_records(adapter, ctx):
    """测试没有记录时的统计信息"""
    stats = adapter.get_statistics(ctx)
    assert stats["total_count"] >= 0
    assert stats["sub_account_count"] >= 0
    assert stats["session_count"] >= 0

def test_create_duplicate_record(adapter, ctx):
    """测试创建重复记录"""
    test_rec = generate_test_user_count("dup-test-1")
    try:
        # 第一次创建应该成功
        assert adapter.create(ctx, test_rec) is True
        # 第二次创建相同记录应该失败（主键冲突）
        assert adapter.create(ctx, test_rec) is False
    finally:
        adapter.mysql_pool.models['user_count'].delete().where(
            (adapter.mysql_pool.models['user_count'].app_id == "app1") &
            (adapter.mysql_pool.models['user_count'].sub_account_uin == "utt_adapter") &
            (adapter.mysql_pool.models['user_count'].record_id == "dup-test-1")
        ).execute()

@with_test_data(
    generate_test_user_count("multi-sess-1"),
    generate_test_user_count("multi-sess-2")
)
def test_get_by_user_multiple_sessions(adapter, ctx):
    """测试获取用户多个会话的记录"""
    result = adapter.get_by_user(ctx, "app1", "utt_adapter")
    assert isinstance(result, list)
    assert len(result) >= 2
    # 验证记录按创建时间倒序排列
    create_times = [r.create_time for r in result if r.create_time]
    if len(create_times) > 1:
        assert create_times == sorted(create_times, reverse=True)

def test_create_with_different_app_id(adapter, ctx):
    """测试不同app_id的记录"""
    test_rec = UserCount(
        app_id="app2",
        sub_account_uin="user1",
        session_id="sess1",
        record_id="diff-app-test",
        create_time=datetime.now()
    )
    try:
        assert adapter.create(ctx, test_rec) is True
        # 验证只能查到app2的记录
        result = adapter.get_by_user(ctx, "app2", "user1")
        assert any(r.record_id == "diff-app-test" for r in result)
        # 验证app1下没有这个记录
        result_app1 = adapter.get_by_user(ctx, "app1", "user1")
        assert not any(r.record_id == "diff-app-test" for r in result_app1)
    finally:
        adapter.mysql_pool.models['user_count'].delete().where(
            (adapter.mysql_pool.models['user_count'].app_id == "app2") &
            (adapter.mysql_pool.models['user_count'].sub_account_uin == "user1") &
            (adapter.mysql_pool.models['user_count'].record_id == "diff-app-test")
        ).execute() 