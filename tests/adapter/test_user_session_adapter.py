import pytest
from infra.adapter.user_session_adapter import UserSessionAdapter
from infra.domain.user_session_entity import UserSession
from tests.adapter.adapter_basic import get_adapter
from common.share.context import Chat<PERSON>ontext
from datetime import datetime
from functools import wraps

@pytest.fixture
def adapter():
    return get_adapter(UserSessionAdapter)

@pytest.fixture
def ctx():
    return ChatContext()

def generate_test_session(session_id: str) -> UserSession:
    return UserSession(
        app_id="app_id_1",
        sub_account_uin="user1",
        session_id=session_id,
        session_title="Test Session",
        db_info="{}",
        created_at=datetime.now(),
        updated_at=datetime.now()
    ), ChatContext(
        input={"SessionId": session_id},
        header={"SubAccountUin": "user1", "AppId": "app_id_1"})

def with_test_data(*test_sessions, extra_cleanup_ids=None):
    def decorator(func):
        @wraps(func)
        def wrapper(adapter, ctx, *args, **kwargs):
            try:
                for (idx, (session, ctx_input)) in enumerate(test_sessions):
                    adapter.create(ctx_input, session)
                    ctx = ctx_input
                return func(adapter, ctx, *args, **kwargs)
            finally:
                for (idx, (session, ctx_input)) in enumerate(test_sessions):
                    adapter.persistence.models['user_session'].delete().where(
                        (adapter.persistence.models['user_session'].sub_account_uin == "user1") &
                        (adapter.persistence.models['user_session'].session_id == session.session_id)
                    ).execute()
                if extra_cleanup_ids:
                    for sid in extra_cleanup_ids:
                        adapter.persistence.models['user_session'].delete().where(
                            (adapter.persistence.models['user_session'].sub_account_uin == "user1") &
                            (adapter.persistence.models['user_session'].session_id == sid)
                        ).execute()
        return wrapper
    return decorator

@with_test_data(generate_test_session("sess-test-1"))
def test_get_by_id_found(adapter: UserSessionAdapter, ctx: ChatContext):
    print(f"test_get_by_id_found {ctx}")
    result = adapter.get_by_id(ctx)
    assert isinstance(result, UserSession)
    assert result.app_id == "app_id_1"
    assert result.session_id == "sess-test-1"
    assert result.sub_account_uin == "user1"

@with_test_data()
def test_get_by_id_not_found(adapter, ctx):
    result = adapter.get_by_id(ctx)
    assert result is None

def test_create_success(adapter, ctx):
    test_session, test_ctx = generate_test_session("sess-test-2")
    try:
        assert adapter.create(ctx, test_session) is True
        # 再查一遍确认
        result = adapter.get_by_id(test_ctx)
        assert result is not None
        assert result.session_id == "sess-test-2"
        assert result.app_id == "app_id_1"
    finally:
        adapter.persistence.models['user_session'].delete().where(
            (adapter.persistence.models['user_session'].sub_account_uin == "user1") &
            (adapter.persistence.models['user_session'].session_id == "sess-test-2")
        ).execute()

@with_test_data(
    generate_test_session("sess-test-3"),
    generate_test_session("sess-test-4"),
    generate_test_session("sess-test-5")
)
def test_get_all_by_user(adapter, ctx):
    """测试获取用户所有会话"""
    result = adapter.get_all_by_user(ctx)
    assert isinstance(result, list)
    assert len(result) >= 3
    # 验证返回的会话都是属于该用户的
    for session in result:
        assert session.sub_account_uin == "user1"

def test_get_all_by_user_no_sessions(adapter, ctx):
    """测试获取没有会话的用户"""
    ctx.sub_account_uin = "user2xxxxxx"
    result = adapter.get_all_by_user(ctx)
    assert result == []

@with_test_data(generate_test_session("delete-test-1"))
def test_mark_as_deleted(adapter, ctx):
    """测试标记会话为已删除"""
    # 先验证会话存在
    session = adapter.get_by_id(ctx)
    assert session is not None
    
    # 标记为删除
    assert adapter.mark_as_deleted(ctx) is True
    
    # 验证会话已被标记为删除（get_by_id应该返回None）
    deleted_session = adapter.get_by_id(ctx)
    assert deleted_session is None

@with_test_data()
def test_mark_as_deleted_not_found(adapter, ctx):
    """测试标记不存在的会话为已删除"""
    result = adapter.mark_as_deleted(ctx)
    # 应该返回True，因为更新0行记录不算错误
    assert result is True

def test_create_duplicate_session(adapter, ctx):
    """测试创建重复会话"""
    test_session, test_ctx = generate_test_session("dup-test-1")
    try:
        # 第一次创建应该成功
        assert adapter.create(test_ctx, test_session) is True
        # 第二次创建相同会话应该失败（主键冲突）
        assert adapter.create(test_ctx, test_session) is False
    finally:
        adapter.persistence.models['user_session'].delete().where(
            (adapter.persistence.models['user_session'].sub_account_uin == "user1") &
            (adapter.persistence.models['user_session'].session_id == "dup-test-1")
        ).execute()

@with_test_data(
    generate_test_session("order-test-1"),
    generate_test_session("order-test-2"),
    generate_test_session("order-test-3")
)
def test_get_all_by_user_order(adapter, ctx):
    """测试获取用户会话的排序"""
    result = adapter.get_all_by_user(ctx)
    assert isinstance(result, list)
    assert len(result) >= 3
    
    # 验证按updated_at倒序排列
    updated_times = [session.updated_at for session in result if session.updated_at]
    if len(updated_times) > 1:
        assert updated_times == sorted(updated_times, reverse=True)

def test_create_with_different_user(adapter: UserSessionAdapter, ctx: ChatContext):
    """测试不同用户的会话"""
    test_session, test_ctx = generate_test_session("diff-user-test")
    test_ctx.sub_account_uin = "user2"
    test_session.sub_account_uin = "user2"
    try:
        assert adapter.create(test_ctx, test_session) is True
        # 验证只能查到user2的会话
        result = adapter.get_by_id(test_ctx)
        assert result is not None
        assert result.sub_account_uin == "user2"
        # 验证user1下没有这个会话
        test_ctx.sub_account_uin = "user1xxx"
        result_user1 = adapter.get_by_id(test_ctx)
        assert result_user1 is None
    finally:
        adapter.persistence.models['user_session'].delete().where(
            (adapter.persistence.models['user_session'].sub_account_uin == "user2") &
            (adapter.persistence.models['user_session'].session_id == "diff-user-test")
        ).execute() 