import pytest
import time
import peewee
from common.share.config import appConfig
from common.database.database import CustomPooledMySQLDatabase
from playhouse.pool import PooledMySQLDatabase
# Check DB config at import time and skip all tests if not configured
try:
    db_config = appConfig.common.metadata_db
    if not db_config.host:
        pytest.skip("DB config not found or incomplete, skipping adapter tests", allow_module_level=True)
except Exception as e:
    pytest.skip(f"DB config not found or failed to load: {str(e)}, skipping adapter tests", allow_module_level=True)

def get_pool_db(conf, pool_type):
    conf_dict = {
        "host": conf.host,
        "port": conf.port,
        "user": conf.user,
        "password": conf.password,
        "max_connections": 1,
        "timeout": conf.timeout,
        "stale_timeout": conf.stale_timeout
    }
    database = conf.database

    if pool_type == "org":
        return PooledMySQLDatabase(
            database,
            **conf_dict,
        )
    elif pool_type == "custom":
        return CustomPooledMySQLDatabase(
            database,
            **conf_dict,
        )
    else:
        raise ValueError(f"Invalid pool type: {pool_type}")

class TestPeeweeConnectionTimeout:
    """测试peewee的MySQL连接超时问题"""

    @pytest.mark.parametrize(
        "pool_db, exist_error",
        [
            (get_pool_db(db_config, "org"), True),
            (get_pool_db(db_config, "custom"), False),
        ]
    )
    def test_connection_timeout_with_pool_reuse(self, pool_db, exist_error):
        """测试连接池重用时的连接超时问题"""

            # 第一次使用连接
        with pool_db:
            cursor = pool_db.execute_sql("SELECT 1")
            cursor.fetchall()
            
            # 获取连接ID
            cursor = pool_db.execute_sql("SELECT CONNECTION_ID()")
            connection_id = cursor.fetchone()[0]
            print(f"Connection ID: {connection_id}")
        
        # 在连接返回池后kill它
        time.sleep(0.2)
        try:
            kill_cursor = pool_db.execute_sql(f"KILL {connection_id}")
            print(f"Killed connection {connection_id} after it was returned to pool")
        except Exception as e:
            print(f"Failed to kill connection: {e}")
        
        # 等待连接被kill
        time.sleep(0.2)

        if exist_error:
                    # 再次尝试使用连接池，应该会抛出超时异常
            with pytest.raises(peewee.OperationalError) as exc_info:
                with pool_db:
                    cursor = pool_db.execute_sql("SELECT 1")
                    cursor.fetchall()
            
            error_msg = str(exc_info.value)
            print(f"Caught exception on reused connection: {error_msg}")
            # 验证异常信息
            assert "MySQL server has gone away" in error_msg or "Lost connection" in error_msg
        else:
            with pool_db:
                cursor = pool_db.execute_sql("SELECT 1")
                assert cursor.fetchall()[0][0] == 1
            assert True
    
    @pytest.mark.parametrize(
        "pool_db, exist_error",
        [
            (get_pool_db(db_config, "org"), True),
            (get_pool_db(db_config, "custom"), False),
        ]
    )
    def test_connection_timeout_with_pool_reuse_in_transaction(self, pool_db, exist_error):
        """测试连接池重用时的连接超时问题"""

        # 第一次使用连接
        with pool_db.atomic():
            cursor = pool_db.execute_sql("SELECT 1")
            cursor.fetchall()
            
            # 获取连接ID
            cursor = pool_db.execute_sql("SELECT CONNECTION_ID()")
            connection_id = cursor.fetchone()[0]
            print(f"Connection ID: {connection_id}")
        
        # 在连接返回池后kill它
        time.sleep(0.2)
        try:
            kill_cursor = pool_db.execute_sql(f"KILL {connection_id}")
            print(f"Killed connection {connection_id} after it was returned to pool")
        except Exception as e:
            print(f"Failed to kill connection: {e}")
        
        # 等待连接被kill
        time.sleep(0.2)
        if exist_error:
                    # 再次尝试使用连接池，应该会抛出超时异常
            with pytest.raises(peewee.OperationalError) as exc_info:
                with pool_db.atomic():
                    cursor = pool_db.execute_sql("SELECT 1")
                    cursor.fetchall()
            
            error_msg = str(exc_info.value)
            print(f"Caught exception on reused connection: {error_msg}")
            # 验证异常信息
            assert "MySQL server has gone away" in error_msg or "Lost connection" in error_msg
        else:
            with pool_db.atomic():
                cursor = pool_db.execute_sql("SELECT 1")
                assert cursor.fetchall()[0][0] == 1
            assert True
