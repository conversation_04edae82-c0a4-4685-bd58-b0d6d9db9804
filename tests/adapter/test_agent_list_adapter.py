import pytest
from infra.adapter.agent_list_adapter import Agent<PERSON><PERSON><PERSON><PERSON>pter
from infra.domain.agent_list_entity import Agent<PERSON><PERSON>
from infra.domain.agent_list_port import Agent<PERSON>ist<PERSON><PERSON>
from common.share.context import Context
from datetime import datetime
from functools import wraps
from tests.adapter.adapter_basic import get_adapter

@pytest.fixture
def test_agents():
    """Fixture to provide test agents"""
    return [generate_test_agent(str(i)) for i in range(1, 3)]

@pytest.fixture
def adapter():
    return get_adapter(AgentListAdapter)

@pytest.fixture
def ctx():
    return Context()

def with_test_data(*test_agents, extra_cleanup_ids=None):
    """装饰器：用于测试数据的插入和清理
    Args:
        *test_agents: 要插入的测试数据列表
        extra_cleanup_ids: 额外需要清理的 agent_id 列表
    """
    def decorator(func):
        @wraps(func)
        def wrapper(adapter: AgentListPort, ctx, *args, **kwargs):
            try:
                # 插入测试数据
                for agent in test_agents:
                    adapter.insert(ctx, agent)
                return func(adapter, ctx, *args, **kwargs)
            finally:
                # 清理测试数据
                for agent in test_agents:
                    adapter.delete_by_id(ctx, agent.agent_id)
                # 清理额外的 id
                if extra_cleanup_ids:
                    for agent_id in extra_cleanup_ids:
                        adapter.delete_by_id(ctx, agent_id)
        return wrapper
    return decorator


def generate_test_agent(agent_id: str) -> AgentList:
    """Generate test agent data"""
    return AgentList(
        agent_id=agent_id,
        agent_name=f"TestAgent{agent_id}",
        agent_type="typeA",
        agent_version="v1",
        description=f"Test Description {agent_id}",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@with_test_data(generate_test_agent("da-test-1"))
def test_get_by_id_found(adapter: AgentListPort, ctx):
    # Test
    result = adapter.get_by_id(ctx, "da-test-1")
    assert isinstance(result, AgentList)
    assert result.agent_id == "da-test-1"
    assert result.agent_name == "TestAgentda-test-1"
    assert result.agent_type == "typeA"

@with_test_data()
def test_get_by_id_not_found(adapter: AgentListPort, ctx):
    result = adapter.get_by_id(ctx, 999)
    assert result is None


@with_test_data(generate_test_agent("da-test-1"))
def test_create_or_update_success(adapter: AgentListPort, ctx):
    test_agent = generate_test_agent("da-test-1")
    assert adapter.create_or_update(ctx, test_agent) is True

@with_test_data(extra_cleanup_ids=[999])
def test_create_or_update_fail(adapter: AgentListPort, ctx):
    test_agent = generate_test_agent("da-test-1")
    # 模拟失败情况：尝试插入重复的ID
    test_agent.agent_id = "999"  # 使用一个不存在的ID
    assert adapter.create_or_update(ctx, test_agent) is True  # 应该成功 