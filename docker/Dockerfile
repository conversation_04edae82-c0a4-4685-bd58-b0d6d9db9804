FROM python:3.12
LABEL authors="samuelzan"

ARG TAG
ENV IMAGE_TAG=${TAG}

RUN mkdir /usr/local/intellix/

ADD ./intellix-ds-agent /usr/local/intellix/intellix-ds-agent
<PERSON>NV PYTHONPATH /usr/local/intellix/intellix-ds-agent
WORKDIR /usr/local/intellix/intellix-ds-agent

RUN \
ln -fs /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
apt-get update && \
apt-get install -y vim && \
cp docker/start.sh ./start.sh && \
pip install -r requirements.txt  && \
echo "ok"

ENTRYPOINT ["./start.sh"]
