# build endpoint
set -xe

tag="$(git rev-parse --abbrev-ref HEAD)_$(date "+%Y%m%d")_$(git rev-parse --short HEAD)"
tag="${tag}"
echo "tag is $tag"

docker build --build-arg TAG=$tag -t ccr.ccs.tencentyun.com/tsf_100041873387/data-agent:$tag -f docker/Dockerfile ../
docker push ccr.ccs.tencentyun.com/tsf_100041873387/data-agent:$tag
echo "docker build ok: ccr.ccs.tencentyun.com/tsf_100041873387/data-agent:$tag"