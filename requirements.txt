# for endpoint
fastapi
uvicorn
ray==2.41.0

# for adapter
playhouse
peewee
pymysql
redis==6.2

# for memory
elasticsearch==8.18.1
mem0ai

# for common
aiohttp
pydantic
pydantic_settings
concurrent-log-handler

# for test
pytest
pytest-asyncio

# for agent
langchain
langchain_openai
openai
langgraph
langmem
langid
sqlparse

# for trace
opentelemetry-sdk
opentelemetry-exporter-otlp

# for tencentcloud
tencentcloud-sdk-python
requests
cos-python-sdk-v5
# opentelemetry-instrumentation-elasticsearch #自动tracees操作
# opentelemetry-instrumentation-mysql #自动tracees操作
# traceloop-sdk # 这个依赖比较多，后面可以优化安装依赖

# for metrics
prometheus_client

# for nl2sql
datasketch
mcp
mcp[cli]
sqlglot
langdetect

# for jupyter
websocket-client
nbformat
ipython