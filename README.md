## 仓库说明

## 开发说明

#### log 
```
from common.logger.logger import logger
logger.info("Importing logger")
```
#### env
```
from common.share import env
print(env.EXECUTION_MODE)
```
#### err
```
from common.share import error
print(error.ErrParam)
```
#### context
```
from common.share import context
context.Context

for fastapi
    def chat(chat_config: ChatConfig, request: Request, response: Response)
        ctx = request.state.context
        logger.info(f"Got chat_context from request.state: {ctx}")

```
#### metrics
```
from common.metric.enpoints import record_request

record_request("url", "200", "usrid")
```

#### trace
```
from common.trace.trace import traceable

@traceable()
def func(xxx) -> xxx
   ...func body

require envs
export OTEL_EXPORTER_OTLP_ENDPOINT=xxx
export OTEL_EXPORTER_OTLP_HEADERS=xxx
export LANGSMITH_TRACING=true
export LANGSMITH_OTEL_ONLY=true
export LANGSMITH_OTEL_ENABLED=true
export MEM0_TELEMETRY=False
```


## 目录结构
```
python版本：python3.12
目录结构划分：
common      # 通用库
├── log
├── metric  
infra/      # 基础业务逻辑模块
├── server/
├── endpoint/
├── agent/
tests/      # 单元测试用例
```

## 其他操作
注册镜像地址帐户名密码：https://console.cloud.tencent.com/tsf/product-image-docker-detail?rid=1&repoName=tsf_100041873387%2Fdata-agent&tab=image

然后使用 docker login xxx

## 构建方法
```
    sh docker/build.sh
```