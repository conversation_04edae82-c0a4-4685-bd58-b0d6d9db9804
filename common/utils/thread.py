import threading
from typing import Dict, Any, Optional, TypeVar, Generic
from datetime import datetime

K = TypeVar('K')
V = TypeVar('V')

class ThreadSafeMap(Generic[K, V]):
    """线程安全的映射类"""
    
    def __init__(self):
        self._data: Dict[K, V] = {}
        self._lock = threading.RLock()  # 使用可重入锁
    
    def __getitem__(self, key: K) -> V:
        """获取值"""
        with self._lock:
            return self._data[key]
    
    def __setitem__(self, key: K, value: V) -> None:
        """设置值"""
        with self._lock:
            self._data[key] = value
    
    def __delitem__(self, key: K) -> None:
        """删除键值对"""
        with self._lock:
            del self._data[key]
    
    def __contains__(self, key: K) -> bool:
        """检查键是否存在"""
        with self._lock:
            return key in self._data
    
    def __len__(self) -> int:
        """获取长度"""
        with self._lock:
            return len(self._data)
    
    def get(self, key: K, default: Optional[V] = None) -> Optional[V]:
        """安全获取值，如果键不存在返回默认值"""
        with self._lock:
            return self._data.get(key, default)
    
    def pop(self, key: K, default: Optional[V] = None) -> Optional[V]:
        """弹出值"""
        with self._lock:
            return self._data.pop(key, default)
    
    def items(self):
        """获取所有键值对（返回副本）"""
        with self._lock:
            return list(self._data.items())
    
    def keys(self):
        """获取所有键（返回副本）"""
        with self._lock:
            return list(self._data.keys())
    
    def values(self):
        """获取所有值（返回副本）"""
        with self._lock:
            return list(self._data.values())
    
    def clear(self) -> None:
        """清空映射"""
        with self._lock:
            self._data.clear()
    
    def update(self, other: Dict[K, V]) -> None:
        """更新映射"""
        with self._lock:
            self._data.update(other)
    
    def setdefault(self, key: K, default: V) -> V:
        """设置默认值"""
        with self._lock:
            return self._data.setdefault(key, default)
    
    def copy(self) -> Dict[K, V]:
        """返回副本"""
        with self._lock:
            return self._data.copy()
    
    def increment_counter(self, key: str, amount: int = 1) -> int:
        """增加字符串键的计数器值"""
        with self._lock:
            if key not in self._data:
                self._data[key] = 0
            self._data[key] += amount
            return self._data[key]
    
    def decrement_counter(self, key: str, amount: int = 1) -> int:
        """减少字符串键的计数器值"""
        with self._lock:
            if key not in self._data:
                self._data[key] = 0
            self._data[key] -= amount
            return self._data[key]
    
    def get_counter(self, key: str, default: int = 0) -> int:
        """获取字符串键的计数器值"""
        with self._lock:
            return self._data.get(key, default)
    
    def set_counter(self, key: str, value: int) -> None:
        """设置字符串键的计数器值"""
        with self._lock:
            self._data[key] = value


class ThreadSafeCounter:
    """线程安全的计数器"""
    
    def __init__(self, initial_value: int = 0):
        self._value = initial_value
        self._lock = threading.Lock()
    
    def increment(self, amount: int = 1) -> int:
        """增加计数"""
        with self._lock:
            self._value += amount
            return self._value
    
    def decrement(self, amount: int = 1) -> int:
        """减少计数"""
        with self._lock:
            self._value -= amount
            return self._value
    
    def get(self) -> int:
        """获取当前值"""
        with self._lock:
            return self._value
    
    def set(self, value: int) -> None:
        """设置值"""
        with self._lock:
            self._value = value
    
    def __int__(self) -> int:
        return self.get()
    
    def __str__(self) -> str:
        return str(self.get())

class ThreadSafeInt:
    """线程安全的整数"""
    
    def __init__(self, initial_value: int = 0):
        self._value = initial_value
        self._lock = threading.Lock()

    def increment(self, amount: int = 1) -> int:
        """增加计数"""
        with self._lock:
            self._value += amount
            return self._value

    def decrement(self, amount: int = 1) -> int:
        """减少计数"""
        with self._lock:
            self._value -= amount
            return self._value

    def get(self) -> int:
        """获取当前值"""
        with self._lock:
            return self._value


class ThreadSafeSet:
    """线程安全的集合"""
    
    def __init__(self):
        self._data = set()
        self._lock = threading.RLock()
    
    def add(self, item: Any) -> None:
        """添加元素"""
        with self._lock:
            self._data.add(item)
    
    def remove(self, item: Any) -> None:
        """移除元素"""
        with self._lock:
            self._data.remove(item)
    
    def discard(self, item: Any) -> None:
        """安全移除元素（如果不存在不报错）"""
        with self._lock:
            self._data.discard(item)
    
    def __contains__(self, item: Any) -> bool:
        """检查元素是否存在"""
        with self._lock:
            return item in self._data
    
    def __len__(self) -> int:
        """获取长度"""
        with self._lock:
            return len(self._data)
    
    def clear(self) -> None:
        """清空集合"""
        with self._lock:
            self._data.clear()
    
    def copy(self) -> set:
        """返回副本"""
        with self._lock:
            return self._data.copy()
    
    def union(self, other: set) -> set:
        """并集"""
        with self._lock:
            return self._data.union(other)
    
    def intersection(self, other: set) -> set:
        """交集"""
        with self._lock:
            return self._data.intersection(other) 
