# datascience_multi_agent/utils/openai_client.py
from openai import OpenAI, APIError, AsyncOpenAI
from openai.types.chat import ChatCompletionMessage, ChatCompletionChunk
from typing import List, Dict, Any, Union, Generator, Optional, AsyncGenerator, AsyncIterator
from common.logger.logger import logger
from common.share.config import appConfig
import asyncio
import json

class OpenAIClient:
    def __init__(self, api_key: str = None, base_url: str = None, model_name: str = None, temperature: float = 0.3):
        try:
            self.client = AsyncOpenAI(
                api_key=api_key or appConfig.common.llm.api_key,
                base_url=base_url or appConfig.common.llm.base_url,
            )
            self.model_name = model_name or appConfig.common.llm.model_name
            self.temperature = temperature or appConfig.common.llm.temperature
        except APIError as e:
            logger.error(f"OpenAI client initialization failed: {e}. Ensure API key and base URL are correct.")
            raise ValueError(f"OpenAI client initialization failed: {e}") from e
            
        logger.info(f"🤖 OpenAIClient initialized with model: {self.model_name}, base_url: {base_url}, temperature: {self.temperature}")

    async def generate(self, messages: List[Dict[str, Any]], tools: Optional[List[Dict[str, Any]]] = None, tool_choice: Union[str, Dict[str, Any]] = "auto", model_name: Optional[str] = None) -> ChatCompletionMessage: #
        """
        Receives a list of messages and calls the OpenAI API to get a response (non-streaming).
        Supports tools (function calling).
        An optional 'model' parameter can override the instance's default model.
        """
        try:
            current_model = model_name if model_name else self.model_name
            logger.info(f"🤖 OpenAIClient.generate calling API with model: {current_model}")
            api_params: Dict[str, Any] = {
                "model": current_model,
                "messages": messages,
                "temperature": self.temperature,
                "max_tokens": 2000, # 您可能需要根据工具调用的复杂性调整
            }
            if tools:
                api_params["tools"] = tools
                api_params["tool_choice"] = tool_choice 
            
            response = await self.client.chat.completions.create(**api_params)
            if response.choices:
                # 🎯 修复：返回完整的message对象，而不是只返回content
                # 这样可以保留tool_calls信息，确保function calling正常工作
                message = response.choices[0].message
                logger.debug(f"🤖 LLM response - content: {bool(message.content)}, tool_calls: {bool(message.tool_calls)}")
                return message
            else:
                logger.error("API call successful but no choices returned.")
                raise ValueError("API call returned no choices.")
        except APIError as e:
            logger.error(f"OpenAI API call error in generate: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in generate: {e}")
            raise

    # datascience_multi_agent/utils/openai_client.py (修改 generate_stream 方法)
    async def generate_stream(self, messages: List[Dict[str, Any]], tools: Optional[List[Dict[str, Any]]] = None, tool_choice: Union[str, Dict[str, Any]] = "auto", model_name: Optional[str] = None) -> AsyncIterator[ChatCompletionChunk]:
        """
        异步流式获取LLM响应，支持区分reasoning_content和content部分。
        返回一个异步迭代器，yield各个响应块。
        """
        try:
            current_model = model_name if model_name else self.model_name
            logger.info(f"🤖 OpenAIClient.generate_stream calling API with model: {current_model}")
            api_params: Dict[str, Any] = {
                "model": current_model,
                "messages": messages,
                "temperature": self.temperature,
                "max_tokens": 2000,
                "stream": True,
            }
            
            
            if tools:
                api_params["tools"] = tools
                api_params["tool_choice"] = tool_choice

            # 创建流式响应
            stream = await self.client.chat.completions.create(**api_params)
            
            # 转换为异步流
            async for chunk in stream:
                # 为了确保是真正的异步流，添加一个小的延迟
                # await asyncio.sleep(0.01)
                yield chunk

        except APIError as e:
            logger.error(f"OpenAI API streaming call error: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in generate_stream: {e}")
            raise

if __name__ == "__main__":
    client = OpenAIClient(
        api_key="",
        base_url="https://api.lkeap.cloud.tencent.com/v1",
        model_name="deepseek-v3",
        temperature=0.3
    )
    async def generate_stream():
        rst = await client.generate([{"role": "user", "content": "Hello, how are you?"}])
        print("chat completion: ", rst)
        async for chunk in client.generate_stream([{"role": "user", "content": "Hello, how are you?"}]):
            print("chunk: ", chunk)
    asyncio.run(generate_stream())