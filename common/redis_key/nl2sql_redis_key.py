nl2sql_sampling_data_key = 'nl2sql:sample:v1:{app_id}:{sub_account_uin}:{engine_type}:{ds_name}:{db}:{tbl}'


def format_redis_key(template: str, **kwargs) -> str:
    """
    该函数可通用处理所有符合以下条件的Redis键模板：
    1. 使用冒号分隔符
    2. 参数命名符合Python格式规范
    3. 不需要保留空占位符

    典型兼容场景：
    ✅ 模板：cache:{user}:{item}:{version}
       参数：user="", item=123 → cache:none:123:none
    ✅ 模板：session:{app}:{device}
       参数：app="mobile" → session:mobile:none
    ✅ 模板：global:config

    不兼容场景：
    ❌ 需要保留空占位符（如 cache::123）
    ❌ 使用非冒号分隔符（如 cache-{user}-{item}）
    ❌ 参数包含特殊字符（如 {user-id}）
    """
    params = {k: (v if v not in (None, "") else "none") for k, v in kwargs.items()}
    key = template.format(**params)
    return key.replace('::', ':').strip(':')