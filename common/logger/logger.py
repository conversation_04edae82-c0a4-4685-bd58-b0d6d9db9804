import logging
import os

from concurrent_log_handler import ConcurrentRotatingFileHandler
from logging import LoggerAdapter
from common.share import env


class SafeFormatter(logging.Formatter):
    def format(self, record):
        if not hasattr(record, 'subuin'):
            record.subuin = "None"
        if not hasattr(record, "session_id"):
            record.session_id = "None"
        if not hasattr(record, "trace_id"):
            record.trace_id = "None"
        if not hasattr(record, "model_name"):
            record.model_name = "None"
        return super().format(record)


def basic_logger():
    log_dir = os.getenv("LOG_PATH", "./logs")
    os.makedirs(log_dir, exist_ok=True)
    logfmt = (
        "%(asctime)s::%(levelname)s::"
        "%(filename)s::%(funcName)s::%(lineno)d::"
        "%(subuin)s::%(session_id)s::%(trace_id)s::%(model_name)s::"
        "%(message)s"
    )

    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    handlers = [
        ConcurrentRotatingFileHandler(
            f"{log_dir}/data_agent.log",
            maxBytes=30 * 1024 * 1024,
            backupCount=100,
        )
    ]
    if env.LOGGER_TO_STDOUT:
        handlers.append(logging.StreamHandler())

    formatter = SafeFormatter(
        fmt=logfmt,
        datefmt="%Y-%m-%d %H:%M:%S"
    )

    for handler in handlers:
        handler.setFormatter(formatter)

    root_logger = logging.getLogger()

    for h in root_logger.handlers[:]:
        root_logger.removeHandler(h)

    for handler in handlers:
        root_logger.addHandler(handler)

    root_logger.setLevel(getattr(logging, log_level, logging.DEBUG))
    return root_logger


class LogRecord(logging.LogRecord):
    def __init__(self, *args, **kwds):
        super().__init__(*args, **kwds)
        self.ctx = getattr(self, "ctx", "")
logging.setLogRecordFactory(LogRecord)


def get_logger(name=None, extra=None):
    if name is None:
        logger = logging.getLogger()
        logger.setLevel(level=logging.DEBUG)
        return logger
    if name == "ray":
        logger = logging.getLogger("ray")
        logger.setLevel(level=logging.DEBUG)
        return logger
    logger = logging.getLogger(name)
    logger.setLevel(level=logging.DEBUG)
    return logger


def get_trace_logger(ctx, model_name=None):
    base_logger = logging.getLogger()
    return LoggerAdapter(base_logger, {
                "subuin": ctx.sub_account_uin,
                "session_id": ctx.session_id,
                "trace_id": ctx.trace_id,
                "model_name": model_name
                }
             )

def get_logger_with_ctx(sub_account_uin: str = "none", session_id: str = "none", trace_id: str = "none", model_name: str = "none"):
    return LoggerAdapter(
            logging.getLogger(), 
            {
            "subuin": sub_account_uin,
            "session_id": session_id,
            "trace_id": trace_id,
            "model_name": model_name
            }
        )

trpc_logger = get_logger("trpc")
ray_logger = get_logger("ray")
tsap_logger = ray_logger
logger = basic_logger()











