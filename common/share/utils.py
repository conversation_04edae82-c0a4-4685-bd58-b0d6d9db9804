import langdetect
from langdetect import detect, LangDetectException
from common.logger.logger import logger
import re

lang_map = {
    # Asian Languages
    "zh-cn": "Chinese",
    "zh": "Chinese",
    "zh-tw": "Chinese",
    "ko": "Chinese",
    # European Languages
    "en": "English",
}


def detect_language(text):
    if re.search('[\u4e00-\u9fff]', text):
        return "Chinese"
    try:
        lang = detect(text)
        return lang_map.get(lang, "English")
    except LangDetectException as e:
        logger.error(f"detect_language error: {e}", exc_info=True)
        return "English"
