# 通用加载配置文件功能
# 1. 支持YAML配置文件，优先加载 yaml配置，yaml中不通过${x}设置环境变量
# 2.yaml配置文件中不存在的值，则从环境变量中加载
# 3.环境变量中不存在的值，则从默认值中加载 环境变量映射关系 COMMON__METADATA_DB__DB_METADATA_USER -> config.common.metadata_db.db_metadata_user
# 4.对于一些必需字段，Field(..) 声明必要，获取不到值则报错
import os
from pydantic import Field, PositiveInt, BaseModel, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict, YamlConfigSettingsSource, PydanticBaseSettingsSource
from typing import Dict, Optional

from common.share import env


# ------------------- 子模型定义 -------------------
class MetadataDBConfig(BaseModel):
    host: Optional[str] = Field("localhost", description="元数据库主机地址")
    port: Optional[PositiveInt] = Field(3306, description="元数据库端口号")
    user: Optional[str] = Field("root", description="数据库用户名")
    password: Optional[str] = Field("", description="数据库密码")
    database: Optional[str] = Field("data_agent", description="数据库名称")
    max_connections: Optional[PositiveInt] = Field(50, description="数据库最大连接数")
    timeout: Optional[PositiveInt] = Field(10, description="数据库连接超时时间")
    stale_timeout: Optional[PositiveInt] = Field(300, description="设置连接超过300秒未使用将被重新连接")

class TraceConfig(BaseModel):
    otel_endpoint: Optional[str] = Field(None, description="OTEL 端点")
    otel_token: Optional[str] = Field(None, description="OTEL 令牌")

class MetricConfig(BaseModel):
    addr: Optional[str] = Field(None, description="Metric 地址")
    port: Optional[PositiveInt] = Field(9090, description="Metric 端口")
    push_gateway: Optional[str] = Field(None, description="Pushgateway 地址")


class ObserveConfig(BaseModel):
    trace: Optional[TraceConfig] = Field(TraceConfig(), description="OTEL 配置")
    metric: Optional[MetricConfig] = Field(MetricConfig(), description="Metric 配置")


class LLMConfig(BaseModel):
    api_key: str = Field("", description="LLM API Key")
    base_url: str = Field("", description="LLM API 地址")
    model_name: str = Field("", description="大模型名称")
    temperature: float = Field(0.7, ge=0.0, le=2.0, description="生成温度")

class HunYuanConfig(BaseModel):
    api_key: str = Field("", description="混元 API Key")
    base_url: str = Field("", description="混元 API 地址")

class LLMEmbeddingConfig(BaseModel):
    api_key: str = Field("", description="嵌入模型API Key")
    base_url: str = Field("", description="嵌入模型API 地址")
    model_name: str = Field("hunyuan-embedding", description="嵌入模型名称")
    model_dims: int = Field(1024, description="嵌入模型维度")

class TencentCloudConfig(BaseModel):
    secret_id: str = Field("", description="腾讯云 Secret ID")
    secret_key: str = Field("", description="腾讯云 Secret Key")
    region: str = Field("ap-guangzhou", description="腾讯云 API 区域")
    endpoint: str = Field("tms.tencentcloudapi.com", description="腾讯云 API 端点")


class TimeOutConfig(BaseModel):
    chat_time_out: int = Field(60 * 15, description="流式响应超时时间")
    chat_heart_time_out: int = Field(30, description="返回心跳超时时间")
    actor_time_out: int = Field(1800, description="返回心跳超时时间")


class RayConfig(BaseModel):
    ray_address: str = Field("127.0.0.1:8265", description="ray地址")
    dependencies: list[str] = Field(
        default_factory=lambda: [
            "playhouse",
            "peewee",
            "pymysql",
            "concurrent-log-handler",
            "langid",
            "tencentcloud-sdk-python",
            "requests",
            "cos-python-sdk-v5",
            # "traceloop-sdk",
            "opentelemetry-sdk",
            "opentelemetry-exporter-otlp",
            "fastapi",
            "elasticsearch==8.18.1",
            "mem0ai",
            "aiohttp",
            "pydantic",
            "pydantic_settings",
            "langchain",
            "langchain_openai",
            "openai",
            "redis",
            "langgraph",
            "datasketch",
            "mcp[cli]",
            "sqlglot",
            "langdetect",
            "websocket-client",
            "sqlparse",
            "ipython",
            "nbformat",
        ], description="Ray运行时需要的Python依赖包列表"
    )


class RedisConfig(BaseModel):
    host: str = Field("", description="redis host")
    port: int = Field(6379, description="redis port")
    password: str = Field("", description="redis password")
    max_connections: int = Field(50, description="redis max connections")
    timeout: int = Field(10, description="redis timeout")


class ESConfig(BaseModel):
    host: str = Field("", description="es地址")
    port: int = Field(9200, description="es端口")
    user: str = Field("elastic", description="es用户名")
    password: str = Field("", description="es密码")
    index_name: str = Field("chat_records", description="es索引名称")
    timeout: int = Field(10, description="es超时时间")
    max_retries: int = Field(3, description="es重试次数")


class RuleConfig(BaseModel):
    enable: bool = Field(False, description="是否启用规则")
    mod: Optional[str] = Field(None, description="模式，例如 local 或 remote")
    disable_classifier: Optional[bool] = Field(None, description="是否禁用分类器")
    disable_llm: Optional[bool] = Field(None, description="是否禁用LLM")
    threshold: Optional[float] = Field(None, description="阈值")


class GuardrailConfig(BaseModel):
    endpoint: str = Field("http://lb-2ohhzn5i-egfmcuxsj56dh1op.clb.cq-tencentclb.com", description="Guardrails公网地址")
    tencent_tsec: RuleConfig = Field(default_factory=lambda: RuleConfig(enable=False), description="腾讯云文本安全检测规则配置")
    sensitive_topics: RuleConfig = Field(default_factory=lambda: RuleConfig(enable=False),
                                         description="敏感话题规则配置")
    jailbreak: RuleConfig = Field(default_factory=lambda: RuleConfig(enable=False),
                                  description="Jailbreak 检测规则配置")
    toxicity: RuleConfig = Field(default_factory=lambda: RuleConfig(enable=False), description="Toxicity 检测规则配置")
    pii_detection: RuleConfig = Field(default_factory=lambda: RuleConfig(enable=False), description="PII 检测规则配置")
    xinan_detect: RuleConfig = Field(default_factory=lambda: RuleConfig(enable=False), description="信安规则配置")
    xinan_detect_input_appid: int = Field(8120700, description="信安检测输入应用ID")
    xinan_detect_output_appid: int = Field(8120701, description="信安检测输出应用ID")
    xinan_detect_input_url: str = Field("https://imsg.is.tencent-cloud.net/interface/request/json", description="信安检测输入URL")

    class Config:
        extra = "allow"  # 允许额外字段


class CodegenMetricsConfig(BaseModel):
    enabled: bool = Field(True, description="是否启用codegen指标追踪")
    data_dir: str = Field("metrics/codegen", description="指标数据存储目录")
    k_values: list[int] = Field([1, 2, 3, 5], description="用于计算功能正确性和执行成功率的k值列表")
    rotation_days: int = Field(30, description="数据保留天数")
    track_token_usage: bool = Field(True, description="是否追踪token使用量")


class ReplayMetricsConfig(BaseModel):
    enabled: bool = Field(True, description="是否启用复现数据收集")
    data_dir: str = Field("replay_data", description="复现数据存储目录")
    sampling_rate: float = Field(1.0, ge=0.0, le=1.0, description="数据采样率 (0.0-1.0)")
    save_actual_data: bool = Field(False, description="是否保存实际查询数据（隐私考虑）")
    rotation_days: int = Field(30, description="数据保留天数")
    max_file_size_mb: int = Field(100, description="单个数据文件最大大小(MB)")


class MetricsConfig(BaseModel):
    codegen: CodegenMetricsConfig = CodegenMetricsConfig()
    replay: ReplayMetricsConfig = ReplayMetricsConfig()


class MemoryConfig(BaseModel):
    es: ESConfig = ESConfig()
    vector_store: ESConfig = ESConfig()
    llm: Optional[LLMConfig] = Field(None, description="[DEPRECATED] 请使用 CommonConfig.llm 配置")
    embedding: Optional[LLMEmbeddingConfig] = Field(None,
                                                    description="[DEPRECATED] 请使用 CommonConfig.llm_embedding 配置")

class NL2SQLConfig(BaseModel):
    llm: LLMConfig = LLMConfig()
    embedding: LLMEmbeddingConfig = LLMEmbeddingConfig()
    es: ESConfig = ESConfig()

class GenADConfig(BaseModel):
    llm: LLMConfig = LLMConfig()
    hunyuan: HunYuanConfig = HunYuanConfig()

class AccessInfo(BaseModel):
    secret_id: str = Field("", description="腾讯云 Secret ID")
    secret_key: str = Field("", description="腾讯云 Secret Key")
    endpoint: str = Field("", description="访问站点")
    service: str = Field("", description="访问业务")
    region: str = Field("", description="访问地域")
    api_version: str = Field("", description="访问版本")


class CosInfo(BaseModel):
    secret_id: str = Field("", description="腾讯云 Secret ID")
    secret_key: str = Field("", description="腾讯云 Secret Key")
    region: str = Field("", description="访问地域")
    bucket: str = Field("", description="访问桶")
    prefix: str = Field("", description="访问前缀")

class KnowledgeBase(BaseModel):
    max_workers: int = Field(10, description="线程池最大工作线程数")
    max_concurrent_tasks: int = Field(10, description="最大并发任务数")
    scan_interval: int = Field(3, description="扫描间隔（秒）")
    lock_name: str = Field("task_processor_global_lock", description="分布式锁名称")
    lock_timeout: int = Field(30, description="分布式锁超时时间（秒）")
    task_timeout: int = Field(3600, description="任务超时时间（秒）")
    heartbeat_interval: int = Field(30, description="心跳间隔（秒）")
    heartbeat_timeout: int = Field(90, description="心跳超时时间（秒）")
    orphaned_check_interval: int = Field(60, description="离线检查间隔（秒）")
    embedding_works: int = Field(2, description="embedding线程数")
    es_write_works: int = Field(6, description="ES写入线程数")

class AISearchConfig(BaseModel):
    es: ESConfig = ESConfig()
    access_info: AccessInfo = AccessInfo()
    cos_info: CosInfo = CosInfo()
    knowledge_base: KnowledgeBase = KnowledgeBase()


class MCPExampleConfig(BaseModel):
    example: dict = Field({}, description="Dictionary to store key-value pairs")


class AtomicConfig(BaseModel):
    nl2sql: NL2SQLConfig = NL2SQLConfig()
    aisearch: AISearchConfig = AISearchConfig()
    mcp: Optional[MCPExampleConfig] = MCPExampleConfig()
    gen_ad: GenADConfig = GenADConfig()

class CountLimitConfig(BaseModel):
    user_count_limit: int = Field(10000, description="主账号会话条数限制")
    sub_user_count_limit: int = Field(1000, description="子账号会话条数限制")
    sub_user_count_session_limit: int = Field(100, description="会话条数限制")


class CommonConfig(BaseModel):
    metadata_db: MetadataDBConfig = MetadataDBConfig()
    llm: LLMConfig = LLMConfig()
    llm_embedding: LLMEmbeddingConfig = LLMEmbeddingConfig()
    tencent_cloud: TencentCloudConfig = TencentCloudConfig()
    time_out: TimeOutConfig = TimeOutConfig()
    ray_config: RayConfig = RayConfig()
    redis: RedisConfig = RedisConfig()
    count_limit: CountLimitConfig = CountLimitConfig()


# ------------------- 主配置类 -------------------
class AppConfig(BaseSettings):
    common: CommonConfig = CommonConfig()
    memory: MemoryConfig = MemoryConfig()
    automic: AtomicConfig = AtomicConfig()
    observe: ObserveConfig = ObserveConfig()
    guardrails: GuardrailConfig = GuardrailConfig()
    metrics: MetricsConfig = MetricsConfig()

    model_config = SettingsConfigDict(
        arbitrary_types_allowed=True,
        env_nested_delimiter="__",
        case_sensitive=False,
        env_file=".env",
    )

    @classmethod
    def settings_customise_sources(
            cls,
            settings_cls: type[BaseSettings],
            init_settings: PydanticBaseSettingsSource,
            env_settings: PydanticBaseSettingsSource,
            dotenv_settings: PydanticBaseSettingsSource,
            file_secret_settings: PydanticBaseSettingsSource,
    ) -> tuple[PydanticBaseSettingsSource, ...]:
        """仅加载 YAML 配置文件，文件不存在时报错"""
        if not os.path.exists(env.CONFIG_PATH):
            raise FileNotFoundError(f"配置文件不存在: {env.CONFIG_PATH}，请检查配置路径和文件是否正确！")
        return (
            YamlConfigSettingsSource(settings_cls, env.CONFIG_PATH, "utf-8"),
        )

    @model_validator(mode='after')
    def model_fallbacks(self) -> 'AppConfig':
        # MemoryConfig fallbacks
        if not self.memory.llm or not self.memory.llm.api_key:
            self.memory.llm = self.common.llm
        if not self.memory.embedding or not self.memory.embedding.api_key:
            self.memory.embedding = self.common.llm_embedding

        # NL2SQLConfig fallbacks
        if not self.automic.nl2sql.llm or not self.automic.nl2sql.llm.api_key:
            self.automic.nl2sql.llm = self.common.llm
        if not self.automic.nl2sql.embedding or not self.automic.nl2sql.embedding.api_key:
            self.automic.nl2sql.embedding = self.common.llm_embedding
        return self

appConfig = AppConfig()

# ------------------- 验证加载 -------------------
if __name__ == "__main__":
    try:
        print("[DEBUG] 当前环境变量:", env.CONFIG_PATH)
        print("\n[SUCCESS] 配置加载成功！")
        print(f"appconfig: {appConfig}")
    except Exception as e:
        print(f"\n[ERROR] 配置加载失败: {e}")
