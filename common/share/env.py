import os

def get_env(key, default=None):
    return os.getenv(key, default)

def get_env_bool(key, default="False"):
    return str(os.getenv(key, default)).lower() == "true"

# sevice common
PRODUCT_NAME = get_env("PRODUCT_NAME", "intellix")
SERVICE_NAME = get_env("SERVICE_NAME", "data-agent")
MODULE_ENDPOINT = "endpoint"
MODULE_MEMORY = "memory"
MODULE_TRACE = "trace"


# runtime config
CFS_PATH = get_env("CFS_PATH", "/ray/")
EXECUTION_MODE = get_env("EXECUTION_MODE", "ray").lower()
EXECUTION_MODE_RAY = EXECUTION_MODE == "ray"
INSTANCE_ID = get_env("INSTANCE_ID", os.uname().nodename)
IMAGE_TAG = get_env("IMAGE_TAG", "")
START_AISEARCH_TASK = get_env_bool("START_AISEARCH_TASK", "FALSE")

# logger config
LOG_LEVEL = get_env("LOG_LEVEL", "INFO")
LOGGER_TO_STDOUT = get_env_bool("LOGGER_TO_STDOUT", False)

# config and init sql path
CONFIG_PATH = get_env("CONF_PATH", "etc/config.yaml")
INIT_SQL_PATH = get_env("INIT_SQL_PATH", "etc/init.sql")


IS_LOG_LEVEL_TRACE = LOG_LEVEL == "TRACE"
DUMP_TO_NOTEBOOK = get_env_bool("DUMP_TO_NOTEBOOK", False)