from enum import Enum

class MCPEngineTypeEnum(Enum):
    """定义支持的MCP引擎类型枚举

    枚举成员:
        DLC: 数据湖计算引擎（Data Lake Compute）
        ES: Elasticsearch（分布式搜索引擎）
        TCHouseC: 腾讯云数据库（Tencent Cloud House C）
        TCHouseX: 腾讯云数据库（Tencent Cloud House X）
        TCHouseD: 腾讯云数据库（Tencent Cloud House D）
    """
    DLC = "DLC"
    ES = "ES"
    TCHouseD = "TCHouseD"
    TCHouseC = "TCHouseC"
    TCHouseX = "TCHouseX"

if __name__ == "__main__":
    print("TCHouse" in MCPEngineTypeEnum)