import datetime
class Context:
    def __init__(self, data: dict = None):
        self.data = data if data else {}

    def set(self, key, value):
        self.data[key] = value

    def get(self, key, default=None):
        return self.data.get(key, default)

    def clear(self):
        self.data.clear()
        
class ChatContext(Context):
    # chat context is a context for a chat session
    def __init__(self, input: dict = {}, header: dict = {}):
        super().__init__({
            "input": input,
        })
        self.path = ""
        self.sub_account_uin = header.get('SubAccountUin', 'none')
        self.session_id = input.get('SessionId', 'none')
        self.trace_id = header.get('TraceId', 'none')
        self.app_id = header.get('AppId', 'none')
        self.uin = header.get('Uin', 'none')
        self.apm_trace_id = input.get('ApmTraceId', 0)
        self.start_time = input.get('StartTime', None)
        self.end_time = input.get('EndTime', None)
        self.deep_thinking = input.get('DeepThinking', True)
        self.parent_span = None
        if self.trace_id is not None and isinstance(self.trace_id, int):
            self.apm_trace_id = self.trace_id
        if len(self.app_id) == 0:
            self.app_id = self.sub_account_uin

    def get_sub_account_uin(self):
        return self.sub_account_uin

    def get_session_id(self):
        return self.session_id

    def get_trace_id(self):
        return self.trace_id
    
    def get_path(self):
        return self.path
    
    def set_path(self, path: str):
        self.path = path

    def get_apm_trace_id(self):
        return self.apm_trace_id
    
    def set_apm_trace_id(self, apm_trace_id: int):
        self.apm_trace_id = apm_trace_id

    def set_parent_span(self, span):
        self.parent_span = span

    def set_start_time(self, start_time: datetime.datetime):
        self.start_time = start_time
    
    def get_cost_seconds(self,current_time: datetime.datetime):
        return (current_time - self.start_time).total_seconds()

    def set_end_time(self, end_time: datetime.datetime):
        self.end_time = end_time

    def get_deep_thinking(self) -> bool:
        return self.deep_thinking

    def set_deep_thinking(self, deep_thinking: bool) -> None:
        self.deep_thinking = deep_thinking

    def get_app_id(self) -> str:
        return self.app_id

    def set_appid(self, app_id: str) -> None:
        self.app_id = app_id

    def get_uin(self) -> str:
        return self.uin

    def set_uin(self, uin: str) -> None:
        self.uin = uin

    def __str__(self) -> str:
        base_info = f"ChatContext(path={self.path}, sub_account_uin={self.sub_account_uin}, session_id={self.session_id}, trace_id={self.trace_id}, appid={self.app_id}, uin={self.uin}, apm_trace_id=0x{format(self.get_apm_trace_id(), '032x')}, start_time={self.start_time}, end_time={self.end_time}, deep_thinking={self.deep_thinking})"
        dynamic_info = ", ".join(f"{k}={v}" for k, v in self.data.items())
        return f"{base_info}, dynamic_data=[{dynamic_info}]"


context = Context()
