import io
import json
import os
import time
import uuid
import zipfile
from datetime import datetime, timezone
from typing import List
from urllib.parse import urlparse

import requests
from langchain_core.documents import Document
from langchain_text_splitters import MarkdownHeaderTextSplitter, Recursive<PERSON>haracterTextSplitter
from pydantic import BaseModel
from tencentcloud.common import credential
from tencentcloud.common.common_client import CommonClient
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile

from common.logger.logger import logger
from common.share.config import appConfig
from common.tencentcloud.cos import get_presigned_url

access = appConfig.automic.aisearch.access_info

TENCENT_CLOUD_SECRET_ID = access.secret_id
TENCENT_CLOUD_SECRET_KEY = access.secret_key
TENCENT_CLOUD_ENDPOINT = access.endpoint
TENCENT_CLOUD_SERVICE = access.service
TENCENT_CLOUD_API_VERSION = access.api_version
TENCENT_CLOUD_REGION = access.region

DOCUMENT_CHUNK_MODEL_NAME = "doc-tree-chunk"
DOCUMENT_PARSE_MODEL_NAME = "doc-llm"
DOCUMENT_CHUNK_SPLIT_MODEL_NAME = "doc-chunk"
EMBEDDING_MODEL_NAME = "bge-m3"

MAX_RETRY_ATTEMPTS = 3
RETRY_DELAY_SECONDS = 1
MAX_CHUNK_SIZE = 1000
BATCH_EMBEDDING_SIZE = 15


class DocumentChunkConfig(BaseModel):
    MaxChunkSize: int = 0
    Delimiters: List[str] = []
    ChunkOverlap: int = 0


class DocumentChunk(BaseModel):
    FileUrl: str
    FileStartPageNumber: int = 0
    FileEndPageNumber: int = 0
    ChunkConfig: DocumentChunkConfig


def initialize_tencent_cloud_client() -> CommonClient:
    logger.info("Initializing Tencent Cloud client...")
    try:
        credentials = credential.Credential(TENCENT_CLOUD_SECRET_ID, TENCENT_CLOUD_SECRET_KEY)
        http_profile = HttpProfile()
        http_profile.endpoint = TENCENT_CLOUD_ENDPOINT
        client_profile = ClientProfile()
        client_profile.httpProfile = http_profile
        client = CommonClient(TENCENT_CLOUD_SERVICE, TENCENT_CLOUD_API_VERSION, credentials, TENCENT_CLOUD_REGION,
                              profile=client_profile)
        logger.info("Tencent Cloud client initialized successfully")
        return client
    except Exception as e:
        logger.error(f"Failed to initialize Tencent Cloud client: {str(e)}")
        raise


def extract_file_extension_from_url(file_url: str) -> str:
    logger.info(f"Extracting file type from URL: {file_url}")
    try:
        parsed_url = urlparse(file_url)
        file_extension = os.path.splitext(parsed_url.path)[1].lower()
        if file_extension.isspace():
            logger.warning("File extension not recognized")

        file_extension = file_extension[1:].upper()
        logger.info(f"Detected file type: {file_extension} for URL: {file_url}")
        return file_extension
    except Exception as e:
        logger.error(f"Error extracting file type: {e}")
        raise


def execute_with_retry(func, *args, **kwargs):
    for attempt in range(MAX_RETRY_ATTEMPTS):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            if attempt == MAX_RETRY_ATTEMPTS - 1:
                logger.error(f"All retries failed: {str(e)}")
                raise
            logger.warning(
                f"Attempt {attempt + 1} failed, retrying in {RETRY_DELAY_SECONDS} seconds... Error: {str(e)}")
            time.sleep(RETRY_DELAY_SECONDS)
    return None


def split_markdown_by_headers(md_content: str) -> list[Document]:
    """根据Markdown标题结构进行智能切片"""
    headers_to_split_on = [
        ("#", "Header1"),
        ("##", "Header2"),
        ("###", "Header3"),
        ("####", "Header4"),
    ]

    splitter = MarkdownHeaderTextSplitter(headers_to_split_on=headers_to_split_on)
    return splitter.split_text(md_content)


def split_large_chunks(chunks: list[Document], separators: list[str], max_size: int = 1000, overlap: int = 200) -> list[
    Document]:
    """对超过最大尺寸的切片进行二次分块"""
    if not separators:
        separators = ["\n\n", "\n", "。", "！", "？", "，", ""]
    sub_splitter = RecursiveCharacterTextSplitter(
        chunk_size=max_size,
        chunk_overlap=overlap,
        separators=separators,
        keep_separator="end",
        length_function=len
    )

    result_chunks = []
    for chunk in chunks:
        if len(chunk.page_content) <= max_size:
            result_chunks.append(chunk)
        else:
            # 保持原有的元数据
            sub_chunks = sub_splitter.create_documents(
                texts=[chunk.page_content],
                metadatas=[chunk.metadata] * len(sub_splitter.split_text(chunk.page_content))
            )
            result_chunks.extend(sub_chunks)

    return result_chunks


def chunk_document_semantic(document_chunk: DocumentChunk) -> List[str]:
    try:
        logger.info(f"Processing document: {document_chunk.FileUrl}")

        doc_list = parse_document_slice(document_chunk)
        if not doc_list:
            logger.warning(f"No content extracted from {document_chunk.FileUrl}")
            raise Exception(f"No content extracted from {document_chunk.FileUrl}")

        logger.info(f"Extracted {len(doc_list)} chunks from {document_chunk.FileUrl}")
        return doc_list

    except Exception as e:
        logger.error(f"Error processing {document_chunk.FileUrl}: {str(e)}")
        raise e


def parse_document_slice(document_chunk: DocumentChunk) -> List[str]:
    start_time = time.time()
    logger.info(f"Starting async document chunking for: {document_chunk.FileUrl}")

    try:
        file_type = extract_file_extension_from_url(document_chunk.FileUrl)
        # 对文件 url进行 pre，编码为一个临时文件
        pre_url = get_presigned_url(document_chunk.FileUrl)
        params = {
            "Document": {
                "FileType": file_type,
                "FileUrl": pre_url,
                "FileName": os.path.basename(urlparse(document_chunk.FileUrl).path)
            },
            "Config": {
                "MaxChunkSize": document_chunk.ChunkConfig.MaxChunkSize,
            },
            "ModelName": DOCUMENT_CHUNK_MODEL_NAME
        }

        start_page = document_chunk.FileStartPageNumber
        end_page = document_chunk.FileEndPageNumber
        if (start_page != 0 or end_page != 0) and file_type in ["PDF", "PPT", "PPTX", "DOC"]:
            params["Document"]["FileStartPageNumber"] = start_page
            params["Document"]["FileEndPageNumber"] = end_page
        response = execute_with_retry(client.call_json, "ChunkDocumentAsync", params)
        logger.info(f"ChunkDocumentAsync response: {response}")
        task_id = response['Response']['TaskId']
        logger.info(f"Created document chunking task_id: {task_id}")

        while True:
            time.sleep(RETRY_DELAY_SECONDS)
            params = {"TaskId": task_id}
            response = execute_with_retry(client.call_json, "GetDocumentChunkResult", params)
            logger.info(f"GetDocumentChunkResult by taskid:{task_id} response: {response}")
            status = response['Response']['Status']

            if status == 1:
                logger.info(f"Document chunking taskid:{task_id} completed successfully")
                result_url = response['Response']['DocumentChunkResultUrl']
                result = download_and_extract_chunks(result_url)
                logger.info(f"parse_document_slice completed in {time.time() - start_time:.2f} seconds")
                return result
            elif status == -1:
                error_msg = f"Document chunking failed. taskid:{task_id} RequestId: {response['Response']['RequestId']}"
                logger.error(error_msg)
                raise Exception(error_msg)
            else:
                logger.info(f"Document chunking taskid:{task_id} in progress...")

    except Exception as e:
        logger.error(f"Error occurred while chunking document: {e}")
        logger.info(f"parse_document_slice failed after {time.time() - start_time:.2f} seconds")
        return []


def parse_document(document_chunk: DocumentChunk) -> List[str]:
    start_time = time.time()
    logger.info(f"Starting async document parsing for: {document_chunk.FileUrl}")

    try:
        file_type = extract_file_extension_from_url(document_chunk.FileUrl)
        # 对文件 url进行 pre，编码为一个临时文件
        pre_url = get_presigned_url(document_chunk.FileUrl)
        params = {
            "Document": {
                "FileType": file_type,
                "FileUrl": pre_url,
                "FileName": os.path.basename(urlparse(document_chunk.FileUrl).path)
            },
            "ModelName": DOCUMENT_PARSE_MODEL_NAME
        }
        start_page = document_chunk.FileStartPageNumber
        end_page = document_chunk.FileEndPageNumber
        if (start_page != 0 or end_page != 0) and file_type in ["PDF", "PPT", "PPTX", "DOC"]:
            params["Document"]["FileStartPageNumber"] = start_page
            params["Document"]["FileEndPageNumber"] = end_page
        response = execute_with_retry(client.call_json, "ParseDocumentAsync", params)
        logger.info(f"ParseDocumentAsync response: {response}")
        task_id = response['Response']['TaskId']
        logger.info(f"Created document parsing task_id: {task_id}")
        while True:
            time.sleep(RETRY_DELAY_SECONDS)
            params = {"TaskId": task_id}
            response = execute_with_retry(client.call_json, "GetDocumentParseResult", params)
            logger.info(f"GetDocumentParseResult by taskid:{task_id} response: {response}")
            status = response['Response']['Status']

            if status == 1:
                logger.info("Document parsing completed successfully")
                result_url = response['Response']['DocumentParseResultUrl']
                result = download_and_rule_chunk(result_url, document_chunk)
                logger.info(f"parse_document completed in {time.time() - start_time:.2f} seconds")
                return result
            elif status == -1:
                error_msg = f"Document parsing failed. taskid:{task_id} RequestId: {response['Response']['RequestId']}"
                logger.error(error_msg)
                raise Exception(error_msg)
            else:
                logger.info(f"Document parsing taskid:{task_id} in progress...")

    except Exception as e:
        logger.error(f"Error occurred while parsing document: {e}")
        logger.info(f"parse_document failed after {time.time() - start_time:.2f} seconds")
        raise e


def download_and_rule_chunk(result_url: str, document_chunk: DocumentChunk) -> List[str]:
    logger.info(f"Downloading chunk result from: {result_url}")

    try:
        response = requests.get(result_url)
        response.raise_for_status()
    except requests.RequestException as e:
        logger.error(f"Failed to download chunk result: {str(e)}")
        raise

    data = ""
    try:
        with zipfile.ZipFile(io.BytesIO(response.content)) as zip_file:
            md_files = [f for f in zip_file.namelist() if f.endswith('.md')]
            if not md_files:
                error_msg = "No .md file found in the zip archive"
                logger.error(error_msg)
                raise Exception(error_msg)

            for file_name in md_files:
                with zip_file.open(file_name) as f:
                    try:
                        data = f.read()
                    except Exception as e:
                        logger.error(f"Failed to parse doc: {str(e)}")
                        continue
    except zipfile.BadZipFile as e:
        logger.error(f"Invalid zip file: {str(e)}")
        raise
    except Exception as e:
        error_msg = f"Failed to extract or process zip file: {str(e)}"
        logger.error(error_msg)
        raise

    if not data:
        logger.info("No content found in the jsonl files")

    logger.info(f"Extracted {len(data)} from document")

    header_based_chunks = split_markdown_by_headers(str(data.decode()))

    final_chunks = split_large_chunks(header_based_chunks, document_chunk.ChunkConfig.Delimiters,
                                      document_chunk.ChunkConfig.MaxChunkSize,
                                      document_chunk.ChunkConfig.ChunkOverlap)

    return [chunk.page_content for chunk in final_chunks]


def download_and_extract_chunks(result_url: str) -> List[str]:
    logger.info(f"Downloading chunk result from: {result_url}")

    try:
        response = requests.get(result_url)
        response.raise_for_status()
    except requests.RequestException as e:
        logger.error(f"Failed to download chunk result: {str(e)}")
        raise

    doc_list = []
    try:
        with zipfile.ZipFile(io.BytesIO(response.content)) as zip_file:
            jsonl_files = [f for f in zip_file.namelist() if f.endswith('.jsonl')]
            if not jsonl_files:
                error_msg = "No .jsonl file found in the zip archive"
                logger.error(error_msg)
                raise Exception(error_msg)

            for file_name in jsonl_files:
                with zip_file.open(file_name) as f:
                    for line in f:
                        try:
                            data = json.loads(line.decode('utf-8'))
                            content = data.get('page_content')
                            if content:
                                cleaned_content = _remove_filename_prefix(content)
                                if cleaned_content:
                                    doc_list.append(cleaned_content)
                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to parse JSON line: {str(e)}")
                            continue

    except zipfile.BadZipFile as e:
        logger.error(f"Invalid zip file: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Failed to extract or process zip file: {str(e)}")
        raise

    if not doc_list:
        logger.info("No page_content found in the jsonl files")

    logger.info(f"Extracted {len(doc_list)} chunks from document")
    return doc_list


def _remove_filename_prefix(content: str) -> str:
    """
    移除内容开头的文件名前缀

    示例:
    "test.txt\nHello World" → "Hello World"
    "document.pdf\n第一页内容" → "第一页内容"
    """
    lines = content.splitlines()
    return '\n'.join(lines[1:]) if len(lines) > 1 else lines[0] if lines else ''


def get_text_embeddings(docs: List[str]) -> List[dict]:
    logger.info(f"Generating embeddings for {len(docs)} documents...")
    params = {
        "ModelName": EMBEDDING_MODEL_NAME,
        "Texts": docs,
    }
    try:
        response = client.call_json("GetTextEmbedding", params)
        embeddings = [
            {
                "content_embedding": data['Embedding'],
                "index": data['Index'],
                "content": docs[index]
            }
            for index, data in enumerate(response['Response']['Data'])
        ]
        logger.info(f"Successfully generated embeddings for {len(embeddings)} documents")
        return embeddings
    except Exception as e:
        logger.error(f"Error occurred while getting embeddings: {e}")
        return []


def chunk_document_rule(document_chunk: DocumentChunk) -> List[str]:
    try:
        logger.info(f"Processing document: {document_chunk.FileUrl}")

        doc_list = parse_document(document_chunk)
        if not doc_list:
            logger.warning(f"No content extracted from {document_chunk.FileUrl}")
            raise Exception(f"No content extracted from {document_chunk.FileUrl}")

        logger.info(f"Extracted {len(doc_list)} chunks from {document_chunk.FileUrl}")
        return doc_list

    except Exception as e:
        logger.error(f"Error processing {document_chunk.FileUrl}: {str(e)}")
        raise e


def process_document_semantic_preview(document_chunk: DocumentChunk) -> List[str]:
    try:

        logger.info("Step 1: Parsing and splitting documents...")
        doc_list = chunk_document_semantic(document_chunk)
        if not doc_list:
            raise ValueError("Document list is empty")
        logger.info(f"Document chunked into {len(doc_list)} parts")
        return doc_list[:10]

    except Exception as e:
        logger.error(f"Exception occurred while processing documents: {e}")
        raise e


def process_document_rule_preview(document_chunk: DocumentChunk) -> List[str]:
    try:
        logger.info("Step 1: Parsing and splitting documents...")
        doc_list = chunk_document_rule(document_chunk)
        if not doc_list:
            raise ValueError("Document list is empty")
        logger.info(f"Document chunked into {len(doc_list)} parts")
        return doc_list[:10]

    except Exception as e:
        logger.error(f"Exception occurred while processing documents: {e}")
        raise e


client = initialize_tencent_cloud_client()

if __name__ == '__main__':

    # 创建文本分割器实例
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=10,  # 每个块的最大字符数
        chunk_overlap=0,  # 相邻块之间的重叠字符数
        separators=["\n\n", "\n", "。", "！", "？", "，"],  # 分隔符列表
        keep_separator="end",
        length_function=len  # 计算文本长度的函数
    )

    # 待分割的文本
    text = "这是一段示例文本，包含多个句子。我们将使用递归字符分割器对其进行分割。"

    # 分割文本
    chunks = text_splitter.split_text(text)

    # 打印分割结果
    for i, chunk in enumerate(chunks):
        print(f"块 {i + 1} ({len(chunk)} 字符):\n{chunk}\n")
