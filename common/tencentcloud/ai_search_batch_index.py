import asyncio
import io
import json
import os
import tempfile
import threading
import time
import traceback
import uuid
import zipfile
from collections import defaultdict
from datetime import datetime, timezone
from typing import List, Optional, Generator, Dict, AsyncGenerator
from urllib.parse import urlparse

import aiohttp
from langchain_text_splitters import RecursiveCharacterTextSplitter
from pydantic import BaseModel
from tencentcloud.common import credential
from tencentcloud.common.common_client import CommonClient
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile

from common.database.database import MysqlPool
from common.logger.logger import logger
from common.metric.enpoints import record_latency
from common.metric.prom_metric import prom_metric
from common.share.config import appConfig
from common.tencentcloud.cos import get_presigned_url
from infra.adapter.task_list_adapter import TaskListAdapter
from infra.memory.knowledge_operator import KnowledgeOperator

DOCUMENT_CHUNK_MODEL_NAME = "doc-tree-chunk"
DOCUMENT_PARSE_MODEL_NAME = "doc-llm"
DOCUMENT_CHUNK_SPLIT_MODEL_NAME = "doc-chunk"
EMBEDDING_MODEL_NAME = "bge-m3"

MAX_RETRY_ATTEMPTS = 3
RETRY_DELAY_SECONDS = 1
STREAM_CHUNK_SIZE = 1024 * 1024 * 1024  # 流式下载块大小
BATCH_EMBEDDING_SIZE = 32

class DocumentChunkConfig(BaseModel):
    MaxChunkSize: int = 0
    Delimiters: List[str] = []
    ChunkOverlap: int = 0


class DocumentChunk(BaseModel):
    TaskId: Optional[str] = None
    FileId: str
    FileUrl: str
    FileStartPageNumber: int = 0
    FileEndPageNumber: int = 0
    AppId: Optional[str] = None
    KnowledgeBaseId: str
    ChunkConfig: DocumentChunkConfig

_tencent_cloud_client = None
_client_lock = threading.Lock()  # 用于线程安全的锁


def get_tencent_cloud_client() -> CommonClient:
    """获取或初始化腾讯云API客户端（线程安全）"""
    global _tencent_cloud_client

    # 双重检查锁定模式
    if _tencent_cloud_client is None:
        with _client_lock:
            # 再次检查，避免在等待锁的过程中其他线程已经初始化
            if _tencent_cloud_client is None:
                access = appConfig.automic.aisearch.access_info
                SECRET_ID = access.secret_id
                SECRET_KEY = access.secret_key
                TENCENT_CLOUD_ENDPOINT = access.endpoint
                SERVICE = access.service
                VERSION = access.api_version
                REGION = access.region

                logger.info("Initializing Tencent Cloud client...")
                try:
                    credentials = credential.Credential(SECRET_ID, SECRET_KEY)
                    http_profile = HttpProfile()
                    http_profile.endpoint = TENCENT_CLOUD_ENDPOINT
                    client_profile = ClientProfile()
                    client_profile.httpProfile = http_profile
                    _tencent_cloud_client = CommonClient(
                        SERVICE, VERSION, credentials, REGION, profile=client_profile
                    )
                    logger.info("Tencent Cloud client initialized successfully")
                except Exception as e:
                    logger.error(f"Failed to initialize Tencent Cloud client: {str(e)}")
                    raise

    return _tencent_cloud_client



def extract_file_extension_from_url(file_url: str) -> str:
    logger.info(f"Extracting file type from URL: {file_url}")
    try:
        parsed_url = urlparse(file_url)
        file_extension = os.path.splitext(parsed_url.path)[1].lower()
        if file_extension.isspace():
            logger.warning("File extension not recognized")

        file_extension = file_extension[1:].upper()
        logger.info(f"Detected file type: {file_extension} for URL: {file_url}")
        return file_extension
    except Exception as e:
        logger.error(f"Error extracting file type: {e}")
        raise


async def execute_with_retry(func, *args, **kwargs):
    for attempt in range(MAX_RETRY_ATTEMPTS):
        try:
            # 如果func是同步函数，使用asyncio.to_thread包装
            if not asyncio.iscoroutinefunction(func):
                return await asyncio.to_thread(func, *args, **kwargs)
            else:
                return await func(*args, **kwargs)
        except Exception as e:
            error_info = {
                'function': func.__name__,
                'args': args,
                'kwargs': kwargs,
                'attempt': attempt + 1,
                'error': str(e)
            }
            logger.error(f"Retry attempt {attempt + 1} failed: {json.dumps(error_info)}")
            if attempt == MAX_RETRY_ATTEMPTS - 1:
                logger.error(f"All {MAX_RETRY_ATTEMPTS} retries failed")
                raise
            logger.warning(f"Retrying in {RETRY_DELAY_SECONDS} seconds...")
            await asyncio.sleep(RETRY_DELAY_SECONDS)
    return None


def process_jsonl_file(text_stream) -> Generator[str, None, None]:
    """极高效处理JSONL文件流，减少内存和计算开销，返回生成器"""
    org_data_map = {}  # 存储org_data的内容到ID的映射
    page_content_map = defaultdict(list)  # 存储ID到page_content列表的映射
    current_id = 0  # 当前分配的ID

    # 直接从文本流中读取行
    for line in text_stream:
        try:
            # 解析JSON行
            data = json.loads(line)

            # 提取字段（假设字段存在）
            page_content = data['page_content']
            org_data = data['org_data']

            # 将org_data转换为可哈希的形式
            if isinstance(org_data, dict):
                org_tuple = tuple(sorted(org_data.items()))
            else:
                org_tuple = (str(org_data),)

            # 检查是否已存在相同的org_data
            if org_tuple in org_data_map:
                org_id = org_data_map[org_tuple]
            else:
                # 分配新ID
                org_id = current_id
                org_data_map[org_tuple] = org_id
                current_id += 1

            # 添加page_content到对应的org_id
            page_content_map[org_id].append(page_content)

        except (KeyError, json.JSONDecodeError):
            continue  # 跳过错误行

    # 构建最终结果并逐个yield
    for org_tuple, org_id in org_data_map.items():
        # 还原org_data
        if len(org_tuple) == 1 and isinstance(org_tuple[0], str):
            org_data = org_tuple[0]
        else:
            org_data = dict(org_tuple)
        org_data = _remove_fileid_prefix(org_data)
        result = {
            'org_data': org_data,
            'page_contents': page_content_map[org_id]
        }
        yield org_data


BATCH_SIZE = 8192  # 8KB批量读取，平衡内存和性能
SLEEP_INTERVAL = 0.0001  # 100微秒延时，几乎不影响吞吐量


async def stream_download_and_extract(result_url: str, file_ext: str, process=True) -> AsyncGenerator[str, None]:
    """
    流式下载并提取文件内容
    返回一个异步生成器，每次生成一个文本块或JSON对象
    如果process为True，则处理JSONL文件并流式返回结果
    """
    logger.info(f"Streaming download from: {result_url}")
    temp_zip_path = None

    try:
        # 使用异步流式下载
        async with aiohttp.ClientSession() as session:
            async with session.get(result_url) as response:
                response.raise_for_status()

                # 创建临时文件存储下载内容
                with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_zip:
                    temp_zip_path = temp_zip.name

                    # 流式写入临时文件
                    async for chunk in response.content.iter_chunked(STREAM_CHUNK_SIZE):
                        if chunk:
                            temp_zip.write(chunk)

                    # 关闭文件以便后续读取
                    temp_zip.close()

                # 打开zip文件进行处理
                with zipfile.ZipFile(temp_zip_path, 'r') as zip_file:
                    if file_ext == 'MD':
                        # 处理Markdown文件（保持不变）
                        md_files = [f for f in zip_file.namelist() if f.endswith('.md')]
                        if not md_files:
                            error_msg = "No .md file found in the zip archive"
                            logger.error(error_msg)
                            raise Exception(error_msg)

                        for file_name in md_files:
                            logger.info(f"Processing Markdown file: {file_name}")
                            with zip_file.open(file_name) as f:
                                text_wrapper = io.TextIOWrapper(f, encoding='utf-8')
                                for line in text_wrapper:
                                    yield line

                    elif file_ext == 'JSONL':
                        # 处理JSONL文件（直接从ZIP读取，不解压）
                        jsonl_files = [f for f in zip_file.namelist() if f.endswith('.jsonl')]
                        if not jsonl_files:
                            error_msg = "No .jsonl file found in the zip archive"
                            logger.error(error_msg)
                            raise Exception(error_msg)

                        for file_name in jsonl_files:
                            logger.info(f"Processing JSONL file: {file_name}")
                            with zip_file.open(file_name) as f:
                                # 直接创建文本流并传递给处理函数
                                text_stream = io.TextIOWrapper(f, encoding='utf-8')
                                if process:
                                    # 直接处理流数据（保持原有函数名，修改函数实现）
                                    for processed_item in process_jsonl_file(text_stream):
                                        yield processed_item
                                else:
                                    # 不处理，直接返回原始行
                                    for line in text_stream:
                                        yield line
                    else:
                        raise ValueError(f"Unsupported file extension for streaming: {file_ext}")

    except Exception as e:
        logger.error(f"Error during streaming download and extraction: {e}")
        logger.error(f"Exception stack trace: {traceback.format_exc()}")
        raise
    finally:
        # 确保临时文件被删除
        if temp_zip_path and os.path.exists(temp_zip_path):
            try:
                os.unlink(temp_zip_path)
                logger.info(f"Temp file deleted: {temp_zip_path}")
            except Exception as e:
                logger.error(f"Failed to delete temp zip file {temp_zip_path}: {e}")


def _remove_fileid_prefix(content: str) -> str:
    lines = content.split('\n', 1)
    if not lines:
        return ''
    first_line = lines[0]
    rest = lines[1] if len(lines) > 1 else ''

    # 处理第一行中的第一个下划线
    idx = first_line.find('_')
    processed_first_line = first_line[idx + 1:] if idx != -1 else first_line

    # 重新组合结果
    return f"{processed_first_line}\n{rest}" if rest else processed_first_line


def _remove_filename_prefix(content: str) -> str:
    """
    移除内容开头的文件名前缀

    示例:
    "test.txt\nHello World" → "Hello World"
    "document.pdf\n第一页内容" → "第一页内容"
    """
    lines = content.splitlines()
    return '\n'.join(lines[1:]) if len(lines) > 1 else lines[0] if lines else ''


def split_text_safely(text: str, max_length: int = 4096) -> list[str]:
    """
    将超长文本分割成多个不超过最大长度的片段
    优先在自然分隔符处分割，避免截断单词或句子
    """
    if len(text) <= max_length:
        return [text]

    separators = ["\n\n", "\n", "。", "！", "？", "；", "，", " ", ""]
    chunks = []
    current_start = 0
    text_length = len(text)

    while current_start < text_length:
        # 确定当前块的最大可能结束位置
        current_max_end = min(current_start + max_length, text_length)

        # 如果剩余文本已经小于等于最大长度，直接添加
        if current_max_end == text_length:
            chunks.append(text[current_start:])
            break

        # 从最大位置向前查找最佳分割点
        best_split_pos = -1
        for separator in separators:
            if not separator:  # 空字符串作为最后的硬分割
                continue

            # 从后往前查找分隔符
            sep_len = len(separator)
            search_start = current_max_end - sep_len

            while search_start >= current_start:
                if text[search_start:search_start + sep_len] == separator:
                    best_split_pos = search_start + sep_len
                    break
                search_start -= 1

            if best_split_pos != -1:
                break

        # 处理找到分隔符的情况
        if best_split_pos != -1:
            chunks.append(text[current_start:best_split_pos])
            current_start = best_split_pos
        else:
            # 硬分割处理
            chunks.append(text[current_start:current_max_end])
            current_start = current_max_end

    return chunks


def split_text_by_length(text: str, max_length: int = 4096) -> list[str]:
    """
    强制按指定长度分割文本，不考虑分隔符位置
    实现简单高效，适用于对分割位置不敏感的场景
    """
    if not text:
        return []

    if len(text) <= max_length:
        return [text]

    # 计算需要分割的块数
    num_chunks = (len(text) + max_length - 1) // max_length
    return [text[i * max_length: (i + 1) * max_length] for i in range(num_chunks)]


async def get_text_embeddings_stream(texts: AsyncGenerator[dict, None]) -> AsyncGenerator[list[dict], None]:
    """
    流式生成文本嵌入，每32个文本作为一批次处理
    接收带顺序标识的文本块，输出带相同顺序标识的嵌入结果
    """
    batch = []

    async def process_batch(texts_batch: list) -> list[dict]:
        """处理一批文本并返回嵌入结果列表"""
        if not texts_batch:
            return []

        logger.info(f"Generating embeddings for batch of {len(texts_batch)} documents")

        try:
            client = get_tencent_cloud_client()  # 获取客户端实例
            response = await asyncio.to_thread(client.call_json, "GetTextEmbedding", {
                "ModelName": EMBEDDING_MODEL_NAME,
                "Texts": [item["content"] for item in texts_batch]
            })
            return [
                {
                    "content_embedding": data['Embedding'],
                    "content": texts_batch[i]["content"],
                    "sort_value": texts_batch[i]["sort_value"]  # 保留顺序标识
                }
                for i, data in enumerate(response['Response']['Data'])
            ]
        except Exception as e:
            logger.error(f"Error occurred while getting embeddings: {e}")
            raise

    try:
        async for text_item in texts:
            batch.append(text_item)
            # 当批次达到32个文本时处理并生成结果
            if len(batch) >= BATCH_EMBEDDING_SIZE:
                results = await process_batch(batch)
                yield results
                batch = []
        # 处理剩余的文本
        if batch:
            results = await process_batch(batch)
            yield results

    except Exception as e:
        error_msg = f"Error getting text embeddings stream: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Exception stack trace: {traceback.format_exc()}")
        raise
    finally:
        batch.clear()


async def write_to_elasticsearch_stream(embeddings: AsyncGenerator[List[Dict], None],
                                        document_chunk: DocumentChunk) -> bool:
    try:
        knowledge_operator = KnowledgeOperator.get_instance(document_chunk.AppId)
        # 先删除旧数据
        if not await knowledge_operator.delete_knowledge_by_file_id(document_chunk.KnowledgeBaseId,
                                                                    document_chunk.FileId):
            logger.info(f"无旧数据: file_id={document_chunk.FileId}")

        batch = []  # 存储批量文档的列表

        async for embedding_list in embeddings:
            # 遍历列表中的每个嵌入字典
            for embedding in embedding_list:
                # 准备文档数据，使用已经分配的sort_value
                doc = {
                    "content_embedding": embedding["content_embedding"],
                    "content": embedding["content"],
                    "file_id": document_chunk.FileId,
                    "knowledge_base_id": document_chunk.KnowledgeBaseId,
                    "chunk_id": str(uuid.uuid4()),
                    "sort_value": embedding["sort_value"],  # 使用预先分配的顺序值
                    "create_time": datetime.now(timezone.utc).isoformat()
                }
                batch.append(doc)  # 将文档添加到批次

            # 批量写入ES
            success = await knowledge_operator.save_knowledge_records(batch)
            if not success:
                logger.error(f"Failed to save batch of {len(batch)} documents")
                raise RuntimeError("Batch write failure")
            else:
                logger.info(f"Successfully saved batch of {len(batch)} documents")
                batch = []  # 清空批次
        return True
    except Exception as e:
        logger.error(f"Failed to process documents: {e}")
        logger.error(f"Exception stack trace: {traceback.format_exc()}")
        raise


async def get_text_embeddings_stream_concurrent(
        texts: AsyncGenerator[dict, None],
        # Add a parameter for the number of concurrent embedding workers
        num_embedding_workers: int = 5
) -> AsyncGenerator[List[Dict], None]:
    """
    流式生成文本嵌入，使用多个并发 worker 处理批次。
    接收带顺序标识的文本块，输出带相同顺序标识的嵌入结果。
    """
    # Queue to hold batches of texts waiting for embedding
    embedding_input_queue = asyncio.Queue()
    # Queue to hold embedding results ready to be yielded
    embedding_output_queue = asyncio.Queue()

    async def _embedding_worker():
        """Worker coroutine to fetch embeddings for a batch."""
        while True:
            # Get a batch from the input queue
            batch_data = await embedding_input_queue.get()
            if batch_data is None:  # Sentinel value to signal termination
                embedding_input_queue.task_done()
                break

            logger.info(f"Embedding worker processing batch of {len(batch_data)} documents.")
            try:
                # Call the Tencent Cloud API. asyncio.to_thread is crucial for blocking calls.
                client = get_tencent_cloud_client()  # 获取客户端实例
                response = await asyncio.to_thread(client.call_json, "GetTextEmbedding", {
                    "ModelName": EMBEDDING_MODEL_NAME,
                    "Texts": [item["content"] for item in batch_data]
                })

                embeddings = [
                    {
                        "content_embedding": data['Embedding'],
                        "content": batch_data[i]["content"],
                        "sort_value": batch_data[i]["sort_value"]
                    }
                    for i, data in enumerate(response['Response']['Data'])
                ]
                await embedding_output_queue.put(embeddings)  # Put results into output queue
            except Exception as e:
                logger.error(f"Error in embedding worker: {e}", exc_info=True)
                # Depending on your error handling strategy, you might want to:
                # 1. Put an error signal into the output queue.
                # 2. Re-raise and let a higher level catch it (might stop the stream).
                # For this example, we log and continue, potentially skipping this batch.
            finally:
                embedding_input_queue.task_done()

    # Create and start the worker coroutines
    workers = [asyncio.create_task(_embedding_worker()) for _ in range(num_embedding_workers)]
    logger.info(f"Started {num_embedding_workers} embedding workers.")

    # Producer part: Read from the upstream text stream and put batches into the input queue
    batch = []
    async for text_item in texts:
        batch.append(text_item)
        if len(batch) >= BATCH_EMBEDDING_SIZE:  # Use your defined BATCH_EMBEDDING_SIZE
            await embedding_input_queue.put(batch)
            batch = []
    if batch:  # Don't forget the last partial batch
        await embedding_input_queue.put(batch)

    # Signal all workers to terminate after all batches are put into the queue
    for _ in range(num_embedding_workers):
        await embedding_input_queue.put(None)

    # Wait for all batches to be processed by the workers
    await embedding_input_queue.join()
    logger.info("All embedding batches sent to workers have been processed.")

    # Consumer part: Yield results from the output queue in the order they become available
    # Note: If order is strict, you'd need a more complex reordering mechanism.
    # For now, we yield as results are ready.
    while not embedding_output_queue.empty():
        yield await embedding_output_queue.get()

    # Ensure all worker tasks are cancelled/cleaned up
    for worker_task in workers:
        worker_task.cancel()
    await asyncio.gather(*workers, return_exceptions=True)  # Wait for tasks to finish cancelling
    logger.info("All embedding workers stopped.")

@prom_metric("rag_write_to_es_total")
async def write_to_elasticsearch_stream_concurrent(
        embeddings_stream: AsyncGenerator[List[Dict], None],
        document_chunk: DocumentChunk,
        num_es_workers: int = 3  # Add a parameter for the number of concurrent ES workers
) -> bool:
    """
    使用多个并发 worker 将嵌入流写入 Elasticsearch。
    """
    es_input_queue = asyncio.Queue()
    knowledge_operator = KnowledgeOperator.get_instance(document_chunk.AppId)

    # Delete old data once at the beginning
    if not await knowledge_operator.delete_knowledge_by_file_id(document_chunk.KnowledgeBaseId,
                                                                document_chunk.FileId):
        logger.info(f"No old data found for file_id={document_chunk.FileId}")

    async def _es_worker():
        """Worker coroutine to write embedding batches to Elasticsearch."""
        while True:
            batch = await es_input_queue.get()
            if batch is None:  # Sentinel value
                es_input_queue.task_done()
                break

            logger.info(f"ES worker processing batch of {len(batch)} documents.")
            try:
                # Prepare documents for ES (already done in your original logic)
                es_docs = []
                for embedding in batch:
                    doc = {
                        "content_embedding": embedding["content_embedding"],
                        "content": embedding["content"],
                        "file_id": document_chunk.FileId,
                        "knowledge_base_id": document_chunk.KnowledgeBaseId,
                        "chunk_id": str(uuid.uuid4()),
                        "sort_value": embedding["sort_value"],
                        "create_time": datetime.now(timezone.utc).isoformat()
                    }
                    es_docs.append(doc)

                success = await knowledge_operator.save_knowledge_records(es_docs)
                if not success:
                    logger.error(f"Failed to save batch of {len(es_docs)} documents to ES.")
                    # Consider adding retry logic here for ES writes if specific errors occur
                    # Or put the batch back into a retry queue
                else:
                    logger.info(f"Successfully saved batch of {len(es_docs)} documents to ES.")
            except Exception as e:
                logger.error(f"Error in ES worker: {e}", exc_info=True)
                raise
            finally:
                es_input_queue.task_done()

    # Create and start the ES worker coroutines
    es_workers = [asyncio.create_task(_es_worker()) for _ in range(num_es_workers)]
    logger.info(f"Started {num_es_workers} ES workers.")

    try:
        # Producer part: Read from the upstream embeddings stream and put batches into the ES input queue
        async for embedding_list in embeddings_stream:
            # Your current `get_text_embeddings_stream` already yields List[Dict],
            # so each `embedding_list` is already a batch.
            await es_input_queue.put(embedding_list)

        # Signal all ES workers to terminate
        for _ in range(num_es_workers):
            await es_input_queue.put(None)

        # Wait for all ES batches to be processed
        await es_input_queue.join()
        logger.info("All ES write batches sent to workers have been processed.")
        return True  # All batches put in queue and processed by workers
    except Exception as e:
        logger.error(f"Failed to process documents for ES writing: {e}", exc_info=True)
        raise
    finally:
        # Ensure all worker tasks are cancelled/cleaned up
        for worker_task in es_workers:
            worker_task.cancel()
        await asyncio.gather(*es_workers, return_exceptions=True)
        logger.info("All ES workers stopped.")

@prom_metric("rag_embedding_total")
async def get_text_embeddings_stream_concurrent_optimized(
        texts: AsyncGenerator[dict, None],
        num_embedding_workers: int = 5
) -> AsyncGenerator[List[Dict], None]:
    embedding_input_queue = asyncio.Queue()
    # 结果队列，用于从 worker 传递结果给 yield
    embedding_output_queue = asyncio.Queue()

    async def _embedding_worker():
        while True:
            # 带有 await embedding_input_queue.join() 的模式，通常在主循环中等待队列完成
            # 所以这里不需要 task_done()，而是由 put 任务来驱动
            batch_data = await embedding_input_queue.get()
            if batch_data is None:  # Sentinel value to signal termination
                embedding_input_queue.task_done()  # 标记这个 get 任务已完成
                break

            logger.debug(f"Embedding worker processing batch of {len(batch_data)} documents.")
            try:
                response = await get_embeddings(batch_data)

                embeddings = [
                    {
                        "content_embedding": data['Embedding'],
                        "content": batch_data[i]["content"],
                        "sort_value": batch_data[i]["sort_value"]
                    }
                    for i, data in enumerate(response['Response']['Data'])
                ]
                await embedding_output_queue.put(embeddings)  # 嵌入完成后立即放入输出队列
            except Exception as e:
                logger.error(f"Error in embedding worker: {e}", exc_info=True)
                raise
            finally:
                embedding_input_queue.task_done()  # 标记这个 get 任务已完成

    @prom_metric("rag_embedding")
    async def get_embeddings(batch_data):
        client = get_tencent_cloud_client()  # 获取客户端实例
        response = await asyncio.to_thread(client.call_json, "GetTextEmbedding", {
            "ModelName": EMBEDDING_MODEL_NAME,
            "Texts": [item["content"] for item in batch_data]
        })
        return response

    async def producer():
        """从上游文本流读取，分批放入输入队列"""
        batch = []
        async for text_item in texts:
            batch.append(text_item)
            if len(batch) >= BATCH_EMBEDDING_SIZE:
                await embedding_input_queue.put(batch)
                batch = []
        if batch:
            await embedding_input_queue.put(batch)

        # 发送终止信号给所有 worker
        for _ in range(num_embedding_workers):
            await embedding_input_queue.put(None)
        logger.info("Producer: All text batches sent to embedding input queue, and termination signals sent.")

    # 使用 TaskGroup (Python 3.11+) 或 asyncio.gather 来管理并发任务
    # 这里我们直接启动所有任务，并在主协程中消费
    workers = [asyncio.create_task(_embedding_worker()) for _ in range(num_embedding_workers)]
    logger.info(f"Started {num_embedding_workers} embedding workers.")

    producer_task = asyncio.create_task(producer())

    try:
        # 在这里消费 embedding_output_queue，实现实时 yield
        # 我们需要知道何时所有嵌入都已生成并放入 output_queue
        # 最简单的方法是，当 producer_task 完成且 input_queue 为空后，
        # 等待所有 worker 完成，然后向 output_queue 发送终止信号
        await producer_task  # 等待所有文本批次都被放入输入队列

        # 等待所有输入队列中的任务（包括 None 信号）被 worker 取走并处理
        await embedding_input_queue.join()
        logger.info("All embedding input queue tasks processed by workers.")

        # 确保所有 worker 都已完成处理并退出
        for worker_task in workers:
            await worker_task  # 等待 worker 任务真正结束

        # 向输出队列发送终止信号
        await embedding_output_queue.put(None)
        logger.info("Sent termination signal to embedding output queue.")

        # 现在，从输出队列消费并 yield
        while True:
            embeddings_batch = await embedding_output_queue.get()
            if embeddings_batch is None:
                embedding_output_queue.task_done()
                break
            yield embeddings_batch
            embedding_output_queue.task_done()
    except Exception as e:
        logger.error(f"Error in get_text_embeddings_stream_concurrent_optimized: {e}", exc_info=True)
        raise
    finally:
        # 确保所有 worker 任务最终被取消和清理
        for worker_task in workers:
            worker_task.cancel()
        await asyncio.gather(*workers, return_exceptions=True)
        logger.info("All embedding workers stopped.")
        # producer_task 应该已经完成，但为了安全也可以 await
        if not producer_task.done():
            producer_task.cancel()
            await asyncio.gather(producer_task, return_exceptions=True)


async def chunk_document_semantic_stream(document_chunk: DocumentChunk, mysql_pool: MysqlPool) -> AsyncGenerator[
    dict, None]:
    """
    流式处理文档语义分块
    返回一个异步生成器，每次生成一个带顺序标识的文本块
    """
    try:
        logger.info(f"Processing document stream: {document_chunk.FileUrl}")

        # 立即执行并检查是否有内容
        doc_stream = await parse_document_slice_stream(document_chunk, mysql_pool)
        if doc_stream is None:
            raise Exception(f"No content extracted from {document_chunk.FileUrl}")

        # 创建并返回异步生成器
        async def content_generator():
            # 流式处理Markdown内容
            # 如果没有配置分块大小，直接返回原始内容
            sort_counter = 0
            async for line in doc_stream:
                sort_counter += 1
                if sort_counter % 1000 == 0:
                    logger.info(f"Processed {sort_counter} lines from document stream")
                # 为每个文本块添加顺序标识
                yield {
                    "content": line,
                    "sort_value": sort_counter
                }

        return content_generator()

    except Exception as e:
        logger.error(f"Error processing document stream: {str(e)}")
        # 添加堆栈信息记录
        logger.error(f"Exception stack trace: {traceback.format_exc()}")
        # 重新抛出异常，确保外层可以捕获
        raise


async def parse_document_slice_stream(document_chunk: DocumentChunk, mysql_pool: MysqlPool) -> AsyncGenerator[
    str, None]:
    """
    异步解析文档并返回内容流
    返回一个异步生成器，每次生成一行文本
    """
    start_time = time.time()
    logger.info(f"Starting async document chunking for: {document_chunk.FileUrl}")

    try:
        file_type = extract_file_extension_from_url(document_chunk.FileUrl)
        pre_url = get_presigned_url(document_chunk.FileUrl)
        params = {
            "Document": {
                "FileType": file_type,
                "FileUrl": pre_url,
                "FileName": os.path.basename(urlparse(document_chunk.FileUrl).path)
            },
            "Config": {
                "MaxChunkSize": document_chunk.ChunkConfig.MaxChunkSize,
            },
            "ModelName": DOCUMENT_CHUNK_MODEL_NAME
        }

        start_page = document_chunk.FileStartPageNumber
        end_page = document_chunk.FileEndPageNumber
        if (start_page != 0 or end_page != 0) and file_type in ["PDF", "PPT", "PPTX", "DOC"]:
            params["Document"]["FileStartPageNumber"] = start_page
            params["Document"]["FileEndPageNumber"] = end_page

        # 立即执行并获取任务ID
        client = get_tencent_cloud_client()  # 获取客户端实例
        response = await execute_with_retry(lambda: client.call_json("ChunkDocumentAsync", params))
        task_id = response['Response']['TaskId']
        logger.info(f"Created document chunking task_id: {task_id}")

        task_list_adapter = TaskListAdapter(mysql_pool)
        task_list_adapter.update_response(document_chunk.TaskId, json.dumps({"TaskId": task_id}))

        # 返回一个新的异步生成器函数，处理异步任务结果
        async def task_result_generator():
            while True:
                await asyncio.sleep(RETRY_DELAY_SECONDS)
                params = {"TaskId": task_id}
                response = await execute_with_retry(lambda: client.call_json("GetDocumentChunkResult", params))
                logger.debug(f"GetDocumentChunkResult by taskid:{task_id} response: {response}")
                status = response['Response']['Status']

                if status == 1:
                    logger.info(f"Document chunking taskid:{task_id} completed successfully")
                    result_url = response['Response']['DocumentChunkResultUrl']
                    task_list_adapter.update_response(document_chunk.TaskId,
                                                      json.dumps({"TaskId": task_id, "result_url": result_url}))
                    duration = time.time() - start_time
                    record_latency("chunk_document_async", duration)
                    async for item in stream_download_and_extract(result_url, 'JSONL'):
                        yield item
                    return
                elif status == -1:
                    error_msg = f"Document chunking failed. taskid:{task_id} RequestId: {response['Response']['RequestId']}"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                else:
                    logger.debug(f"Document chunking taskid:{task_id} in progress...")

        # 立即返回生成器，而不是在函数内部执行
        return task_result_generator()

    except Exception as e:
        logger.error(f"Error occurred while chunking document: {e}")
        logger.info(f"parse_document_slice_stream failed after {time.time() - start_time:.2f} seconds")
        # 重新抛出异常，确保调用者能捕获
        raise


async def parse_document_stream(document_chunk: DocumentChunk, mysql_pool: MysqlPool) -> AsyncGenerator[str, None]:
    """
    异步解析文档并返回内容流
    返回一个异步生成器，每次生成一行文本
    """
    start_time = time.time()
    logger.info(f"Starting async document parsing for: {document_chunk.FileUrl}")

    try:
        file_type = extract_file_extension_from_url(document_chunk.FileUrl)
        pre_url = get_presigned_url(document_chunk.FileUrl)
        params = {
            "Document": {
                "FileType": file_type,
                "FileUrl": pre_url,
                "FileName": os.path.basename(urlparse(document_chunk.FileUrl).path)
            },
            "ModelName": DOCUMENT_PARSE_MODEL_NAME
        }
        start_page = document_chunk.FileStartPageNumber
        end_page = document_chunk.FileEndPageNumber
        if (start_page != 0 or end_page != 0) and file_type in ["PDF", "PPT", "PPTX", "DOC"]:
            params["Document"]["FileStartPageNumber"] = start_page
            params["Document"]["FileEndPageNumber"] = end_page

        # 立即执行并获取任务ID
        client = get_tencent_cloud_client()  # 获取客户端实例
        response = await execute_with_retry(lambda: client.call_json("ParseDocumentAsync", params))
        task_id = response['Response']['TaskId']
        logger.info(f"Created document parsing task_id: {task_id}")

        task_list_adapter = TaskListAdapter(mysql_pool)
        task_list_adapter.update_response(document_chunk.TaskId, json.dumps({"TaskId": task_id}))

        # 返回一个新的异步生成器函数，处理异步任务结果
        async def task_result_generator():
            while True:
                await asyncio.sleep(RETRY_DELAY_SECONDS)
                params = {"TaskId": task_id}
                response = await execute_with_retry(lambda: client.call_json("GetDocumentParseResult", params))
                logger.debug(f"GetDocumentParseResult by taskid:{task_id} response: {response}")
                status = response['Response']['Status']

                if status == 1:
                    logger.info("Document parsing completed successfully")
                    result_url = response['Response']['DocumentParseResultUrl']
                    task_list_adapter.update_response(document_chunk.TaskId,
                                                      json.dumps({"TaskId": task_id, "result_url": result_url}))
                    duration = time.time() - start_time
                    record_latency("parse_document_async", duration)
                    async for item in stream_download_and_extract(result_url, 'MD'):
                        yield item
                    return
                elif status == -1:
                    error_msg = f"Document parsing failed. taskid:{task_id} RequestId: {response['Response']['RequestId']}"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                else:
                    logger.debug(f"Document parsing taskid:{task_id} in progress...")

        # 立即返回生成器，而不是在函数内部执行
        return task_result_generator()

    except Exception as e:
        logger.error(f"Error occurred while parsing document: {e}")
        logger.info(f"parse_document_stream failed after {time.time() - start_time:.2f} seconds")
        # 重新抛出异常，确保调用者能捕获
        raise


async def chunk_document_rule_stream(document_chunk: DocumentChunk, mysql_pool: MysqlPool) -> AsyncGenerator[
    dict, None]:
    """
    流式处理文档规则分块
    返回一个异步生成器，每次生成一个带顺序标识的文本块
    """
    logger.info(f"Processing document stream: {document_chunk.FileUrl}")
    try:
        # 获取文档内容流
        doc_stream = await parse_document_stream(document_chunk, mysql_pool)
        if not doc_stream:
            raise ValueError(f"No content extracted from {document_chunk.FileUrl}")

        # 配置文本分割器
        delimiters = document_chunk.ChunkConfig.Delimiters or ["\n\n", "\n", "。", "！", "？", "，", ""]
        # delimiters = list(set(delimiters + ["\n\n", "\n", "。", "！", "？", "，", ""]))

        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=document_chunk.ChunkConfig.MaxChunkSize,
            chunk_overlap=document_chunk.ChunkConfig.ChunkOverlap,
            separators=delimiters,
            keep_separator="end",
            length_function=len
        )
        sort_counter = 0
        # 异步流式处理文档内容
        async for text_chunk in doc_stream:
            if not text_chunk:
                continue

            # 分割大文本块
            sub_chunks = text_splitter.split_text(text_chunk)
            # 确保每个块不超过最大尺寸
            for sub_chunk in sub_chunks:
                safe_chunks = split_text_by_length(sub_chunk, document_chunk.ChunkConfig.MaxChunkSize)
                for safe_chunk in safe_chunks:
                    sort_counter += 1
                    # 为每个文本块添加顺序标识
                    yield {
                        "content": safe_chunk,
                        "sort_value": sort_counter
                    }

    except Exception as e:
        logger.error(f"Failed to process document stream: {e}", exc_info=True)
        raise

@prom_metric("rag_semantic_task")
async def process_document_semantic_stream(document_chunk: DocumentChunk, mysql_pool: MysqlPool) -> bool:
    """
    完全流式处理语义文档
    从下载到写入ES全程使用流式处理
    """
    try:
        logger.info("Step 1: Streaming document parsing and splitting...")
        # 立即执行并检查是否有内容
        doc_stream = await chunk_document_semantic_stream(document_chunk, mysql_pool)
        if doc_stream is None:
            raise Exception(f"No content extracted from {document_chunk.FileUrl}")

        logger.info("Step 2: Generating embeddings...")
        # 立即执行并创建嵌入生成器
        EMBEDDING_WORKERS = appConfig.automic.aisearch.knowledge_base.embedding_works
        ES_WRITE_WORKERS = appConfig.automic.aisearch.knowledge_base.es_write_works

        embedding_stream = get_text_embeddings_stream_concurrent_optimized(
            doc_stream,
            num_embedding_workers=EMBEDDING_WORKERS  # Configure your desired concurrency here
        )
        logger.info("Step 3: Streaming embeddings to Elasticsearch...")
        # 立即执行写入操作
        success = await write_to_elasticsearch_stream_concurrent(
            embedding_stream,
            document_chunk,
            num_es_workers=ES_WRITE_WORKERS  # Configure your desired concurrency here
        )

        if success:
            logger.info("Document processing completed successfully using stream mode")
            return True
        else:
            logger.error("Failed to process document using stream mode")
            return False

    except Exception as e:
        logger.error(f"Exception occurred while processing document stream: {e}")
        # 添加堆栈信息记录
        logger.error(f"Exception stack trace: {traceback.format_exc()}")
        # 确保上层能捕获到异常
        raise

@prom_metric("rag_rule_task")
async def process_document_rule_stream(document_chunk: DocumentChunk, mysql_pool: MysqlPool) -> bool:
    """
    完全流式处理规则文档
    从下载到写入ES全程使用流式处理
    """
    try:
        logger.info("Step 1: Streaming document parsing and splitting...")
        # 立即执行并检查是否有内容
        doc_stream = chunk_document_rule_stream(document_chunk, mysql_pool)
        if doc_stream is None:
            raise Exception(f"No content extracted from {document_chunk.FileUrl}")

        logger.info("Step 2: Generating embeddings...")
        # 立即执行并创建嵌入生成器
        EMBEDDING_WORKERS = appConfig.automic.aisearch.knowledge_base.embedding_works
        ES_WRITE_WORKERS = appConfig.automic.aisearch.knowledge_base.es_write_works

        embedding_stream = get_text_embeddings_stream_concurrent_optimized(
            doc_stream,
            num_embedding_workers=EMBEDDING_WORKERS  # Configure your desired concurrency here
        )
        logger.info("Step 3: Streaming embeddings to Elasticsearch...")
        # 立即执行写入操作
        success = await write_to_elasticsearch_stream_concurrent(
            embedding_stream,
            document_chunk,
            num_es_workers=ES_WRITE_WORKERS  # Configure your desired concurrency here
        )

        if success:
            logger.info("Document processing completed successfully using stream mode")
            return True
        else:
            logger.error("Failed to process document using stream mode")
            return False

    except Exception as e:
        logger.error(f"Exception occurred while processing document stream: {e}")
        # 添加堆栈信息记录
        logger.error(f"Exception stack trace: {traceback.format_exc()}")
        # 确保异常被正确抛出
        raise
