import threading

import requests
from qcloud_cos import CosConfig, CosS3Client
from common.logger.logger import logger
from common.share.config import appConfig

# 全局变量
_client = None
_config = None
_client_lock = threading.Lock()  # 用于线程安全的锁


def _get_cos_client_and_config():
    """初始化COS客户端（如果尚未初始化），线程安全"""
    global _client, _config

    # 双重检查锁定模式
    if _client is None:
        with _client_lock:
            # 再次检查，避免在等待锁的过程中其他线程已经初始化
            if _client is None:
                # 从配置中获取COS信息
                cos_config = appConfig.automic.aisearch.cos_info
                secret_id = cos_config.secret_id
                secret_key = cos_config.secret_key
                region = cos_config.region
                bucket = cos_config.bucket
                token = None
                scheme = 'https'

                # 保存配置（用于生成URL）
                _config = {
                    'region': region,
                    'bucket': bucket,
                    'cos_url': f"https://{bucket}.cos.{region}.myqcloud.com"
                }

                # 创建COS客户端
                config = CosConfig(
                    Region=region,
                    SecretId=secret_id,
                    SecretKey=secret_key,
                    Token=token,
                    Scheme=scheme
                )
                _client = CosS3Client(config)

    return _client, _config


# 字节流上传方法
def upload_byte_stream(object_key: str, byte_stream: bytes) -> str:
    """上传字节流到COS"""
    try:
        logger.info(f"开始上传字节流到COS，对象键：{object_key}")
        client, config = _get_cos_client_and_config()  # 确保客户端已初始化
        response = client.put_object(
            Bucket=config['bucket'],
            Body=byte_stream,
            Key=object_key
        )
        logger.info(f"上传成功，ETag：{response.get('ETag', '')}")
        return f"{config['cos_url']}/{object_key}"
    except Exception as e:
        logger.error(f"COS字节流上传失败：{str(e)}")
        raise


# 生成预签名URL方法
def get_presigned_url(cos_url: str, expired: int = 3600) -> str:
    """根据COS URL生成预签名URL"""
    try:
        client, _ = _get_cos_client_and_config()  # 确保客户端已初始化

        # 解析URL获取bucket和key
        url_parts = cos_url.replace("https://", "").split("/")
        bucket_part = url_parts[0].split(".")
        bucket = bucket_part[0]
        key = "/".join(url_parts[1:])

        # 生成预签名URL
        presigned_url = client.get_presigned_url(
            Method='GET',
            Bucket=bucket,
            Key=key,
            Expired=expired
        )
        logger.info(f"生成预签名URL成功：{presigned_url}")
        return presigned_url
    except Exception as e:
        logger.error(f"生成预签名URL失败: {str(e)}")
        raise ValueError(f"无效的COS URL格式: {cos_url}")


if __name__ == '__main__':
    test_url = "https://data-agent-dev-1353879163.cos.ap-chongqing.myqcloud.com/test.txt"

    try:
        presigned_test_url = get_presigned_url(test_url)
        print(f"Generated Presigned URL for test_url: {presigned_test_url}")
        fetch_url = presigned_test_url
    except ValueError as e:
        print(f"Error generating presigned URL: {e}. Attempting to fetch directly from original URL.")
        fetch_url = test_url
    print(f"\nAttempting to fetch content from: {fetch_url}")
    response = requests.get(fetch_url)

    if response.status_code == 200:
        file_content_bytes = response.content
        print(f"Successfully fetched content. Content length: {len(file_content_bytes)} bytes.")
        new_object_key = "jupyter/uploaded_from_fetched_content/fetched_test_file.txt"
        try:
            uploaded_url = upload_byte_stream(byte_stream=file_content_bytes, object_key=new_object_key)
            print(f"\nSuccessfully uploaded fetched content to COS at: {uploaded_url}")
        except Exception as e:
            print(f"\nError uploading fetched content: {e}")
    else:
        print(
            f"Failed to fetch content from {fetch_url}. Status code: {response.status_code}, Reason: {response.reason}")
        print(f"Response text: {response.text}")
