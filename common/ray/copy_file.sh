#!/bin/sh

IMAGE_TAG="$1"
TARGET_DIR="/ray/$IMAGE_TAG"

# 1. 保留 /ray 下最近修改的一个目录，其它全部删除
cd /ray

# 找到最近修改的一个目录
latest_dir=$(ls -td */ | head -n 1 | sed 's#/##')

# 删除其它目录（不包括最新的）
#for dir in */; do
#    dir_name="${dir%/}"
#    if [ "$dir_name" != "$latest_dir" ]; then
#        rm -rf "$dir_name"
#    fi
#done

# 2. 判断目标目录是否存在
if [ -d "$TARGET_DIR" ]; then
    rm -rf "$TARGET_DIR"/*
else
    mkdir -p "$TARGET_DIR"
fi

# 3. 拷贝 common、etc、infra 目录到目标目录下
cd -  # 回到原目录
for folder in common etc infra; do
    src="./$folder"
    if [ -d "$src" ]; then
        cp -r "$src" "$TARGET_DIR/"
    fi
done