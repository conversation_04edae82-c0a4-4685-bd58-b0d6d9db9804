from prometheus_client import Counter, Histogram

# 定义计数器，用于记录规则验证的成功和失败次数
COUNTER_RULE_VALIDATION = Counter(
    'guardrail_rule_validation_total',
    'Total number of rule validations',
    ['session_id','rule', 'status']  # 标签：规则名称和状态（成功/失败）
)

# 定义直方图，用于记录每个规则的耗时
HISTOGRAM_RULE_DURATION = Histogram(
    'guardrail_rule_duration_seconds',
    'Duration of rule validation in seconds',
    ['session_id','rule']  # 标签：规则名称
)

def record_rule_validation(session_id: str,rule: str, status: str):
    """
    记录规则验证的成功或失败次数
    :param rule: 规则名称
    :param status: 验证状态（success 或 failure）
    """
    COUNTER_RULE_VALIDATION.labels(session_id=session_id,rule=rule, status=status).inc()

def record_rule_duration(session_id: str,rule: str, duration: float):
    """
    记录规则验证的耗时
    :param rule: 规则名称
    :param duration: 验证耗时（秒）
    """
    HISTOGRAM_RULE_DURATION.labels(session_id=session_id,rule=rule).observe(duration)
