from prometheus_client import Counter, Histogram, Gauge, Summary, CollectorRegistry, push_to_gateway

from common.logger.logger import logger
from common.share import env
from common.share.config import appConfig


# 定义指标
PRODUCT_NAME = env.PRODUCT_NAME
SERVICE_NAME = env.SERVICE_NAME
MODULE_NAME = __name__.split(".")[-1]
PREFIX = f"{PRODUCT_NAME}_{SERVICE_NAME}_{MODULE_NAME}".replace("-", "_")

registry = CollectorRegistry()


PUSHGATEWAY_URL = appConfig.observe.metric.push_gateway
SERVICE_NAME = env.SERVICE_NAME


def push_metrics():
    if not PUSHGATEWAY_URL:
        # 你可以选择 log warning 或直接 return
        logger.warning("PUSHGATEWAY_URL is not set, skip pushing metrics.")
        return
    try:
        push_to_gateway(PUSHGATEWAY_URL, job=SERVICE_NAME, registry=registry)
    except Exception as e:
        logger.error(f"Failed to push metrics to gateway: {e}")


# 定义基础指标
COUNTER_REQUEST_TOTAL = Counter(
    f'{PREFIX}_request_total',
    'Total number of requests',
    ['path', 'status', 'userid']
)

HISTOGRAM_LATENCY = Histogram(
    f'{PREFIX}_path_seconds',
    'Request latency in seconds',
    ['path', 'userid'],
    buckets=(0.1, 0.5, 1.0, 1.5, 2.0, 3.0, 5.0)
)

HISTOGRAM_CHAT_LATENCY = Histogram(
    f'{PREFIX}_chat_seconds',
    'Request chat latency in seconds',
    ['path', 'userid'],
    buckets=(1.0, 8.0, 15.0, 30.0, 60.0, 120.0, 240.0, 360.0)
)

HISTOGRAM_RAG_LATENCY = Histogram(
    f'{PREFIX}_rag_seconds',
    'Rag task in seconds',
    ['path', 'userid'],
    buckets=(1.0, 5.0, 15.0, 30.0, 60.0, 300.0, 600.0, 1200.0, 1800.0)
)

HISTOGRAM_TOOL_LATENCY = Histogram(
    f'{PREFIX}_call_tool_seconds',
    'Request call_tool_latency in seconds',
    ['path', 'userid'],
    buckets=(1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0)
)

GAUGE_MEMORY_USAGE = Gauge(
    f'{PREFIX}_memory_usage_bytes',
    'Memory usage in bytes',
    ['component', 'userid']
)

COUNTER_ERROR_TOTAL = Counter(
    f'{PREFIX}_error_total',
    'Total number of errors',
    ['path', 'error_type', 'userid']
)

SUMMARY_OPERATION_DURATION = Summary(
    f'{PREFIX}_operation_duration_seconds',
    'Duration of operations in seconds',
    ['operation', 'status', 'userid']
)

HISTOGRAM_CALL_TOOL_LATENCY = Histogram(
    f"{PREFIX}_call_tool_seconds",
    "Call tool latency in seconds",
    ["server_name", "tool_name", "status"],
    registry=registry,
    buckets=(1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0)
)

COUNTER_CALL_TOOL_TOTAL = Counter(
    f"{PREFIX}_call_tool_total",
    "Total call_tool count",
    ["server_name", "tool_name", "status"],
    registry=registry
)


def record_request(path: str, status: str = '200', userid: str = 'unknown'):
    """记录请求计数"""
    if status == 'success':
        status = '200'
    COUNTER_REQUEST_TOTAL.labels(path=path, status=status, userid=userid).inc()


def record_latency(path: str, duration: float, userid: str = 'unknown'):
    """记录请求延迟"""
    if path == "/chat":
        HISTOGRAM_CHAT_LATENCY.labels(path=path, userid=userid).observe(duration)
    elif path.startswith("call_tool"):
        HISTOGRAM_TOOL_LATENCY.labels(path=path, userid=userid).observe(duration)
    elif path.startswith("rag"):
        HISTOGRAM_RAG_LATENCY.labels(path=path, userid=userid).observe(duration)
    else:
        HISTOGRAM_LATENCY.labels(path=path, userid=userid).observe(duration)


def record_error(path: str, error_type: str, userid: str = 'unknown'):
    """记录错误计数"""
    COUNTER_ERROR_TOTAL.labels(path=path, error_type=error_type, userid=userid).inc()


def record_memory_usage(component: str, bytes_used: int, userid: str = 'unknown'):
    """记录内存使用"""
    GAUGE_MEMORY_USAGE.labels(component=component, userid=userid).set(bytes_used)


def record_operation(operation: str, duration: float, status: str = 'success', userid: str = 'unknown'):
    """记录操作持续时间"""
    SUMMARY_OPERATION_DURATION.labels(operation=operation, status=status, userid=userid).observe(duration)
