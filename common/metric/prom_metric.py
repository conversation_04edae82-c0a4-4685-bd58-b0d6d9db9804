import time
import asyncio
import inspect

from functools import wraps
from typing import Optional, Dict, Any, Callable
from common.metric.enpoints import record_error, record_request, record_operation, record_latency


def prom_metric(
    name: Optional[str] = None,
    labels: Optional[Dict[str, str]] = None,
    should_record_latency: bool = True,
    record_errors: bool = True,
    expand_name: Optional[str] = None
):
    """Prometheus 指标装饰器
    
    Args:
        name: 操作名称，默认使用函数名
        labels: 额外的标签
        should_record_latency: 是否记录延迟
        record_errors: bool = True
        expand_name: 拓展
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
            start_time = time.time()
            operation_name = name or func.__name__
            _expand_name = None
            if expand_name:
                if isinstance(expand_name, str):
                    sig = inspect.signature(func)
                    bound = sig.bind(*args, **kwargs)
                    bound.apply_defaults()
                    _expand_name = bound.arguments.get(expand_name)
                elif callable(expand_name):
                    _expand_name = expand_name(*args, **kwargs)
                else:
                    _expand_name = expand_name
            if _expand_name:
                operation_name = f"{operation_name}__{_expand_name}"

            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                # 记录成功请求
                record_request(operation_name, 'success')
                if should_record_latency:
                    record_latency(operation_name, duration)
                record_operation(operation_name, duration, 'success')
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                
                # 记录失败请求
                record_request(operation_name, 'error')
                if should_record_latency:
                    record_latency(operation_name, duration)
                if record_errors:
                    record_error(operation_name, type(e).__name__)
                record_operation(operation_name, duration, 'error')
                
                raise

        @wraps(func)
        def sync_wrapper(*args: Any, **kwargs: Any) -> Any:
            start_time = time.time()
            operation_name = name or func.__name__
            _expand_name = None
            if expand_name:
                if isinstance(expand_name, str):
                    sig = inspect.signature(func)
                    bound = sig.bind(*args, **kwargs)
                    bound.apply_defaults()
                    _expand_name = bound.arguments.get(expand_name)
                elif callable(expand_name):
                    _expand_name = expand_name(*args, **kwargs)
                else:
                    _expand_name = expand_name
            if _expand_name:
                operation_name = f"{operation_name}__{_expand_name}"

            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # 记录成功请求
                record_request(operation_name, 'success')
                if should_record_latency:
                    record_latency(operation_name, duration)
                record_operation(operation_name, duration, 'success')
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                
                # 记录失败请求
                record_request(operation_name, 'error')
                if should_record_latency:
                    record_latency(operation_name, duration)
                if record_errors:
                    record_error(operation_name, type(e).__name__)
                record_operation(operation_name, duration, 'error')
                
                raise

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator 