from prometheus_client import start_http_server
import threading
from common.share import env
from common.logger.logger import logger
from common.share.config import appConfig

# global server instance
server = None


class MetricsServer:
    def __init__(self, port: int = 9090, addr: str = '0.0.0.0'):
        """
        Initialize the metrics server
        
        Args:
            port: Port to expose metrics on (default: 9090)
            addr: Address to bind to
        """
        self.port = port
        self.addr = addr
        self.server_thread = None
        self._running = False

    def start(self):
        """Start the metrics server in a background thread"""
        if self._running:
            logger.warning("Metrics server is already running")
            return

        def run_server():
            try:
                start_http_server(self.port, addr=self.addr)
                logger.info(f"Started Prometheus metrics server on {self.addr}:{self.port}")
                self._running = True
            except Exception as e:
                logger.error(f"Failed to start metrics server: {e}")
                self._running = False

        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        # Wait for server to start
        self.server_thread.join(timeout=1)
        return self._running

    def stop(self):
        """Stop the metrics server"""
        # Note: Prometheus client doesn't provide a direct way to stop the server
        # The server will be stopped when the process exits
        self._running = False
        logger.info("Metrics server stopped")



def start_metrics_server(port: int = None, addr: str = None):
    """
    Start the metrics server with optional port and address override
    
    Args:
        port: Optional port to override the default/environment setting
        addr: Optional address to override the default/environment setting
    """
    global server
    server = MetricsServer(
            port=port if port is not None else int(appConfig.observe.metric.port),
            addr=addr if addr is not None else appConfig.observe.metric.addr
    )
    server.start()
    return server

def stop_metrics_server():
    """
    Stop the metrics server
    
    Args:
        server: Optional specific server instance to stop. If None, stops the default server.
    """
    global server
    if server is not None:
        server.stop()