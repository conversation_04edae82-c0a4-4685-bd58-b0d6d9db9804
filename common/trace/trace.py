from typing import Any, Callable, Union
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import (
    BatchSpanProcessor,
)
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import Resource
from common.share.config import appConfig
from common.logger.logger import logger
from opentelemetry.sdk.trace.export import SimpleSpanProcessor
from opentelemetry.sdk.trace.export import ConsoleSpanExporter
from opentelemetry.trace import Tracer
from opentelemetry.trace import SpanContext
from opentelemetry.trace import TraceFlags, NonRecordingSpan
import uuid

from common.share.context import ChatContext
from opentelemetry.trace import SpanKind,Span
from common.share import env
from opentelemetry.trace import StatusCode

from langsmith import traceable as langsmith_traceable
from langsmith import get_current_run_tree

TraceName = "intellix-ds-agent"
trace_loop_enabled = False
local_tracer = None
def traceable(
    *args: Any,
    **kwargs: Any,
) -> Union[Callable, Callable[[Callable], Callable]]:
    return langsmith_traceable(*args, **kwargs)

# try:
#     from traceloop.sdk import Traceloop
#     Traceloop.init(
#         app_name=TraceName,
#         metrics_exporter = False,
#         resource_attributes={
#             "token": appConfig.observe.trace.otel_token,
#             "host.name": env.INSTANCE_ID,
#             "service.name": TraceName
#         },
#         exporter=OTLPSpanExporter(
#             timeout=20,
#             endpoint=appConfig.observe.trace.otel_endpoint,
#             headers=(
#                 ("authorization", appConfig.observe.trace.otel_token),
#             )
#         )
#     )
#     trace_loop_enabled = True
#     logger.info("TraceLoop enabled")
# except Exception as e:
#     logger.info("TraceLoop disabled, error: %s", e)
#     trace_loop_enabled = False
#     logger.info("TraceLoop disabled")

# not work
# try:
#     from opentelemetry.instrumentation.elasticsearch import ElasticsearchInstrumentor
#     # from opentelemetry.instrumentation.instrumentor import BaseInstrumentor
#     # from opentelemetry.trace import SpanKind, Status, StatusCode, get_tracer
#     ElasticsearchInstrumentor("intellix").instrument(
#         tracer_provider=trace.get_tracer_provider(),
#     )
#     logger.info("Elasticsearch instrumentation enabled")
# except Exception as e:
#     logger.error("Elasticsearch instrumentation disabled, error: %s", e)
#     logger.info("Elasticsearch instrumentation disabled")

# try:
#     from opentelemetry.instrumentation.mysql import MySQLInstrumentor
#     MySQLInstrumentor().instrument()
#     logger.info("MySQL instrumentation enabled")
# except Exception as e:
#     logger.error("MySQL instrumentation disabled, error: %s", e)
#     logger.info("MySQL instrumentation disabled")

def get_tracer(name: str = TraceName):
    trace.set_tracer_provider(TracerProvider(
        resource=Resource.create({
            "service.name": name if name is not None else TraceName,
            "service.version": "1.0.0",
            "deployment.environment": "development"
        })
    ))
    if appConfig.observe.trace.otel_endpoint:
        otlp_exporter = OTLPSpanExporter(
            timeout=10,
            endpoint=appConfig.observe.trace.otel_endpoint,
            headers=(
                ("authorization", appConfig.observe.trace.otel_token),
            )
        )
        trace.get_tracer_provider().add_span_processor(
            BatchSpanProcessor(otlp_exporter)
        )
        if env.IS_LOG_LEVEL_TRACE:
            trace.get_tracer_provider().add_span_processor(
            SimpleSpanProcessor(ConsoleSpanExporter())
        )
    else:
        trace.get_tracer_provider().add_span_processor(
            SimpleSpanProcessor(ConsoleSpanExporter())
        )
    tracer : Tracer = trace.get_tracer(name if name is not None else TraceName)
    return tracer


def flush_tracer():
    # trace.get_tracer_provider().force_flush()
    pass


def get_true_tracer(name: str = None):
    global local_tracer, trace_loop_enabled
    if local_tracer is not None:
        return local_tracer
    if trace_loop_enabled:
        local_tracer = trace.get_tracer(TraceName)
    else:
        local_tracer = get_tracer(name)

    return local_tracer


def new_child_span(ctx: ChatContext, name: str):
    tracer = get_true_tracer()
    child_span = tracer.start_span(
        name=name,
        context=trace.set_span_in_context(ctx.parent_span),
        attributes=set_attributes(ctx)
    )
    return child_span


def start_parent_span(ctx: ChatContext, name = None):
    tracer = get_true_tracer()
    span_context = SpanContext(
        trace_id=ctx.get_apm_trace_id(),
        span_id=int(uuid.uuid4().hex[:16], 16) if ctx.parent_span is None else ctx.parent_span,
        is_remote=False,
        trace_flags=TraceFlags(TraceFlags.SAMPLED)
    )
    span = NonRecordingSpan(span_context)
    context = trace.set_span_in_context(span)
    if name is None:
        name = ctx.get_path()

    parent_span = tracer.start_span(
        name=name,
        context=context,
        attributes=set_attributes(ctx),
        kind=SpanKind.SERVER if ctx.parent_span is None else SpanKind.INTERNAL,
    )

    return parent_span


def set_attributes(ctx: ChatContext):
    return {
        "trace_id": ctx.get_trace_id(),
        "session_id": ctx.get_session_id(),
        "sub_account_uin": ctx.get_sub_account_uin(),
        "endpoint_name": ctx.get_path(),
        "service.instance.id": env.INSTANCE_ID,
    }


def add_metadata(ctx: ChatContext):
    run_tree = get_current_run_tree()
    run_tree.add_metadata(
        set_attributes(ctx),
    )

def add_event(event_name: str, event_data: dict):
    span_add_event(trace.get_current_span(), event_name, event_data)


def set_status(error: bool):
    set_span_status(trace.get_current_span(), error)


def span_add_event(span: Span, event_name: str, event_data: dict):
    span.add_event(event_name, attributes=event_data)


def set_span_status(span: Span, error: bool):
    span.set_status(StatusCode.ERROR if error else StatusCode.OK)