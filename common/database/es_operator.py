from functools import wraps
from elasticsearch import Elasticsearch, AsyncElasticsearch, exceptions
from common.logger.logger import logger
from common.share.config import ESConfig
from threading import Lock
from typing import Dict, Type, Any


class RetryError(Exception):
    pass


class ESOperator:
    def __init__(self, config: ESConfig):
        self.config = config
        self.sync_client = Elasticsearch(
            hosts=[{"host": config.host, "port": config.port, "scheme": "http"}],
            basic_auth=(config.user, config.password) if config.user else None,
            request_timeout=config.timeout,
            connections_per_node=10,
        )

        # 异步客户端（支持认证）
        self.async_client = AsyncElasticsearch(
            hosts=[{"host": config.host, "port": config.port, "scheme": "http"}],
            basic_auth=(config.user, config.password) if config.user else None,
            request_timeout=config.timeout,
            connections_per_node=10,
        )

    @staticmethod
    def _retry_decorator(func):  # 同步方法装饰器
        @wraps(func)
        def wrapper(instance, *args, **kwargs):  # 参数名改为 instance
            max_retries = instance.config.max_retries
            for attempt in range(max_retries):
                try:
                    return func(instance, *args, **kwargs)
                except (exceptions.ConnectionError, exceptions.TransportError) as e:
                    logger.error(f"Sync Retry {attempt + 1}/{max_retries}: {e}")
            raise RetryError("Sync retries exhausted")

        return wrapper

    @classmethod
    def _async_retry_decorator(cls, func):  # 异步方法装饰器
        @wraps(func)
        async def async_wrapper(instance, *args, **kwargs):
            max_retries = instance.config.max_retries  # 通过 instance 访问
            for attempt in range(max_retries):
                try:
                    return await func(instance, *args, **kwargs)
                except (exceptions.ConnectionError, exceptions.TransportError) as e:
                    logger.error(f"Async Retry {attempt + 1}/{max_retries}: {e}")
            raise RetryError("Async retries exhausted")

        return async_wrapper
