from playhouse.pool import PooledMySQLDatabase
from playhouse.reflection import generate_models
from common.logger.logger import logger
from common.share.config import appConfig
import peewee
import pymysql

class CustomPooledMySQLDatabase(PooledMySQLDatabase):
    """自定义的PooledMySQLDatabase，添加连接可用性检查和重连逻辑"""
    
    def __enter__(self):
        """重写__enter__方法，添加连接可用性检查"""
        try:
            # 检查连接是否可用
            if self._state.conn and self._is_closed(self._state.conn):
                logger.warning("Database connection is not usable, closing and reconnecting...")
                super().close()
                super().connect()
                logger.info("Database connection reconnected successfully")
            
            # 调用父类的__enter__方法
            return super(CustomPooledMySQLDatabase, self).__enter__()
        except Exception as e:
            logger.error(f"Error in database __enter__: {e}")
            # 如果出现异常，尝试重新连接
            try:
                super(CustomPooledMySQLDatabase, self).close()
                super(CustomPooledMySQLDatabase, self).connect()
                logger.info("Database connection reconnected after error")
                return super(CustomPooledMySQLDatabase, self).__enter__()
            except Exception as reconnect_error:
                logger.error(f"Failed to reconnect database: {reconnect_error}")
                raise

    def atomic(self, *args, **kwargs):
        """重写atomic方法，添加连接检查和重连逻辑"""
        try:
            if self._state.conn and self._is_closed(self._state.conn):
                logger.warning("Database connection is not usable, closing and reconnecting...")
                super(CustomPooledMySQLDatabase, self).close()
                super(CustomPooledMySQLDatabase, self).connect()
                logger.info("Database connection reconnected successfully")
            return super(CustomPooledMySQLDatabase, self).atomic(*args, **kwargs)
        except (peewee.OperationalError, pymysql.err.OperationalError) as e:
            # 检查是否是连接相关的错误
            error_msg = str(e).lower()
            if any(keyword in error_msg for keyword in [
                'mysql server has gone away', 
                'lost connection', 
                'connection timed out',
                'server has gone away'
            ]):
                logger.warning(f"Connection error detected: {e}, attempting to reconnect...")
                try:
                    # 关闭并重新连接
                    super(CustomPooledMySQLDatabase, self).close()
                    super(CustomPooledMySQLDatabase, self).connect()
                    logger.info("Successfully reconnected after connection error")
                    # 重试操作
                    return super(CustomPooledMySQLDatabase, self).atomic(*args, **kwargs)
                except Exception as reconnect_error:
                    logger.error(f"Failed to reconnect after connection error: {reconnect_error}")
                    raise
            else:
                # 不是连接错误，直接抛出
                raise

class MysqlPool:
    @staticmethod
    def check_conn(func):
        '''
            在执行peewee操作时，如果连接不可用，则关闭连接并重新连接
            这个装饰器可以不用，因为CustomPooledMySQLDatabase已经实现了这个功能
        '''
        """数据库连接检查装饰器"""
        def wrapper(*args, **kwargs):
            if not args[0].persistence.pool_db.is_connection_usable():
                args[0].persistence.pool_db.close()
                args[0].persistence.pool_db.connect()
            return func(*args, **kwargs)
        return wrapper

    def __init__(self, conf):
        self.mysql_config = conf.copy()  # 避免修改原始配置
        password = self.mysql_config.get("password")
        self.mysql_config["password"] = str(password)
        self.database = self.mysql_config.pop("database")
        # 使用自定义的PooledMySQLDatabase
        self.pool_db = CustomPooledMySQLDatabase(
            self.database,
            **self.mysql_config,
        )
        self.models = generate_models(self.pool_db)
        logger.info(f"MysqlPool init success, self.host: {self.mysql_config['host']}")

    def get_model(self, model_name):
        return self.models[model_name]


g_db_pool = None


def global_metadata_db_pool():
    global g_db_pool
    if g_db_pool is None:
        g_db_pool = get_metadata_db_pool(appConfig.common.metadata_db)
    return g_db_pool


def get_metadata_db_pool(conf=None):
    if conf is None:
        conf = appConfig.common.metadata_db
    if conf is None or conf.host is None or conf.port is None or conf.user is None or conf.password is None or conf.database is None:
        return None
    return MysqlPool({
        "host": conf.host,
        "port": conf.port,
        "user": conf.user,
        "password": conf.password,
        "database": conf.database,
        "max_connections": conf.max_connections,
        "timeout": conf.timeout,
        "stale_timeout": conf.stale_timeout
    })
