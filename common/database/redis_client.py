import threading
from typing import Dict

from redis import Redis, ConnectionPool
from common.logger import logger
from common.share.config import RedisConfig, appConfig

logger = logger.get_logger(__name__)


class RedisClient:
    """支持多配置的Redis客户端管理器（按需创建连接池）"""

    _instances: Dict[str, 'RedisClient'] = {}  # 按配置哈希存储实例
    _lock = threading.Lock()  # 线程安全锁

    def __init__(self, config: RedisConfig):
        """私有构造函数（通过类方法获取实例）"""
        self.config = config
        self.client = None
        self._init_client()

    @classmethod
    def get_instance(cls, config: RedisConfig) -> Redis:
        """
        获取指定配置的Redis客户端
        参数：
            config: RedisConfig - Redis连接配置
        返回：
            Redis - 配置对应的客户端实例
        """
        # 生成配置唯一标识
        config_key = cls._generate_config_key(config)

        with cls._lock:
            instance = cls._instances.get(config_key)

            if not instance:
                # 创建新实例并初始化
                instance = RedisClient(config)
                cls._instances[config_key] = instance
                logger.info(f"创建新Redis连接池 {config.host}:{config.port}")

            return instance.client

    @staticmethod
    def _generate_config_key(config: RedisConfig) -> str:
        """生成配置唯一标识"""
        return f"{config.host}:{config.port}:{config.password}"

    def _init_client(self):
        """初始化客户端连接"""
        # 创建连接池
        pool = ConnectionPool(
            host=self.config.host,
            port=self.config.port,
            password=self.config.password,
            max_connections=self.config.max_connections,
            socket_timeout=self.config.timeout,
            decode_responses=True,
            health_check_interval=15,
            retry_on_timeout=True
        )

        # 创建客户端实例
        self.client = Redis(
            connection_pool=pool,
            decode_responses=True
        )

        # 立即验证连接
        if not self.test_connection():
            raise ConnectionError(f"Redis连接失败 {self.config.host}:{self.config.port}")

        logger.info(f"Redis连接池初始化成功 {self.config.host}:{self.config.port}")

    def test_connection(self) -> bool:
        """测试连接是否有效"""
        try:
            return self.client.ping()
        except Exception as e:
            logger.error(f"Redis连接测试失败 {self.config.host}:{self.config.port}: {str(e)}")
            raise ConnectionError(f"Redis连接异常: {str(e)}")


if __name__ == '__main__':
    redis_conf = appConfig.common.redis
    redis = RedisClient.get_instance(redis_conf)
    print(redis)
