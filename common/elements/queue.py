import threading
import queue
import asyncio
import traceback
import time
import logging
class StreamQueueClosed(BaseException):
    """The Future or Task was cancelled."""

class StreamQueueTimeout(BaseException):
    """The Future or Task was cancelled."""

class StreamQueue():
    def __init__(self, name: str = None, logger: logging.Logger = None):
        self.name = name if name is not None else f"StreamQueue-{id(self)}"
        self.logger = logger if logger is not None else logging.getLogger(f"StreamQueue {self.name}")
        self.event = threading.Event()
        self.queue = queue.Queue()
    
    def put_sync(self, data, skip_closed=True): 
        if self.event.is_set():
            if skip_closed:
                return
            raise StreamQueueClosed()
        self.queue.put(data)

    async def put(self, data, skip_closed=True):
        await asyncio.to_thread(self.put_sync, data, skip_closed)

    async def get(self, timeout=None):
        return await asyncio.to_thread(self.get_sync, timeout=timeout)
    
    def get_sync(self, timeout=None):
        start_time = time.time()
        while True:
            if self.event.is_set():
                if self.queue.empty():
                    raise StreamQueueClosed()
            if timeout is not None and time.time() - start_time > timeout:
                raise StreamQueueTimeout()
            try:
                item = self.queue.get(timeout=0.1)
                return item
            except queue.Empty:
                time.sleep(0.1)

    def is_closed(self):
        return self.event.is_set()

    def __del__(self):
        while not self.queue.empty():
            item = self.queue.get()
            print(f"StreamQueue {self.name} destroyed: {item}")
        print(f"StreamQueue {self.name} destroyed")

    def close(self):
        print(f"StreamQueue {self.name} close")
        self.event.set()


class StreamQueueManager:
    def __init__(self, name: str = None, logger: logging.Logger = None):
        self.name = name if name is not None else f"StreamQueueManager-{id(self)}"
        self.logger = logger if logger is not None else logging.getLogger(f"StreamQueueManager {self.name}")
        self.stream_queue : StreamQueue = StreamQueue(name, self.logger)
        self.stream_producer_thread = None
        self.stream_consumer_thread = None
        self.loop_producer = None
        self.loop_consumer = None

    def start_producer(self, coroutine_func, *args):    
        def _handle_stream_producer(self: StreamQueueManager, coroutine, *args):        
            # 创建新的事件循环
            try:
                self.loop_producer = asyncio.new_event_loop()
                asyncio.set_event_loop(self.loop_producer)
                async def wrapper(*args):
                    try:
                        async for result in coroutine(*args):
                            await self.stream_queue.put(result)
                    except StreamQueueClosed:
                        pass
                    except Exception as e:
                        self.logger.error(f"StreamQueueManager {self.name} Error: {e} , traceback: {traceback.format_exc()}")
                    
                # asyncio.run_coroutine_threadsafe(wrapper(*args),self.loop_producer)
                self.loop_producer.run_until_complete(wrapper(*args))
            except Exception as e:
                self.logger.error(f"StreamQueueManager {self.name} Error: {e} , traceback: {traceback.format_exc()}")
            finally:
                self.stream_queue.close()
                self.loop_producer.stop()
                self.loop_producer = None
            
        self.stream_producer_thread = threading.Thread(
            target=_handle_stream_producer,
            args=(self, coroutine_func, *args),
            daemon=True
        )
        self.stream_producer_thread.start()
        return self.stream_producer_thread
    
    def start_consumer(self, coroutine_func, *args):    
        def _handle_stream_consumer(self, coroutine, *args):
            try:
                self.loop_consumer = asyncio.new_event_loop()
                asyncio.set_event_loop(self.loop_consumer)
                async def wrapper(*args):
                    await coroutine(*args)
                self.loop_consumer.run_until_complete(wrapper(*args))
            except Exception as e:
                self.logger.error(f"StreamQueueManager {self.name} Error: {e} , traceback: {traceback.format_exc()}")
            finally:
                self.loop_consumer.stop()
                self.loop_consumer = None

        self.stream_consumer_thread = threading.Thread(
            target=_handle_stream_consumer,
            args=(self, coroutine_func, *args),
            daemon=True
        )
        self.stream_consumer_thread.start()
        return self.stream_consumer_thread
    

    def __del__(self):
        self.close()
        self.logger.info(f"StreamQueueManager {self.name} destroyed")

    def close(self):
        if self.stream_queue is not None:
            self.stream_queue.close()
        if self.stream_producer_thread is not None:
            self.stream_producer_thread.join(timeout=1)
            self.stream_producer_thread = None
        if self.stream_consumer_thread is not None:
            self.stream_consumer_thread.join(timeout=1)
        self.stream_consumer_thread = None
        self.coroutine = None
        self.task = None
